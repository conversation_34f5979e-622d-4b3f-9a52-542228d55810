{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/layout/HeaderQuickActions.vue"}, "originalCode": "<template>\n  <div class=\"flex items-center space-x-2\">\n    <button\n      @click=\"$emit('quick-create-project')\"\n      class=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n    >\n      <svg class=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n      </svg>\n      Nuovo Progetto\n    </button>\n\n    <button\n      @click=\"$emit('quick-add-task')\"\n      class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n    >\n      <svg class=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n      </svg>\n      Nuovo Task\n    </button>\n  </div>\n</template>\n\n<script setup>\ndefineEmits(['quick-create-project', 'quick-add-task'])\n</script>", "modifiedCode": "<template>\n  <div class=\"flex items-center space-x-2\">\n    <!-- Quick actions basate sulla pagina corrente -->\n    <template v-if=\"showProjectActions\">\n      <button\n        @click=\"$emit('quick-create-project')\"\n        class=\"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n      >\n        <svg class=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n        </svg>\n        Nuovo Progetto\n      </button>\n    </template>\n\n    <template v-if=\"showTaskActions\">\n      <button\n        @click=\"$emit('quick-add-task')\"\n        class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n      >\n        <svg class=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n        </svg>\n        Nuovo Task\n      </button>\n    </template>\n\n    <!-- Azioni generiche sempre disponibili -->\n    <button\n      @click=\"$emit('quick-search')\"\n      class=\"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n      title=\"Ricerca rapida\"\n    >\n      <svg class=\"h-3 w-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n      </svg>\n    </button>\n  </div>\n</template>\n\n<script setup>\ndefineEmits(['quick-create-project', 'quick-add-task'])\n</script>"}