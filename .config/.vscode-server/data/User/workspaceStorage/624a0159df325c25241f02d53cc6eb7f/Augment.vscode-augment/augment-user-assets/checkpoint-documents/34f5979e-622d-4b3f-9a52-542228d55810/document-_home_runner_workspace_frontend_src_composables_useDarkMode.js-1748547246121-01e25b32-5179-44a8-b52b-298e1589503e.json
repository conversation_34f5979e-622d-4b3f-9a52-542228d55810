{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/composables/useDarkMode.js"}, "originalCode": "import { ref, watch } from 'vue'\n\n// Global dark mode state\nconst isDarkMode = ref(false)\nlet isInitialized = false\n\nexport function useDarkMode() {\n  const toggleDarkMode = () => {\n    isDarkMode.value = !isDarkMode.value\n  }\n\n  const setDarkMode = (value) => {\n    isDarkMode.value = value\n  }\n\n  const initializeDarkMode = () => {\n    if (isInitialized) return\n\n    const savedDarkMode = localStorage.getItem('darkMode')\n\n    if (savedDarkMode === 'true') {\n      isDarkMode.value = true\n    } else if (savedDarkMode === 'false') {\n      isDarkMode.value = false\n    } else {\n      // Default to system preference\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches\n      isDarkMode.value = prefersDark\n    }\n\n    isInitialized = true\n  }\n\n  // Watch for changes and apply to DOM\n  if (!isInitialized) {\n    watch(isDarkMode, (newValue) => {\n      if (newValue) {\n        document.documentElement.classList.add('dark')\n        localStorage.setItem('darkMode', 'true')\n      } else {\n        document.documentElement.classList.remove('dark')\n        localStorage.setItem('darkMode', 'false')\n      }\n    }, { immediate: false })\n\n    // Listen for system theme changes\n    if (typeof window !== 'undefined') {\n      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')\n      const handleSystemThemeChange = (e) => {\n        // Only update if user hasn't manually set a preference\n        const savedDarkMode = localStorage.getItem('darkMode')\n        if (!savedDarkMode) {\n          isDarkMode.value = e.matches\n        }\n      }\n      mediaQuery.addEventListener('change', handleSystemThemeChange)\n    }\n  }\n\n  return {\n    isDarkMode,\n    toggleDarkMode,\n    setDarkMode,\n    initializeDarkMode\n  }\n}\n", "modifiedCode": "import { ref } from 'vue'\n\n// Global dark mode state\nconst isDarkMode = ref(false)\n\n// Apply dark mode to DOM\nconst applyDarkMode = (value) => {\n  if (value) {\n    document.documentElement.classList.add('dark')\n    localStorage.setItem('darkMode', 'true')\n  } else {\n    document.documentElement.classList.remove('dark')\n    localStorage.setItem('darkMode', 'false')\n  }\n}\n\nexport function useDarkMode() {\n  const toggleDarkMode = () => {\n    isDarkMode.value = !isDarkMode.value\n    applyDarkMode(isDarkMode.value)\n  }\n\n  const setDarkMode = (value) => {\n    isDarkMode.value = value\n    applyDarkMode(value)\n  }\n\n  const initializeDarkMode = () => {\n    const savedDarkMode = localStorage.getItem('darkMode')\n\n    if (savedDarkMode === 'true') {\n      isDarkMode.value = true\n      applyDarkMode(true)\n    } else if (savedDarkMode === 'false') {\n      isDarkMode.value = false\n      applyDarkMode(false)\n    } else {\n      // Default to system preference\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches\n      isDarkMode.value = prefersDark\n      applyDarkMode(prefersDark)\n    }\n  }\n\n  return {\n    isDarkMode,\n    toggleDarkMode,\n    setDarkMode,\n    initializeDarkMode\n  }\n}\n"}