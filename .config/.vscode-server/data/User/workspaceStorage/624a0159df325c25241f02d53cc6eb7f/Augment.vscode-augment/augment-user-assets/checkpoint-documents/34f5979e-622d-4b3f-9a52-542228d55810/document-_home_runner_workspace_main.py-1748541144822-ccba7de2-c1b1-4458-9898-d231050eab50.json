{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "main.py"}, "originalCode": "import sys\nimport os\nsys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))\n\nfrom backend.app import create_app\n\napp = create_app()", "modifiedCode": "import sys\nimport os\nsys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))\n\nfrom backend.app import create_app\n\napp = create_app()\n\nif __name__ == '__main__':\n    print(\"Starting Flask development server...\")\n    app.run(host='0.0.0.0', port=5000, debug=True)"}