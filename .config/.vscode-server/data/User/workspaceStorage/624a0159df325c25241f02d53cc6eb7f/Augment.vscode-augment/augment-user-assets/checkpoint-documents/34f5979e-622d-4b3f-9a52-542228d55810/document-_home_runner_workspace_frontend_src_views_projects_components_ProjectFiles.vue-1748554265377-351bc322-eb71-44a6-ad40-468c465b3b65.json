{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectFiles.vue"}, "originalCode": "<template>\n  <div class=\"project-files\">\n    <div class=\"bg-white shadow rounded-lg p-6\">\n      <h3 class=\"text-lg font-medium text-gray-900 mb-6\">File del Progetto</h3>\n\n      <div class=\"text-center py-8\">\n        <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\" />\n        </svg>\n        <h3 class=\"mt-2 text-sm font-medium text-gray-900\">Gestione File in sviluppo</h3>\n        <p class=\"mt-1 text-sm text-gray-500\">Upload e gestione documenti in arrivo...</p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\ndefineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n</script>\n", "modifiedCode": "<template>\n  <div class=\"project-files\">\n    <div class=\"bg-white shadow rounded-lg p-6\">\n      <h3 class=\"text-lg font-medium text-gray-900 mb-6\">File del Progetto</h3>\n\n      <!-- File Upload Area -->\n      <div class=\"border-2 border-dashed border-gray-300 rounded-lg p-6 mb-6 hover:border-gray-400 transition-colors\">\n        <div class=\"text-center\">\n          <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n          </svg>\n          <div class=\"mt-4\">\n            <label class=\"cursor-pointer\">\n              <span class=\"mt-2 block text-sm font-medium text-gray-900\">\n                Trascina file qui o clicca per selezionare\n              </span>\n              <input type=\"file\" class=\"sr-only\" multiple>\n            </label>\n            <p class=\"mt-2 text-xs text-gray-500\">\n              PNG, JPG, PDF, DOC, XLS fino a 10MB\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <!-- Files List -->\n      <div class=\"space-y-3\">\n        <div v-for=\"file in files\" :key=\"file.id\" class=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50\">\n          <div class=\"flex items-center space-x-4\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n            </div>\n            <div class=\"flex-1\">\n              <h4 class=\"text-sm font-medium text-gray-900\">{{ file.name }}</h4>\n              <div class=\"flex items-center space-x-4 text-xs text-gray-500\">\n                <span>{{ formatFileSize(file.size) }}</span>\n                <span>{{ formatDate(file.uploaded_at) }}</span>\n              </div>\n            </div>\n          </div>\n          <div class=\"flex items-center space-x-2\">\n            <button class=\"text-gray-400 hover:text-gray-600\" title=\"Scarica\">\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n        <div v-if=\"files.length === 0\" class=\"text-center py-8\">\n          <p class=\"text-gray-500\">Nessun file caricato per questo progetto</p>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\ndefineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n</script>\n"}