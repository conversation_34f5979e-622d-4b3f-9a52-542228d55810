{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/templates/vue_app.html"}, "originalCode": "<!DOCTYPE html>\n<html lang=\"it\">\n  <head>\n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <title>DatPortal - Enterprise Intranet</title>\n    <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n    <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap\" rel=\"stylesheet\">\n\n    <!-- Favicon -->\n    <link rel=\"icon\" type=\"image/x-icon\" href=\"{{ url_for('static', filename='favicon.ico') }}\">\n\n    <!-- Meta tags for SEO -->\n    <meta name=\"description\" content=\"DatPortal - Sistema di gestione progetti, task e risorse\">\n    <meta name=\"keywords\" content=\"progetti, task, gestione, risorse, KPI, dashboard\">\n    <meta name=\"author\" content=\"DatVinci\">\n\n    <!-- Open Graph meta tags -->\n    <meta property=\"og:title\" content=\"DatPortal\">\n    <meta property=\"og:description\" content=\"Sistema di gestione progetti, task e risorse\">\n    <meta property=\"og:type\" content=\"website\">\n\n    <!-- CSRF Token for API requests -->\n    <meta name=\"csrf-token\" content=\"{{ csrf_token() }}\">\n\n\n\n\n  </head>\n  <body>\n    <div id=\"app\"></div>\n\n    <!-- Global Configuration for Vue.js -->\n    <script>\n      // Global app configuration\n      window.APP_CONFIG = {\n        apiUrl: '/api',\n        baseUrl: '{{ request.url_root }}',\n        csrfToken: '{{ csrf_token() }}',\n        user: {{ current_user.to_dict()|tojson if current_user.is_authenticated else 'null' }},\n        isAuthenticated: {{ 'true' if current_user.is_authenticated else 'false' }},\n        version: '1.0.0',\n        environment: '{{ config.ENV }}',\n        debug: {{ 'true' if config.DEBUG else 'false' }}\n      };\n\n      // La configurazione tenant verrà caricata dal frontend Vue.js\n\n      // Global error handler\n      window.addEventListener('error', function(event) {\n        console.error('Global error:', event.error);\n      });\n\n      // Global unhandled promise rejection handler\n      window.addEventListener('unhandledrejection', function(event) {\n        console.error('Unhandled promise rejection:', event.reason);\n      });\n    </script>\n  </body>\n</html>", "modifiedCode": "<!DOCTYPE html>\n<html lang=\"it\">\n  <head>\n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <title>DatPortal - Enterprise Intranet</title>\n    <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n    <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap\" rel=\"stylesheet\">\n    \n    <!-- Favicon -->\n    <link rel=\"icon\" type=\"image/x-icon\" href=\"{{ url_for('static', filename='favicon.ico') }}\">\n    \n    <!-- Meta tags for SEO -->\n    <meta name=\"description\" content=\"DatPortal - Sistema di gestione progetti, task e risorse\">\n    <meta name=\"keywords\" content=\"progetti, task, gestione, risorse, KPI, dashboard\">\n    <meta name=\"author\" content=\"DatVinci\">\n    \n    <!-- Open Graph meta tags -->\n    <meta property=\"og:title\" content=\"DatPortal\">\n    <meta property=\"og:description\" content=\"Sistema di gestione progetti, task e risorse\">\n    <meta property=\"og:type\" content=\"website\">\n    \n    <!-- CSRF Token for API requests -->\n    <meta name=\"csrf-token\" content=\"{{ csrf_token() }}\">\n    \n    \n    \n    \n  </head>\n  <body>\n    <div id=\"app\"></div>\n    \n    <!-- Global Configuration for Vue.js -->\n    <script>\n      // Global app configuration\n      window.APP_CONFIG = {\n        apiUrl: '/api',\n        baseUrl: '{{ request.url_root }}',\n        csrfToken: '{{ csrf_token() }}',\n        user: {{ current_user.to_dict()|tojson if current_user.is_authenticated else 'null' }},\n        isAuthenticated: {{ 'true' if current_user.is_authenticated else 'false' }},\n        version: '1.0.0',\n        environment: '{{ config.ENV }}',\n        debug: {{ 'true' if config.DEBUG else 'false' }}\n      };\n\n      // La configurazione tenant verrà caricata dal frontend Vue.js\n\n      // Global error handler\n      window.addEventListener('error', function(event) {\n        console.error('Global error:', event.error);\n      });\n\n      // Global unhandled promise rejection handler\n      window.addEventListener('unhandledrejection', function(event) {\n        console.error('Unhandled promise rejection:', event.reason);\n      });\n    </script>\n  </body>\n</html>"}