{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/auth.py"}, "originalCode": "\"\"\"\nAPI endpoints for authentication and user session management.\nProvides REST API for current user data, session validation, and user preferences.\n\"\"\"\n\nfrom flask import Blueprint, request, jsonify\nfrom flask_login import current_user, login_user, logout_user\nfrom werkzeug.security import check_password_hash\nfrom datetime import datetime\n\nfrom utils.db import db\nfrom models import User, UserProfile\nfrom utils.api_utils import (\n    api_response, api_login_required, handle_api_error\n)\nfrom utils.permissions import ROLE_PERMISSIONS\nfrom extensions import csrf\n\n# Create blueprint\napi_auth = Blueprint('api_auth', __name__, url_prefix='/auth')\n\ndef get_user_permissions(user_role):\n    \"\"\"\n    Get all permissions for a user role.\n\n    Args:\n        user_role (str): User role\n\n    Returns:\n        list: List of permissions for the role\n    \"\"\"\n    if not user_role or user_role not in ROLE_PERMISSIONS:\n        return []\n\n    # Admin has all permissions\n    if user_role == 'admin':\n        from utils.permissions import ALL_PERMISSIONS\n        return list(ALL_PERMISSIONS)\n\n    return list(ROLE_PERMISSIONS.get(user_role, set()))\n\n@api_auth.route('/login', methods=['POST'])\*************\ndef login():\n    \"\"\"\n    Login API endpoint.\n\n    Expects JSON data with username/email and password.\n    Returns user data and session info on success.\n    \"\"\"\n    try:\n        data = request.get_json()\n\n        if not data:\n            return api_response(\n                success=False,\n                message=\"No data provided\",\n                status_code=400\n            )\n\n        # Get credentials - support both username and email\n        username_or_email = data.get('username') or data.get('email')\n        password = data.get('password')\n\n        if not username_or_email or not password:\n            return api_response(\n                message=\"Username/email and password are required\",\n                status_code=400\n            )\n\n        # Find user by username or email\n        user = User.query.filter(\n            (User.username == username_or_email) |\n            (User.email == username_or_email)\n        ).first()\n\n        if not user or not check_password_hash(user.password_hash, password):\n            return api_response(\n                message=\"Invalid credentials\",\n                status_code=401\n            )\n\n        if not user.is_active:\n            return api_response(\n                message=\"Account is deactivated\",\n                status_code=401\n            )\n\n        # Login user\n        login_user(user, remember=True)\n\n        # Update last login\n        user.last_login = datetime.utcnow()\n        db.session.commit()\n\n        # Get user permissions\n        permissions = get_user_permissions(user.role)\n\n        return api_response(\n            data={\n                'user': {\n                    'id': user.id,\n                    'username': user.username,\n                    'email': user.email,\n                    'first_name': user.first_name,\n                    'last_name': user.last_name,\n                    'full_name': user.full_name,\n                    'role': user.role,\n                    'permissions': permissions\n                }\n            },\n            message=\"Login successful\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_auth.route('/logout', methods=['POST'])\n@api_login_required\ndef logout():\n    \"\"\"\n    Logout API endpoint.\n\n    Logs out the current user and clears the session.\n    \"\"\"\n    try:\n        logout_user()\n        return api_response(\n            message=\"Logout successful\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_auth.route('/me', methods=['GET'])\*************\n@api_login_required\ndef get_current_user():\n    \"\"\"\n    Get current user information with permissions and preferences.\n\n    Returns:\n        JSON with current user data including:\n        - Basic user information\n        - Role and permissions\n        - User preferences\n        - Profile completion status\n    \"\"\"\n    try:\n        # Get user permissions\n        permissions = get_user_permissions(current_user.role)\n\n        # Calculate profile completion\n        profile_completion = 0\n        if current_user.profile:\n            total_fields = 6  # Basic fields to check\n            completed_fields = 0\n\n            if current_user.first_name:\n                completed_fields += 1\n            if current_user.last_name:\n                completed_fields += 1\n            if current_user.email:\n                completed_fields += 1\n            if current_user.phone:\n                completed_fields += 1\n            if current_user.profile.address:\n                completed_fields += 1\n            if current_user.bio:\n                completed_fields += 1\n\n            profile_completion = (completed_fields / total_fields) * 100\n\n        user_data = {\n            'id': current_user.id,\n            'username': current_user.username,\n            'email': current_user.email,\n            'first_name': current_user.first_name,\n            'last_name': current_user.last_name,\n            'full_name': current_user.full_name,\n            'role': current_user.role,\n            'department_id': current_user.department_id,\n            'department_name': current_user.department_obj.name if current_user.department_obj else None,\n            'position': current_user.position,\n            'phone': current_user.phone,\n            'bio': current_user.bio,\n            'profile_image': current_user.profile_image,\n            'is_active': current_user.is_active,\n            'dark_mode': current_user.dark_mode,\n            'created_at': current_user.created_at.isoformat() if current_user.created_at else None,\n            'last_login': current_user.last_login.isoformat() if current_user.last_login else None,\n            'hire_date': current_user.hire_date.isoformat() if current_user.hire_date else None,\n            'permissions': permissions,\n            'profile_completion': round(profile_completion, 1),\n            'preferences': {\n                'dark_mode': current_user.dark_mode,\n                'language': 'it',  # Default language\n                'timezone': 'Europe/Rome'  # Default timezone\n            }\n        }\n\n        return api_response(\n            data={'user': user_data},\n            message=\"Current user data retrieved successfully\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_auth.route('/check-session', methods=['GET'])\n@api_login_required\ndef check_session():\n    \"\"\"\n    Check if current session is valid.\n\n    Returns:\n        JSON with session validity status\n    \"\"\"\n    try:\n        return api_response(\n            data={\n                'valid': True,\n                'user_id': current_user.id,\n                'username': current_user.username,\n                'role': current_user.role,\n                'last_activity': datetime.utcnow().isoformat()\n            },\n            message=\"Session is valid\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_auth.route('/preferences', methods=['GET', 'PUT'])\n@api_login_required\ndef user_preferences():\n    \"\"\"\n    Get or update user preferences.\n\n    GET: Returns current user preferences\n    PUT: Updates user preferences\n\n    Returns:\n        JSON with user preferences\n    \"\"\"\n    try:\n        if request.method == 'GET':\n            preferences = {\n                'dark_mode': current_user.dark_mode,\n                'language': 'it',  # Default for now\n                'timezone': 'Europe/Rome',  # Default for now\n                'notifications': {\n                    'email': True,  # Default settings\n                    'browser': True,\n                    'mobile': True\n                }\n            }\n\n            return api_response(\n                data={'preferences': preferences},\n                message=\"User preferences retrieved successfully\"\n            )\n\n        elif request.method == 'PUT':\n            data = request.get_json()\n\n            if not data:\n                return api_response(\n                    success=False,\n                    message=\"No data provided\",\n                    status_code=400\n                )\n\n            # Update dark mode preference\n            if 'dark_mode' in data:\n                current_user.dark_mode = bool(data['dark_mode'])\n\n            # Save changes\n            db.session.commit()\n\n            return api_response(\n                data={'preferences': {\n                    'dark_mode': current_user.dark_mode,\n                    'language': 'it',\n                    'timezone': 'Europe/Rome'\n                }},\n                message=\"User preferences updated successfully\"\n            )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_auth.route('/profile', methods=['PUT'])\n@api_login_required\ndef update_profile():\n    \"\"\"\n    Update current user profile information.\n\n    Returns:\n        JSON with updated user data\n    \"\"\"\n    try:\n        data = request.get_json()\n\n        if not data:\n            return api_response(\n                success=False,\n                message=\"No data provided\",\n                status_code=400\n            )\n\n        # Update basic user fields\n        if 'first_name' in data:\n            current_user.first_name = data['first_name']\n        if 'last_name' in data:\n            current_user.last_name = data['last_name']\n        if 'phone' in data:\n            current_user.phone = data['phone']\n        if 'bio' in data:\n            current_user.bio = data['bio']\n\n        # Update profile fields if profile exists\n        if current_user.profile:\n            if 'address' in data:\n                current_user.profile.address = data['address']\n            if 'emergency_contact_name' in data:\n                current_user.profile.emergency_contact_name = data['emergency_contact_name']\n            if 'emergency_contact_phone' in data:\n                current_user.profile.emergency_contact_phone = data['emergency_contact_phone']\n        else:\n            # Create profile if it doesn't exist\n            profile = UserProfile(\n                user_id=current_user.id,\n                address=data.get('address'),\n                emergency_contact_name=data.get('emergency_contact_name'),\n                emergency_contact_phone=data.get('emergency_contact_phone')\n            )\n            db.session.add(profile)\n\n        db.session.commit()\n\n        return api_response(\n            data={'user': {\n                'id': current_user.id,\n                'first_name': current_user.first_name,\n                'last_name': current_user.last_name,\n                'full_name': current_user.full_name,\n                'phone': current_user.phone,\n                'bio': current_user.bio\n            }},\n            message=\"Profile updated successfully\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n", "modifiedCode": "\"\"\"\nAPI endpoints for authentication and user session management.\nProvides REST API for current user data, session validation, and user preferences.\n\"\"\"\n\nfrom flask import Blueprint, request, jsonify\nfrom flask_login import current_user, login_user, logout_user\nfrom werkzeug.security import check_password_hash\nfrom datetime import datetime\n\nfrom utils.db import db\nfrom models import User, UserProfile\nfrom utils.api_utils import (\n    api_response, api_login_required, handle_api_error\n)\nfrom utils.permissions import ROLE_PERMISSIONS\nfrom extensions import csrf\n\n# Create blueprint\napi_auth = Blueprint('api_auth', __name__, url_prefix='/auth')\n\ndef get_user_permissions(user_role):\n    \"\"\"\n    Get all permissions for a user role.\n\n    Args:\n        user_role (str): User role\n\n    Returns:\n        list: List of permissions for the role\n    \"\"\"\n    if not user_role or user_role not in ROLE_PERMISSIONS:\n        return []\n\n    # Admin has all permissions\n    if user_role == 'admin':\n        from utils.permissions import ALL_PERMISSIONS\n        return list(ALL_PERMISSIONS)\n\n    return list(ROLE_PERMISSIONS.get(user_role, set()))\n\n@api_auth.route('/login', methods=['POST'])\*************\ndef login():\n    \"\"\"\n    Login API endpoint.\n\n    Expects JSON data with username/email and password.\n    Returns user data and session info on success.\n    \"\"\"\n    try:\n        data = request.get_json()\n\n        if not data:\n            return api_response(\n                success=False,\n                message=\"No data provided\",\n                status_code=400\n            )\n\n        # Get credentials - support both username and email\n        username_or_email = data.get('username') or data.get('email')\n        password = data.get('password')\n\n        if not username_or_email or not password:\n            return api_response(\n                message=\"Username/email and password are required\",\n                status_code=400\n            )\n\n        # Find user by username or email\n        user = User.query.filter(\n            (User.username == username_or_email) |\n            (User.email == username_or_email)\n        ).first()\n\n        if not user or not check_password_hash(user.password_hash, password):\n            return api_response(\n                message=\"Invalid credentials\",\n                status_code=401\n            )\n\n        if not user.is_active:\n            return api_response(\n                message=\"Account is deactivated\",\n                status_code=401\n            )\n\n        # Login user\n        login_user(user, remember=True)\n\n        # Update last login\n        user.last_login = datetime.utcnow()\n        db.session.commit()\n\n        # Get user permissions\n        permissions = get_user_permissions(user.role)\n\n        return api_response(\n            data={\n                'user': {\n                    'id': user.id,\n                    'username': user.username,\n                    'email': user.email,\n                    'first_name': user.first_name,\n                    'last_name': user.last_name,\n                    'full_name': user.full_name,\n                    'role': user.role,\n                    'permissions': permissions\n                }\n            },\n            message=\"Login successful\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_auth.route('/logout', methods=['POST'])\n@api_login_required\ndef logout():\n    \"\"\"\n    Logout API endpoint.\n\n    Logs out the current user and clears the session.\n    \"\"\"\n    try:\n        logout_user()\n        return api_response(\n            message=\"Logout successful\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_auth.route('/me', methods=['GET'])\*************\n@api_login_required\ndef get_current_user():\n    \"\"\"\n    Get current user information with permissions and preferences.\n\n    Returns:\n        JSON with current user data including:\n        - Basic user information\n        - Role and permissions\n        - User preferences\n        - Profile completion status\n    \"\"\"\n    try:\n        # Get user permissions\n        permissions = get_user_permissions(current_user.role)\n\n        # Calculate profile completion\n        profile_completion = 0\n        if current_user.profile:\n            total_fields = 6  # Basic fields to check\n            completed_fields = 0\n\n            if current_user.first_name:\n                completed_fields += 1\n            if current_user.last_name:\n                completed_fields += 1\n            if current_user.email:\n                completed_fields += 1\n            if current_user.phone:\n                completed_fields += 1\n            if current_user.profile.address:\n                completed_fields += 1\n            if current_user.bio:\n                completed_fields += 1\n\n            profile_completion = (completed_fields / total_fields) * 100\n\n        user_data = {\n            'id': current_user.id,\n            'username': current_user.username,\n            'email': current_user.email,\n            'first_name': current_user.first_name,\n            'last_name': current_user.last_name,\n            'full_name': current_user.full_name,\n            'role': current_user.role,\n            'department_id': current_user.department_id,\n            'department_name': current_user.department_obj.name if current_user.department_obj else None,\n            'position': current_user.position,\n            'phone': current_user.phone,\n            'bio': current_user.bio,\n            'profile_image': current_user.profile_image,\n            'is_active': current_user.is_active,\n            'dark_mode': current_user.dark_mode,\n            'created_at': current_user.created_at.isoformat() if current_user.created_at else None,\n            'last_login': current_user.last_login.isoformat() if current_user.last_login else None,\n            'hire_date': current_user.hire_date.isoformat() if current_user.hire_date else None,\n            'permissions': permissions,\n            'profile_completion': round(profile_completion, 1),\n            'preferences': {\n                'dark_mode': current_user.dark_mode,\n                'language': 'it',  # Default language\n                'timezone': 'Europe/Rome'  # Default timezone\n            }\n        }\n\n        return api_response(\n            data={'user': user_data},\n            message=\"Current user data retrieved successfully\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_auth.route('/debug', methods=['GET'])\*************\ndef debug_auth():\n    \"\"\"Debug endpoint per verificare lo stato dell'autenticazione\"\"\"\n    from flask_login import current_user\n    from flask import session\n\n    return api_response(\n        data={\n            'current_user_authenticated': current_user.is_authenticated,\n            'current_user_id': getattr(current_user, 'id', None),\n            'current_user_username': getattr(current_user, 'username', None),\n            'session_keys': list(session.keys()),\n            'session_permanent': session.permanent,\n            'has_user_id_in_session': '_user_id' in session,\n            'user_id_from_session': session.get('_user_id')\n        },\n        message=\"Debug info\"\n    )\n\n@api_auth.route('/check-session', methods=['GET'])\*************\n@api_login_required\ndef check_session():\n    \"\"\"\n    Check if current session is valid.\n\n    Returns:\n        JSON with session validity status\n    \"\"\"\n    try:\n        return api_response(\n            data={\n                'valid': True,\n                'user_id': current_user.id,\n                'username': current_user.username,\n                'role': current_user.role,\n                'last_activity': datetime.utcnow().isoformat()\n            },\n            message=\"Session is valid\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_auth.route('/preferences', methods=['GET', 'PUT'])\n@api_login_required\ndef user_preferences():\n    \"\"\"\n    Get or update user preferences.\n\n    GET: Returns current user preferences\n    PUT: Updates user preferences\n\n    Returns:\n        JSON with user preferences\n    \"\"\"\n    try:\n        if request.method == 'GET':\n            preferences = {\n                'dark_mode': current_user.dark_mode,\n                'language': 'it',  # Default for now\n                'timezone': 'Europe/Rome',  # Default for now\n                'notifications': {\n                    'email': True,  # Default settings\n                    'browser': True,\n                    'mobile': True\n                }\n            }\n\n            return api_response(\n                data={'preferences': preferences},\n                message=\"User preferences retrieved successfully\"\n            )\n\n        elif request.method == 'PUT':\n            data = request.get_json()\n\n            if not data:\n                return api_response(\n                    success=False,\n                    message=\"No data provided\",\n                    status_code=400\n                )\n\n            # Update dark mode preference\n            if 'dark_mode' in data:\n                current_user.dark_mode = bool(data['dark_mode'])\n\n            # Save changes\n            db.session.commit()\n\n            return api_response(\n                data={'preferences': {\n                    'dark_mode': current_user.dark_mode,\n                    'language': 'it',\n                    'timezone': 'Europe/Rome'\n                }},\n                message=\"User preferences updated successfully\"\n            )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_auth.route('/profile', methods=['PUT'])\n@api_login_required\ndef update_profile():\n    \"\"\"\n    Update current user profile information.\n\n    Returns:\n        JSON with updated user data\n    \"\"\"\n    try:\n        data = request.get_json()\n\n        if not data:\n            return api_response(\n                success=False,\n                message=\"No data provided\",\n                status_code=400\n            )\n\n        # Update basic user fields\n        if 'first_name' in data:\n            current_user.first_name = data['first_name']\n        if 'last_name' in data:\n            current_user.last_name = data['last_name']\n        if 'phone' in data:\n            current_user.phone = data['phone']\n        if 'bio' in data:\n            current_user.bio = data['bio']\n\n        # Update profile fields if profile exists\n        if current_user.profile:\n            if 'address' in data:\n                current_user.profile.address = data['address']\n            if 'emergency_contact_name' in data:\n                current_user.profile.emergency_contact_name = data['emergency_contact_name']\n            if 'emergency_contact_phone' in data:\n                current_user.profile.emergency_contact_phone = data['emergency_contact_phone']\n        else:\n            # Create profile if it doesn't exist\n            profile = UserProfile(\n                user_id=current_user.id,\n                address=data.get('address'),\n                emergency_contact_name=data.get('emergency_contact_name'),\n                emergency_contact_phone=data.get('emergency_contact_phone')\n            )\n            db.session.add(profile)\n\n        db.session.commit()\n\n        return api_response(\n            data={'user': {\n                'id': current_user.id,\n                'first_name': current_user.first_name,\n                'last_name': current_user.last_name,\n                'full_name': current_user.full_name,\n                'phone': current_user.phone,\n                'bio': current_user.bio\n            }},\n            message=\"Profile updated successfully\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n"}