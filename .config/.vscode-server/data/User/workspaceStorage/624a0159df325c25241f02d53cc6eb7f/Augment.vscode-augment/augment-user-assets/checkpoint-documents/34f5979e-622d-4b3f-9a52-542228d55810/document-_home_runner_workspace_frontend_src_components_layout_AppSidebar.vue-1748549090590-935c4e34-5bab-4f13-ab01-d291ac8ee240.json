{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/layout/AppSidebar.vue"}, "originalCode": "<template>\n  <div class=\"flex\">\n    <!-- Desktop Sidebar -->\n    <div class=\"hidden lg:flex lg:flex-shrink-0 lg:fixed lg:inset-y-0 z-10\">\n      <div\n        class=\"flex flex-col transition-all duration-300\"\n        :class=\"[\n          isCollapsed ? 'w-20' : 'w-64'\n        ]\"\n      >\n        <div class=\"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm\">\n          <!-- Logo and Toggle -->\n          <div class=\"flex items-center flex-shrink-0 px-4\">\n            <div class=\"flex items-center\" :class=\"{ 'justify-center': isCollapsed }\">\n              <!-- Logo when sidebar is open -->\n              <router-link\n                to=\"/app/dashboard\"\n                class=\"flex items-center\"\n                :class=\"{ 'hidden': isCollapsed }\"\n              >\n                <div class=\"w-10 h-10 bg-primary-600 rounded flex items-center justify-center mr-3\">\n                  <span class=\"text-white font-bold text-lg\">{{ brandInitials }}</span>\n                </div>\n                <h3 class=\"text-xl font-semibold text-gray-900\">{{ companyName }}</h3>\n              </router-link>\n              <!-- Compact logo for collapsed sidebar -->\n              <router-link\n                to=\"/app/dashboard\"\n                class=\"flex items-center justify-center\"\n                :class=\"{ 'hidden': !isCollapsed }\"\n              >\n                <div class=\"w-8 h-8 bg-primary-600 rounded flex items-center justify-center\">\n                  <span class=\"text-white font-bold text-sm\">{{ brandInitials }}</span>\n                </div>\n              </router-link>\n            </div>\n            <button\n              @click=\"toggleCollapsed\"\n              class=\"ml-auto text-gray-600 focus:outline-none hover:bg-gray-100 p-1 rounded\"\n            >\n              <svg class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\n                      :d=\"isCollapsed ? 'M13 5l7 7-7 7M5 5l7 7-7 7' : 'M11 19l-7-7 7-7m8 14l-7-7 7-7'\" />\n              </svg>\n            </button>\n          </div>\n\n          <SidebarNavigation\n            :is-collapsed=\"isCollapsed\"\n            @item-click=\"handleItemClick\"\n          />\n\n          <SidebarFooter :is-collapsed=\"isCollapsed\" />\n        </div>\n      </div>\n    </div>\n\n    <!-- Mobile Sidebar -->\n    <div\n      :class=\"[\n        'fixed inset-y-0 left-0 z-30 w-64 bg-primary-700 transform transition-transform duration-300 ease-in-out lg:hidden',\n        isMobileOpen ? 'translate-x-0' : '-translate-x-full'\n      ]\"\n    >\n      <div class=\"flex flex-col h-full pt-5 pb-4 overflow-y-auto bg-white border-r border-gray-200 shadow-sm\">\n        <div class=\"flex items-center justify-between px-4 mb-4\">\n          <router-link to=\"/app/dashboard\" class=\"flex items-center\">\n            <div class=\"w-8 h-8 bg-primary-600 rounded flex items-center justify-center mr-3\">\n              <span class=\"text-white font-bold text-sm\">{{ brandInitials }}</span>\n            </div>\n            <h3 class=\"text-xl font-semibold text-gray-900\">{{ companyName }}</h3>\n          </router-link>\n          <button\n            @click=\"$emit('close')\"\n            class=\"p-2 rounded-md text-gray-600 hover:bg-gray-100\"\n          >\n            <svg class=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        <SidebarNavigation\n          :is-collapsed=\"false\"\n          @item-click=\"$emit('close')\"\n        />\n\n        <SidebarFooter :is-collapsed=\"false\" />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue'\nimport { useTenantStore } from '@/stores/tenant'\nimport SidebarNavigation from './SidebarNavigation.vue'\nimport SidebarFooter from './SidebarFooter.vue'\n\nconst props = defineProps({\n  isMobileOpen: {\n    type: Boolean,\n    default: false\n  }\n})\n\ndefineEmits(['close'])\n\nconst tenantStore = useTenantStore()\nconst isCollapsed = ref(false)\n\nconst tenantConfig = computed(() => tenantStore.config || {})\nconst companyName = computed(() => tenantConfig.value.company?.name || 'DatPortal')\nconst brandInitials = computed(() => {\n  const name = companyName.value\n  return name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2)\n})\n\nfunction toggleCollapsed() {\n  isCollapsed.value = !isCollapsed.value\n}\n\nfunction handleItemClick() {\n  // Auto-expand sidebar when clicking items if collapsed\n  if (isCollapsed.value) {\n    isCollapsed.value = false\n  }\n}\n</script>", "modifiedCode": "<template>\n  <div class=\"flex\">\n    <!-- Desktop Sidebar -->\n    <div class=\"hidden lg:flex lg:flex-shrink-0 lg:fixed lg:inset-y-0 z-10\">\n      <div\n        class=\"flex flex-col transition-all duration-300\"\n        :class=\"[\n          isCollapsed ? 'w-20' : 'w-64'\n        ]\"\n      >\n        <div class=\"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm\">\n          <!-- Logo and Toggle -->\n          <div class=\"flex items-center flex-shrink-0 px-4\">\n            <div class=\"flex items-center\" :class=\"{ 'justify-center': isCollapsed }\">\n              <!-- Logo when sidebar is open -->\n              <router-link\n                to=\"/app/dashboard\"\n                class=\"flex items-center\"\n                :class=\"{ 'hidden': isCollapsed }\"\n              >\n                <div class=\"w-10 h-10 bg-primary-600 rounded flex items-center justify-center mr-3\">\n                  <span class=\"text-white font-bold text-lg\">{{ brandInitials }}</span>\n                </div>\n                <h3 class=\"text-xl font-semibold text-gray-900 dark:text-white\">{{ companyName }}</h3>\n              </router-link>\n              <!-- Compact logo for collapsed sidebar -->\n              <router-link\n                to=\"/app/dashboard\"\n                class=\"flex items-center justify-center\"\n                :class=\"{ 'hidden': !isCollapsed }\"\n              >\n                <div class=\"w-8 h-8 bg-primary-600 rounded flex items-center justify-center\">\n                  <span class=\"text-white font-bold text-sm\">{{ brandInitials }}</span>\n                </div>\n              </router-link>\n            </div>\n            <button\n              @click=\"toggleCollapsed\"\n              class=\"ml-auto text-gray-600 focus:outline-none hover:bg-gray-100 p-1 rounded\"\n            >\n              <svg class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\n                      :d=\"isCollapsed ? 'M13 5l7 7-7 7M5 5l7 7-7 7' : 'M11 19l-7-7 7-7m8 14l-7-7 7-7'\" />\n              </svg>\n            </button>\n          </div>\n\n          <SidebarNavigation\n            :is-collapsed=\"isCollapsed\"\n            @item-click=\"handleItemClick\"\n          />\n\n          <SidebarFooter :is-collapsed=\"isCollapsed\" />\n        </div>\n      </div>\n    </div>\n\n    <!-- Mobile Sidebar -->\n    <div\n      :class=\"[\n        'fixed inset-y-0 left-0 z-30 w-64 bg-primary-700 transform transition-transform duration-300 ease-in-out lg:hidden',\n        isMobileOpen ? 'translate-x-0' : '-translate-x-full'\n      ]\"\n    >\n      <div class=\"flex flex-col h-full pt-5 pb-4 overflow-y-auto bg-white border-r border-gray-200 shadow-sm\">\n        <div class=\"flex items-center justify-between px-4 mb-4\">\n          <router-link to=\"/app/dashboard\" class=\"flex items-center\">\n            <div class=\"w-8 h-8 bg-primary-600 rounded flex items-center justify-center mr-3\">\n              <span class=\"text-white font-bold text-sm\">{{ brandInitials }}</span>\n            </div>\n            <h3 class=\"text-xl font-semibold text-gray-900\">{{ companyName }}</h3>\n          </router-link>\n          <button\n            @click=\"$emit('close')\"\n            class=\"p-2 rounded-md text-gray-600 hover:bg-gray-100\"\n          >\n            <svg class=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        <SidebarNavigation\n          :is-collapsed=\"false\"\n          @item-click=\"$emit('close')\"\n        />\n\n        <SidebarFooter :is-collapsed=\"false\" />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue'\nimport { useTenantStore } from '@/stores/tenant'\nimport SidebarNavigation from './SidebarNavigation.vue'\nimport SidebarFooter from './SidebarFooter.vue'\n\nconst props = defineProps({\n  isMobileOpen: {\n    type: Boolean,\n    default: false\n  }\n})\n\ndefineEmits(['close'])\n\nconst tenantStore = useTenantStore()\nconst isCollapsed = ref(false)\n\nconst tenantConfig = computed(() => tenantStore.config || {})\nconst companyName = computed(() => tenantConfig.value.company?.name || 'DatPortal')\nconst brandInitials = computed(() => {\n  const name = companyName.value\n  return name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2)\n})\n\nfunction toggleCollapsed() {\n  isCollapsed.value = !isCollapsed.value\n}\n\nfunction handleItemClick() {\n  // Auto-expand sidebar when clicking items if collapsed\n  if (isCollapsed.value) {\n    isCollapsed.value = false\n  }\n}\n</script>"}