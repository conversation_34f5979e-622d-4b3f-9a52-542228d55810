{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectOverview.vue"}, "originalCode": "<template>\n  <div class=\"project-overview\">\n    <div v-if=\"loading\" class=\"animate-pulse space-y-4\">\n      <div class=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n      <div class=\"h-4 bg-gray-200 rounded w-1/2\"></div>\n      <div class=\"h-32 bg-gray-200 rounded\"></div>\n    </div>\n\n    <div v-else-if=\"project\" class=\"space-y-6\">\n      <!-- Project Description -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <h3 class=\"text-lg font-medium text-gray-900 mb-4\">Descrizione Progetto</h3>\n        <p v-if=\"project.description\" class=\"text-gray-600\">{{ project.description }}</p>\n        <p v-else class=\"text-gray-400 italic\">Nessuna descrizione disponibile</p>\n      </div>\n\n      <!-- Project Stats -->\n      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Task Totali</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ project.task_count || 0 }}</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Task Completati</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ project.completed_tasks || 0 }}</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Membri Team</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ project.team_count || 0 }}</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Budget</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatCurrency(project.budget) }}</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Progress Bar -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <h3 class=\"text-lg font-medium text-gray-900 mb-4\">Progresso Progetto</h3>\n        <div class=\"w-full bg-gray-200 rounded-full h-2.5\">\n          <div\n            class=\"bg-blue-600 h-2.5 rounded-full transition-all duration-300\"\n            :style=\"{ width: `${projectProgress}%` }\"\n          ></div>\n        </div>\n        <p class=\"text-sm text-gray-500 mt-2\">{{ projectProgress }}% completato</p>\n      </div>\n\n      <!-- Recent Activity -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <h3 class=\"text-lg font-medium text-gray-900 mb-4\">Attività Recenti</h3>\n        <div class=\"space-y-3\">\n          <div v-for=\"activity in recentActivities\" :key=\"activity.id\" class=\"flex items-start space-x-3\">\n            <div class=\"flex-shrink-0\">\n              <div class=\"w-2 h-2 bg-blue-600 rounded-full mt-2\"></div>\n            </div>\n            <div class=\"flex-1\">\n              <p class=\"text-sm text-gray-900\">{{ activity.description }}</p>\n              <p class=\"text-xs text-gray-500\">{{ formatDate(activity.created_at) }}</p>\n            </div>\n          </div>\n          <div v-if=\"recentActivities.length === 0\" class=\"text-center py-4\">\n            <p class=\"text-gray-500\">Nessuna attività recente</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <div v-else class=\"text-center py-8\">\n      <p class=\"text-gray-500\">Progetto non trovato</p>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\n\nconst props = defineProps({\n  project: {\n    type: Object,\n    default: null\n  },\n  loading: {\n    type: Boolean,\n    default: false\n  }\n})\n\n// Computed\nconst projectProgress = computed(() => {\n  if (!props.project || !props.project.task_count) return 0\n  const completed = props.project.completed_tasks || 0\n  const total = props.project.task_count || 1\n  return Math.round((completed / total) * 100)\n})\n\nconst recentActivities = computed(() => {\n  // TODO: Implementare caricamento attività reali\n  return props.project?.recent_activities || []\n})\n\n// Methods\nconst formatCurrency = (amount) => {\n  if (!amount) return 'Non specificato'\n  return new Intl.NumberFormat('it-IT', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(amount)\n}\n\nconst formatDate = (dateString) => {\n  if (!dateString) return ''\n  return new Date(dateString).toLocaleDateString('it-IT', {\n    day: 'numeric',\n    month: 'short',\n    hour: '2-digit',\n    minute: '2-digit'\n  })\n}\n</script>\n\n<style scoped>\n.project-overview {\n  @apply space-y-6;\n}\n</style>\n", "modifiedCode": "<template>\n  <div class=\"project-overview\">\n    <div v-if=\"loading\" class=\"animate-pulse space-y-4\">\n      <div class=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n      <div class=\"h-4 bg-gray-200 rounded w-1/2\"></div>\n      <div class=\"h-32 bg-gray-200 rounded\"></div>\n    </div>\n\n    <div v-else-if=\"project\" class=\"space-y-6\">\n      <!-- Project Description -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <h3 class=\"text-lg font-medium text-gray-900 mb-4\">Descrizione Progetto</h3>\n        <p v-if=\"project.description\" class=\"text-gray-600\">{{ project.description }}</p>\n        <p v-else class=\"text-gray-400 italic\">Nessuna descrizione disponibile</p>\n      </div>\n\n      <!-- Project Stats -->\n      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Task Totali</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ project.task_count || 0 }}</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Task Completati</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ project.completed_tasks || 0 }}</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Membri Team</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ project.team_count || 0 }}</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Budget</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatCurrency(project.budget) }}</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Progress Bar -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <h3 class=\"text-lg font-medium text-gray-900 mb-4\">Progresso Progetto</h3>\n        <div class=\"w-full bg-gray-200 rounded-full h-2.5\">\n          <div\n            class=\"bg-blue-600 h-2.5 rounded-full transition-all duration-300\"\n            :style=\"{ width: `${projectProgress}%` }\"\n          ></div>\n        </div>\n        <p class=\"text-sm text-gray-500 mt-2\">{{ projectProgress }}% completato</p>\n      </div>\n\n      <!-- Budget vs Spese Chart -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <h3 class=\"text-lg font-medium text-gray-900 mb-4\">Budget vs Spese</h3>\n        <div class=\"space-y-4\">\n          <div class=\"flex justify-between items-center\">\n            <span class=\"text-sm text-gray-600\">Budget Totale</span>\n            <span class=\"text-sm font-medium\">{{ formatCurrency(project.budget) }}</span>\n          </div>\n          <div class=\"w-full bg-gray-200 rounded-full h-3\">\n            <div\n              class=\"bg-blue-600 h-3 rounded-full transition-all duration-300\"\n              :style=\"{ width: '100%' }\"\n            ></div>\n          </div>\n          <div class=\"flex justify-between items-center\">\n            <span class=\"text-sm text-gray-600\">Spese Sostenute</span>\n            <span class=\"text-sm font-medium\">{{ formatCurrency(estimatedExpenses) }}</span>\n          </div>\n          <div class=\"w-full bg-gray-200 rounded-full h-3\">\n            <div\n              class=\"h-3 rounded-full transition-all duration-300\"\n              :class=\"expenseProgressColor\"\n              :style=\"{ width: expensePercentage + '%' }\"\n            ></div>\n          </div>\n          <div class=\"flex justify-between items-center text-sm\">\n            <span class=\"text-gray-600\">Rimanente</span>\n            <span class=\"font-medium\" :class=\"remainingBudgetTextColor\">{{ formatCurrency(remainingBudget) }}</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Team Members -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h3 class=\"text-lg font-medium text-gray-900\">Team Members</h3>\n          <button class=\"text-sm text-blue-600 hover:text-blue-800\">Visualizza tutti</button>\n        </div>\n        <div class=\"space-y-3\">\n          <div v-for=\"member in teamMembers\" :key=\"member.id\" class=\"flex items-center space-x-3\">\n            <div class=\"flex-shrink-0\">\n              <img\n                v-if=\"member.profile_image\"\n                :src=\"member.profile_image\"\n                :alt=\"member.full_name\"\n                class=\"w-8 h-8 rounded-full\"\n              >\n              <div v-else class=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                <span class=\"text-xs font-medium text-gray-600\">{{ getInitials(member.full_name) }}</span>\n              </div>\n            </div>\n            <div class=\"flex-1\">\n              <p class=\"text-sm font-medium text-gray-900\">{{ member.full_name }}</p>\n              <p class=\"text-xs text-gray-500\">{{ member.role || 'Team Member' }}</p>\n            </div>\n            <div class=\"text-right\">\n              <p class=\"text-xs text-gray-500\">{{ member.hours_worked || 0 }}h</p>\n            </div>\n          </div>\n          <div v-if=\"teamMembers.length === 0\" class=\"text-center py-4\">\n            <p class=\"text-gray-500\">Nessun membro del team assegnato</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- Recent Activity -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h3 class=\"text-lg font-medium text-gray-900\">Attività Recenti</h3>\n          <button class=\"text-sm text-blue-600 hover:text-blue-800\">Visualizza tutte</button>\n        </div>\n        <div class=\"space-y-3\">\n          <div v-for=\"activity in recentActivities\" :key=\"activity.id\" class=\"flex items-start space-x-3\">\n            <div class=\"flex-shrink-0\">\n              <div class=\"w-2 h-2 rounded-full mt-2\" :class=\"getActivityColor(activity.type)\"></div>\n            </div>\n            <div class=\"flex-1\">\n              <p class=\"text-sm text-gray-900\">{{ activity.description }}</p>\n              <div class=\"flex items-center space-x-2 mt-1\">\n                <p class=\"text-xs text-gray-500\">{{ formatDate(activity.created_at) }}</p>\n                <span class=\"text-xs text-gray-400\">•</span>\n                <p class=\"text-xs text-gray-500\">{{ activity.user_name }}</p>\n              </div>\n            </div>\n          </div>\n          <div v-if=\"recentActivities.length === 0\" class=\"text-center py-4\">\n            <p class=\"text-gray-500\">Nessuna attività recente</p>\n            <button class=\"text-sm text-blue-600 hover:text-blue-800 mt-2\">Aggiungi prima attività</button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <div v-else class=\"text-center py-8\">\n      <p class=\"text-gray-500\">Progetto non trovato</p>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\n\nconst props = defineProps({\n  project: {\n    type: Object,\n    default: null\n  },\n  loading: {\n    type: Boolean,\n    default: false\n  }\n})\n\n// Computed\nconst projectProgress = computed(() => {\n  if (!props.project || !props.project.task_count) return 0\n  const completed = props.project.completed_tasks || 0\n  const total = props.project.task_count || 1\n  return Math.round((completed / total) * 100)\n})\n\nconst recentActivities = computed(() => {\n  // TODO: Implementare caricamento attività reali\n  return props.project?.recent_activities || []\n})\n\n// Methods\nconst formatCurrency = (amount) => {\n  if (!amount) return 'Non specificato'\n  return new Intl.NumberFormat('it-IT', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(amount)\n}\n\nconst formatDate = (dateString) => {\n  if (!dateString) return ''\n  return new Date(dateString).toLocaleDateString('it-IT', {\n    day: 'numeric',\n    month: 'short',\n    hour: '2-digit',\n    minute: '2-digit'\n  })\n}\n</script>\n\n<style scoped>\n.project-overview {\n  @apply space-y-6;\n}\n</style>\n"}