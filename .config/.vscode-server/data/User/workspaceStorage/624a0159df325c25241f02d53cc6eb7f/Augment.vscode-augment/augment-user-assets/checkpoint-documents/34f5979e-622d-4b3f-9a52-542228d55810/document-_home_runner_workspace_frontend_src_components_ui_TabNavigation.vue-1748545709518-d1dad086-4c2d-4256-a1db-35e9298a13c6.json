{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/ui/TabNavigation.vue"}, "modifiedCode": "<template>\n  <div class=\"tab-navigation\">\n    <div class=\"border-b border-gray-200\">\n      <nav class=\"-mb-px flex space-x-8\" aria-label=\"Tabs\">\n        <button\n          v-for=\"tab in tabs\"\n          :key=\"tab.id\"\n          @click=\"selectTab(tab.id)\"\n          :class=\"[\n            'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2',\n            isActive(tab.id)\n              ? 'border-primary-500 text-primary-600'\n              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n          ]\"\n          :aria-current=\"isActive(tab.id) ? 'page' : undefined\"\n        >\n          <!-- Icon -->\n          <component \n            v-if=\"tab.icon\" \n            :is=\"getIconComponent(tab.icon)\"\n            class=\"w-4 h-4\"\n          />\n          \n          <!-- Label -->\n          <span>{{ tab.label }}</span>\n          \n          <!-- Badge/Count -->\n          <span \n            v-if=\"tab.count !== undefined\"\n            class=\"ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600\"\n          >\n            {{ tab.count }}\n          </span>\n        </button>\n      </nav>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\n\nconst props = defineProps({\n  modelValue: {\n    type: String,\n    required: true\n  },\n  tabs: {\n    type: Array,\n    required: true,\n    validator: (tabs) => {\n      return tabs.every(tab => \n        typeof tab === 'object' && \n        tab.id && \n        tab.label\n      )\n    }\n  }\n})\n\nconst emit = defineEmits(['update:modelValue'])\n\nconst isActive = (tabId) => {\n  return props.modelValue === tabId\n}\n\nconst selectTab = (tabId) => {\n  emit('update:modelValue', tabId)\n}\n\n// Icon components mapping\nconst getIconComponent = (iconName) => {\n  const icons = {\n    'chart-bar': {\n      template: `<svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n      </svg>`\n    },\n    'clipboard-list': {\n      template: `<svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\" />\n      </svg>`\n    },\n    'users': {\n      template: `<svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z\" />\n      </svg>`\n    },\n    'folder': {\n      template: `<svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\" />\n      </svg>`\n    },\n    'trending-up': {\n      template: `<svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n      </svg>`\n    },\n    'calendar': {\n      template: `<svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n      </svg>`\n    },\n    'clock': {\n      template: `<svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n      </svg>`\n    }\n  }\n  \n  return icons[iconName] || icons['chart-bar']\n}\n</script>\n\n<style scoped>\n.tab-navigation {\n  @apply bg-white;\n}\n\n/* Smooth transition for tab changes */\nbutton {\n  @apply transition-all duration-200 ease-in-out;\n}\n\nbutton:focus {\n  @apply outline-none ring-2 ring-primary-500 ring-offset-2;\n}\n</style>\n"}