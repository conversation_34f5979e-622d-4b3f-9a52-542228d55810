{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectKPI.vue"}, "modifiedCode": "iniziam<template>\n  <div class=\"project-kpi\">\n    <div class=\"bg-white shadow rounded-lg p-6\">\n      <h3 class=\"text-lg font-medium text-gray-900 mb-6\">KPI del Progetto</h3>\n      \n      <div class=\"text-center py-8\">\n        <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n        </svg>\n        <h3 class=\"mt-2 text-sm font-medium text-gray-900\">KPI e Metriche in sviluppo</h3>\n        <p class=\"mt-1 text-sm text-gray-500\">Dashboard KPI progetto in arrivo...</p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\ndefineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n</script>\n"}