{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/tailwind.config.js"}, "originalCode": "/** @type {import('tailwindcss').Config} */\nexport default {\n  content: [\n    \"./index.html\",\n    \"./src/**/*.{vue,js,ts,jsx,tsx}\",\n  ],\n  theme: {\n    extend: {\n      colors: {\n        'brand-primary': {\n          50: 'var(--brand-primary-50)',\n          100: 'var(--brand-primary-100)',\n          200: 'var(--brand-primary-200)',\n          300: 'var(--brand-primary-300)',\n          400: 'var(--brand-primary-400)',\n          500: 'var(--brand-primary-500)',\n          600: 'var(--brand-primary-600)',\n          700: 'var(--brand-primary-700)',\n          800: 'var(--brand-primary-800)',\n          900: 'var(--brand-primary-900)',\n        },\n        'brand-secondary': {\n          50: 'var(--brand-secondary-50)',\n          100: 'var(--brand-secondary-100)',\n          200: 'var(--brand-secondary-200)',\n          300: 'var(--brand-secondary-300)',\n          400: 'var(--brand-secondary-400)',\n          500: 'var(--brand-secondary-500)',\n          600: 'var(--brand-secondary-600)',\n          700: 'var(--brand-secondary-700)',\n          800: 'var(--brand-secondary-800)',\n          900: 'var(--brand-secondary-900)',\n        },\n        'brand-accent': {\n          50: 'var(--brand-accent-50)',\n          100: 'var(--brand-accent-100)',\n          200: 'var(--brand-accent-200)',\n          300: 'var(--brand-accent-300)',\n          400: 'var(--brand-accent-400)',\n          500: 'var(--brand-accent-500)',\n          600: 'var(--brand-accent-600)',\n          700: 'var(--brand-accent-700)',\n          800: 'var(--brand-accent-800)',\n          900: 'var(--brand-accent-900)',\n        },\n        primary: {\n          50: 'var(--brand-primary-50)',\n          500: 'var(--brand-primary-500)',\n          600: 'var(--brand-primary-600)',\n          700: 'var(--brand-primary-700)',\n        }\n      },\n      fontFamily: {\n        sans: ['Inter', 'system-ui', 'sans-serif'],\n      },\n      animation: {\n        'fade-in': 'fadeIn 0.5s ease-in-out',\n        'slide-up': 'slideUp 0.3s ease-out',\n      },\n      keyframes: {\n        fadeIn: {\n          '0%': { opacity: '0' },\n          '100%': { opacity: '1' },\n        },\n        slideUp: {\n          '0%': { transform: 'translateY(10px)', opacity: '0' },\n          '100%': { transform: 'translateY(0)', opacity: '1' },\n        },\n      }\n    },\n  },\n  plugins: [],\n}", "modifiedCode": "/** @type {import('tailwindcss').Config} */\nexport default {\n  content: [\n    \"./index.html\",\n    \"./src/**/*.{vue,js,ts,jsx,tsx}\",\n  ],\n  darkMode: 'class',\n  theme: {\n    extend: {\n      colors: {\n        'brand-primary': {\n          50: 'var(--brand-primary-50)',\n          100: 'var(--brand-primary-100)',\n          200: 'var(--brand-primary-200)',\n          300: 'var(--brand-primary-300)',\n          400: 'var(--brand-primary-400)',\n          500: 'var(--brand-primary-500)',\n          600: 'var(--brand-primary-600)',\n          700: 'var(--brand-primary-700)',\n          800: 'var(--brand-primary-800)',\n          900: 'var(--brand-primary-900)',\n        },\n        'brand-secondary': {\n          50: 'var(--brand-secondary-50)',\n          100: 'var(--brand-secondary-100)',\n          200: 'var(--brand-secondary-200)',\n          300: 'var(--brand-secondary-300)',\n          400: 'var(--brand-secondary-400)',\n          500: 'var(--brand-secondary-500)',\n          600: 'var(--brand-secondary-600)',\n          700: 'var(--brand-secondary-700)',\n          800: 'var(--brand-secondary-800)',\n          900: 'var(--brand-secondary-900)',\n        },\n        'brand-accent': {\n          50: 'var(--brand-accent-50)',\n          100: 'var(--brand-accent-100)',\n          200: 'var(--brand-accent-200)',\n          300: 'var(--brand-accent-300)',\n          400: 'var(--brand-accent-400)',\n          500: 'var(--brand-accent-500)',\n          600: 'var(--brand-accent-600)',\n          700: 'var(--brand-accent-700)',\n          800: 'var(--brand-accent-800)',\n          900: 'var(--brand-accent-900)',\n        },\n        primary: {\n          50: 'var(--brand-primary-50)',\n          500: 'var(--brand-primary-500)',\n          600: 'var(--brand-primary-600)',\n          700: 'var(--brand-primary-700)',\n        }\n      },\n      fontFamily: {\n        sans: ['Inter', 'system-ui', 'sans-serif'],\n      },\n      animation: {\n        'fade-in': 'fadeIn 0.5s ease-in-out',\n        'slide-up': 'slideUp 0.3s ease-out',\n      },\n      keyframes: {\n        fadeIn: {\n          '0%': { opacity: '0' },\n          '100%': { opacity: '1' },\n        },\n        slideUp: {\n          '0%': { transform: 'translateY(10px)', opacity: '0' },\n          '100%': { transform: 'translateY(0)', opacity: '1' },\n        },\n      }\n    },\n  },\n  plugins: [],\n}"}