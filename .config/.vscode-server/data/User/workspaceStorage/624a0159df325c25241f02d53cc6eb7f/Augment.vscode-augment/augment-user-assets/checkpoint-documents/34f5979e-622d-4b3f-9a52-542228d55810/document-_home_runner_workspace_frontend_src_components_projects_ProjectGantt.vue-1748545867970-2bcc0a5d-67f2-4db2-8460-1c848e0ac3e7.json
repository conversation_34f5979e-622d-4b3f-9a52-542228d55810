{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/projects/ProjectGantt.vue"}, "modifiedCode": "<template>\n  <div class=\"project-gantt\">\n    <div class=\"bg-white shadow rounded-lg p-6\">\n      <h3 class=\"text-lg font-medium text-gray-900 mb-6\">Diagramma di Gantt</h3>\n      \n      <div class=\"text-center py-8\">\n        <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n        </svg>\n        <h3 class=\"mt-2 text-sm font-medium text-gray-900\">Gantt Chart in sviluppo</h3>\n        <p class=\"mt-1 text-sm text-gray-500\">Integrazione Frappe Gantt in arrivo...</p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\ndefineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n</script>\n"}