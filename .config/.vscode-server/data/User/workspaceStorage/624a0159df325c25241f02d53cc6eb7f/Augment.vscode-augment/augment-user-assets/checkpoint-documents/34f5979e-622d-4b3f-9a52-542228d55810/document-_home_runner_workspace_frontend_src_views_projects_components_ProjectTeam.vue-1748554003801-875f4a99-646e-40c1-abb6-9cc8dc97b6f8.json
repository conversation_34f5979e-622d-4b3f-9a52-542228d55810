{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTeam.vue"}, "originalCode": "<template>\n  <div class=\"project-team\">\n    <div class=\"bg-white shadow rounded-lg p-6\">\n      <h3 class=\"text-lg font-medium text-gray-900 mb-6\">Team del Progetto</h3>\n\n      <!-- Team Members Grid -->\n      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        <div v-for=\"member in teamMembers\" :key=\"member.id\" class=\"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\">\n          <div class=\"flex items-center space-x-4\">\n            <div class=\"flex-shrink-0\">\n              <img\n                v-if=\"member.profile_image\"\n                :src=\"member.profile_image\"\n                :alt=\"member.full_name\"\n                class=\"w-12 h-12 rounded-full\"\n              >\n              <div v-else class=\"w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center\">\n                <span class=\"text-sm font-medium text-gray-600\">{{ getInitials(member.full_name) }}</span>\n              </div>\n            </div>\n            <div class=\"flex-1\">\n              <h4 class=\"text-lg font-medium text-gray-900\">{{ member.full_name }}</h4>\n              <p class=\"text-sm text-gray-600\">{{ member.role || 'Team Member' }}</p>\n              <p class=\"text-xs text-gray-500\">{{ member.email }}</p>\n            </div>\n          </div>\n          <div class=\"mt-4 grid grid-cols-2 gap-4 text-sm\">\n            <div>\n              <span class=\"text-gray-600\">Ore Lavorate:</span>\n              <span class=\"font-medium ml-1\">{{ member.hours_worked || 0 }}h</span>\n            </div>\n            <div>\n              <span class=\"text-gray-600\">Task Assegnati:</span>\n              <span class=\"font-medium ml-1\">{{ getAssignedTasksCount(member.id) }}</span>\n            </div>\n          </div>\n        </div>\n        <div v-if=\"teamMembers.length === 0\" class=\"col-span-full text-center py-8\">\n          <p class=\"text-gray-500\">Nessun membro del team assegnato</p>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\ndefineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n</script>\n", "modifiedCode": "<template>\n  <div class=\"project-team\">\n    <div class=\"bg-white shadow rounded-lg p-6\">\n      <h3 class=\"text-lg font-medium text-gray-900 mb-6\">Team del Progetto</h3>\n\n      <!-- Team Members Grid -->\n      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        <div v-for=\"member in teamMembers\" :key=\"member.id\" class=\"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\">\n          <div class=\"flex items-center space-x-4\">\n            <div class=\"flex-shrink-0\">\n              <img\n                v-if=\"member.profile_image\"\n                :src=\"member.profile_image\"\n                :alt=\"member.full_name\"\n                class=\"w-12 h-12 rounded-full\"\n              >\n              <div v-else class=\"w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center\">\n                <span class=\"text-sm font-medium text-gray-600\">{{ getInitials(member.full_name) }}</span>\n              </div>\n            </div>\n            <div class=\"flex-1\">\n              <h4 class=\"text-lg font-medium text-gray-900\">{{ member.full_name }}</h4>\n              <p class=\"text-sm text-gray-600\">{{ member.role || 'Team Member' }}</p>\n              <p class=\"text-xs text-gray-500\">{{ member.email }}</p>\n            </div>\n          </div>\n          <div class=\"mt-4 grid grid-cols-2 gap-4 text-sm\">\n            <div>\n              <span class=\"text-gray-600\">Ore Lavorate:</span>\n              <span class=\"font-medium ml-1\">{{ member.hours_worked || 0 }}h</span>\n            </div>\n            <div>\n              <span class=\"text-gray-600\">Task Assegnati:</span>\n              <span class=\"font-medium ml-1\">{{ getAssignedTasksCount(member.id) }}</span>\n            </div>\n          </div>\n        </div>\n        <div v-if=\"teamMembers.length === 0\" class=\"col-span-full text-center py-8\">\n          <p class=\"text-gray-500\">Nessun membro del team assegnato</p>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\n\nconst props = defineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n\nconst teamMembers = computed(() => {\n  return props.project?.team_members || []\n})\n\nconst getInitials = (fullName) => {\n  if (!fullName) return '??'\n  return fullName\n    .split(' ')\n    .map(name => name.charAt(0).toUpperCase())\n    .slice(0, 2)\n    .join('')\n}\n\nconst getAssignedTasksCount = (memberId) => {\n  const tasks = props.project?.tasks || []\n  return tasks.filter(task => task.assignee_id === memberId).length\n}\n</script>\n"}