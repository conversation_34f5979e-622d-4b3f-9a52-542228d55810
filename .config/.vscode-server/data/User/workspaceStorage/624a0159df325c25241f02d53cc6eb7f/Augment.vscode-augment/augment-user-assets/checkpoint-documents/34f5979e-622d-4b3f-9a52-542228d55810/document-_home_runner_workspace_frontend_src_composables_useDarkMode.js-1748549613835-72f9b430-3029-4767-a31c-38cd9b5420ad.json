{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/composables/useDarkMode.js"}, "originalCode": "import { ref, watch } from 'vue'\n\n// Global dark mode state\nconst isDarkMode = ref(false)\nlet isInitialized = false\n\n// Apply dark mode to DOM\nconst applyDarkMode = (value) => {\n  if (value) {\n    document.documentElement.classList.add('dark')\n    localStorage.setItem('darkMode', 'true')\n  } else {\n    document.documentElement.classList.remove('dark')\n    localStorage.setItem('darkMode', 'false')\n  }\n}\n\n// Initialize watcher only once\nconst initializeWatcher = () => {\n  if (isInitialized) return\n\n  watch(isDarkMode, (newValue) => {\n    console.log('Dark mode changed to:', newValue) // Debug log\n    applyDarkMode(newValue)\n  })\n\n  isInitialized = true\n}\n\nexport function useDarkMode() {\n  // Initialize watcher on first use\n  initializeWatcher()\n\n  const toggleDarkMode = () => {\n    console.log('Toggling dark mode from:', isDarkMode.value) // Debug log\n    isDarkMode.value = !isDarkMode.value\n  }\n\n  const setDarkMode = (value) => {\n    isDarkMode.value = value\n  }\n\n  const initializeDarkMode = () => {\n    console.log('Initializing dark mode...') // Debug log\n    const savedDarkMode = localStorage.getItem('darkMode')\n\n    // Check if DOM already has dark class (from system preference)\n    const isDomDark = document.documentElement.classList.contains('dark')\n    console.log('DOM already has dark class:', isDomDark)\n\n    if (savedDarkMode === 'true') {\n      isDarkMode.value = true\n    } else if (savedDarkMode === 'false') {\n      isDarkMode.value = false\n    } else {\n      // Default to system preference or current DOM state\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches\n      isDarkMode.value = isDomDark || prefersDark\n      console.log('Setting to system/DOM preference:', isDarkMode.value)\n    }\n\n    // Apply immediately\n    applyDarkMode(isDarkMode.value)\n\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')\n    const handleSystemThemeChange = (e) => {\n      console.log('System theme changed to:', e.matches ? 'dark' : 'light')\n      // Only update if user hasn't manually set a preference\n      const savedDarkMode = localStorage.getItem('darkMode')\n      if (!savedDarkMode || savedDarkMode === 'null') {\n        console.log('Updating to system preference:', e.matches)\n        isDarkMode.value = e.matches\n      } else {\n        console.log('User has manual preference, ignoring system change')\n      }\n    }\n    mediaQuery.addEventListener('change', handleSystemThemeChange)\n  }\n\n  return {\n    isDarkMode,\n    toggleDarkMode,\n    setDarkMode,\n    initializeDarkMode\n  }\n}\n", "modifiedCode": "import { ref, watch } from 'vue'\n\n// Global dark mode state\nconst isDarkMode = ref(false)\nlet isInitialized = false\n\n// Apply dark mode to DOM\nconst applyDarkMode = (value) => {\n  if (value) {\n    document.documentElement.classList.add('dark')\n    localStorage.setItem('darkMode', 'true')\n  } else {\n    document.documentElement.classList.remove('dark')\n    localStorage.setItem('darkMode', 'false')\n  }\n}\n\n// Initialize watcher only once\nconst initializeWatcher = () => {\n  if (isInitialized) return\n\n  watch(isDarkMode, (newValue) => {\n    applyDarkMode(newValue)\n  })\n\n  isInitialized = true\n}\n\nexport function useDarkMode() {\n  // Initialize watcher on first use\n  initializeWatcher()\n\n  const toggleDarkMode = () => {\n    console.log('Toggling dark mode from:', isDarkMode.value) // Debug log\n    isDarkMode.value = !isDarkMode.value\n  }\n\n  const setDarkMode = (value) => {\n    isDarkMode.value = value\n  }\n\n  const initializeDarkMode = () => {\n    console.log('Initializing dark mode...') // Debug log\n    const savedDarkMode = localStorage.getItem('darkMode')\n\n    // Check if DOM already has dark class (from system preference)\n    const isDomDark = document.documentElement.classList.contains('dark')\n    console.log('DOM already has dark class:', isDomDark)\n\n    if (savedDarkMode === 'true') {\n      isDarkMode.value = true\n    } else if (savedDarkMode === 'false') {\n      isDarkMode.value = false\n    } else {\n      // Default to system preference or current DOM state\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches\n      isDarkMode.value = isDomDark || prefersDark\n      console.log('Setting to system/DOM preference:', isDarkMode.value)\n    }\n\n    // Apply immediately\n    applyDarkMode(isDarkMode.value)\n\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')\n    const handleSystemThemeChange = (e) => {\n      console.log('System theme changed to:', e.matches ? 'dark' : 'light')\n      // Only update if user hasn't manually set a preference\n      const savedDarkMode = localStorage.getItem('darkMode')\n      if (!savedDarkMode || savedDarkMode === 'null') {\n        console.log('Updating to system preference:', e.matches)\n        isDarkMode.value = e.matches\n      } else {\n        console.log('User has manual preference, ignoring system change')\n      }\n    }\n    mediaQuery.addEventListener('change', handleSystemThemeChange)\n  }\n\n  return {\n    isDarkMode,\n    toggleDarkMode,\n    setDarkMode,\n    initializeDarkMode\n  }\n}\n"}