{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/projects/ProjectTeam.vue"}, "modifiedCode": "<template>\n  <div class=\"project-team\">\n    <div class=\"bg-white shadow rounded-lg p-6\">\n      <h3 class=\"text-lg font-medium text-gray-900 mb-6\">Team del Progetto</h3>\n      \n      <div class=\"text-center py-8\">\n        <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n        </svg>\n        <h3 class=\"mt-2 text-sm font-medium text-gray-900\">Componente Team in sviluppo</h3>\n        <p class=\"mt-1 text-sm text-gray-500\">\n          Gestione membri del team in arrivo...\n        </p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\ndefineProps({\n  project: {\n    type: Object,\n    default: null\n  },\n  loading: {\n    type: Boolean,\n    default: false\n  }\n})\n</script>\n"}