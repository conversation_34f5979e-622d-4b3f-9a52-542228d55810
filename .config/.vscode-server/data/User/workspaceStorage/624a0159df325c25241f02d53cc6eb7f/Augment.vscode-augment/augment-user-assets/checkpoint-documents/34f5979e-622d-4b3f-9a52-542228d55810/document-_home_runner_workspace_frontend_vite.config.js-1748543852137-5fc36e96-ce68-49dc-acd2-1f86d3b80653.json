{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/vite.config.js"}, "originalCode": "import { defineConfig } from 'vite'\nimport vue from '@vitejs/plugin-vue'\nimport { fileURLToPath, URL } from 'node:url'\nimport fs from 'fs'\nimport path from 'path'\n\n// Plugin per verificare che i file siano stati generati correttamente\nfunction verifyBuildAssets() {\n  return {\n    name: 'verify-build-assets',\n    writeBundle(options, bundle) {\n      const assets = Object.keys(bundle).filter(key =>\n        key.endsWith('.js') || key.endsWith('.css')\n      )\n\n      const jsFile = assets.find(asset => asset === 'app.js')\n      const cssFile = assets.find(asset => asset === 'index.css')\n      const vendorFile = assets.find(asset => asset === 'vendor.js')\n\n      console.log('✅ Build assets generati:')\n      console.log(`   - CSS: ${cssFile || 'MANCANTE'}`)\n      console.log(`   - App JS: ${jsFile || 'MANCANTE'}`)\n      console.log(`   - Vendor JS: ${vendorFile || 'MANCANTE'}`)\n      console.log('✅ Template Flask deve avere questi link:')\n      console.log(`   <link rel=\"stylesheet\" href=\"{{ url_for('static', filename='dist/assets/index.css') }}\">`)\n      console.log(`   <script type=\"module\" src=\"{{ url_for('static', filename='dist/assets/app.js') }}\"></script>`)\n      console.log(`   <link rel=\"modulepreload\" href=\"{{ url_for('static', filename='dist/assets/vendor.js') }}\">`)\n\n      // Non modifichiamo più il template automaticamente\n    }\n  }\n}\n\nexport default defineConfig({\n  plugins: [vue(), generateFlaskTemplate()],\n  build: {\n    outDir: '../backend/static/dist',\n    emptyOutDir: true,\n    rollupOptions: {\n      output: {\n        entryFileNames: 'assets/app.js',\n        chunkFileNames: 'assets/[name].js',\n        assetFileNames: 'assets/[name].[ext]',\n        manualChunks: {\n          vendor: ['vue', 'vue-router', 'pinia', 'axios', 'chart.js']\n        }\n      }\n    }\n  },\n  server: {\n    port: 3000,\n    proxy: {\n      '/api': {\n        target: 'http://localhost:5000',\n        changeOrigin: true\n      },\n      '/auth': {\n        target: 'http://localhost:5000',\n        changeOrigin: true\n      },\n      '/admin': {\n        target: 'http://localhost:5000',\n        changeOrigin: true\n      },\n      '/dashboard': {\n        target: 'http://localhost:5000',\n        changeOrigin: true\n      }\n    }\n  },\n  resolve: {\n    alias: {\n      '@': fileURLToPath(new URL('./src', import.meta.url)),\n      '@components': fileURLToPath(new URL('./src/components', import.meta.url)),\n      '@views': fileURLToPath(new URL('./src/views', import.meta.url)),\n      '@stores': fileURLToPath(new URL('./src/stores', import.meta.url)),\n      '@utils': fileURLToPath(new URL('./src/utils', import.meta.url))\n    }\n  }\n})", "modifiedCode": "import { defineConfig } from 'vite'\nimport vue from '@vitejs/plugin-vue'\nimport { fileURLToPath, URL } from 'node:url'\nimport fs from 'fs'\nimport path from 'path'\n\n// Plugin per verificare che i file siano stati generati correttamente\nfunction verifyBuildAssets() {\n  return {\n    name: 'verify-build-assets',\n    writeBundle(options, bundle) {\n      const assets = Object.keys(bundle).filter(key =>\n        key.endsWith('.js') || key.endsWith('.css')\n      )\n\n      const jsFile = assets.find(asset => asset === 'app.js')\n      const cssFile = assets.find(asset => asset === 'index.css')\n      const vendorFile = assets.find(asset => asset === 'vendor.js')\n\n      console.log('✅ Build assets generati:')\n      console.log(`   - CSS: ${cssFile || 'MANCANTE'}`)\n      console.log(`   - App JS: ${jsFile || 'MANCANTE'}`)\n      console.log(`   - Vendor JS: ${vendorFile || 'MANCANTE'}`)\n      console.log('✅ Template Flask deve avere questi link:')\n      console.log(`   <link rel=\"stylesheet\" href=\"{{ url_for('static', filename='dist/assets/index.css') }}\">`)\n      console.log(`   <script type=\"module\" src=\"{{ url_for('static', filename='dist/assets/app.js') }}\"></script>`)\n      console.log(`   <link rel=\"modulepreload\" href=\"{{ url_for('static', filename='dist/assets/vendor.js') }}\">`)\n\n      // Non modifichiamo più il template automaticamente\n    }\n  }\n}\n\nexport default defineConfig({\n  plugins: [vue(), verifyBuildAssets()],\n  build: {\n    outDir: '../backend/static/dist',\n    emptyOutDir: true,\n    rollupOptions: {\n      output: {\n        entryFileNames: 'assets/app.js',\n        chunkFileNames: 'assets/[name].js',\n        assetFileNames: 'assets/[name].[ext]',\n        manualChunks: {\n          vendor: ['vue', 'vue-router', 'pinia', 'axios', 'chart.js']\n        }\n      }\n    }\n  },\n  server: {\n    port: 3000,\n    proxy: {\n      '/api': {\n        target: 'http://localhost:5000',\n        changeOrigin: true\n      },\n      '/auth': {\n        target: 'http://localhost:5000',\n        changeOrigin: true\n      },\n      '/admin': {\n        target: 'http://localhost:5000',\n        changeOrigin: true\n      },\n      '/dashboard': {\n        target: 'http://localhost:5000',\n        changeOrigin: true\n      }\n    }\n  },\n  resolve: {\n    alias: {\n      '@': fileURLToPath(new URL('./src', import.meta.url)),\n      '@components': fileURLToPath(new URL('./src/components', import.meta.url)),\n      '@views': fileURLToPath(new URL('./src/views', import.meta.url)),\n      '@stores': fileURLToPath(new URL('./src/stores', import.meta.url)),\n      '@utils': fileURLToPath(new URL('./src/utils', import.meta.url))\n    }\n  }\n})"}