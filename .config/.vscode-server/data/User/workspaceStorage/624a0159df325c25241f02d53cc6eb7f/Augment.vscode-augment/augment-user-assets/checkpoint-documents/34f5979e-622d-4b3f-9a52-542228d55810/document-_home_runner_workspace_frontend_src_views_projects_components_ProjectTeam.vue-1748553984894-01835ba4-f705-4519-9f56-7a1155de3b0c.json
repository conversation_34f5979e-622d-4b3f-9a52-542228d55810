{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTeam.vue"}, "originalCode": "<template>\n  <div class=\"project-team\">\n    <div class=\"bg-white shadow rounded-lg p-6\">\n      <h3 class=\"text-lg font-medium text-gray-900 mb-6\">Team del Progetto</h3>\n\n      <div class=\"text-center py-8\">\n        <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n        </svg>\n        <h3 class=\"mt-2 text-sm font-medium text-gray-900\">Componente Team in sviluppo</h3>\n        <p class=\"mt-1 text-sm text-gray-500\">Gestione membri del team in arrivo...</p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\ndefineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n</script>\n", "modifiedCode": "<template>\n  <div class=\"project-team\">\n    <div class=\"bg-white shadow rounded-lg p-6\">\n      <h3 class=\"text-lg font-medium text-gray-900 mb-6\">Team del Progetto</h3>\n\n      <!-- Team Members Grid -->\n      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        <div v-for=\"member in teamMembers\" :key=\"member.id\" class=\"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\">\n          <div class=\"flex items-center space-x-4\">\n            <div class=\"flex-shrink-0\">\n              <img\n                v-if=\"member.profile_image\"\n                :src=\"member.profile_image\"\n                :alt=\"member.full_name\"\n                class=\"w-12 h-12 rounded-full\"\n              >\n              <div v-else class=\"w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center\">\n                <span class=\"text-sm font-medium text-gray-600\">{{ getInitials(member.full_name) }}</span>\n              </div>\n            </div>\n            <div class=\"flex-1\">\n              <h4 class=\"text-lg font-medium text-gray-900\">{{ member.full_name }}</h4>\n              <p class=\"text-sm text-gray-600\">{{ member.role || 'Team Member' }}</p>\n              <p class=\"text-xs text-gray-500\">{{ member.email }}</p>\n            </div>\n          </div>\n          <div class=\"mt-4 grid grid-cols-2 gap-4 text-sm\">\n            <div>\n              <span class=\"text-gray-600\">Ore Lavorate:</span>\n              <span class=\"font-medium ml-1\">{{ member.hours_worked || 0 }}h</span>\n            </div>\n            <div>\n              <span class=\"text-gray-600\">Task Assegnati:</span>\n              <span class=\"font-medium ml-1\">{{ getAssignedTasksCount(member.id) }}</span>\n            </div>\n          </div>\n        </div>\n        <div v-if=\"teamMembers.length === 0\" class=\"col-span-full text-center py-8\">\n          <p class=\"text-gray-500\">Nessun membro del team assegnato</p>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\ndefineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n</script>\n"}