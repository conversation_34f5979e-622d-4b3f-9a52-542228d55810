{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}, "originalCode": "<template>\n  <div class=\"p-6\">\n    <h1 class=\"text-3xl font-bold text-red-500\">PROGETTI FUNZIONA!</h1>\n    <p class=\"text-lg text-blue-500\">Se vedi questo, il componente si carica</p>\n  </div>\n</template>\n\n<script setup>\nconsole.log('Projects.vue loaded successfully!')\n</script>\n", "modifiedCode": "<template>\n  <div>\n    <!-- Header -->\n    <div class=\"mb-6\">\n      <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\"><PERSON><PERSON><PERSON></h1>\n          <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            Gestisci e monitora tutti i progetti aziendali\n          </p>\n        </div>\n        <div class=\"mt-4 sm:mt-0\">\n          <button\n            @click=\"createProject\"\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n          >\n            <svg class=\"h-4 w-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 4v16m8-8H4\" />\n            </svg>\n            Nuovo Progetto\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Filtri e Ricerca -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Stato</label>\n          <select\n            v-model=\"filters.status\"\n            @change=\"applyFilters\"\n            class=\"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500\"\n          >\n            <option value=\"\">Tutti gli stati</option>\n            <option value=\"planning\">Pianificazione</option>\n            <option value=\"active\">Attivo</option>\n            <option value=\"completed\">Completato</option>\n            <option value=\"on-hold\">In Pausa</option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Cliente</label>\n          <select\n            v-model=\"filters.client\"\n            @change=\"applyFilters\"\n            class=\"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500\"\n          >\n            <option value=\"\">Tutti i clienti</option>\n            <option v-for=\"client in clients\" :key=\"client.id\" :value=\"client.id\">\n              {{ client.name }}\n            </option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Ricerca</label>\n          <input\n            v-model=\"searchQuery\"\n            @input=\"search\"\n            type=\"text\"\n            placeholder=\"Cerca progetti...\"\n            class=\"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500\"\n          >\n        </div>\n        <div class=\"flex items-end\">\n          <button\n            @click=\"resetFilters\"\n            class=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600\"\n          >\n            Reset Filtri\n          </button>\n        </div>\n      </div>\n    </div>\n"}