{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/main.js"}, "originalCode": "import { createApp } from 'vue'\nimport { createPinia } from 'pinia'\nimport App from './App.vue'\nimport router from './router'\nimport { useAuthStore } from './stores/auth'\nimport './assets/css/main.css'\n\nconst app = createApp(App)\nconst pinia = createPinia()\n\napp.use(pinia)\napp.use(router)\n\n// Inizializza l'autenticazione prima di montare l'app\nconst authStore = useAuthStore()\nauthStore.initializeAuth().then(() => {\n  app.mount('#app')\n})", "modifiedCode": "import { createApp } from 'vue'\nimport { createPinia } from 'pinia'\nimport App from './App.vue'\nimport router from './router'\nimport { useAuthStore } from './stores/auth'\nimport './assets/css/main.css'\n\nconst app = createApp(App)\nconst pinia = createPinia()\n\napp.use(pinia)\napp.use(router)\n\n// Inizializza l'autenticazione prima di montare l'app\nconst authStore = useAuthStore()\n\n// Monta l'app sempre, anche se l'auth fallisce\nauthStore.initializeAuth()\n  .then(() => {\n    console.log('Auth initialized successfully')\n    app.mount('#app')\n  })\n  .catch((error) => {\n    console.error('Auth initialization failed:', error)\n    // Monta l'app comunque\n    app.mount('#app')\n  })"}