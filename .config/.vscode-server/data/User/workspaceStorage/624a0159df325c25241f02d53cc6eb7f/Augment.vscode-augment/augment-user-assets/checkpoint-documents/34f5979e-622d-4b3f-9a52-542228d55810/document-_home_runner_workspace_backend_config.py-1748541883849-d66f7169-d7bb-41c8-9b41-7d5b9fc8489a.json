{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/config.py"}, "originalCode": "import os\nfrom datetime import timedelta\n\nclass Config:\n    # Flask config\n    DEBUG = os.environ.get('FLASK_DEBUG', 'True') == 'True'\n    TESTING = False\n\n    # Database config\n    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')\n    SQLALCHEMY_TRACK_MODIFICATIONS = False\n    SQLALCHEMY_ENGINE_OPTIONS = {\n        \"pool_recycle\": 300,\n        \"pool_pre_ping\": True,\n    }\n\n    # API keys\n    OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')\n    PERPLEXITY_API_KEY = os.environ.get('PERPLEXITY_API_KEY')\n\n    # Application config\n    COMPANY_NAME = \"DatVinci\"\n    APPLICATION_NAME = \"DatPortal\"\n    UPLOAD_FOLDER = os.path.join('static', 'uploads')\n    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max upload size\n    PASSWORD_RESET_TOKEN_EXPIRATION_SECONDS = 3600 * 24  # 24 ore\n\n    # Email config\n    MAIL_SERVER = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')\n    MAIL_PORT = int(os.environ.get('MAIL_PORT', 587))\n    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'True') == 'True'\n    MAIL_USE_SSL = os.environ.get('MAIL_USE_SSL', 'False') == 'True'\n    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')\n    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')\n    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER', f'noreply@{COMPANY_NAME.lower()}.com')\n\n    # Session management\n    SESSION_PERMANENT = True\n    PERMANENT_SESSION_LIFETIME = timedelta(minutes=30)  # Idle timeout\n    ABSOLUTE_SESSION_LIFETIME = 3600  # Absolute timeout in seconds\n    REMEMBER_COOKIE_DURATION = timedelta(days=7)\n", "modifiedCode": "import os\nfrom datetime import timedelta\n\nclass Config:\n    # Flask config\n    DEBUG = os.environ.get('FLASK_DEBUG', 'True') == 'True'\n    TESTING = False\n\n    # Database config\n    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')\n    SQLALCHEMY_TRACK_MODIFICATIONS = False\n    SQLALCHEMY_ENGINE_OPTIONS = {\n        \"pool_recycle\": 300,\n        \"pool_pre_ping\": True,\n    }\n\n    # API keys\n    OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')\n    PERPLEXITY_API_KEY = os.environ.get('PERPLEXITY_API_KEY')\n\n    # Application config\n    COMPANY_NAME = \"DatVinci\"\n    APPLICATION_NAME = \"DatPortal\"\n    UPLOAD_FOLDER = os.path.join('static', 'uploads')\n    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max upload size\n    PASSWORD_RESET_TOKEN_EXPIRATION_SECONDS = 3600 * 24  # 24 ore\n\n    # Email config\n    MAIL_SERVER = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')\n    MAIL_PORT = int(os.environ.get('MAIL_PORT', 587))\n    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'True') == 'True'\n    MAIL_USE_SSL = os.environ.get('MAIL_USE_SSL', 'False') == 'True'\n    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')\n    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')\n    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER', f'noreply@{COMPANY_NAME.lower()}.com')\n\n    # Session management\n    SESSION_PERMANENT = True\n    PERMANENT_SESSION_LIFETIME = timedelta(minutes=30)  # Idle timeout\n    ABSOLUTE_SESSION_LIFETIME = 3600  # Absolute timeout in seconds\n    REMEMBER_COOKIE_DURATION = timedelta(days=7)\n\n    # Cookie configuration for CORS/SPA\n    SESSION_COOKIE_SECURE = False  # Set to True in production with HTTPS\n    SESSION_COOKIE_HTTPONLY = True  # Prevent XSS attacks\n    SESSION_COOKIE_SAMESITE = 'Lax'  # Allow cross-origin requests\n    SESSION_COOKIE_NAME = 'session'\n"}