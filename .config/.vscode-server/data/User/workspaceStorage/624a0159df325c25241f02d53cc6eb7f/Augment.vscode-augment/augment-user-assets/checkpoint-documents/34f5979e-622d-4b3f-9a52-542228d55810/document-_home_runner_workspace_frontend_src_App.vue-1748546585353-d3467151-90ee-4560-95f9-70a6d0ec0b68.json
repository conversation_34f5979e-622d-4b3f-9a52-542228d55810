{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/App.vue"}, "originalCode": "<template>\n  <div id=\"app\">\n    <router-view />\n  </div>\n</template>\n\n<script setup>\n// L'App principale è solo un contenitore per il router\n// Tutti i layout sono gestiti dalle singole route\n</script>", "modifiedCode": "<template>\n  <div id=\"app\">\n    <router-view />\n  </div>\n</template>\n\n<script setup>\nimport { useDarkMode } from '@/composables/useDarkMode'\n\n// Initialize dark mode globally\nconst { initializeDarkMode } = useDarkMode()\ninitializeDarkMode()\n</script>"}