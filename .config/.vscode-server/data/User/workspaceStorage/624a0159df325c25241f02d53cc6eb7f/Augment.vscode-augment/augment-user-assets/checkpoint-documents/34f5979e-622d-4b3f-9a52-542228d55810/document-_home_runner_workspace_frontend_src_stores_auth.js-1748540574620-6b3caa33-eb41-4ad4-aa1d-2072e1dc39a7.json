{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/stores/auth.js"}, "originalCode": "import { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\nimport api from '@/utils/api'\n\nexport const useAuthStore = defineStore('auth', () => {\n  // Rimuoviamo la gestione del token per usare solo le sessioni Flask\n  const storedUser = localStorage.getItem('user')\n  const user = ref(storedUser ? JSON.parse(storedUser) : null)\n  const loading = ref(false)\n  const error = ref(null)\n  const sessionChecked = ref(false)\n\n  const isAuthenticated = computed(() => !!user.value && sessionChecked.value)\n\n  async function login(credentials) {\n    loading.value = true\n    error.value = null\n\n    try {\n      const response = await api.post('/api/auth/login', credentials)\n\n      if (response.data.success) {\n        user.value = response.data.data.user\n        localStorage.setItem('user', JSON.stringify(user.value))\n        sessionChecked.value = true\n        return { success: true }\n      } else {\n        error.value = response.data.message || 'Errore durante il login'\n        return { success: false, error: error.value }\n      }\n    } catch (err) {\n      error.value = err.response?.data?.message || 'Errore di connessione'\n      return { success: false, error: error.value }\n    } finally {\n      loading.value = false\n    }\n  }\n\n  async function register(userData) {\n    loading.value = true\n    error.value = null\n\n    try {\n      const response = await api.post('/api/auth/register', userData)\n\n      if (response.data.success) {\n        return { success: true, message: response.data.message }\n      } else {\n        error.value = response.data.message || 'Errore durante la registrazione'\n        return { success: false, error: error.value }\n      }\n    } catch (err) {\n      error.value = err.response?.data?.message || 'Errore di connessione'\n      return { success: false, error: error.value }\n    } finally {\n      loading.value = false\n    }\n  }\n\n  async function logout() {\n    try {\n      await api.post('/auth/logout')\n    } catch (err) {\n      console.warn('Errore durante il logout:', err)\n    } finally {\n      token.value = null\n      user.value = null\n      localStorage.removeItem('token')\n    }\n  }\n\n  async function checkAuth() {\n    if (!token.value) return false\n\n    try {\n      const response = await api.get('/auth/me')\n      if (response.data.success) {\n        user.value = response.data.user\n        return true\n      } else {\n        logout()\n        return false\n      }\n    } catch (err) {\n      logout()\n      return false\n    }\n  }\n\n  return {\n    user,\n    token,\n    loading,\n    error,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    checkAuth\n  }\n})", "modifiedCode": "import { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\nimport api from '@/utils/api'\n\nexport const useAuthStore = defineStore('auth', () => {\n  // Rimuoviamo la gestione del token per usare solo le sessioni Flask\n  const storedUser = localStorage.getItem('user')\n  const user = ref(storedUser ? JSON.parse(storedUser) : null)\n  const loading = ref(false)\n  const error = ref(null)\n  const sessionChecked = ref(false)\n\n  const isAuthenticated = computed(() => !!user.value && sessionChecked.value)\n\n  async function login(credentials) {\n    loading.value = true\n    error.value = null\n\n    try {\n      const response = await api.post('/api/auth/login', credentials)\n\n      if (response.data.success) {\n        user.value = response.data.data.user\n        localStorage.setItem('user', JSON.stringify(user.value))\n        sessionChecked.value = true\n        return { success: true }\n      } else {\n        error.value = response.data.message || 'Errore durante il login'\n        return { success: false, error: error.value }\n      }\n    } catch (err) {\n      error.value = err.response?.data?.message || 'Errore di connessione'\n      return { success: false, error: error.value }\n    } finally {\n      loading.value = false\n    }\n  }\n\n  async function register(userData) {\n    loading.value = true\n    error.value = null\n\n    try {\n      const response = await api.post('/api/auth/register', userData)\n\n      if (response.data.success) {\n        return { success: true, message: response.data.message }\n      } else {\n        error.value = response.data.message || 'Errore durante la registrazione'\n        return { success: false, error: error.value }\n      }\n    } catch (err) {\n      error.value = err.response?.data?.message || 'Errore di connessione'\n      return { success: false, error: error.value }\n    } finally {\n      loading.value = false\n    }\n  }\n\n  async function logout() {\n    try {\n      await api.post('/api/auth/logout')\n    } catch (err) {\n      console.warn('Errore durante il logout:', err)\n    } finally {\n      user.value = null\n      sessionChecked.value = false\n      localStorage.removeItem('user')\n    }\n  }\n\n  async function checkAuth() {\n    if (sessionChecked.value) {\n      return isAuthenticated.value\n    }\n\n    try {\n      const response = await api.get('/api/auth/me')\n      if (response.data.success) {\n        user.value = response.data.data.user\n        localStorage.setItem('user', JSON.stringify(user.value))\n        sessionChecked.value = true\n        return true\n      } else {\n        await logout()\n        return false\n      }\n    } catch (err) {\n      await logout()\n      return false\n    }\n  }\n\n  async function initializeAuth() {\n    // Controlla se c'è un utente salvato e verifica la sessione\n    if (user.value) {\n      return await checkAuth()\n    }\n    sessionChecked.value = true\n    return false\n  }\n\n  return {\n    user,\n    loading,\n    error,\n    sessionChecked,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    checkAuth,\n    initializeAuth\n  }\n})"}