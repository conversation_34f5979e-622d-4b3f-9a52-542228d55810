{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "specs/vue_professional_migration.md"}, "originalCode": "# Vue.js Professional Migration - DatPortal\n\n## 🎯 Obiettivo\n\nMigrazione da setup ibrido Vue.js (CDN + template inline) a architettura professionale con Single File Components e build system Vite.\n\n## 📊 Stato Attuale (Aggiornato)\n\n### ✅ Completato\n- ✅ Setup frontend Vue.js 3 + Vite + SFC (Single File Components)\n- ✅ Routing Vue Router 4 con layout AppLayout e PublicLayout\n- ✅ Tailwind CSS + PostCSS configurato\n- ✅ Stores Pinia (auth, tenant) implementati\n- ✅ Pagine pubbliche migrate (Home, About, Contact, Services)\n- ✅ Autenticazione Login/Register migrata\n- ✅ Layout responsive con sidebar funzionante\n- ✅ Backend Flask completo (12 blueprints, 9 API)\n- ✅ Build system configurato (vite.config.js, proxy Flask)\n- ✅ **Dashboard.vue completamente funzionante** (KPI, grafici, statistiche)\n- ✅ **Template Flask integrato** con build assets automatici\n- ✅ **Navbar dinamica** (azioni contestuali per pagina)\n- ✅ **API backend corrette** (auth, dashboard, KPI, progetti, task, ecc.)\n- ✅ **SPA navigation** senza reload di pagina\n\n### 🔄 In Corso - Prossime Viste da Migrare\n- 🔄 Projects.vue (placeholder → vista completa con tab)\n- 🔄 Personnel.vue (placeholder → vista completa con organigramma)\n- 🔄 Tasks.vue (da creare)\n- 🔄 Admin.vue (da creare)\n\n### ❌ Da Completare - Piano Prioritario\n1. **Projects.vue** (Alta priorità)\n   - Migrazione da template legacy con 3000+ righe\n   - Tab: Overview, Tasks, Team, Files, KPI, Gantt\n   - Componenti: ProjectHeader, ProjectTabs, TaskList, TeamView\n\n2. **Personnel.vue** (Alta priorità)\n   - Migrazione modulo HR con 12 template\n   - Tab: Directory, Orgchart, Skills, Departments\n   - Componenti: PersonnelCard, OrgChart, SkillsMatrix\n\n3. **Tasks.vue** (Media priorità)\n   - Vista task standalone (non solo in progetti)\n   - Kanban board, filtri, assegnazioni\n\n4. **Admin.vue** (Bassa priorità)\n   - Pannello amministrazione\n   - Gestione utenti, permessi, configurazioni\n\n### 🎯 Prossimo Step: Migrazione Projects.vue\n\n## 🏗️ Architettura Target\n\n```\nproject/\n├── backend/                    # Flask app (MANTENIAMO TUTTO)\n│   ├── app.py, main.py, models.py\n│   ├── blueprints/ (12 moduli)\n│   ├── utils/ (10 moduli)\n│   ├── tests/ (19 test files)\n│   ├── templates/index.html    # Solo entry point\n│   └── static/dist/            # Build output\n│\n├── frontend/                   # Vue.js app (NUOVO)\n│   ├── src/\n│   │   ├── components/\n│   │   │   ├── layout/         # Sidebar, Navigation, etc.\n│   │   │   ├── ui/             # Modal, DataTable, etc.\n│   │   │   └── forms/          # Form components\n│   │   ├── views/\n│   │   │   ├── public/         # Home, About, Contact\n│   │   │   ├── auth/           # Login, Register\n│   │   │   ├── dashboard/      # Dashboard views\n│   │   │   ├── projects/       # Project management\n│   │   │   └── personnel/      # HR management\n│   │   ├── stores/             # Pinia stores\n│   │   ├── router/             # Vue Router\n│   │   ├── utils/              # API, helpers\n│   │   └── assets/             # CSS, images\n│   ├── package.json\n│   ├── vite.config.js\n│   └── index.html\n```\n\n## 📋 Piano Migrazione (5h 30min)\n\n### Fase 1: Setup Build System (45min)\n```bash\n# 1. Inizializza frontend\ncd project\nnpm create vue@latest frontend\ncd frontend\nnpm install\n\n# 2. Configura Vite\n# vite.config.js con proxy Flask\n# tailwind.config.js\n# package.json scripts\n```\n\n### Fase 2: Core Migration (1h)\n- Migra `templates/auth/` → `src/views/auth/`\n- Migra `templates/components/` → `src/components/layout/`\n- Setup stores Pinia principali\n\n### Fase 3: Projects Module (1h)\n- Migra `templates/projects/view.html` (3000+ righe) → componenti modulari\n- Crea `ProjectView.vue`, `ProjectTasks.vue`, `ProjectGantt.vue`\n- Setup `stores/projects.js`\n\n### Fase 4: Personnel Module (1h)\n- Migra 12 template personnel → Single File Components\n- Orgchart, Skills, Directory views\n- Setup `stores/personnel.js`\n\n### Fase 5: Finalizzazione (45min)\n- Migra moduli rimanenti (Admin, Products)\n- Setup testing (Vitest, Cypress)\n- Cleanup template obsoleti\n\n## ⚙️ Configurazione Tecnica\n\n### Vite Config\n```javascript\n// frontend/vite.config.js\nimport { defineConfig } from 'vite'\nimport vue from '@vitejs/plugin-vue'\n\nexport default defineConfig({\n  plugins: [vue()],\n  build: {\n    outDir: '../backend/static/dist',\n    emptyOutDir: true\n  },\n  server: {\n    port: 3000,\n    proxy: {\n      '/api': 'http://localhost:5000',\n      '/auth': 'http://localhost:5000'\n    }\n  },\n  resolve: {\n    alias: {\n      '@': '/src',\n      '@components': '/src/components',\n      '@views': '/src/views'\n    }\n  }\n})\n```\n\n### Package.json\n```json\n{\n  \"scripts\": {\n    \"dev\": \"vite\",\n    \"build\": \"vite build\",\n    \"test\": \"vitest\",\n    \"lint\": \"eslint src --ext .vue,.js\"\n  },\n  \"dependencies\": {\n    \"vue\": \"^3.4.0\",\n    \"vue-router\": \"^4.2.0\",\n    \"pinia\": \"^2.1.0\",\n    \"axios\": \"^1.6.0\"\n  },\n  \"devDependencies\": {\n    \"@vitejs/plugin-vue\": \"^4.5.0\",\n    \"vite\": \"^5.0.0\",\n    \"vitest\": \"^1.0.0\",\n    \"tailwindcss\": \"^3.4.0\"\n  }\n}\n```\n\n## 🔄 Workflow Development\n\n### Terminal 1: Backend Flask\n```bash\ncd backend\npython main.py\n# → http://localhost:5000\n```\n\n### Terminal 2: Frontend Vue.js\n```bash\ncd frontend\nnpm run dev\n# → http://localhost:3000 (proxy to Flask)\n```\n\n### Flusso Richieste\n```\nBrowser → http://localhost:3000 (Vite Dev Server)\n    ↓ (HMR, Vue DevTools)\nVue.js Components\n    ↓ (/api/* requests)\nProxy → http://localhost:5000 (Flask)\n    ↓ (Database, Business Logic)\nResponse → Vue.js → Browser Update\n```\n\n## 💻 Esempi Migrazione\n\n### Da Template HTML a Vue SFC\n```html\n<!-- PRIMA: templates/projects/view.html (3000+ righe) -->\n<div class=\"project-view\">\n  <!-- HTML complesso con Alpine.js -->\n</div>\n```\n\n```vue\n<!-- DOPO: src/views/projects/ProjectView.vue -->\n<template>\n  <div class=\"project-view\">\n    <ProjectHeader :project=\"project\" />\n    <TabNavigation v-model=\"activeTab\" />\n    <component :is=\"currentComponent\" :project=\"project\" />\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue'\nimport { useProjectsStore } from '@/stores/projects'\n\nconst store = useProjectsStore()\nconst activeTab = ref('overview')\nconst project = computed(() => store.currentProject)\n</script>\n\n<style scoped>\n.project-view {\n  @apply container mx-auto p-6;\n}\n</style>\n```\n\n## ✅ Checklist Migrazione - STATO AGGIORNATO\n\n### Setup ✅ COMPLETATO\n- [x] Inizializza progetto Vue.js con Vite\n- [x] Configura Tailwind CSS + PostCSS\n- [x] Setup proxy development server\n- [x] Configura alias path e build output\n\n### Componenti Core ✅ COMPLETATO\n- [x] Migra layout components (AppLayout, AppSidebar, AppHeader)\n- [x] Sidebar collassabile con navigazione completa\n- [x] Header modulare con breadcrumbs, notifiche, ricerca\n- [x] Migra auth views (Login, Register)\n- [x] Setup stores Pinia principali (auth, tenant)\n- [x] Coerenza stile tra PublicLayout e AppLayout\n\n### Moduli Business 🔄 IN CORSO\n- [x] Dashboard view (base implementata)\n- [ ] Projects module (placeholder → componenti avanzati)\n- [ ] Personnel module (placeholder → componenti avanzati)\n- [ ] Admin interface\n- [ ] Integrazione API reali con backend Flask\n\n### Testing & Quality ❌ NON INIZIATO\n- [ ] Setup Vitest per unit tests\n- [ ] Setup Cypress per E2E tests\n- [ ] Configura ESLint + Prettier\n- [ ] Test coverage frontend\n- [ ] Test componenti layout modulari\n\n### Cleanup ⏳ PIANIFICATO\n- [ ] Rimuovi template HTML obsoleti\n- [ ] Rimuovi file .js con template inline\n- [ ] Aggiorna documentazione\n- [ ] Test deployment production\n\n## 📊 Stato Corrente (Maggio 2025)\n\n### ✅ Completato (70%)\n- Architettura Vue.js 3 + Vite funzionante\n- Layout modulare professionale\n- Navigazione completa con 10+ elementi\n- Autenticazione integrata\n- Gestione configurazioni tenant\n- Build system ottimizzato\n\n### 🔄 In Sviluppo (20%)\n- Componenti business (Dashboard, Projects, Personnel)\n- Integrazione API Flask\n- Componenti UI avanzati\n\n### ❌ Da Fare (10%)\n- Testing suite completa\n- Cleanup template legacy\n\n## 🚀 Vantaggi Finali\n\n✅ **Syntax highlighting** completo per HTML/CSS/JS\n✅ **Hot Module Replacement** per sviluppo rapido\n✅ **Vue DevTools** per debugging avanzato\n✅ **Code splitting** automatico per performance\n✅ **TypeScript support** (opzionale)\n✅ **Testing integrato** con Vitest/Cypress\n✅ **Build ottimizzato** per production\n✅ **Architettura scalabile** per crescita futura\n\n## 🔧 Troubleshooting\n\n### Problemi Comuni\n- **CORS errors**: Verificare configurazione proxy Vite\n- **Asset loading**: Controllare path alias in vite.config.js\n- **CSS conflicts**: Usare scoped styles nei componenti\n- **Build errors**: Verificare import paths e dependencies\n\n### Debug Tips\n- Usare Vue DevTools per ispezionare componenti\n- Console browser per errori JavaScript\n- Network tab per verificare API calls\n- Vite dev server logs per errori build\n\n## 📈 Metriche Successo\n\n### Performance\n- Bundle size < 500KB (gzipped)\n- First Contentful Paint < 2s\n- Time to Interactive < 3s\n\n### Developer Experience\n- Hot reload < 100ms\n- Build time < 30s\n- Test execution < 10s\n\n### Code Quality\n- ESLint errors: 0\n- Test coverage > 80%\n- TypeScript errors: 0 (se abilitato)\n\n## 📚 Riferimenti\n\n- [Vue.js 3 Documentation](https://vuejs.org/)\n- [Vite Documentation](https://vitejs.dev/)\n- [Vue Router 4](https://router.vuejs.org/)\n- [Pinia State Management](https://pinia.vuejs.org/)\n- [Vitest Testing](https://vitest.dev/)\n- [Tailwind CSS](https://tailwindcss.com/)\n- [Vue DevTools](https://devtools.vuejs.org/)\n", "modifiedCode": "# Vue.js Professional Migration - DatPortal\n\n## 🎯 Obiettivo\n\nMigrazione da setup ibrido Vue.js (CDN + template inline) a architettura professionale con Single File Components e build system Vite.\n\n## 📊 Stato Attuale (Aggiornato)\n\n### ✅ Completato\n- ✅ Setup frontend Vue.js 3 + Vite + SFC (Single File Components)\n- ✅ Routing Vue Router 4 con layout AppLayout e PublicLayout\n- ✅ Tailwind CSS + PostCSS configurato\n- ✅ Stores Pinia (auth, tenant) implementati\n- ✅ Pagine pubbliche migrate (Home, About, Contact, Services)\n- ✅ Autenticazione Login/Register migrata\n- ✅ Layout responsive con sidebar funzionante\n- ✅ Backend Flask completo (12 blueprints, 9 API)\n- ✅ Build system configurato (vite.config.js, proxy Flask)\n- ✅ **Dashboard.vue completamente funzionante** (KPI, grafici, statistiche)\n- ✅ **Template Flask integrato** con build assets automatici\n- ✅ **Navbar dinamica** (azioni contestuali per pagina)\n- ✅ **API backend corrette** (auth, dashboard, KPI, progetti, task, ecc.)\n- ✅ **SPA navigation** senza reload di pagina\n\n### 🔄 In Corso - Prossime Viste da Migrare\n- 🔄 Projects.vue (placeholder → vista completa con tab)\n- 🔄 Personnel.vue (placeholder → vista completa con organigramma)\n- 🔄 Tasks.vue (da creare)\n- 🔄 Admin.vue (da creare)\n\n### ❌ Da Completare - Piano Prioritario\n1. **Projects.vue** (Alta priorità)\n   - Migrazione da template legacy con 3000+ righe\n   - Tab: Overview, Tasks, Team, Files, KPI, Gantt\n   - Componenti: ProjectHeader, ProjectTabs, TaskList, TeamView\n\n2. **Personnel.vue** (Alta priorità)\n   - Migrazione modulo HR con 12 template\n   - Tab: Directory, Orgchart, Skills, Departments\n   - Componenti: PersonnelCard, OrgChart, SkillsMatrix\n\n3. **Tasks.vue** (Media priorità)\n   - Vista task standalone (non solo in progetti)\n   - Kanban board, filtri, assegnazioni\n\n4. **Admin.vue** (Bassa priorità)\n   - Pannello amministrazione\n   - Gestione utenti, permessi, configurazioni\n\n### 🎯 Prossimo Step: Migrazione Projects.vue\n\n## 🏗️ Architettura Target\n\n```\nproject/\n├── backend/                    # Flask app (MANTENIAMO TUTTO)\n│   ├── app.py, main.py, models.py\n│   ├── blueprints/ (12 moduli)\n│   ├── utils/ (10 moduli)\n│   ├── tests/ (19 test files)\n│   ├── templates/index.html    # Solo entry point\n│   └── static/dist/            # Build output\n│\n├── frontend/                   # Vue.js app (NUOVO)\n│   ├── src/\n│   │   ├── components/\n│   │   │   ├── layout/         # Sidebar, Navigation, etc.\n│   │   │   ├── ui/             # Modal, DataTable, etc.\n│   │   │   └── forms/          # Form components\n│   │   ├── views/\n│   │   │   ├── public/         # Home, About, Contact\n│   │   │   ├── auth/           # Login, Register\n│   │   │   ├── dashboard/      # Dashboard views\n│   │   │   ├── projects/       # Project management\n│   │   │   └── personnel/      # HR management\n│   │   ├── stores/             # Pinia stores\n│   │   ├── router/             # Vue Router\n│   │   ├── utils/              # API, helpers\n│   │   └── assets/             # CSS, images\n│   ├── package.json\n│   ├── vite.config.js\n│   └── index.html\n```\n\n## 📋 Piano Migrazione (5h 30min)\n\n### Fase 1: Setup Build System (45min)\n```bash\n# 1. Inizializza frontend\ncd project\nnpm create vue@latest frontend\ncd frontend\nnpm install\n\n# 2. Configura Vite\n# vite.config.js con proxy Flask\n# tailwind.config.js\n# package.json scripts\n```\n\n### Fase 2: Core Migration (1h)\n- Migra `templates/auth/` → `src/views/auth/`\n- Migra `templates/components/` → `src/components/layout/`\n- Setup stores Pinia principali\n\n### Fase 3: Projects Module (1h)\n- Migra `templates/projects/view.html` (3000+ righe) → componenti modulari\n- Crea `ProjectView.vue`, `ProjectTasks.vue`, `ProjectGantt.vue`\n- Setup `stores/projects.js`\n\n### Fase 4: Personnel Module (1h)\n- Migra 12 template personnel → Single File Components\n- Orgchart, Skills, Directory views\n- Setup `stores/personnel.js`\n\n### Fase 5: Finalizzazione (45min)\n- Migra moduli rimanenti (Admin, Products)\n- Setup testing (Vitest, Cypress)\n- Cleanup template obsoleti\n\n## ⚙️ Configurazione Tecnica\n\n### Vite Config\n```javascript\n// frontend/vite.config.js\nimport { defineConfig } from 'vite'\nimport vue from '@vitejs/plugin-vue'\n\nexport default defineConfig({\n  plugins: [vue()],\n  build: {\n    outDir: '../backend/static/dist',\n    emptyOutDir: true\n  },\n  server: {\n    port: 3000,\n    proxy: {\n      '/api': 'http://localhost:5000',\n      '/auth': 'http://localhost:5000'\n    }\n  },\n  resolve: {\n    alias: {\n      '@': '/src',\n      '@components': '/src/components',\n      '@views': '/src/views'\n    }\n  }\n})\n```\n\n### Package.json\n```json\n{\n  \"scripts\": {\n    \"dev\": \"vite\",\n    \"build\": \"vite build\",\n    \"test\": \"vitest\",\n    \"lint\": \"eslint src --ext .vue,.js\"\n  },\n  \"dependencies\": {\n    \"vue\": \"^3.4.0\",\n    \"vue-router\": \"^4.2.0\",\n    \"pinia\": \"^2.1.0\",\n    \"axios\": \"^1.6.0\"\n  },\n  \"devDependencies\": {\n    \"@vitejs/plugin-vue\": \"^4.5.0\",\n    \"vite\": \"^5.0.0\",\n    \"vitest\": \"^1.0.0\",\n    \"tailwindcss\": \"^3.4.0\"\n  }\n}\n```\n\n## 🔄 Workflow Development\n\n### Terminal 1: Backend Flask\n```bash\ncd backend\npython main.py\n# → http://localhost:5000\n```\n\n### Terminal 2: Frontend Vue.js\n```bash\ncd frontend\nnpm run dev\n# → http://localhost:3000 (proxy to Flask)\n```\n\n### Flusso Richieste\n```\nBrowser → http://localhost:3000 (Vite Dev Server)\n    ↓ (HMR, Vue DevTools)\nVue.js Components\n    ↓ (/api/* requests)\nProxy → http://localhost:5000 (Flask)\n    ↓ (Database, Business Logic)\nResponse → Vue.js → Browser Update\n```\n\n## 💻 Esempi Migrazione\n\n### Da Template HTML a Vue SFC\n```html\n<!-- PRIMA: templates/projects/view.html (3000+ righe) -->\n<div class=\"project-view\">\n  <!-- HTML complesso con Alpine.js -->\n</div>\n```\n\n```vue\n<!-- DOPO: src/views/projects/ProjectView.vue -->\n<template>\n  <div class=\"project-view\">\n    <ProjectHeader :project=\"project\" />\n    <TabNavigation v-model=\"activeTab\" />\n    <component :is=\"currentComponent\" :project=\"project\" />\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue'\nimport { useProjectsStore } from '@/stores/projects'\n\nconst store = useProjectsStore()\nconst activeTab = ref('overview')\nconst project = computed(() => store.currentProject)\n</script>\n\n<style scoped>\n.project-view {\n  @apply container mx-auto p-6;\n}\n</style>\n```\n\n## ✅ Checklist Migrazione - STATO AGGIORNATO\n\n### Setup ✅ COMPLETATO\n- [x] Inizializza progetto Vue.js con Vite\n- [x] Configura Tailwind CSS + PostCSS\n- [x] Setup proxy development server\n- [x] Configura alias path e build output\n\n### Componenti Core ✅ COMPLETATO\n- [x] Migra layout components (AppLayout, AppSidebar, AppHeader)\n- [x] Sidebar collassabile con navigazione completa\n- [x] Header modulare con breadcrumbs, notifiche, ricerca\n- [x] Migra auth views (Login, Register)\n- [x] Setup stores Pinia principali (auth, tenant)\n- [x] Coerenza stile tra PublicLayout e AppLayout\n\n### Moduli Business 🔄 IN CORSO\n- [x] Dashboard view (✅ COMPLETATA - KPI, grafici, statistiche)\n- [ ] Projects module (🔄 PROSSIMA - placeholder → componenti avanzati)\n- [ ] Personnel module (⏳ PIANIFICATA - placeholder → componenti avanzati)\n- [ ] Tasks module (⏳ PIANIFICATA - vista standalone)\n- [ ] Admin interface (⏳ PIANIFICATA)\n- [x] Integrazione API reali con backend Flask (✅ COMPLETATA)\n\n### Testing & Quality ❌ NON INIZIATO\n- [ ] Setup Vitest per unit tests\n- [ ] Setup Cypress per E2E tests\n- [ ] Configura ESLint + Prettier\n- [ ] Test coverage frontend\n- [ ] Test componenti layout modulari\n\n### Cleanup ⏳ PIANIFICATO\n- [ ] Rimuovi template HTML obsoleti\n- [ ] Rimuovi file .js con template inline\n- [ ] Aggiorna documentazione\n- [ ] Test deployment production\n\n## 📊 Stato Corrente (Maggio 2025)\n\n### ✅ Completato (70%)\n- Architettura Vue.js 3 + Vite funzionante\n- Layout modulare professionale\n- Navigazione completa con 10+ elementi\n- Autenticazione integrata\n- Gestione configurazioni tenant\n- Build system ottimizzato\n\n### 🔄 In Sviluppo (20%)\n- Componenti business (Dashboard, Projects, Personnel)\n- Integrazione API Flask\n- Componenti UI avanzati\n\n### ❌ Da Fare (10%)\n- Testing suite completa\n- Cleanup template legacy\n\n## 🚀 Vantaggi Finali\n\n✅ **Syntax highlighting** completo per HTML/CSS/JS\n✅ **Hot Module Replacement** per sviluppo rapido\n✅ **Vue DevTools** per debugging avanzato\n✅ **Code splitting** automatico per performance\n✅ **TypeScript support** (opzionale)\n✅ **Testing integrato** con Vitest/Cypress\n✅ **Build ottimizzato** per production\n✅ **Architettura scalabile** per crescita futura\n\n## 🔧 Troubleshooting\n\n### Problemi Comuni\n- **CORS errors**: Verificare configurazione proxy Vite\n- **Asset loading**: Controllare path alias in vite.config.js\n- **CSS conflicts**: Usare scoped styles nei componenti\n- **Build errors**: Verificare import paths e dependencies\n\n### Debug Tips\n- Usare Vue DevTools per ispezionare componenti\n- Console browser per errori JavaScript\n- Network tab per verificare API calls\n- Vite dev server logs per errori build\n\n## 📈 Metriche Successo\n\n### Performance\n- Bundle size < 500KB (gzipped)\n- First Contentful Paint < 2s\n- Time to Interactive < 3s\n\n### Developer Experience\n- Hot reload < 100ms\n- Build time < 30s\n- Test execution < 10s\n\n### Code Quality\n- ESLint errors: 0\n- Test coverage > 80%\n- TypeScript errors: 0 (se abilitato)\n\n## 📚 Riferimenti\n\n- [Vue.js 3 Documentation](https://vuejs.org/)\n- [Vite Documentation](https://vitejs.dev/)\n- [Vue Router 4](https://router.vuejs.org/)\n- [Pinia State Management](https://pinia.vuejs.org/)\n- [Vitest Testing](https://vitest.dev/)\n- [Tailwind CSS](https://tailwindcss.com/)\n- [Vue DevTools](https://devtools.vuejs.org/)\n"}