{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/ui/StatusBadge.vue"}, "modifiedCode": "<template>\n  <span \n    class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n    :class=\"badgeClasses\"\n  >\n    <span v-if=\"showDot\" class=\"w-1.5 h-1.5 rounded-full mr-1.5\" :class=\"dotClasses\"></span>\n    {{ label }}\n  </span>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\n\nconst props = defineProps({\n  status: {\n    type: String,\n    required: true\n  },\n  type: {\n    type: String,\n    default: 'project', // project, task, user, etc.\n    validator: (value) => ['project', 'task', 'user', 'generic'].includes(value)\n  },\n  showDot: {\n    type: Boolean,\n    default: false\n  }\n})\n\nconst statusConfig = {\n  project: {\n    planning: { label: 'Pianificazione', classes: 'bg-yellow-100 text-yellow-800', dot: 'bg-yellow-400' },\n    active: { label: 'Attivo', classes: 'bg-green-100 text-green-800', dot: 'bg-green-400' },\n    on_hold: { label: 'In Pausa', classes: 'bg-orange-100 text-orange-800', dot: 'bg-orange-400' },\n    completed: { label: 'Completato', classes: 'bg-blue-100 text-blue-800', dot: 'bg-blue-400' },\n    cancelled: { label: 'Annullato', classes: 'bg-red-100 text-red-800', dot: 'bg-red-400' }\n  },\n  task: {\n    todo: { label: 'Da Fare', classes: 'bg-gray-100 text-gray-800', dot: 'bg-gray-400' },\n    in_progress: { label: 'In Corso', classes: 'bg-blue-100 text-blue-800', dot: 'bg-blue-400' },\n    review: { label: 'In Revisione', classes: 'bg-purple-100 text-purple-800', dot: 'bg-purple-400' },\n    done: { label: 'Completato', classes: 'bg-green-100 text-green-800', dot: 'bg-green-400' },\n    blocked: { label: 'Bloccato', classes: 'bg-red-100 text-red-800', dot: 'bg-red-400' }\n  },\n  user: {\n    active: { label: 'Attivo', classes: 'bg-green-100 text-green-800', dot: 'bg-green-400' },\n    inactive: { label: 'Inattivo', classes: 'bg-gray-100 text-gray-800', dot: 'bg-gray-400' },\n    pending: { label: 'In Attesa', classes: 'bg-yellow-100 text-yellow-800', dot: 'bg-yellow-400' }\n  },\n  generic: {\n    success: { label: 'Successo', classes: 'bg-green-100 text-green-800', dot: 'bg-green-400' },\n    warning: { label: 'Attenzione', classes: 'bg-yellow-100 text-yellow-800', dot: 'bg-yellow-400' },\n    error: { label: 'Errore', classes: 'bg-red-100 text-red-800', dot: 'bg-red-400' },\n    info: { label: 'Info', classes: 'bg-blue-100 text-blue-800', dot: 'bg-blue-400' }\n  }\n}\n\nconst currentConfig = computed(() => {\n  const typeConfig = statusConfig[props.type] || statusConfig.generic\n  return typeConfig[props.status] || {\n    label: props.status,\n    classes: 'bg-gray-100 text-gray-800',\n    dot: 'bg-gray-400'\n  }\n})\n\nconst label = computed(() => currentConfig.value.label)\nconst badgeClasses = computed(() => currentConfig.value.classes)\nconst dotClasses = computed(() => currentConfig.value.dot)\n</script>\n\n<style scoped>\n/* Additional custom styles if needed */\n</style>\n"}