{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectKPI.vue"}, "originalCode": "<template>\n  <div class=\"project-kpi\">\n    <div v-if=\"loading\" class=\"animate-pulse space-y-4\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div v-for=\"i in 4\" :key=\"i\" class=\"bg-gray-200 rounded-lg h-24\"></div>\n      </div>\n      <div class=\"bg-gray-200 rounded-lg h-64\"></div>\n    </div>\n\n    <div v-else-if=\"project\" class=\"space-y-6\">\n      <!-- K<PERSON> Header -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <div class=\"flex items-center justify-between\">\n          <div>\n            <h3 class=\"text-lg font-medium text-gray-900\">KPI Progetto</h3>\n            <p class=\"text-sm text-gray-600\">Dashboard metriche e performance del progetto</p>\n          </div>\n          <button\n            @click=\"refreshKPIs\"\n            :disabled=\"refreshing\"\n            class=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" :class=\"{ 'animate-spin': refreshing }\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n            </svg>\n            Aggiorna\n          </button>\n        </div>\n      </div>\n\n      <!-- KPI Overview Cards -->\n      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <!-- Ore Totali -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Ore Totali</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatHours(kpiData.totalHours) }}</dd>\n                <dd class=\"text-xs text-gray-500\">{{ kpiData.workDays }} giorni lavorati</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <!-- Costi Totali -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Costi Totali</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatCurrency(kpiData.totalCosts) }}</dd>\n                <dd class=\"text-xs\" :class=\"costVarianceClass\">{{ formatCurrency(kpiData.costVariance) }} vs budget</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <!-- Ricavi Potenziali -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Ricavi Potenziali</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatCurrency(kpiData.potentialRevenue) }}</dd>\n                <dd class=\"text-xs text-gray-500\">{{ formatCurrency(kpiData.actualRevenue) }} fatturati</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <!-- Margine Progetto -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Margine</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatPercentage(kpiData.marginPercentage) }}</dd>\n                <dd class=\"text-xs\" :class=\"marginClass\">{{ marginStatus }}</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Performance Charts -->\n      <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <!-- Budget Progress Chart -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <h4 class=\"text-lg font-medium text-gray-900 mb-4\">Andamento Budget</h4>\n          <div class=\"space-y-4\">\n            <div class=\"flex justify-between text-sm\">\n              <span class=\"text-gray-600\">Budget Totale</span>\n              <span class=\"font-medium\">{{ formatCurrency(project.budget || 0) }}</span>\n            </div>\n            <div class=\"w-full bg-gray-200 rounded-full h-3\">\n              <div\n                class=\"bg-blue-600 h-3 rounded-full transition-all duration-300\"\n                :style=\"{ width: budgetUsagePercentage + '%' }\"\n              ></div>\n            </div>\n            <div class=\"flex justify-between text-sm\">\n              <span class=\"text-gray-600\">Utilizzato: {{ formatCurrency(kpiData.totalCosts) }}</span>\n              <span class=\"font-medium\">{{ budgetUsagePercentage }}%</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Time Progress Chart -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <h4 class=\"text-lg font-medium text-gray-900 mb-4\">Andamento Tempo</h4>\n          <div class=\"space-y-4\">\n            <div class=\"flex justify-between text-sm\">\n              <span class=\"text-gray-600\">Ore Stimate</span>\n              <span class=\"font-medium\">{{ formatHours(project.estimated_hours || 0) }}</span>\n            </div>\n            <div class=\"w-full bg-gray-200 rounded-full h-3\">\n              <div\n                class=\"bg-green-600 h-3 rounded-full transition-all duration-300\"\n                :style=\"{ width: timeUsagePercentage + '%' }\"\n              ></div>\n            </div>\n            <div class=\"flex justify-between text-sm\">\n              <span class=\"text-gray-600\">Lavorate: {{ formatHours(kpiData.totalHours) }}</span>\n              <span class=\"font-medium\">{{ timeUsagePercentage }}%</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- KPI Thresholds Configuration -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h4 class=\"text-lg font-medium text-gray-900\">Soglie KPI</h4>\n          <button\n            @click=\"showThresholdModal = true\"\n            class=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\"\n          >\n            Configura\n          </button>\n        </div>\n\n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div class=\"text-center p-4 border rounded-lg\">\n            <div class=\"text-2xl font-bold\" :class=\"budgetThresholdClass\">\n              {{ budgetUsagePercentage }}%\n            </div>\n            <div class=\"text-sm text-gray-600\">Budget Usage</div>\n            <div class=\"text-xs text-gray-500\">Soglia: {{ kpiThresholds.budget }}%</div>\n          </div>\n\n          <div class=\"text-center p-4 border rounded-lg\">\n            <div class=\"text-2xl font-bold\" :class=\"timeThresholdClass\">\n              {{ timeUsagePercentage }}%\n            </div>\n            <div class=\"text-sm text-gray-600\">Time Usage</div>\n            <div class=\"text-xs text-gray-500\">Soglia: {{ kpiThresholds.time }}%</div>\n          </div>\n\n          <div class=\"text-center p-4 border rounded-lg\">\n            <div class=\"text-2xl font-bold\" :class=\"marginThresholdClass\">\n              {{ formatPercentage(kpiData.marginPercentage) }}\n            </div>\n            <div class=\"text-sm text-gray-600\">Margine</div>\n            <div class=\"text-xs text-gray-500\">Soglia: {{ kpiThresholds.margin }}%</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <div v-else class=\"text-center py-8\">\n      <p class=\"text-gray-500\">Progetto non trovato</p>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport api from '@/utils/api'\n\n// Props\nconst props = defineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n\n// Emits\nconst emit = defineEmits(['refresh'])\n\n// Reactive data\nconst refreshing = ref(false)\nconst showThresholdModal = ref(false)\nconst kpiData = ref({\n  totalHours: 0,\n  workDays: 0,\n  totalCosts: 0,\n  costVariance: 0,\n  potentialRevenue: 0,\n  actualRevenue: 0,\n  marginPercentage: 0\n})\n\nconst kpiThresholds = ref({\n  budget: 80,\n  time: 85,\n  margin: 15\n})\n\n// Computed properties\nconst budgetUsagePercentage = computed(() => {\n  if (!props.project?.budget || kpiData.value.totalCosts === 0) return 0\n  return Math.round((kpiData.value.totalCosts / props.project.budget) * 100)\n})\n\nconst timeUsagePercentage = computed(() => {\n  if (!props.project?.estimated_hours || kpiData.value.totalHours === 0) return 0\n  return Math.round((kpiData.value.totalHours / props.project.estimated_hours) * 100)\n})\n\nconst costVarianceClass = computed(() => {\n  const variance = kpiData.value.costVariance\n  if (variance > 0) return 'text-red-600'\n  if (variance < 0) return 'text-green-600'\n  return 'text-gray-600'\n})\n\nconst marginClass = computed(() => {\n  const margin = kpiData.value.marginPercentage\n  if (margin >= kpiThresholds.value.margin) return 'text-green-600'\n  if (margin >= kpiThresholds.value.margin * 0.7) return 'text-yellow-600'\n  return 'text-red-600'\n})\n\nconst marginStatus = computed(() => {\n  const margin = kpiData.value.marginPercentage\n  if (margin >= kpiThresholds.value.margin) return 'Ottimo'\n  if (margin >= kpiThresholds.value.margin * 0.7) return 'Accettabile'\n  return 'Critico'\n})\n\nconst budgetThresholdClass = computed(() => {\n  const usage = budgetUsagePercentage.value\n  if (usage >= kpiThresholds.value.budget) return 'text-red-600'\n  if (usage >= kpiThresholds.value.budget * 0.8) return 'text-yellow-600'\n  return 'text-green-600'\n})\n\nconst timeThresholdClass = computed(() => {\n  const usage = timeUsagePercentage.value\n  if (usage >= kpiThresholds.value.time) return 'text-red-600'\n  if (usage >= kpiThresholds.value.time * 0.8) return 'text-yellow-600'\n  return 'text-green-600'\n})\n\nconst marginThresholdClass = computed(() => {\n  const margin = kpiData.value.marginPercentage\n  if (margin >= kpiThresholds.value.margin) return 'text-green-600'\n  if (margin >= kpiThresholds.value.margin * 0.7) return 'text-yellow-600'\n  return 'text-red-600'\n})\n\n// Methods\nconst formatCurrency = (amount) => {\n  return new Intl.NumberFormat('it-IT', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(amount || 0)\n}\n\nconst formatHours = (hours) => {\n  return `${hours || 0}h`\n}\n\nconst formatPercentage = (percentage) => {\n  return `${(percentage || 0).toFixed(1)}%`\n}\n\nconst loadKPIData = async () => {\n  if (!props.project?.id) return\n\n  try {\n    const response = await api.get(`/api/projects/${props.project.id}/kpi`)\n    kpiData.value = response.data\n  } catch (error) {\n    console.error('Error loading KPI data:', error)\n    // Use fallback calculations\n    calculateFallbackKPIs()\n  }\n}\n\nconst calculateFallbackKPIs = () => {\n  // Calculate basic KPIs from project data\n  const project = props.project\n  if (!project) return\n\n  // Basic calculations (these would normally come from API)\n  kpiData.value = {\n    totalHours: project.total_hours || 0,\n    workDays: Math.ceil((project.total_hours || 0) / 8),\n    totalCosts: (project.total_hours || 0) * 50, // Assuming 50€/hour average\n    costVariance: ((project.total_hours || 0) * 50) - (project.budget || 0),\n    potentialRevenue: project.budget || 0,\n    actualRevenue: project.invoiced_amount || 0,\n    marginPercentage: project.budget ?\n      (((project.budget - ((project.total_hours || 0) * 50)) / project.budget) * 100) : 0\n  }\n}\n\nconst refreshKPIs = async () => {\n  refreshing.value = true\n  try {\n    await loadKPIData()\n    emit('refresh')\n  } catch (error) {\n    console.error('Error refreshing KPIs:', error)\n  } finally {\n    refreshing.value = false\n  }\n}\n\n// Watchers\nwatch(() => props.project, (newProject) => {\n  if (newProject) {\n    loadKPIData()\n  }\n}, { immediate: true })\n\n// Lifecycle\nonMounted(() => {\n  if (props.project) {\n    loadKPIData()\n  }\n})\n</script>", "modifiedCode": "<template>\n  <div class=\"project-kpi\">\n    <div v-if=\"loading\" class=\"animate-pulse space-y-4\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div v-for=\"i in 4\" :key=\"i\" class=\"bg-gray-200 rounded-lg h-24\"></div>\n      </div>\n      <div class=\"bg-gray-200 rounded-lg h-64\"></div>\n    </div>\n\n    <div v-else-if=\"project\" class=\"space-y-6\">\n      <!-- K<PERSON> Header -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <div class=\"flex items-center justify-between\">\n          <div>\n            <h3 class=\"text-lg font-medium text-gray-900\">KPI Progetto</h3>\n            <p class=\"text-sm text-gray-600\">Dashboard metriche e performance del progetto</p>\n          </div>\n          <button\n            @click=\"refreshKPIs\"\n            :disabled=\"refreshing\"\n            class=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" :class=\"{ 'animate-spin': refreshing }\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n            </svg>\n            Aggiorna\n          </button>\n        </div>\n      </div>\n\n      <!-- KPI Overview Cards -->\n      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <!-- Ore Totali -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Ore Totali</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatHours(kpiData.totalHours) }}</dd>\n                <dd class=\"text-xs text-gray-500\">{{ kpiData.workDays }} giorni lavorati</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <!-- Costi Totali -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Costi Totali</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatCurrency(kpiData.totalCosts) }}</dd>\n                <dd class=\"text-xs\" :class=\"costVarianceClass\">{{ formatCurrency(kpiData.costVariance) }} vs budget</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <!-- Ricavi Potenziali -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Ricavi Potenziali</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatCurrency(kpiData.potentialRevenue) }}</dd>\n                <dd class=\"text-xs text-gray-500\">{{ formatCurrency(kpiData.actualRevenue) }} fatturati</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <!-- Margine Progetto -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Margine</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatPercentage(kpiData.marginPercentage) }}</dd>\n                <dd class=\"text-xs\" :class=\"marginClass\">{{ marginStatus }}</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Performance Charts -->\n      <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <!-- Budget Progress Chart -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <h4 class=\"text-lg font-medium text-gray-900 mb-4\">Andamento Budget</h4>\n          <div class=\"space-y-4\">\n            <div class=\"flex justify-between text-sm\">\n              <span class=\"text-gray-600\">Budget Totale</span>\n              <span class=\"font-medium\">{{ formatCurrency(project.budget || 0) }}</span>\n            </div>\n            <div class=\"w-full bg-gray-200 rounded-full h-3\">\n              <div\n                class=\"bg-blue-600 h-3 rounded-full transition-all duration-300\"\n                :style=\"{ width: budgetUsagePercentage + '%' }\"\n              ></div>\n            </div>\n            <div class=\"flex justify-between text-sm\">\n              <span class=\"text-gray-600\">Utilizzato: {{ formatCurrency(kpiData.totalCosts) }}</span>\n              <span class=\"font-medium\">{{ budgetUsagePercentage }}%</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Time Progress Chart -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <h4 class=\"text-lg font-medium text-gray-900 mb-4\">Andamento Tempo</h4>\n          <div class=\"space-y-4\">\n            <div class=\"flex justify-between text-sm\">\n              <span class=\"text-gray-600\">Ore Stimate</span>\n              <span class=\"font-medium\">{{ formatHours(project.estimated_hours || 0) }}</span>\n            </div>\n            <div class=\"w-full bg-gray-200 rounded-full h-3\">\n              <div\n                class=\"bg-green-600 h-3 rounded-full transition-all duration-300\"\n                :style=\"{ width: timeUsagePercentage + '%' }\"\n              ></div>\n            </div>\n            <div class=\"flex justify-between text-sm\">\n              <span class=\"text-gray-600\">Lavorate: {{ formatHours(kpiData.totalHours) }}</span>\n              <span class=\"font-medium\">{{ timeUsagePercentage }}%</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- KPI Thresholds Configuration -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h4 class=\"text-lg font-medium text-gray-900\">Soglie KPI</h4>\n          <button\n            @click=\"showThresholdModal = true\"\n            class=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\"\n          >\n            Configura\n          </button>\n        </div>\n\n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div class=\"text-center p-4 border rounded-lg\">\n            <div class=\"text-2xl font-bold\" :class=\"budgetThresholdClass\">\n              {{ budgetUsagePercentage }}%\n            </div>\n            <div class=\"text-sm text-gray-600\">Budget Usage</div>\n            <div class=\"text-xs text-gray-500\">Soglia: {{ kpiThresholds.budget }}%</div>\n          </div>\n\n          <div class=\"text-center p-4 border rounded-lg\">\n            <div class=\"text-2xl font-bold\" :class=\"timeThresholdClass\">\n              {{ timeUsagePercentage }}%\n            </div>\n            <div class=\"text-sm text-gray-600\">Time Usage</div>\n            <div class=\"text-xs text-gray-500\">Soglia: {{ kpiThresholds.time }}%</div>\n          </div>\n\n          <div class=\"text-center p-4 border rounded-lg\">\n            <div class=\"text-2xl font-bold\" :class=\"marginThresholdClass\">\n              {{ formatPercentage(kpiData.marginPercentage) }}\n            </div>\n            <div class=\"text-sm text-gray-600\">Margine</div>\n            <div class=\"text-xs text-gray-500\">Soglia: {{ kpiThresholds.margin }}%</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <div v-else class=\"text-center py-8\">\n      <p class=\"text-gray-500\">Progetto non trovato</p>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport api from '@/utils/api'\n\n// Props\nconst props = defineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n\n// Emits\nconst emit = defineEmits(['refresh'])\n\n// Reactive data\nconst refreshing = ref(false)\nconst showThresholdModal = ref(false)\nconst kpiData = ref({\n  totalHours: 0,\n  workDays: 0,\n  totalCosts: 0,\n  costVariance: 0,\n  potentialRevenue: 0,\n  actualRevenue: 0,\n  marginPercentage: 0\n})\n\nconst kpiThresholds = ref({\n  budget: 80,\n  time: 85,\n  margin: 15\n})\n\n// Computed properties\nconst budgetUsagePercentage = computed(() => {\n  if (!props.project?.budget || kpiData.value.totalCosts === 0) return 0\n  return Math.round((kpiData.value.totalCosts / props.project.budget) * 100)\n})\n\nconst timeUsagePercentage = computed(() => {\n  if (!props.project?.estimated_hours || kpiData.value.totalHours === 0) return 0\n  return Math.round((kpiData.value.totalHours / props.project.estimated_hours) * 100)\n})\n\nconst costVarianceClass = computed(() => {\n  const variance = kpiData.value.costVariance\n  if (variance > 0) return 'text-red-600'\n  if (variance < 0) return 'text-green-600'\n  return 'text-gray-600'\n})\n\nconst marginClass = computed(() => {\n  const margin = kpiData.value.marginPercentage\n  if (margin >= kpiThresholds.value.margin) return 'text-green-600'\n  if (margin >= kpiThresholds.value.margin * 0.7) return 'text-yellow-600'\n  return 'text-red-600'\n})\n\nconst marginStatus = computed(() => {\n  const margin = kpiData.value.marginPercentage\n  if (margin >= kpiThresholds.value.margin) return 'Ottimo'\n  if (margin >= kpiThresholds.value.margin * 0.7) return 'Accettabile'\n  return 'Critico'\n})\n\nconst budgetThresholdClass = computed(() => {\n  const usage = budgetUsagePercentage.value\n  if (usage >= kpiThresholds.value.budget) return 'text-red-600'\n  if (usage >= kpiThresholds.value.budget * 0.8) return 'text-yellow-600'\n  return 'text-green-600'\n})\n\nconst timeThresholdClass = computed(() => {\n  const usage = timeUsagePercentage.value\n  if (usage >= kpiThresholds.value.time) return 'text-red-600'\n  if (usage >= kpiThresholds.value.time * 0.8) return 'text-yellow-600'\n  return 'text-green-600'\n})\n\nconst marginThresholdClass = computed(() => {\n  const margin = kpiData.value.marginPercentage\n  if (margin >= kpiThresholds.value.margin) return 'text-green-600'\n  if (margin >= kpiThresholds.value.margin * 0.7) return 'text-yellow-600'\n  return 'text-red-600'\n})\n\n// Methods\nconst formatCurrency = (amount) => {\n  return new Intl.NumberFormat('it-IT', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(amount || 0)\n}\n\nconst formatHours = (hours) => {\n  return `${hours || 0}h`\n}\n\nconst formatPercentage = (percentage) => {\n  return `${(percentage || 0).toFixed(1)}%`\n}\n\nconst loadKPIData = async () => {\n  if (!props.project?.id) return\n\n  // API endpoint non esiste ancora, usa sempre calcoli fallback\n  calculateFallbackKPIs()\n}\n\nconst calculateFallbackKPIs = () => {\n  // Calculate basic KPIs from project data\n  const project = props.project\n  if (!project) return\n\n  // Basic calculations (these would normally come from API)\n  kpiData.value = {\n    totalHours: project.total_hours || 0,\n    workDays: Math.ceil((project.total_hours || 0) / 8),\n    totalCosts: (project.total_hours || 0) * 50, // Assuming 50€/hour average\n    costVariance: ((project.total_hours || 0) * 50) - (project.budget || 0),\n    potentialRevenue: project.budget || 0,\n    actualRevenue: project.invoiced_amount || 0,\n    marginPercentage: project.budget ?\n      (((project.budget - ((project.total_hours || 0) * 50)) / project.budget) * 100) : 0\n  }\n}\n\nconst refreshKPIs = async () => {\n  refreshing.value = true\n  try {\n    await loadKPIData()\n    emit('refresh')\n  } catch (error) {\n    console.error('Error refreshing KPIs:', error)\n  } finally {\n    refreshing.value = false\n  }\n}\n\n// Watchers\nwatch(() => props.project, (newProject) => {\n  if (newProject) {\n    loadKPIData()\n  }\n}, { immediate: true })\n\n// Lifecycle\nonMounted(() => {\n  if (props.project) {\n    loadKPIData()\n  }\n})\n</script>"}