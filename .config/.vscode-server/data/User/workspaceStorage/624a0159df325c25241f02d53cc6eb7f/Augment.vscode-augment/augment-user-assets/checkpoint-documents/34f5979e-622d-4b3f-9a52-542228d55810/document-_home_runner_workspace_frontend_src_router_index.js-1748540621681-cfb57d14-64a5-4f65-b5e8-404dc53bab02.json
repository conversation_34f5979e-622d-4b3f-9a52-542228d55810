{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/router/index.js"}, "originalCode": "import { createRouter, createWebHistory } from 'vue-router'\nimport { useAuthStore } from '@/stores/auth'\n\n// Layout components\nimport AppLayout from '@/components/layout/AppLayout.vue'\nimport PublicLayout from '@/components/layout/PublicLayout.vue'\n\n// Public views\nimport Home from '@/views/public/Home.vue'\nimport About from '@/views/public/About.vue'\nimport Contact from '@/views/public/Contact.vue'\nimport Services from '@/views/public/Services.vue'\n\n// Auth views\nimport Login from '@/views/auth/Login.vue'\nimport Register from '@/views/auth/Register.vue'\n\n// Protected views\nimport Dashboard from '@/views/dashboard/Dashboard.vue'\nimport Projects from '@/views/projects/Projects.vue'\nimport Personnel from '@/views/personnel/Personnel.vue'\n\nconst routes = [\n  // Public routes\n  {\n    path: '/',\n    component: PublicLayout,\n    children: [\n      { path: '', name: 'home', component: Home },\n      { path: 'about', name: 'about', component: About },\n      { path: 'contact', name: 'contact', component: Contact },\n      { path: 'services', name: 'services', component: Services }\n    ]\n  },\n\n  // Auth routes\n  {\n    path: '/auth',\n    component: PublicLayout,\n    children: [\n      { path: 'login', name: 'login', component: Login },\n      { path: 'register', name: 'register', component: Register }\n    ]\n  },\n\n  // Protected routes\n  {\n    path: '/app',\n    component: AppLayout,\n    meta: { requiresAuth: true },\n    children: [\n      { path: '', redirect: '/app/dashboard' },\n      { path: 'dashboard', name: 'dashboard', component: Dashboard },\n      { path: 'projects', name: 'projects', component: Projects },\n      { path: 'personnel', name: 'personnel', component: Personnel }\n    ]\n  }\n]\n\nconst router = createRouter({\n  history: createWebHistory(),\n  routes\n})\n\n// Navigation guard per autenticazione\nrouter.beforeEach((to, from, next) => {\n  const authStore = useAuthStore()\n\n  if (to.meta.requiresAuth && !authStore.isAuthenticated) {\n    next('/auth/login')\n  } else {\n    next()\n  }\n})\n\nexport default router", "modifiedCode": "import { createRouter, createWebHistory } from 'vue-router'\nimport { useAuthStore } from '@/stores/auth'\n\n// Layout components\nimport AppLayout from '@/components/layout/AppLayout.vue'\nimport PublicLayout from '@/components/layout/PublicLayout.vue'\n\n// Public views\nimport Home from '@/views/public/Home.vue'\nimport About from '@/views/public/About.vue'\nimport Contact from '@/views/public/Contact.vue'\nimport Services from '@/views/public/Services.vue'\n\n// Auth views\nimport Login from '@/views/auth/Login.vue'\nimport Register from '@/views/auth/Register.vue'\n\n// Protected views\nimport Dashboard from '@/views/dashboard/Dashboard.vue'\nimport Projects from '@/views/projects/Projects.vue'\nimport Personnel from '@/views/personnel/Personnel.vue'\n\nconst routes = [\n  // Public routes\n  {\n    path: '/',\n    component: PublicLayout,\n    children: [\n      { path: '', name: 'home', component: Home },\n      { path: 'about', name: 'about', component: About },\n      { path: 'contact', name: 'contact', component: Contact },\n      { path: 'services', name: 'services', component: Services }\n    ]\n  },\n\n  // Auth routes\n  {\n    path: '/auth',\n    component: PublicLayout,\n    children: [\n      { path: 'login', name: 'login', component: Login },\n      { path: 'register', name: 'register', component: Register }\n    ]\n  },\n\n  // Protected routes\n  {\n    path: '/app',\n    component: AppLayout,\n    meta: { requiresAuth: true },\n    children: [\n      { path: '', redirect: '/app/dashboard' },\n      { path: 'dashboard', name: 'dashboard', component: Dashboard },\n      { path: 'projects', name: 'projects', component: Projects },\n      { path: 'personnel', name: 'personnel', component: Personnel }\n    ]\n  }\n]\n\nconst router = createRouter({\n  history: createWebHistory(),\n  routes\n})\n\n// Navigation guard per autenticazione\nrouter.beforeEach(async (to, from, next) => {\n  const authStore = useAuthStore()\n\n  if (to.meta.requiresAuth) {\n    // Controlla l'autenticazione se non è già stata verificata\n    if (!authStore.sessionChecked) {\n      await authStore.initializeAuth()\n    }\n\n    if (!authStore.isAuthenticated) {\n      next('/auth/login')\n      return\n    }\n  }\n\n  next()\n})\n\nexport default router"}