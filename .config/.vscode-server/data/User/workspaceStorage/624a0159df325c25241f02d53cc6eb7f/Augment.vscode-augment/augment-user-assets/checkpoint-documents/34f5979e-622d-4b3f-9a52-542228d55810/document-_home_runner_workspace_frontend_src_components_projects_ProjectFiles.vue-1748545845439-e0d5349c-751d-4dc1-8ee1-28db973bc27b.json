{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/projects/ProjectFiles.vue"}, "modifiedCode": "<template>\n  <div class=\"project-files\">\n    <div class=\"bg-white shadow rounded-lg p-6\">\n      <h3 class=\"text-lg font-medium text-gray-900 mb-6\">File del Progetto</h3>\n      \n      <div class=\"text-center py-8\">\n        <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\" />\n        </svg>\n        <h3 class=\"mt-2 text-sm font-medium text-gray-900\">Gestione File in sviluppo</h3>\n        <p class=\"mt-1 text-sm text-gray-500\">Upload e gestione documenti in arrivo...</p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\ndefineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n</script>\n"}