{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectKPI.vue"}, "originalCode": "iniziam<template>\n  <div class=\"project-kpi\">\n    <div class=\"bg-white shadow rounded-lg p-6\">\n      <h3 class=\"text-lg font-medium text-gray-900 mb-6\">KPI del Progetto</h3>\n\n      <div class=\"text-center py-8\">\n        <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n        </svg>\n        <h3 class=\"mt-2 text-sm font-medium text-gray-900\">KPI e Metriche in sviluppo</h3>\n        <p class=\"mt-1 text-sm text-gray-500\">Dashboard KPI progetto in arrivo...</p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\ndefineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n</script>\n", "modifiedCode": "<template>\n  <div class=\"project-kpi\">\n    <div v-if=\"loading\" class=\"animate-pulse space-y-4\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div v-for=\"i in 4\" :key=\"i\" class=\"bg-gray-200 rounded-lg h-24\"></div>\n      </div>\n      <div class=\"bg-gray-200 rounded-lg h-64\"></div>\n    </div>\n\n    <div v-else-if=\"project\" class=\"space-y-6\">\n      <!-- K<PERSON> Header -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <div class=\"flex items-center justify-between\">\n          <div>\n            <h3 class=\"text-lg font-medium text-gray-900\">KPI Progetto</h3>\n            <p class=\"text-sm text-gray-600\">Dashboard metriche e performance del progetto</p>\n          </div>\n          <button\n            @click=\"refreshKPIs\"\n            :disabled=\"refreshing\"\n            class=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" :class=\"{ 'animate-spin': refreshing }\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n            </svg>\n            Aggiorna\n          </button>\n        </div>\n      </div>\n\n      <!-- KPI Overview Cards -->\n      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <!-- Ore Totali -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Ore Totali</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatHours(kpiData.totalHours) }}</dd>\n                <dd class=\"text-xs text-gray-500\">{{ kpiData.workDays }} giorni lavorati</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <!-- Costi Totali -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Costi Totali</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatCurrency(kpiData.totalCosts) }}</dd>\n                <dd class=\"text-xs\" :class=\"costVarianceClass\">{{ formatCurrency(kpiData.costVariance) }} vs budget</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <!-- Ricavi Potenziali -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Ricavi Potenziali</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatCurrency(kpiData.potentialRevenue) }}</dd>\n                <dd class=\"text-xs text-gray-500\">{{ formatCurrency(kpiData.actualRevenue) }} fatturati</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n\n        <!-- Margine Progetto -->\n        <div class=\"bg-white shadow rounded-lg p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-8 w-8 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 truncate\">Margine</dt>\n                <dd class=\"text-lg font-medium text-gray-900\">{{ formatPercentage(kpiData.marginPercentage) }}</dd>\n                <dd class=\"text-xs\" :class=\"marginClass\">{{ marginStatus }}</dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n"}