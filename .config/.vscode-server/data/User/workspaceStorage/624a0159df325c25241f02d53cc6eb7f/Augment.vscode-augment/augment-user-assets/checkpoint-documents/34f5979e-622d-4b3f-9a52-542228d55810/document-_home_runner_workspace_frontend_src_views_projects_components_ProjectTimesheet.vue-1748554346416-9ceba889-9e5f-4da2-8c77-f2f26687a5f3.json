{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTimesheet.vue"}, "originalCode": "<template>\n  <div class=\"project-timesheet\">\n    <div class=\"bg-white shadow rounded-lg p-6\">\n      <h3 class=\"text-lg font-medium text-gray-900 mb-6\">Timesheet del Progetto</h3>\n\n      <div class=\"text-center py-8\">\n        <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n        </svg>\n        <h3 class=\"mt-2 text-sm font-medium text-gray-900\">Timesheet in sviluppo</h3>\n        <p class=\"mt-1 text-sm text-gray-500\">Gestione ore lavorate in arrivo...</p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\ndefineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n</script>\n", "modifiedCode": "<template>\n  <div class=\"project-timesheet\">\n    <div class=\"bg-white shadow rounded-lg p-6\">\n      <h3 class=\"text-lg font-medium text-gray-900 mb-6\">Timesheet del Progetto</h3>\n\n      <!-- Timesheet Summary -->\n      <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\n        <div class=\"bg-blue-50 p-4 rounded-lg\">\n          <div class=\"text-sm text-blue-600\">Ore Totali Mese</div>\n          <div class=\"text-2xl font-bold text-blue-900\">{{ totalHours }}h</div>\n        </div>\n        <div class=\"bg-green-50 p-4 rounded-lg\">\n          <div class=\"text-sm text-green-600\">Membri Attivi</div>\n          <div class=\"text-2xl font-bold text-green-900\">{{ teamMembers.length }}</div>\n        </div>\n        <div class=\"bg-purple-50 p-4 rounded-lg\">\n          <div class=\"text-sm text-purple-600\">Media Giornaliera</div>\n          <div class=\"text-2xl font-bold text-purple-900\">{{ averageDaily }}h</div>\n        </div>\n      </div>\n\n      <!-- Timesheet Entries -->\n      <div class=\"space-y-4\">\n        <div v-for=\"entry in timesheetEntries\" :key=\"entry.id\" class=\"border border-gray-200 rounded-lg p-4\">\n          <div class=\"flex items-center justify-between\">\n            <div class=\"flex items-center space-x-4\">\n              <div class=\"flex-shrink-0\">\n                <img v-if=\"entry.user.profile_image\" :src=\"entry.user.profile_image\" :alt=\"entry.user.full_name\" class=\"h-8 w-8 rounded-full\">\n                <div v-else class=\"h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center\">\n                  <span class=\"text-xs font-medium text-gray-600\">{{ getInitials(entry.user.full_name) }}</span>\n                </div>\n              </div>\n              <div>\n                <h4 class=\"text-sm font-medium text-gray-900\">{{ entry.user.full_name }}</h4>\n                <p class=\"text-xs text-gray-500\">{{ formatDate(entry.date) }}</p>\n              </div>\n            </div>\n            <div class=\"flex items-center space-x-4\">\n              <div class=\"text-right\">\n                <div class=\"text-sm font-medium text-gray-900\">{{ entry.hours }}h</div>\n                <div class=\"text-xs text-gray-500\">{{ entry.task?.name || 'Generale' }}</div>\n              </div>\n            </div>\n          </div>\n          <div v-if=\"entry.description\" class=\"mt-2 text-sm text-gray-600\">\n            {{ entry.description }}\n          </div>\n        </div>\n        <div v-if=\"timesheetEntries.length === 0\" class=\"text-center py-8\">\n          <p class=\"text-gray-500\">Nessuna registrazione timesheet per questo progetto</p>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\ndefineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n</script>\n"}