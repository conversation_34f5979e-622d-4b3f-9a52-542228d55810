{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}, "originalCode": "<template>\n  <div class=\"p-6\">\n    <h1 class=\"text-3xl font-bold text-red-500\">TEST PROGETTI - SE VEDI QUESTO FUNZIONA!</h1>\n    <p class=\"text-lg text-blue-500\">Componente Projects.vue caricato correttamente</p>\n\n    <!-- Header originale -->\n    <div class=\"mb-6 mt-6\">\n      <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Progetti</h1>\n          <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            Gestisci e monitora tutti i progetti aziendali\n          </p>\n        </div>\n        <div class=\"mt-4 sm:mt-0\">\n          <button\n            @click=\"createProject\"\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n          >\n            <svg class=\"h-4 w-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 4v16m8-8H4\" />\n            </svg>\n            Nuovo Progetto\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Filtri e Ricerca -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Stato</label>\n          <select\n            v-model=\"filters.status\"\n            @change=\"applyFilters\"\n            class=\"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500\"\n          >\n            <option value=\"\">Tutti gli stati</option>\n            <option value=\"planning\">Pianificazione</option>\n            <option value=\"active\">Attivo</option>\n            <option value=\"completed\">Completato</option>\n            <option value=\"on-hold\">In Pausa</option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Cliente</label>\n          <select\n            v-model=\"filters.client\"\n            @change=\"applyFilters\"\n            class=\"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500\"\n          >\n            <option value=\"\">Tutti i clienti</option>\n            <option v-for=\"client in clients\" :key=\"client.id\" :value=\"client.id\">\n              {{ client.name }}\n            </option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Ricerca</label>\n          <input\n            v-model=\"searchQuery\"\n            @input=\"search\"\n            type=\"text\"\n            placeholder=\"Cerca progetti...\"\n            class=\"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500\"\n          >\n        </div>\n        <div class=\"flex items-end\">\n          <button\n            @click=\"resetFilters\"\n            class=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600\"\n          >\n            Reset Filtri\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Projects List -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n          Progetti ({{ filteredProjects.length }})\n        </h3>\n      </div>\n\n      <div v-if=\"isLoading\" class=\"p-6 text-center\">\n        <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto\"></div>\n        <p class=\"mt-2 text-gray-600 dark:text-gray-400\">Caricamento progetti...</p>\n      </div>\n\n      <div v-else-if=\"filteredProjects.length === 0\" class=\"p-6 text-center\">\n        <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n        </svg>\n        <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun progetto</h3>\n        <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">Inizia creando il tuo primo progetto.</p>\n      </div>\n\n      <div v-else class=\"divide-y divide-gray-200 dark:divide-gray-700\">\n        <div\n          v-for=\"project in filteredProjects\"\n          :key=\"project.id\"\n          class=\"p-6 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer\"\n          @click=\"viewProject(project.id)\"\n        >\n          <div class=\"flex items-center justify-between\">\n            <div class=\"flex-1\">\n              <div class=\"flex items-center\">\n                <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  {{ project.name }}\n                </h4>\n                <span\n                  :class=\"getStatusClass(project.status)\"\n                  class=\"ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                >\n                  {{ getStatusLabel(project.status) }}\n                </span>\n              </div>\n              <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n                {{ project.description }}\n              </p>\n              <div class=\"mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400\">\n                <span>Cliente: {{ project.client }}</span>\n                <span class=\"mx-2\">•</span>\n                <span>Scadenza: {{ formatDate(project.deadline) }}</span>\n                <span class=\"mx-2\">•</span>\n                <span>Budget: {{ formatCurrency(project.budget) }}</span>\n              </div>\n            </div>\n            <div class=\"ml-4 flex items-center space-x-2\">\n              <div class=\"text-right\">\n                <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ project.progress }}%\n                </div>\n                <div class=\"w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1\">\n                  <div\n                    class=\"bg-primary-600 h-2 rounded-full\"\n                    :style=\"{ width: project.progress + '%' }\"\n                  ></div>\n                </div>\n              </div>\n              <svg class=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\" />\n              </svg>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Create Project Modal -->\n    <div v-if=\"showCreateModal\" class=\"fixed inset-0 z-50 overflow-y-auto\">\n      <div class=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\n        <div class=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" @click=\"showCreateModal = false\"></div>\n\n        <div class=\"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\">\n          <div class=\"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\n            <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Nuovo Progetto</h3>\n            <p class=\"text-gray-600 dark:text-gray-400\">Funzionalità in fase di sviluppo...</p>\n          </div>\n          <div class=\"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\">\n            <button\n              @click=\"showCreateModal = false\"\n              class=\"w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 sm:ml-3 sm:w-auto sm:text-sm\"\n            >\n              Chiudi\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\n\nconst router = useRouter()\n\n// Reactive data\nconst isLoading = ref(true)\nconst projects = ref([])\nconst clients = ref([])\nconst searchQuery = ref('')\nconst filters = ref({\n  status: '',\n  client: ''\n})\n\n// Mock data per ora\nconst mockProjects = [\n  {\n    id: 1,\n    name: 'Progetto Alpha',\n    description: 'Sviluppo piattaforma web per gestione clienti',\n    client: 'Acme Corp',\n    client_id: 1,\n    status: 'active',\n    progress: 75,\n    deadline: '2024-03-15',\n    budget: 50000,\n    team_members: [\n      { id: 1, full_name: 'Mario Rossi', profile_image: null }\n    ]\n  },\n  {\n    id: 2,\n    name: 'Progetto Beta',\n    description: 'Migrazione sistema legacy verso cloud',\n    client: 'Tech Solutions',\n    client_id: 2,\n    status: 'planning',\n    progress: 25,\n    deadline: '2024-04-20',\n    budget: 75000,\n    team_members: [\n      { id: 2, full_name: 'Laura Bianchi', profile_image: null }\n    ]\n  }\n]\n\nconst mockClients = [\n  { id: 1, name: 'Acme Corp' },\n  { id: 2, name: 'Tech Solutions' }\n]\n\n// Computed\nconst filteredProjects = computed(() => {\n  let filtered = projects.value\n\n  if (filters.value.status) {\n    filtered = filtered.filter(p => p.status === filters.value.status)\n  }\n\n  if (filters.value.client) {\n    filtered = filtered.filter(p => p.client_id == filters.value.client)\n  }\n\n  if (searchQuery.value) {\n    const search = searchQuery.value.toLowerCase()\n    filtered = filtered.filter(p =>\n      p.name.toLowerCase().includes(search) ||\n      p.description.toLowerCase().includes(search) ||\n      p.client.toLowerCase().includes(search)\n    )\n  }\n\n  return filtered\n})\n\n// Methods\nconst loadProjects = async () => {\n  isLoading.value = true\n  try {\n    // TODO: Sostituire con chiamata API reale\n    await new Promise(resolve => setTimeout(resolve, 500))\n    projects.value = mockProjects\n    clients.value = mockClients\n  } catch (error) {\n    console.error('Error loading projects:', error)\n  } finally {\n    isLoading.value = false\n  }\n}\n\nconst search = () => {\n  // La ricerca è reattiva tramite computed\n}\n\nconst applyFilters = () => {\n  // I filtri sono reattivi tramite computed\n}\n\nconst resetFilters = () => {\n  filters.value = {\n    status: '',\n    client: ''\n  }\n  searchQuery.value = ''\n}\n\nconst createProject = () => {\n  router.push('/app/projects/create')\n}\n\nconst viewProject = (projectId) => {\n  router.push(`/app/projects/${projectId}`)\n}\n\nconst getStatusClass = (status) => {\n  const classes = {\n    planning: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',\n    active: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',\n    completed: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',\n    'on-hold': 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'\n  }\n  return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'\n}\n\nconst getStatusLabel = (status) => {\n  const labels = {\n    planning: 'Pianificazione',\n    active: 'Attivo',\n    completed: 'Completato',\n    'on-hold': 'In Pausa'\n  }\n  return labels[status] || status\n}\n\nconst formatDate = (dateString) => {\n  return new Date(dateString).toLocaleDateString('it-IT')\n}\n\nconst formatCurrency = (amount) => {\n  return new Intl.NumberFormat('it-IT', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(amount)\n}\n\n// Lifecycle\nonMounted(() => {\n  loadProjects()\n})\n</script>", "modifiedCode": "<template>\n  <div class=\"p-6\">\n    <h1 class=\"text-3xl font-bold text-red-500\">PROGETTI FUNZIONA!</h1>\n\n    <!-- Header originale -->\n    <div class=\"mb-6 mt-6\">\n      <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\"><PERSON><PERSON><PERSON></h1>\n          <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            Gestisci e monitora tutti i progetti aziendali\n          </p>\n        </div>\n        <div class=\"mt-4 sm:mt-0\">\n          <button\n            @click=\"createProject\"\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n          >\n            <svg class=\"h-4 w-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 4v16m8-8H4\" />\n            </svg>\n            Nuovo Progetto\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Filtri e Ricerca -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Stato</label>\n          <select\n            v-model=\"filters.status\"\n            @change=\"applyFilters\"\n            class=\"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500\"\n          >\n            <option value=\"\">Tutti gli stati</option>\n            <option value=\"planning\">Pianificazione</option>\n            <option value=\"active\">Attivo</option>\n            <option value=\"completed\">Completato</option>\n            <option value=\"on-hold\">In Pausa</option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Cliente</label>\n          <select\n            v-model=\"filters.client\"\n            @change=\"applyFilters\"\n            class=\"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500\"\n          >\n            <option value=\"\">Tutti i clienti</option>\n            <option v-for=\"client in clients\" :key=\"client.id\" :value=\"client.id\">\n              {{ client.name }}\n            </option>\n          </select>\n        </div>\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Ricerca</label>\n          <input\n            v-model=\"searchQuery\"\n            @input=\"search\"\n            type=\"text\"\n            placeholder=\"Cerca progetti...\"\n            class=\"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500\"\n          >\n        </div>\n        <div class=\"flex items-end\">\n          <button\n            @click=\"resetFilters\"\n            class=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600\"\n          >\n            Reset Filtri\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Projects List -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n          Progetti ({{ filteredProjects.length }})\n        </h3>\n      </div>\n\n      <div v-if=\"isLoading\" class=\"p-6 text-center\">\n        <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto\"></div>\n        <p class=\"mt-2 text-gray-600 dark:text-gray-400\">Caricamento progetti...</p>\n      </div>\n\n      <div v-else-if=\"filteredProjects.length === 0\" class=\"p-6 text-center\">\n        <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n        </svg>\n        <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun progetto</h3>\n        <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">Inizia creando il tuo primo progetto.</p>\n      </div>\n\n      <div v-else class=\"divide-y divide-gray-200 dark:divide-gray-700\">\n        <div\n          v-for=\"project in filteredProjects\"\n          :key=\"project.id\"\n          class=\"p-6 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer\"\n          @click=\"viewProject(project.id)\"\n        >\n          <div class=\"flex items-center justify-between\">\n            <div class=\"flex-1\">\n              <div class=\"flex items-center\">\n                <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  {{ project.name }}\n                </h4>\n                <span\n                  :class=\"getStatusClass(project.status)\"\n                  class=\"ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                >\n                  {{ getStatusLabel(project.status) }}\n                </span>\n              </div>\n              <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n                {{ project.description }}\n              </p>\n              <div class=\"mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400\">\n                <span>Cliente: {{ project.client }}</span>\n                <span class=\"mx-2\">•</span>\n                <span>Scadenza: {{ formatDate(project.deadline) }}</span>\n                <span class=\"mx-2\">•</span>\n                <span>Budget: {{ formatCurrency(project.budget) }}</span>\n              </div>\n            </div>\n            <div class=\"ml-4 flex items-center space-x-2\">\n              <div class=\"text-right\">\n                <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ project.progress }}%\n                </div>\n                <div class=\"w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1\">\n                  <div\n                    class=\"bg-primary-600 h-2 rounded-full\"\n                    :style=\"{ width: project.progress + '%' }\"\n                  ></div>\n                </div>\n              </div>\n              <svg class=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\" />\n              </svg>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Create Project Modal -->\n    <div v-if=\"showCreateModal\" class=\"fixed inset-0 z-50 overflow-y-auto\">\n      <div class=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\n        <div class=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" @click=\"showCreateModal = false\"></div>\n\n        <div class=\"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\">\n          <div class=\"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\n            <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Nuovo Progetto</h3>\n            <p class=\"text-gray-600 dark:text-gray-400\">Funzionalità in fase di sviluppo...</p>\n          </div>\n          <div class=\"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\">\n            <button\n              @click=\"showCreateModal = false\"\n              class=\"w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 sm:ml-3 sm:w-auto sm:text-sm\"\n            >\n              Chiudi\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\n\nconst router = useRouter()\n\n// Reactive data\nconst isLoading = ref(true)\nconst projects = ref([])\nconst clients = ref([])\nconst searchQuery = ref('')\nconst filters = ref({\n  status: '',\n  client: ''\n})\n\n// Mock data per ora\nconst mockProjects = [\n  {\n    id: 1,\n    name: 'Progetto Alpha',\n    description: 'Sviluppo piattaforma web per gestione clienti',\n    client: 'Acme Corp',\n    client_id: 1,\n    status: 'active',\n    progress: 75,\n    deadline: '2024-03-15',\n    budget: 50000,\n    team_members: [\n      { id: 1, full_name: 'Mario Rossi', profile_image: null }\n    ]\n  },\n  {\n    id: 2,\n    name: 'Progetto Beta',\n    description: 'Migrazione sistema legacy verso cloud',\n    client: 'Tech Solutions',\n    client_id: 2,\n    status: 'planning',\n    progress: 25,\n    deadline: '2024-04-20',\n    budget: 75000,\n    team_members: [\n      { id: 2, full_name: 'Laura Bianchi', profile_image: null }\n    ]\n  }\n]\n\nconst mockClients = [\n  { id: 1, name: 'Acme Corp' },\n  { id: 2, name: 'Tech Solutions' }\n]\n\n// Computed\nconst filteredProjects = computed(() => {\n  let filtered = projects.value\n\n  if (filters.value.status) {\n    filtered = filtered.filter(p => p.status === filters.value.status)\n  }\n\n  if (filters.value.client) {\n    filtered = filtered.filter(p => p.client_id == filters.value.client)\n  }\n\n  if (searchQuery.value) {\n    const search = searchQuery.value.toLowerCase()\n    filtered = filtered.filter(p =>\n      p.name.toLowerCase().includes(search) ||\n      p.description.toLowerCase().includes(search) ||\n      p.client.toLowerCase().includes(search)\n    )\n  }\n\n  return filtered\n})\n\n// Methods\nconst loadProjects = async () => {\n  isLoading.value = true\n  try {\n    // TODO: Sostituire con chiamata API reale\n    await new Promise(resolve => setTimeout(resolve, 500))\n    projects.value = mockProjects\n    clients.value = mockClients\n  } catch (error) {\n    console.error('Error loading projects:', error)\n  } finally {\n    isLoading.value = false\n  }\n}\n\nconst search = () => {\n  // La ricerca è reattiva tramite computed\n}\n\nconst applyFilters = () => {\n  // I filtri sono reattivi tramite computed\n}\n\nconst resetFilters = () => {\n  filters.value = {\n    status: '',\n    client: ''\n  }\n  searchQuery.value = ''\n}\n\nconst createProject = () => {\n  router.push('/app/projects/create')\n}\n\nconst viewProject = (projectId) => {\n  router.push(`/app/projects/${projectId}`)\n}\n\nconst getStatusClass = (status) => {\n  const classes = {\n    planning: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',\n    active: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',\n    completed: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',\n    'on-hold': 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'\n  }\n  return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'\n}\n\nconst getStatusLabel = (status) => {\n  const labels = {\n    planning: 'Pianificazione',\n    active: 'Attivo',\n    completed: 'Completato',\n    'on-hold': 'In Pausa'\n  }\n  return labels[status] || status\n}\n\nconst formatDate = (dateString) => {\n  return new Date(dateString).toLocaleDateString('it-IT')\n}\n\nconst formatCurrency = (amount) => {\n  return new Intl.NumberFormat('it-IT', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(amount)\n}\n\n// Lifecycle\nonMounted(() => {\n  loadProjects()\n})\n</script>"}