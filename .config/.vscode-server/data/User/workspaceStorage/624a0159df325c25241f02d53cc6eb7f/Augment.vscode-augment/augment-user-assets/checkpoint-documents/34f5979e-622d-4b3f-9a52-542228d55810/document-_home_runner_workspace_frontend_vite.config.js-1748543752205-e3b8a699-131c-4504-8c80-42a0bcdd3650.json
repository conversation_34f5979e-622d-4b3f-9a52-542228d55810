{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/vite.config.js"}, "originalCode": "import { defineConfig } from 'vite'\nimport vue from '@vitejs/plugin-vue'\nimport { fileURLToPath, URL } from 'node:url'\nimport fs from 'fs'\nimport path from 'path'\n\n// Plugin per generare automaticamente il template Flask\nfunction generateFlaskTemplate() {\n  return {\n    name: 'generate-flask-template',\n    writeBundle(options, bundle) {\n      const assets = Object.keys(bundle).filter(key =>\n        key.endsWith('.js') || key.endsWith('.css')\n      )\n\n      const jsFile = assets.find(asset => asset.startsWith('index-') && asset.endsWith('.js'))\n      const cssFile = assets.find(asset => asset.startsWith('index-') && asset.endsWith('.css'))\n      const vendorFile = assets.find(asset => asset.startsWith('vendor-') && asset.endsWith('.js'))\n\n      const templateContent = `<!DOCTYPE html>\n<html lang=\"it\">\n  <head>\n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <title>DatPortal - Enterprise Intranet</title>\n    <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n    <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap\" rel=\"stylesheet\">\n\n    <!-- Favicon -->\n    <link rel=\"icon\" type=\"image/x-icon\" href=\"{{ url_for('static', filename='favicon.ico') }}\">\n\n    <!-- Meta tags for SEO -->\n    <meta name=\"description\" content=\"DatPortal - Sistema di gestione progetti, task e risorse\">\n    <meta name=\"keywords\" content=\"progetti, task, gestione, risorse, KPI, dashboard\">\n    <meta name=\"author\" content=\"DatVinci\">\n\n    <!-- Open Graph meta tags -->\n    <meta property=\"og:title\" content=\"DatPortal\">\n    <meta property=\"og:description\" content=\"Sistema di gestione progetti, task e risorse\">\n    <meta property=\"og:type\" content=\"website\">\n\n    <!-- CSRF Token for API requests -->\n    <meta name=\"csrf-token\" content=\"{{ csrf_token() }}\">\n\n    ${jsFile ? `<script type=\"module\" crossorigin src=\"{{ url_for('static', filename='dist/assets/${jsFile}') }}\"></script>` : ''}\n    ${vendorFile ? `<link rel=\"modulepreload\" crossorigin href=\"{{ url_for('static', filename='dist/assets/${vendorFile}') }}\">` : ''}\n    ${cssFile ? `<link rel=\"stylesheet\" crossorigin href=\"{{ url_for('static', filename='dist/assets/${cssFile}') }}\">` : ''}\n  </head>\n  <body>\n    <div id=\"app\"></div>\n\n    <!-- Global Configuration for Vue.js -->\n    <script>\n      // Global app configuration\n      window.APP_CONFIG = {\n        apiUrl: '/api',\n        baseUrl: '{{ request.url_root }}',\n        csrfToken: '{{ csrf_token() }}',\n        user: {{ current_user.to_dict()|tojson if current_user.is_authenticated else 'null' }},\n        isAuthenticated: {{ 'true' if current_user.is_authenticated else 'false' }},\n        version: '1.0.0',\n        environment: '{{ config.ENV }}',\n        debug: {{ 'true' if config.DEBUG else 'false' }}\n      };\n\n      // La configurazione tenant verrà caricata dal frontend Vue.js\n\n      // Global error handler\n      window.addEventListener('error', function(event) {\n        console.error('Global error:', event.error);\n      });\n\n      // Global unhandled promise rejection handler\n      window.addEventListener('unhandledrejection', function(event) {\n        console.error('Unhandled promise rejection:', event.reason);\n      });\n    </script>\n  </body>\n</html>`\n\n      // Scrivi il template aggiornato\n      const templatePath = path.resolve('../backend/templates/vue_app.html')\n      fs.writeFileSync(templatePath, templateContent)\n\n      console.log('✅ Template Flask aggiornato automaticamente con i nuovi asset paths')\n    }\n  }\n}\n\nexport default defineConfig({\n  plugins: [vue(), generateFlaskTemplate()],\n  build: {\n    outDir: '../backend/static/dist',\n    emptyOutDir: true,\n    rollupOptions: {\n      output: {\n        entryFileNames: 'assets/app.js',\n        chunkFileNames: 'assets/[name].js',\n        assetFileNames: 'assets/[name].[ext]',\n        manualChunks: {\n          vendor: ['vue', 'vue-router', 'pinia', 'axios', 'chart.js']\n        }\n      }\n    }\n  },\n  server: {\n    port: 3000,\n    proxy: {\n      '/api': {\n        target: 'http://localhost:5000',\n        changeOrigin: true\n      },\n      '/auth': {\n        target: 'http://localhost:5000',\n        changeOrigin: true\n      },\n      '/admin': {\n        target: 'http://localhost:5000',\n        changeOrigin: true\n      },\n      '/dashboard': {\n        target: 'http://localhost:5000',\n        changeOrigin: true\n      }\n    }\n  },\n  resolve: {\n    alias: {\n      '@': fileURLToPath(new URL('./src', import.meta.url)),\n      '@components': fileURLToPath(new URL('./src/components', import.meta.url)),\n      '@views': fileURLToPath(new URL('./src/views', import.meta.url)),\n      '@stores': fileURLToPath(new URL('./src/stores', import.meta.url)),\n      '@utils': fileURLToPath(new URL('./src/utils', import.meta.url))\n    }\n  }\n})", "modifiedCode": "import { defineConfig } from 'vite'\nimport vue from '@vitejs/plugin-vue'\nimport { fileURLToPath, URL } from 'node:url'\nimport fs from 'fs'\nimport path from 'path'\n\n// Plugin per generare automaticamente il template Flask\nfunction generateFlaskTemplate() {\n  return {\n    name: 'generate-flask-template',\n    writeBundle(options, bundle) {\n      const assets = Object.keys(bundle).filter(key =>\n        key.endsWith('.js') || key.endsWith('.css')\n      )\n\n      const jsFile = assets.find(asset => asset === 'app.js')\n      const cssFile = assets.find(asset => asset === 'index.css')\n      const vendorFile = assets.find(asset => asset === 'vendor.js')\n\n      const templateContent = `<!DOCTYPE html>\n<html lang=\"it\">\n  <head>\n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <title>DatPortal - Enterprise Intranet</title>\n    <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n    <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap\" rel=\"stylesheet\">\n\n    <!-- Favicon -->\n    <link rel=\"icon\" type=\"image/x-icon\" href=\"{{ url_for('static', filename='favicon.ico') }}\">\n\n    <!-- Meta tags for SEO -->\n    <meta name=\"description\" content=\"DatPortal - Sistema di gestione progetti, task e risorse\">\n    <meta name=\"keywords\" content=\"progetti, task, gestione, risorse, KPI, dashboard\">\n    <meta name=\"author\" content=\"DatVinci\">\n\n    <!-- Open Graph meta tags -->\n    <meta property=\"og:title\" content=\"DatPortal\">\n    <meta property=\"og:description\" content=\"Sistema di gestione progetti, task e risorse\">\n    <meta property=\"og:type\" content=\"website\">\n\n    <!-- CSRF Token for API requests -->\n    <meta name=\"csrf-token\" content=\"{{ csrf_token() }}\">\n\n    ${jsFile ? `<script type=\"module\" crossorigin src=\"{{ url_for('static', filename='dist/assets/${jsFile}') }}\"></script>` : ''}\n    ${vendorFile ? `<link rel=\"modulepreload\" crossorigin href=\"{{ url_for('static', filename='dist/assets/${vendorFile}') }}\">` : ''}\n    ${cssFile ? `<link rel=\"stylesheet\" crossorigin href=\"{{ url_for('static', filename='dist/assets/${cssFile}') }}\">` : ''}\n  </head>\n  <body>\n    <div id=\"app\"></div>\n\n    <!-- Global Configuration for Vue.js -->\n    <script>\n      // Global app configuration\n      window.APP_CONFIG = {\n        apiUrl: '/api',\n        baseUrl: '{{ request.url_root }}',\n        csrfToken: '{{ csrf_token() }}',\n        user: {{ current_user.to_dict()|tojson if current_user.is_authenticated else 'null' }},\n        isAuthenticated: {{ 'true' if current_user.is_authenticated else 'false' }},\n        version: '1.0.0',\n        environment: '{{ config.ENV }}',\n        debug: {{ 'true' if config.DEBUG else 'false' }}\n      };\n\n      // La configurazione tenant verrà caricata dal frontend Vue.js\n\n      // Global error handler\n      window.addEventListener('error', function(event) {\n        console.error('Global error:', event.error);\n      });\n\n      // Global unhandled promise rejection handler\n      window.addEventListener('unhandledrejection', function(event) {\n        console.error('Unhandled promise rejection:', event.reason);\n      });\n    </script>\n  </body>\n</html>`\n\n      // Scrivi il template aggiornato\n      const templatePath = path.resolve('../backend/templates/vue_app.html')\n      fs.writeFileSync(templatePath, templateContent)\n\n      console.log('✅ Template Flask aggiornato automaticamente con i nuovi asset paths')\n    }\n  }\n}\n\nexport default defineConfig({\n  plugins: [vue(), generateFlaskTemplate()],\n  build: {\n    outDir: '../backend/static/dist',\n    emptyOutDir: true,\n    rollupOptions: {\n      output: {\n        entryFileNames: 'assets/app.js',\n        chunkFileNames: 'assets/[name].js',\n        assetFileNames: 'assets/[name].[ext]',\n        manualChunks: {\n          vendor: ['vue', 'vue-router', 'pinia', 'axios', 'chart.js']\n        }\n      }\n    }\n  },\n  server: {\n    port: 3000,\n    proxy: {\n      '/api': {\n        target: 'http://localhost:5000',\n        changeOrigin: true\n      },\n      '/auth': {\n        target: 'http://localhost:5000',\n        changeOrigin: true\n      },\n      '/admin': {\n        target: 'http://localhost:5000',\n        changeOrigin: true\n      },\n      '/dashboard': {\n        target: 'http://localhost:5000',\n        changeOrigin: true\n      }\n    }\n  },\n  resolve: {\n    alias: {\n      '@': fileURLToPath(new URL('./src', import.meta.url)),\n      '@components': fileURLToPath(new URL('./src/components', import.meta.url)),\n      '@views': fileURLToPath(new URL('./src/views', import.meta.url)),\n      '@stores': fileURLToPath(new URL('./src/stores', import.meta.url)),\n      '@utils': fileURLToPath(new URL('./src/utils', import.meta.url))\n    }\n  }\n})"}