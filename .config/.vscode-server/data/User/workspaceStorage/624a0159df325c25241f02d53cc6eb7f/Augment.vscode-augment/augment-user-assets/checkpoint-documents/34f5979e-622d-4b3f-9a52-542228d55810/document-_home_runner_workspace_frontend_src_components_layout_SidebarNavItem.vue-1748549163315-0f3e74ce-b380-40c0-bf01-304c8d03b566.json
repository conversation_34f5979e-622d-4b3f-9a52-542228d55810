{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/layout/SidebarNavItem.vue"}, "originalCode": "<template>\n  <div>\n    <!-- Main Navigation Item -->\n    <router-link\n      v-if=\"item.path !== '#'\"\n      :to=\"item.path\"\n      class=\"group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150\"\n      :class=\"[\n        navItemClasses,\n        { 'justify-center': isCollapsed }\n      ]\"\n      active-class=\"text-primary-600 bg-primary-50 border-r-2 border-primary-600\"\n      @click=\"$emit('click')\"\n    >\n      <SidebarIcon\n        :icon=\"item.icon\"\n        :class=\"[\n          'flex-shrink-0 h-6 w-6',\n          { 'mr-0': isCollapsed, 'mr-3': !isCollapsed }\n        ]\"\n      />\n      <span\n        v-if=\"!isCollapsed\"\n        class=\"truncate\"\n      >\n        {{ item.name }}\n      </span>\n    </router-link>\n\n    <!-- Disabled/Placeholder Item -->\n    <div\n      v-else\n      class=\"group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 cursor-not-allowed opacity-75\"\n      :class=\"[\n        'text-gray-400 hover:text-gray-500',\n        { 'justify-center': isCollapsed }\n      ]\"\n    >\n      <SidebarIcon\n        :icon=\"item.icon\"\n        :class=\"[\n          'flex-shrink-0 h-6 w-6',\n          { 'mr-0': isCollapsed, 'mr-3': !isCollapsed }\n        ]\"\n      />\n      <span\n        v-if=\"!isCollapsed\"\n        class=\"truncate\"\n      >\n        {{ item.name }}\n      </span>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\nimport SidebarIcon from './SidebarIcon.vue'\n\nconst props = defineProps({\n  item: {\n    type: Object,\n    required: true\n  },\n  isCollapsed: {\n    type: Boolean,\n    default: false\n  }\n})\n\ndefineEmits(['click'])\n\nconst navItemClasses = computed(() => [\n  'text-gray-700 hover:bg-gray-50 hover:text-primary-600'\n])\n</script>", "modifiedCode": "<template>\n  <div>\n    <!-- Main Navigation Item -->\n    <router-link\n      v-if=\"item.path !== '#'\"\n      :to=\"item.path\"\n      class=\"group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150\"\n      :class=\"[\n        navItemClasses,\n        { 'justify-center': isCollapsed }\n      ]\"\n      active-class=\"text-primary-600 bg-primary-50 border-r-2 border-primary-600\"\n      @click=\"$emit('click')\"\n    >\n      <SidebarIcon\n        :icon=\"item.icon\"\n        :class=\"[\n          'flex-shrink-0 h-6 w-6',\n          { 'mr-0': isCollapsed, 'mr-3': !isCollapsed }\n        ]\"\n      />\n      <span\n        v-if=\"!isCollapsed\"\n        class=\"truncate\"\n      >\n        {{ item.name }}\n      </span>\n    </router-link>\n\n    <!-- Disabled/Placeholder Item -->\n    <div\n      v-else\n      class=\"group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 cursor-not-allowed opacity-75\"\n      :class=\"[\n        'text-gray-400 hover:text-gray-500',\n        { 'justify-center': isCollapsed }\n      ]\"\n    >\n      <SidebarIcon\n        :icon=\"item.icon\"\n        :class=\"[\n          'flex-shrink-0 h-6 w-6',\n          { 'mr-0': isCollapsed, 'mr-3': !isCollapsed }\n        ]\"\n      />\n      <span\n        v-if=\"!isCollapsed\"\n        class=\"truncate\"\n      >\n        {{ item.name }}\n      </span>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\nimport SidebarIcon from './SidebarIcon.vue'\n\nconst props = defineProps({\n  item: {\n    type: Object,\n    required: true\n  },\n  isCollapsed: {\n    type: Boolean,\n    default: false\n  }\n})\n\ndefineEmits(['click'])\n\nconst navItemClasses = computed(() => [\n  'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400'\n])\n</script>"}