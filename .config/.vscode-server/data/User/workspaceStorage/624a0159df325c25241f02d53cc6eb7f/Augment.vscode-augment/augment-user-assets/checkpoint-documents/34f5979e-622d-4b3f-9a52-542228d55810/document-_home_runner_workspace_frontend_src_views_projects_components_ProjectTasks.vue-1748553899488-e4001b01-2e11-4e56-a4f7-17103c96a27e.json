{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTasks.vue"}, "originalCode": "<template>\n  <div class=\"project-tasks\">\n    <div class=\"bg-white shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center mb-6\">\n        <h3 class=\"text-lg font-medium text-gray-900\">Task del Progetto</h3>\n        <button class=\"btn-primary\">\n          <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n          </svg>\n          Nuovo Task\n        </button>\n      </div>\n\n      <!-- Task List -->\n      <div class=\"space-y-4\">\n        <div v-for=\"task in tasks\" :key=\"task.id\" class=\"border border-gray-200 rounded-lg p-4 hover:bg-gray-50\">\n          <div class=\"flex items-start justify-between\">\n            <div class=\"flex-1\">\n              <div class=\"flex items-center space-x-3\">\n                <div class=\"flex-shrink-0\">\n                  <div class=\"w-3 h-3 rounded-full\" :class=\"getStatusColor(task.status)\"></div>\n                </div>\n                <h5 class=\"text-lg font-medium text-gray-900\">{{ task.name }}</h5>\n                <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\" :class=\"getPriorityClass(task.priority)\">\n                  {{ getPriorityLabel(task.priority) }}\n                </span>\n              </div>\n              <p class=\"mt-2 text-sm text-gray-600\">{{ task.description }}</p>\n              <div class=\"mt-3 flex items-center space-x-4 text-sm text-gray-500\">\n                <span v-if=\"task.assignee_id\">Assegnato a: {{ getAssigneeName(task.assignee_id) }}</span>\n                <span v-if=\"task.due_date\">Scadenza: {{ formatDate(task.due_date) }}</span>\n                <span v-if=\"task.estimated_hours\">Stimate: {{ task.estimated_hours }}h</span>\n              </div>\n            </div>\n            <div class=\"flex items-center space-x-2\">\n              <button class=\"text-gray-400 hover:text-gray-600\">\n                <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n        <div v-if=\"tasks.length === 0\" class=\"text-center py-8\">\n          <p class=\"text-gray-500\">Nessun task trovato per questo progetto</p>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\ndefineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n</script>\n", "modifiedCode": "<template>\n  <div class=\"project-tasks\">\n    <div class=\"bg-white shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center mb-6\">\n        <h3 class=\"text-lg font-medium text-gray-900\">Task del Progetto</h3>\n        <button class=\"btn-primary\">\n          <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n          </svg>\n          Nuovo Task\n        </button>\n      </div>\n\n      <!-- Task List -->\n      <div class=\"space-y-4\">\n        <div v-for=\"task in tasks\" :key=\"task.id\" class=\"border border-gray-200 rounded-lg p-4 hover:bg-gray-50\">\n          <div class=\"flex items-start justify-between\">\n            <div class=\"flex-1\">\n              <div class=\"flex items-center space-x-3\">\n                <div class=\"flex-shrink-0\">\n                  <div class=\"w-3 h-3 rounded-full\" :class=\"getStatusColor(task.status)\"></div>\n                </div>\n                <h5 class=\"text-lg font-medium text-gray-900\">{{ task.name }}</h5>\n                <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\" :class=\"getPriorityClass(task.priority)\">\n                  {{ getPriorityLabel(task.priority) }}\n                </span>\n              </div>\n              <p class=\"mt-2 text-sm text-gray-600\">{{ task.description }}</p>\n              <div class=\"mt-3 flex items-center space-x-4 text-sm text-gray-500\">\n                <span v-if=\"task.assignee_id\">Assegnato a: {{ getAssigneeName(task.assignee_id) }}</span>\n                <span v-if=\"task.due_date\">Scadenza: {{ formatDate(task.due_date) }}</span>\n                <span v-if=\"task.estimated_hours\">Stimate: {{ task.estimated_hours }}h</span>\n              </div>\n            </div>\n            <div class=\"flex items-center space-x-2\">\n              <button class=\"text-gray-400 hover:text-gray-600\">\n                <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n        <div v-if=\"tasks.length === 0\" class=\"text-center py-8\">\n          <p class=\"text-gray-500\">Nessun task trovato per questo progetto</p>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\n\nconst props = defineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n\n// Computed\nconst tasks = computed(() => {\n  // I task vengono caricati dall'API del progetto\n  return props.project?.tasks || []\n})\n\nconst teamMembers = computed(() => {\n  return props.project?.team_members || []\n})\n\n// Methods\nconst getStatusColor = (status) => {\n  const colors = {\n    'todo': 'bg-gray-400',\n    'in-progress': 'bg-blue-500',\n    'completed': 'bg-green-500',\n    'on-hold': 'bg-yellow-500'\n  }\n  return colors[status] || 'bg-gray-400'\n}\n\nconst getPriorityClass = (priority) => {\n  const classes = {\n    'low': 'bg-green-100 text-green-800',\n    'medium': 'bg-yellow-100 text-yellow-800',\n    'high': 'bg-red-100 text-red-800'\n  }\n  return classes[priority] || 'bg-gray-100 text-gray-800'\n}\n\nconst getPriorityLabel = (priority) => {\n  const labels = {\n    'low': 'Bassa',\n    'medium': 'Media',\n    'high': 'Alta'\n  }\n  return labels[priority] || 'Non specificata'\n}\n\nconst getAssigneeName = (assigneeId) => {\n  const member = teamMembers.value.find(m => m.id === assigneeId)\n  return member ? member.full_name : 'Non assegnato'\n}\n\nconst formatDate = (dateString) => {\n  if (!dateString) return ''\n  return new Date(dateString).toLocaleDateString('it-IT')\n}\n</script>\n"}