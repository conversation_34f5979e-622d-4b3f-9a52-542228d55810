{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectGantt.vue"}, "originalCode": "<template>\n  <div class=\"project-gantt\">\n    <div class=\"bg-white shadow rounded-lg p-6\">\n      <h3 class=\"text-lg font-medium text-gray-900 mb-6\">Diagramma di Gantt</h3>\n\n      <!-- Task Timeline List -->\n      <div class=\"space-y-3\">\n        <div v-for=\"task in tasks\" :key=\"task.id\" class=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\n          <div class=\"flex items-center justify-between\">\n            <div class=\"flex-1\">\n              <div class=\"flex items-center space-x-3\">\n                <div class=\"flex-shrink-0\">\n                  <div class=\"w-3 h-3 rounded-full\" :class=\"getStatusColor(task.status)\"></div>\n                </div>\n                <h4 class=\"text-sm font-medium text-gray-900\">{{ task.name }}</h4>\n                <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\" :class=\"getPriorityClass(task.priority)\">\n                  {{ getPriorityLabel(task.priority) }}\n                </span>\n              </div>\n              <div class=\"mt-2 flex items-center space-x-4 text-sm text-gray-500\">\n                <span v-if=\"task.start_date\">Inizio: {{ formatDate(task.start_date) }}</span>\n                <span v-if=\"task.due_date\">Fine: {{ formatDate(task.due_date) }}</span>\n                <span v-if=\"task.estimated_hours\">Durata: {{ task.estimated_hours }}h</span>\n              </div>\n              <!-- Timeline Bar -->\n              <div class=\"mt-3\">\n                <div class=\"flex items-center space-x-2\">\n                  <span class=\"text-xs text-gray-500 w-16\">{{ formatDate(task.start_date) }}</span>\n                  <div class=\"flex-1 bg-gray-200 rounded-full h-2\">\n                    <div\n                      class=\"h-2 rounded-full transition-all duration-300\"\n                      :class=\"getTaskBarColor(task.status)\"\n                      :style=\"{ width: getTaskProgress(task) + '%' }\"\n                    ></div>\n                  </div>\n                  <span class=\"text-xs text-gray-500 w-16\">{{ formatDate(task.due_date) }}</span>\n                </div>\n              </div>\n            </div>\n            <div class=\"flex items-center space-x-2 ml-4\">\n              <div class=\"text-right\">\n                <div class=\"text-xs text-gray-500\">Progresso</div>\n                <div class=\"text-sm font-medium text-gray-900\">{{ getTaskProgress(task) }}%</div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div v-if=\"tasks.length === 0\" class=\"text-center py-8\">\n          <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n          </svg>\n          <p class=\"text-gray-500 mt-2\">Nessun task pianificato per questo progetto</p>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\ndefineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n</script>\n", "modifiedCode": "<template>\n  <div class=\"project-gantt\">\n    <div class=\"bg-white shadow rounded-lg p-6\">\n      <h3 class=\"text-lg font-medium text-gray-900 mb-6\">Diagramma di Gantt</h3>\n\n      <!-- Task Timeline List -->\n      <div class=\"space-y-3\">\n        <div v-for=\"task in tasks\" :key=\"task.id\" class=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\n          <div class=\"flex items-center justify-between\">\n            <div class=\"flex-1\">\n              <div class=\"flex items-center space-x-3\">\n                <div class=\"flex-shrink-0\">\n                  <div class=\"w-3 h-3 rounded-full\" :class=\"getStatusColor(task.status)\"></div>\n                </div>\n                <h4 class=\"text-sm font-medium text-gray-900\">{{ task.name }}</h4>\n                <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\" :class=\"getPriorityClass(task.priority)\">\n                  {{ getPriorityLabel(task.priority) }}\n                </span>\n              </div>\n              <div class=\"mt-2 flex items-center space-x-4 text-sm text-gray-500\">\n                <span v-if=\"task.start_date\">Inizio: {{ formatDate(task.start_date) }}</span>\n                <span v-if=\"task.due_date\">Fine: {{ formatDate(task.due_date) }}</span>\n                <span v-if=\"task.estimated_hours\">Durata: {{ task.estimated_hours }}h</span>\n              </div>\n              <!-- Timeline Bar -->\n              <div class=\"mt-3\">\n                <div class=\"flex items-center space-x-2\">\n                  <span class=\"text-xs text-gray-500 w-16\">{{ formatDate(task.start_date) }}</span>\n                  <div class=\"flex-1 bg-gray-200 rounded-full h-2\">\n                    <div\n                      class=\"h-2 rounded-full transition-all duration-300\"\n                      :class=\"getTaskBarColor(task.status)\"\n                      :style=\"{ width: getTaskProgress(task) + '%' }\"\n                    ></div>\n                  </div>\n                  <span class=\"text-xs text-gray-500 w-16\">{{ formatDate(task.due_date) }}</span>\n                </div>\n              </div>\n            </div>\n            <div class=\"flex items-center space-x-2 ml-4\">\n              <div class=\"text-right\">\n                <div class=\"text-xs text-gray-500\">Progresso</div>\n                <div class=\"text-sm font-medium text-gray-900\">{{ getTaskProgress(task) }}%</div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div v-if=\"tasks.length === 0\" class=\"text-center py-8\">\n          <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n          </svg>\n          <p class=\"text-gray-500 mt-2\">Nessun task pianificato per questo progetto</p>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\n\nconst props = defineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n\nconst tasks = computed(() => {\n  return props.project?.tasks || []\n})\n\nconst getStatusColor = (status) => {\n  const colors = {\n    'todo': 'bg-gray-400',\n    'in-progress': 'bg-blue-500',\n    'completed': 'bg-green-500',\n    'on-hold': 'bg-yellow-500'\n  }\n  return colors[status] || 'bg-gray-400'\n}\n\nconst getTaskBarColor = (status) => {\n  const colors = {\n    'todo': 'bg-gray-400',\n    'in-progress': 'bg-blue-500',\n    'completed': 'bg-green-500',\n    'on-hold': 'bg-yellow-500'\n  }\n  return colors[status] || 'bg-gray-400'\n}\n\nconst getPriorityClass = (priority) => {\n  const classes = {\n    'low': 'bg-green-100 text-green-800',\n    'medium': 'bg-yellow-100 text-yellow-800',\n    'high': 'bg-red-100 text-red-800'\n  }\n  return classes[priority] || 'bg-gray-100 text-gray-800'\n}\n\nconst getPriorityLabel = (priority) => {\n  const labels = {\n    'low': 'Bassa',\n    'medium': 'Media',\n    'high': 'Alta'\n  }\n  return labels[priority] || 'Non specificata'\n}\n\nconst getTaskProgress = (task) => {\n  // Calcolo semplificato del progresso basato sullo status\n  const statusProgress = {\n    'todo': 0,\n    'in-progress': 50,\n    'completed': 100,\n    'on-hold': 25\n  }\n  return statusProgress[task.status] || 0\n}\n\nconst formatDate = (dateString) => {\n  if (!dateString) return ''\n  return new Date(dateString).toLocaleDateString('it-IT', {\n    day: '2-digit',\n    month: '2-digit'\n  })\n}\n</script>\n"}