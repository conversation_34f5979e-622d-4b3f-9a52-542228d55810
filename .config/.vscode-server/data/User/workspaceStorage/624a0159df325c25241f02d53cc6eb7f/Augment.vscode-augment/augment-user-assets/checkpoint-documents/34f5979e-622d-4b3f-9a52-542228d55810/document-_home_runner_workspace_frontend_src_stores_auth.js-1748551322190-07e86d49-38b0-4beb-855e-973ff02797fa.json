{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/stores/auth.js"}, "originalCode": "import { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\nimport api from '@/utils/api'\n\nexport const useAuthStore = defineStore('auth', () => {\n  // Rimuoviamo la gestione del token per usare solo le sessioni Flask\n  const storedUser = localStorage.getItem('user')\n  const user = ref(storedUser ? JSON.parse(storedUser) : null)\n  const loading = ref(false)\n  const error = ref(null)\n  const sessionChecked = ref(false)\n\n  const isAuthenticated = computed(() => !!user.value && sessionChecked.value)\n\n  // Permission system - mapping from backend\n  const ROLE_PERMISSIONS = {\n    admin: [\n      'admin', 'manage_users', 'assign_roles', 'view_all_projects', 'create_project',\n      'edit_project', 'delete_project', 'assign_to_project', 'manage_project_tasks',\n      'manage_project_resources', 'approve_timesheets', 'view_personnel_data',\n      'edit_personnel_data', 'view_contracts', 'manage_contracts', 'view_crm',\n      'manage_clients', 'manage_proposals', 'view_reports', 'view_dashboard',\n      'submit_timesheet', 'view_own_timesheets', 'view_funding', 'manage_funding',\n      'view_products', 'manage_products', 'view_performance', 'manage_performance',\n      'view_communications', 'manage_communications', 'view_startup', 'manage_startup'\n    ],\n    manager: [\n      'view_dashboard', 'view_all_projects', 'edit_project', 'assign_to_project',\n      'manage_project_tasks', 'manage_project_resources', 'approve_timesheets',\n      'view_personnel_data', 'view_crm', 'view_reports', 'submit_timesheet',\n      'view_own_timesheets', 'manage_clients', 'manage_proposals', 'view_funding',\n      'manage_funding', 'view_products', 'manage_products', 'view_performance',\n      'manage_performance', 'view_communications', 'manage_communications',\n      'view_startup', 'manage_startup'\n    ],\n    employee: [\n      'view_dashboard', 'view_own_timesheets', 'submit_timesheet'\n    ],\n    sales: [\n      'view_dashboard', 'view_crm', 'manage_clients', 'manage_proposals',\n      'submit_timesheet', 'view_own_timesheets', 'view_reports', 'view_funding',\n      'view_products', 'manage_products'\n    ],\n    human_resources: [\n      'view_dashboard', 'manage_users', 'view_personnel_data', 'edit_personnel_data',\n      'view_contracts', 'manage_contracts', 'submit_timesheet', 'view_own_timesheets',\n      'view_reports', 'view_funding', 'manage_funding', 'view_performance',\n      'manage_performance', 'view_communications', 'manage_communications',\n      'view_startup', 'manage_startup'\n    ]\n  }\n\n  // Helper function to check permissions\n  const hasPermission = (permission) => {\n    if (!user.value || !user.value.role) return false\n\n    // Admin has all permissions\n    if (user.value.role === 'admin') return true\n\n    const userPermissions = ROLE_PERMISSIONS[user.value.role] || []\n    return userPermissions.includes(permission)\n  }\n\n  async function login(credentials) {\n    loading.value = true\n    error.value = null\n\n    try {\n      const response = await api.post('/api/auth/login', credentials)\n\n      if (response.data.success) {\n        user.value = response.data.data.user\n        localStorage.setItem('user', JSON.stringify(user.value))\n        sessionChecked.value = true\n        return { success: true }\n      } else {\n        error.value = response.data.message || 'Errore durante il login'\n        return { success: false, error: error.value }\n      }\n    } catch (err) {\n      error.value = err.response?.data?.message || 'Errore di connessione'\n      return { success: false, error: error.value }\n    } finally {\n      loading.value = false\n    }\n  }\n\n  async function register(userData) {\n    loading.value = true\n    error.value = null\n\n    try {\n      const response = await api.post('/api/auth/register', userData)\n\n      if (response.data.success) {\n        return { success: true, message: response.data.message }\n      } else {\n        error.value = response.data.message || 'Errore durante la registrazione'\n        return { success: false, error: error.value }\n      }\n    } catch (err) {\n      error.value = err.response?.data?.message || 'Errore di connessione'\n      return { success: false, error: error.value }\n    } finally {\n      loading.value = false\n    }\n  }\n\n  async function logout() {\n    try {\n      await api.post('/api/auth/logout')\n    } catch (err) {\n      console.warn('Errore durante il logout:', err)\n    } finally {\n      user.value = null\n      sessionChecked.value = false\n      localStorage.removeItem('user')\n    }\n  }\n\n  async function checkAuth() {\n    if (sessionChecked.value) {\n      return isAuthenticated.value\n    }\n\n    try {\n      const response = await api.get('/api/auth/me')\n      if (response.data.success) {\n        user.value = response.data.data.user\n        localStorage.setItem('user', JSON.stringify(user.value))\n        sessionChecked.value = true\n        return true\n      } else {\n        await logout()\n        return false\n      }\n    } catch (err) {\n      await logout()\n      return false\n    }\n  }\n\n  async function initializeAuth() {\n    // Controlla se c'è un utente salvato e verifica la sessione\n    if (user.value) {\n      return await checkAuth()\n    }\n    sessionChecked.value = true\n    return false\n  }\n\n  return {\n    user,\n    loading,\n    error,\n    sessionChecked,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    checkAuth,\n    initializeAuth\n  }\n})", "modifiedCode": "import { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\nimport api from '@/utils/api'\n\nexport const useAuthStore = defineStore('auth', () => {\n  // Rimuoviamo la gestione del token per usare solo le sessioni Flask\n  const storedUser = localStorage.getItem('user')\n  const user = ref(storedUser ? JSON.parse(storedUser) : null)\n  const loading = ref(false)\n  const error = ref(null)\n  const sessionChecked = ref(false)\n\n  const isAuthenticated = computed(() => !!user.value && sessionChecked.value)\n\n  // Permission system - mapping from backend\n  const ROLE_PERMISSIONS = {\n    admin: [\n      'admin', 'manage_users', 'assign_roles', 'view_all_projects', 'create_project',\n      'edit_project', 'delete_project', 'assign_to_project', 'manage_project_tasks',\n      'manage_project_resources', 'approve_timesheets', 'view_personnel_data',\n      'edit_personnel_data', 'view_contracts', 'manage_contracts', 'view_crm',\n      'manage_clients', 'manage_proposals', 'view_reports', 'view_dashboard',\n      'submit_timesheet', 'view_own_timesheets', 'view_funding', 'manage_funding',\n      'view_products', 'manage_products', 'view_performance', 'manage_performance',\n      'view_communications', 'manage_communications', 'view_startup', 'manage_startup'\n    ],\n    manager: [\n      'view_dashboard', 'view_all_projects', 'edit_project', 'assign_to_project',\n      'manage_project_tasks', 'manage_project_resources', 'approve_timesheets',\n      'view_personnel_data', 'view_crm', 'view_reports', 'submit_timesheet',\n      'view_own_timesheets', 'manage_clients', 'manage_proposals', 'view_funding',\n      'manage_funding', 'view_products', 'manage_products', 'view_performance',\n      'manage_performance', 'view_communications', 'manage_communications',\n      'view_startup', 'manage_startup'\n    ],\n    employee: [\n      'view_dashboard', 'view_own_timesheets', 'submit_timesheet'\n    ],\n    sales: [\n      'view_dashboard', 'view_crm', 'manage_clients', 'manage_proposals',\n      'submit_timesheet', 'view_own_timesheets', 'view_reports', 'view_funding',\n      'view_products', 'manage_products'\n    ],\n    human_resources: [\n      'view_dashboard', 'manage_users', 'view_personnel_data', 'edit_personnel_data',\n      'view_contracts', 'manage_contracts', 'submit_timesheet', 'view_own_timesheets',\n      'view_reports', 'view_funding', 'manage_funding', 'view_performance',\n      'manage_performance', 'view_communications', 'manage_communications',\n      'view_startup', 'manage_startup'\n    ]\n  }\n\n  // Helper function to check permissions\n  const hasPermission = (permission) => {\n    if (!user.value || !user.value.role) return false\n\n    // Admin has all permissions\n    if (user.value.role === 'admin') return true\n\n    const userPermissions = ROLE_PERMISSIONS[user.value.role] || []\n    return userPermissions.includes(permission)\n  }\n\n  async function login(credentials) {\n    loading.value = true\n    error.value = null\n\n    try {\n      const response = await api.post('/api/auth/login', credentials)\n\n      if (response.data.success) {\n        user.value = response.data.data.user\n        localStorage.setItem('user', JSON.stringify(user.value))\n        sessionChecked.value = true\n        return { success: true }\n      } else {\n        error.value = response.data.message || 'Errore durante il login'\n        return { success: false, error: error.value }\n      }\n    } catch (err) {\n      error.value = err.response?.data?.message || 'Errore di connessione'\n      return { success: false, error: error.value }\n    } finally {\n      loading.value = false\n    }\n  }\n\n  async function register(userData) {\n    loading.value = true\n    error.value = null\n\n    try {\n      const response = await api.post('/api/auth/register', userData)\n\n      if (response.data.success) {\n        return { success: true, message: response.data.message }\n      } else {\n        error.value = response.data.message || 'Errore durante la registrazione'\n        return { success: false, error: error.value }\n      }\n    } catch (err) {\n      error.value = err.response?.data?.message || 'Errore di connessione'\n      return { success: false, error: error.value }\n    } finally {\n      loading.value = false\n    }\n  }\n\n  async function logout() {\n    try {\n      await api.post('/api/auth/logout')\n    } catch (err) {\n      console.warn('Errore durante il logout:', err)\n    } finally {\n      user.value = null\n      sessionChecked.value = false\n      localStorage.removeItem('user')\n    }\n  }\n\n  async function checkAuth() {\n    if (sessionChecked.value) {\n      return isAuthenticated.value\n    }\n\n    try {\n      const response = await api.get('/api/auth/me')\n      if (response.data.success) {\n        user.value = response.data.data.user\n        localStorage.setItem('user', JSON.stringify(user.value))\n        sessionChecked.value = true\n        return true\n      } else {\n        await logout()\n        return false\n      }\n    } catch (err) {\n      await logout()\n      return false\n    }\n  }\n\n  async function initializeAuth() {\n    // Controlla se c'è un utente salvato e verifica la sessione\n    if (user.value) {\n      return await checkAuth()\n    }\n    sessionChecked.value = true\n    return false\n  }\n\n  return {\n    user,\n    loading,\n    error,\n    sessionChecked,\n    isAuthenticated,\n    hasPermission,\n    login,\n    register,\n    logout,\n    checkAuth,\n    initializeAuth\n  }\n})"}