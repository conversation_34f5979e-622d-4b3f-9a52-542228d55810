{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTasks.vue"}, "modifiedCode": "<template>\n  <div class=\"project-tasks\">\n    <div class=\"bg-white shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center mb-6\">\n        <h3 class=\"text-lg font-medium text-gray-900\">Task del Progetto</h3>\n        <button class=\"btn-primary\">\n          <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n          </svg>\n          Nuovo Task\n        </button>\n      </div>\n      \n      <div class=\"text-center py-8\">\n        <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\" />\n        </svg>\n        <h3 class=\"mt-2 text-sm font-medium text-gray-900\">Componente Task in sviluppo</h3>\n        <p class=\"mt-1 text-sm text-gray-500\">Migrazione da template legacy Alpine.js in corso...</p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\ndefineProps({\n  project: { type: Object, default: null },\n  loading: { type: Boolean, default: false }\n})\n</script>\n"}