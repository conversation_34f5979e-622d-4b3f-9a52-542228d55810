{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/composables/useDarkMode.js"}, "originalCode": "import { ref } from 'vue'\n\n// Global dark mode state\nconst isDarkMode = ref(false)\n\n// Apply dark mode to DOM\nconst applyDarkMode = (value) => {\n  if (value) {\n    document.documentElement.classList.add('dark')\n    localStorage.setItem('darkMode', 'true')\n  } else {\n    document.documentElement.classList.remove('dark')\n    localStorage.setItem('darkMode', 'false')\n  }\n}\n\nexport function useDarkMode() {\n  const toggleDarkMode = () => {\n    isDarkMode.value = !isDarkMode.value\n    applyDarkMode(isDarkMode.value)\n  }\n\n  const setDarkMode = (value) => {\n    isDarkMode.value = value\n    applyDarkMode(value)\n  }\n\n  const initializeDarkMode = () => {\n    const savedDarkMode = localStorage.getItem('darkMode')\n\n    if (savedDarkMode === 'true') {\n      isDarkMode.value = true\n      applyDarkMode(true)\n    } else if (savedDarkMode === 'false') {\n      isDarkMode.value = false\n      applyDarkMode(false)\n    } else {\n      // Default to system preference\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches\n      isDarkMode.value = prefersDark\n      applyDarkMode(prefersDark)\n    }\n  }\n\n  return {\n    isDarkMode,\n    toggleDarkMode,\n    setDarkMode,\n    initializeDarkMode\n  }\n}\n", "modifiedCode": "import { ref, watch } from 'vue'\n\n// Global dark mode state\nconst isDarkMode = ref(false)\nlet isInitialized = false\n\n// Apply dark mode to DOM\nconst applyDarkMode = (value) => {\n  console.log('Applying dark mode:', value) // Debug log\n  if (value) {\n    document.documentElement.classList.add('dark')\n    localStorage.setItem('darkMode', 'true')\n  } else {\n    document.documentElement.classList.remove('dark')\n    localStorage.setItem('darkMode', 'false')\n  }\n}\n\n// Initialize watcher only once\nconst initializeWatcher = () => {\n  if (isInitialized) return\n\n  watch(isDarkMode, (newValue) => {\n    console.log('Dark mode changed to:', newValue) // Debug log\n    applyDarkMode(newValue)\n  })\n\n  isInitialized = true\n}\n\nexport function useDarkMode() {\n  // Initialize watcher on first use\n  initializeWatcher()\n\n  const toggleDarkMode = () => {\n    console.log('Toggling dark mode from:', isDarkMode.value) // Debug log\n    isDarkMode.value = !isDarkMode.value\n  }\n\n  const setDarkMode = (value) => {\n    isDarkMode.value = value\n  }\n\n  const initializeDarkMode = () => {\n    console.log('Initializing dark mode...') // Debug log\n    const savedDarkMode = localStorage.getItem('darkMode')\n\n    if (savedDarkMode === 'true') {\n      isDarkMode.value = true\n    } else if (savedDarkMode === 'false') {\n      isDarkMode.value = false\n    } else {\n      // Default to system preference\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches\n      isDarkMode.value = prefersDark\n    }\n\n    // Apply immediately\n    applyDarkMode(isDarkMode.value)\n  }\n\n  return {\n    isDarkMode,\n    toggleDarkMode,\n    setDarkMode,\n    initializeDarkMode\n  }\n}\n"}