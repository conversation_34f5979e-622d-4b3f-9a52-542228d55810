{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/layout/HeaderUserMenu.vue"}, "originalCode": "<template>\n  <div class=\"relative\">\n    <button\n      @click=\"showUserMenu = !showUserMenu\"\n      class=\"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n    >\n      <span class=\"sr-only\">Apri menu utente</span>\n      <div class=\"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center\">\n        <span class=\"text-sm font-medium text-primary-700\">\n          {{ userInitials }}\n        </span>\n      </div>\n    </button>\n\n    <!-- Dropdown Menu -->\n    <div\n      v-if=\"showUserMenu\"\n      @click.away=\"showUserMenu = false\"\n      class=\"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50\"\n    >\n      <div class=\"py-1\">\n        <div class=\"px-4 py-2 border-b border-gray-100\">\n          <p class=\"text-sm font-medium text-gray-900\">{{ userName }}</p>\n          <p class=\"text-xs text-gray-500\">{{ userEmail }}</p>\n        </div>\n\n        <router-link\n          to=\"/app/profile\"\n          class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n          @click=\"showUserMenu = false\"\n        >\n          Il tuo profilo\n        </router-link>\n        <router-link\n          to=\"/app/settings\"\n          class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n          @click=\"showUserMenu = false\"\n        >\n          Impostazioni\n        </router-link>\n\n        <div class=\"border-t border-gray-100 my-1\"></div>\n\n        <button\n          @click=\"toggleDarkMode\"\n          class=\"flex items-center justify-between w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n        >\n          <span>{{ isDarkMode ? 'Modalità chiara' : 'Modalità scura' }}</span>\n          <i :class=\"isDarkMode ? 'fas fa-sun' : 'fas fa-moon'\" class=\"text-xs\"></i>\n        </button>\n\n        <button\n          @click=\"logout\"\n          class=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n        >\n          Esci\n        </button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { useAuthStore } from '@/stores/auth'\n\nconst router = useRouter()\nconst authStore = useAuthStore()\nconst showUserMenu = ref(false)\nconst isDarkMode = ref(false)\n\nconst userName = computed(() => {\n  if (!authStore.user) return 'Utente'\n  return authStore.user.name || authStore.user.username || 'Utente'\n})\n\nconst userEmail = computed(() => {\n  return authStore.user?.email || ''\n})\n\nconst userInitials = computed(() => {\n  if (!authStore.user) return 'U'\n  const name = userName.value\n  return name.charAt(0).toUpperCase()\n})\n\nasync function logout() {\n  showUserMenu.value = false\n  await authStore.logout()\n  router.push('/auth/login')\n}\n</script>", "modifiedCode": "<template>\n  <div class=\"relative\">\n    <button\n      @click=\"showUserMenu = !showUserMenu\"\n      class=\"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n    >\n      <span class=\"sr-only\">Apri menu utente</span>\n      <div class=\"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center\">\n        <span class=\"text-sm font-medium text-primary-700\">\n          {{ userInitials }}\n        </span>\n      </div>\n    </button>\n\n    <!-- Dropdown Menu -->\n    <div\n      v-if=\"showUserMenu\"\n      @click.away=\"showUserMenu = false\"\n      class=\"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50\"\n    >\n      <div class=\"py-1\">\n        <div class=\"px-4 py-2 border-b border-gray-100\">\n          <p class=\"text-sm font-medium text-gray-900\">{{ userName }}</p>\n          <p class=\"text-xs text-gray-500\">{{ userEmail }}</p>\n        </div>\n\n        <router-link\n          to=\"/app/profile\"\n          class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n          @click=\"showUserMenu = false\"\n        >\n          Il tuo profilo\n        </router-link>\n        <router-link\n          to=\"/app/settings\"\n          class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n          @click=\"showUserMenu = false\"\n        >\n          Impostazioni\n        </router-link>\n\n        <div class=\"border-t border-gray-100 my-1\"></div>\n\n        <button\n          @click=\"toggleDarkMode\"\n          class=\"flex items-center justify-between w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n        >\n          <span>{{ isDarkMode ? 'Modalità chiara' : 'Modalità scura' }}</span>\n          <i :class=\"isDarkMode ? 'fas fa-sun' : 'fas fa-moon'\" class=\"text-xs\"></i>\n        </button>\n\n        <button\n          @click=\"logout\"\n          class=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n        >\n          Esci\n        </button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { useAuthStore } from '@/stores/auth'\nimport { useDarkMode } from '@/composables/useDarkMode'\n\nconst router = useRouter()\nconst authStore = useAuthStore()\nconst showUserMenu = ref(false)\n\n// Dark mode composable\nconst { isDarkMode, toggleDarkMode } = useDarkMode()\nconst isDarkMode = ref(false)\n\nconst userName = computed(() => {\n  if (!authStore.user) return 'Utente'\n  return authStore.user.name || authStore.user.username || 'Utente'\n})\n\nconst userEmail = computed(() => {\n  return authStore.user?.email || ''\n})\n\nconst userInitials = computed(() => {\n  if (!authStore.user) return 'U'\n  const name = userName.value\n  return name.charAt(0).toUpperCase()\n})\n\nasync function logout() {\n  showUserMenu.value = false\n  await authStore.logout()\n  router.push('/auth/login')\n}\n</script>"}