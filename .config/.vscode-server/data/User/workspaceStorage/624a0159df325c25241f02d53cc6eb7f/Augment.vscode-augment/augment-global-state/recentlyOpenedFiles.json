[["/home/<USER>/workspace/static/js/vue/stores/app.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/stores/app.js"}}], ["/home/<USER>/workspace/docs/css-architecture.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/css-architecture.md"}}], ["/home/<USER>/workspace/static/js/vue/components/ui/ModalContainer.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/ui/ModalContainer.vue"}}], ["/home/<USER>/workspace/static/js/vue/components/ui/LoadingOverlay.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/ui/LoadingOverlay.vue"}}], ["/home/<USER>/workspace/static/js/vue/views/Dashboard.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/Dashboard.vue"}}], ["/home/<USER>/workspace/blueprints/landing.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/landing.py"}}], ["/home/<USER>/workspace/templates/base.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/base.html"}}], ["/home/<USER>/workspace/static/js/vue/views/public/Services.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/public/Services.vue"}}], ["/home/<USER>/workspace/static/js/vue/views/public/ServiceDetail.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/public/ServiceDetail.vue"}}], ["/home/<USER>/workspace/static/js/vue/views/public/Privacy.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/public/Privacy.vue"}}], ["/home/<USER>/workspace/static/js/vue/views/public/Home.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/public/Home.vue"}}], ["/home/<USER>/workspace/static/js/vue/views/public/Contact.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/public/Contact.vue"}}], ["/home/<USER>/workspace/static/js/vue/views/public/About.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/public/About.vue"}}], ["/home/<USER>/workspace/static/js/alpine-init.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/alpine-init.js"}}], ["/home/<USER>/workspace/static/js/app.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/app.js"}}], ["/home/<USER>/workspace/static/js/components.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/components.js"}}], ["/home/<USER>/workspace/static/js/spa-navigation.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/spa-navigation.js"}}], ["/home/<USER>/workspace/static/js/theme.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/theme.js"}}], ["/home/<USER>/workspace/static/js/charts.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/charts.js"}}], ["/home/<USER>/workspace/static/js/utils.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/utils.js"}}], ["/home/<USER>/workspace/static/js/vue/stores/brand.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/stores/brand.js"}}], ["/home/<USER>/workspace/templates/spa.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/spa.html"}}], ["/home/<USER>/workspace/static/js/vue/router/index.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/router/index.js"}}], ["/home/<USER>/workspace/static/js/vue/main.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/main.js"}}], ["/home/<USER>/workspace/static/js/vue/components/public/PublicFooter.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/public/PublicFooter.vue"}}], ["/home/<USER>/workspace/static/js/vue/components/public/PublicNavigation.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/public/PublicNavigation.vue"}}], ["/home/<USER>/workspace/static/js/vue/components/App.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/App.vue"}}], ["/home/<USER>/workspace/static/js/vue/views/auth/Login.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/auth/Login.js"}}], ["/home/<USER>/workspace/static/js/vue/components/layout/UserDropdown.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/layout/UserDropdown.vue"}}], ["/home/<USER>/workspace/static/js/vue/components/public/PublicNavigation.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/public/PublicNavigation.js"}}], ["/home/<USER>/workspace/static/js/vue/components/dashboard/StatsCard.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/dashboard/StatsCard.vue"}}], ["/home/<USER>/workspace/static/js/vue/views/public/Privacy.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/views/public/Privacy.js"}}], ["/home/<USER>/workspace/static/js/vue/components/layout/UserProfile.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/layout/UserProfile.vue"}}], ["/home/<USER>/workspace/static/js/vue/components/public/PublicFooter.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/js/vue/components/public/PublicFooter.js"}}], ["/home/<USER>/workspace/config/tenant_config.json", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "config/tenant_config.json"}}], ["/home/<USER>/workspace/remove_spa.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "remove_spa.py"}}], ["/home/<USER>/workspace/test_cv.txt", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "test_cv.txt"}}], ["/home/<USER>/workspace/extensions.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "extensions.py"}}], ["/home/<USER>/workspace/db_update.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "db_update.py"}}], ["/home/<USER>/workspace/config.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "config.py"}}], ["/home/<USER>/workspace/ai_services.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "ai_services.py"}}], ["/home/<USER>/workspace/.env.example", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": ".env.example"}}], ["/home/<USER>/workspace/utils/permissions.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "utils/permissions.py"}}], ["/home/<USER>/workspace/utils/image_utils.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "utils/image_utils.py"}}], ["/home/<USER>/workspace/specs/task_16_vue_refactoring.txt", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "specs/task_16_vue_refactoring.txt"}}], ["/home/<USER>/workspace/app.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "app.py"}}], ["/home/<USER>/workspace/main.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "main.py"}}], ["/home/<USER>/workspace/backend/blueprints/api/kpis.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/kpis.py"}}], ["/home/<USER>/workspace/backend/cookies.txt", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/cookies.txt"}}], ["/home/<USER>/workspace/cookies.txt", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "cookies.txt"}}], ["/home/<USER>/workspace/frontend/package.json", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/package.json"}}], ["/home/<USER>/workspace/frontend/src/main.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/main.js"}}], ["/home/<USER>/workspace/frontend/package-lock.json", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/package-lock.json"}}], ["/home/<USER>/workspace/frontend/.eslintrc.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/.eslintrc.js"}}], ["/home/<USER>/workspace/backend/config.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/config.py"}}], ["/home/<USER>/workspace/backend/static/dist/index.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/static/dist/index.html"}}], ["/home/<USER>/workspace/backend/static/dist/assets/app.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/static/dist/assets/app.js"}}], ["/home/<USER>/workspace/backend/templates/vue_app.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/templates/vue_app.html"}}], ["/home/<USER>/workspace/frontend/vite.config.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/vite.config.js"}}], ["/home/<USER>/workspace/frontend/src/components/layout/HeaderSearch.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/layout/HeaderSearch.vue"}}], ["/home/<USER>/workspace/frontend/src/components/layout/HeaderQuickActions.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/layout/HeaderQuickActions.vue"}}], ["/home/<USER>/workspace/frontend/src/components/layout/AppHeader.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/layout/AppHeader.vue"}}], ["/home/<USER>/workspace/specs/vue_professional_migration.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "specs/vue_professional_migration.md"}}], ["/home/<USER>/workspace/frontend/tailwind.config.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/tailwind.config.js"}}]]