{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://json.schemastore.org/ava.json", "additionalProperties": false, "definitions": {"path": {"type": "string", "minLength": 1}, "array-of-strings": {"type": "array", "items": {"type": "string"}}, "array-of-paths": {"type": "array", "items": {"$ref": "#/definitions/path"}}}, "description": "Configuration Schema for the JavaScript test runner <PERSON><PERSON>", "properties": {"files": {"$ref": "#/definitions/array-of-paths", "description": "An array of glob patterns to select test files. Files with an underscore prefix are ignored. By default only selects files with `cjs`, `mjs` & `js` extensions, even if the pattern matches other files. Specify `extensions` to allow other file extensions"}, "ignoredByWatcher": {"$ref": "#/definitions/array-of-paths", "description": "An array of glob patterns to match files that, even if changed, are ignored by the watcher"}, "match": {"$ref": "#/definitions/array-of-paths", "description": "Not typically useful in the `package.json` configuration, but equivalent to specifying `--match` on the CLI"}, "cache": {"type": "boolean", "default": true, "description": "Defaults to `true` to cache compiled files under `node_modules/.cache/ava.` If `false`, files are cached in a temporary directory instead"}, "concurrency": {"type": "number", "description": "Max number of test files running at the same time (default: CPU cores)"}, "workerThreads": {"type": "boolean", "default": true, "description": "Use worker threads to run tests (enabled by default). If `false`, tests will run in child processes"}, "failFast": {"type": "boolean", "default": false, "description": "Stop running further tests once a test fails"}, "failWithoutAssertions": {"type": "boolean", "default": true, "description": "If `false`, does not fail a test if it doesn't run assertions"}, "environmentVariables": {"title": "environment variables", "type": "object", "description": "Specifies environment variables to be made available to the tests. The environment variables defined here override the ones from `process.env`", "additionalProperties": {"type": "string"}}, "serial": {"type": "boolean", "default": false, "description": "if `true`, prevents parallel execution of tests within a file"}, "tap": {"type": "boolean", "default": false, "description": "If `true`, enables the TAP reporter"}, "verbose": {"type": "boolean", "default": false, "description": "If `true`, enables verbose output (though currently non-verbose output is not supported)"}, "snapshotDir": {"$ref": "#/definitions/path", "description": "Specifies a fixed location for storing snapshot files. Use this if your snapshots are ending up in the wrong location"}, "extensions": {"anyOf": [{"$ref": "#/definitions/array-of-strings"}, {"title": "extensions", "type": "object", "patternProperties": {"^(c|m)?js$": {"enum": [true]}}, "additionalProperties": {"enum": ["commonjs", "module"]}}], "default": ["cjs", "mjs", "js"], "description": "Extensions of test files. Setting this overrides the default `[\"cjs\", \"mjs\", \"js\"]` value, so make sure to include those extensions in the list. Experimentally you can configure how files are loaded"}, "require": {"$ref": "#/definitions/array-of-paths", "description": "Extra modules to require before tests are run. Modules are required in the worker processes"}, "timeout": {"anyOf": [{"type": "number", "minimum": 0}, {"type": "string", "pattern": "^(\\d+)(s|m)$"}], "default": "10s", "description": "Timeouts in AVA behave differently than in other test frameworks. AVA resets a timer after each test, forcing tests to quit if no new test results were received within the specified timeout. This can be used to handle stalled tests. See our timeout documentation for more options"}, "nodeArguments": {"$ref": "#/definitions/array-of-strings", "description": "Configure Node.js arguments used to launch worker processes"}, "utilizeParallelBuilds": {"type": "boolean", "default": true, "description": "If `false`, disable parallel builds (default: `true`)"}, "typescript": {"title": "configuration", "type": "object", "description": "Configures @ava/typescript for projects that precompile TypeScript. Alternatively, you can use `ts-node` to do live testing without transpiling, in which case you shouldn't use the `typescript` property", "properties": {"extensions": {"$ref": "#/definitions/array-of-paths", "default": ["ts"], "description": "You can configure AVA to recognize additional file extensions as TypeScript (e.g., `[\"ts\", \"tsx\"]` to add partial JSX support). Note that the preserve mode for JSX is not (yet) supported. See also AVA's `extensions` object"}, "rewritePaths": {"title": "paths", "type": "object", "description": "AVA searches your entire project for `*.js`, `*.cjs`, `*.mjs` and `*.ts` files (or other extensions you've configured). It will ignore such files found in the `rewritePaths` targets (e.g. `build/`). If you use more specific paths, for instance `build/main/`, you may need to change AVA's `files` configuration to ignore other directories. Paths are relative to your project directory", "patternProperties": {"/$": {"type": "string", "pattern": "/$"}}}, "compile": {"enum": [false, "tsc"], "default": false, "description": "If `false`, AVA will assume you have already compiled your project. If set to `'tsc'`, AVA will run the TypeScript compiler before running your tests. This can be inefficient when using AVA in watch mode"}}}}, "title": "AVA Config Schema", "type": "object"}