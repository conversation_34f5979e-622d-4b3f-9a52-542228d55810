{"$schema": "http://json-schema.org/draft-07/schema#", "title": "JSON schema for NPM package.json files", "definitions": {"person": {"description": "A person who has been involved in creating or maintaining this package.", "type": ["object", "string"], "required": ["name"], "properties": {"name": {"type": "string"}, "url": {"type": "string", "format": "uri"}, "email": {"type": "string", "format": "email"}}}, "dependency": {"description": "Dependencies are specified with a simple hash of package name to version range. The version range is a string which has one or more space-separated descriptors. Dependencies can also be identified with a tarball or git URL.", "type": "object", "additionalProperties": {"type": "string"}}, "devDependency": {"description": "Specifies dependencies that are required for the development and testing of the project. These dependencies are not needed in the production environment.", "type": "object", "additionalProperties": {"type": "string"}}, "optionalDependency": {"description": "Specifies dependencies that are optional for your project. These dependencies are attempted to be installed during the npm install process, but if they fail to install, the installation process will not fail.", "type": "object", "additionalProperties": {"type": "string"}}, "peerDependency": {"description": "Specifies dependencies that are required by the package but are expected to be provided by the consumer of the package.", "type": "object", "additionalProperties": {"type": "string"}}, "peerDependencyMeta": {"description": "When a user installs your package, warnings are emitted if packages specified in \"peerDependencies\" are not already installed. The \"peerDependenciesMeta\" field serves to provide more information on how your peer dependencies are utilized. Most commonly, it allows peer dependencies to be marked as optional. Metadata for this field is specified with a simple hash of the package name to a metadata object.", "type": "object", "additionalProperties": {"type": "object", "additionalProperties": true, "properties": {"optional": {"description": "Specifies that this peer dependency is optional and should not be installed automatically.", "type": "boolean"}}}}, "license": {"anyOf": [{"type": "string"}, {"enum": ["AGPL-3.0-only", "Apache-2.0", "BSD-2-<PERSON><PERSON>", "BSD-3-<PERSON><PERSON>", "BSL-1.0", "CC0-1.0", "CDDL-1.0", "CDDL-1.1", "EPL-1.0", "EPL-2.0", "GPL-2.0-only", "GPL-3.0-only", "ISC", "LGPL-2.0-only", "LGPL-2.1-only", "LGPL-2.1-or-later", "LGPL-3.0-only", "LGPL-3.0-or-later", "MIT", "MPL-2.0", "MS-PL", "UNLICENSED"]}]}, "scriptsInstallAfter": {"description": "Run AFTER the package is installed.", "type": "string", "x-intellij-language-injection": "<PERSON> Script"}, "scriptsPublishAfter": {"description": "Run AFTER the package is published.", "type": "string", "x-intellij-language-injection": "<PERSON> Script"}, "scriptsRestart": {"description": "Run by the 'npm restart' command. Note: 'npm restart' will run the stop and start scripts if no restart script is provided.", "type": "string", "x-intellij-language-injection": "<PERSON> Script"}, "scriptsStart": {"description": "Run by the 'npm start' command.", "type": "string", "x-intellij-language-injection": "<PERSON> Script"}, "scriptsStop": {"description": "Run by the 'npm stop' command.", "type": "string", "x-intellij-language-injection": "<PERSON> Script"}, "scriptsTest": {"description": "Run by the 'npm test' command.", "type": "string", "x-intellij-language-injection": "<PERSON> Script"}, "scriptsUninstallBefore": {"description": "Run BEFORE the package is uninstalled.", "type": "string", "x-intellij-language-injection": "<PERSON> Script"}, "scriptsVersionBefore": {"description": "Run BEFORE bump the package version.", "type": "string", "x-intellij-language-injection": "<PERSON> Script"}, "packageExportsEntryPath": {"type": ["string", "null"], "description": "The module path that is resolved when this specifier is imported. Set to `null` to disallow importing this module.", "pattern": "^\\./"}, "packageExportsEntryObject": {"type": "object", "description": "Used to specify conditional exports, note that Conditional exports are unsupported in older environments, so it's recommended to use the fallback array option if support for those environments is a concern.", "properties": {"require": {"$ref": "#/definitions/packageExportsEntryOrFallback", "description": "The module path that is resolved when this specifier is imported as a CommonJS module using the `require(...)` function."}, "import": {"$ref": "#/definitions/packageExportsEntryOrFallback", "description": "The module path that is resolved when this specifier is imported as an ECMAScript module using an `import` declaration or the dynamic `import(...)` function."}, "node": {"$ref": "#/definitions/packageExportsEntryOrFallback", "description": "The module path that is resolved when this environment is Node.js."}, "default": {"$ref": "#/definitions/packageExportsEntryOrFallback", "description": "The module path that is resolved when no other export type matches."}, "types": {"$ref": "#/definitions/packageExportsEntryOrFallback", "description": "The module path that is resolved for TypeScript types when this specifier is imported. Should be listed before other conditions. Additionally, versioned \"types\" condition in the form \"types@{selector}\" are supported."}}, "patternProperties": {"^[^.0-9]+$": {"$ref": "#/definitions/packageExportsEntryOrFallback", "description": "The module path that is resolved when this environment matches the property name."}, "^types@.+$": {"$ref": "#/definitions/packageExportsEntryOrFallback", "description": "The module path that is resolved for TypeScript types when this specifier is imported. Should be listed before other conditions. Additionally, versioned \"types\" condition in the form \"types@{selector}\" are supported."}}, "additionalProperties": false}, "packageExportsEntry": {"oneOf": [{"$ref": "#/definitions/packageExportsEntryPath"}, {"$ref": "#/definitions/packageExportsEntryObject"}]}, "packageExportsFallback": {"type": "array", "description": "Used to allow fallbacks in case this environment doesn't support the preceding entries.", "items": {"$ref": "#/definitions/packageExportsEntry"}}, "packageExportsEntryOrFallback": {"oneOf": [{"$ref": "#/definitions/packageExportsEntry"}, {"$ref": "#/definitions/packageExportsFallback"}]}, "packageImportsEntryPath": {"type": ["string", "null"], "description": "The module path that is resolved when this specifier is imported. Set to `null` to disallow importing this module."}, "packageImportsEntryObject": {"type": "object", "description": "Used to specify conditional exports, note that Conditional exports are unsupported in older environments, so it's recommended to use the fallback array option if support for those environments is a concern.", "properties": {"require": {"$ref": "#/definitions/packageImportsEntryOrFallback", "description": "The module path that is resolved when this specifier is imported as a CommonJS module using the `require(...)` function."}, "import": {"$ref": "#/definitions/packageImportsEntryOrFallback", "description": "The module path that is resolved when this specifier is imported as an ECMAScript module using an `import` declaration or the dynamic `import(...)` function."}, "node": {"$ref": "#/definitions/packageImportsEntryOrFallback", "description": "The module path that is resolved when this environment is Node.js."}, "default": {"$ref": "#/definitions/packageImportsEntryOrFallback", "description": "The module path that is resolved when no other export type matches."}, "types": {"$ref": "#/definitions/packageImportsEntryOrFallback", "description": "The module path that is resolved for TypeScript types when this specifier is imported. Should be listed before other conditions. Additionally, versioned \"types\" condition in the form \"types@{selector}\" are supported."}}, "patternProperties": {"^[^.0-9]+$": {"$ref": "#/definitions/packageImportsEntryOrFallback", "description": "The module path that is resolved when this environment matches the property name."}, "^types@.+$": {"$ref": "#/definitions/packageImportsEntryOrFallback", "description": "The module path that is resolved for TypeScript types when this specifier is imported. Should be listed before other conditions. Additionally, versioned \"types\" condition in the form \"types@{selector}\" are supported."}}, "additionalProperties": false}, "packageImportsEntry": {"oneOf": [{"$ref": "#/definitions/packageImportsEntryPath"}, {"$ref": "#/definitions/packageImportsEntryObject"}]}, "packageImportsFallback": {"type": "array", "description": "Used to allow fallbacks in case this environment doesn't support the preceding entries.", "items": {"$ref": "#/definitions/packageImportsEntry"}}, "packageImportsEntryOrFallback": {"oneOf": [{"$ref": "#/definitions/packageImportsEntry"}, {"$ref": "#/definitions/packageImportsFallback"}]}, "fundingUrl": {"type": "string", "format": "uri", "description": "URL to a website with details about how to fund the package."}, "fundingWay": {"type": "object", "description": "Used to inform about ways to help fund development of the package.", "properties": {"url": {"$ref": "#/definitions/fundingUrl"}, "type": {"type": "string", "description": "The type of funding or the platform through which funding can be provided, e.g. patreon, opencollective, tidelift or github."}}, "additionalProperties": false, "required": ["url"]}, "devEngineDependency": {"description": "Specifies requirements for development environment components such as operating systems, runtimes, or package managers. Used to ensure consistent development environments across the team.", "type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "The name of the dependency, with allowed values depending on the parent field"}, "version": {"type": "string", "description": "The version range for the dependency"}, "onFail": {"type": "string", "enum": ["ignore", "warn", "error", "download"], "description": "What action to take if validation fails"}}}}, "type": "object", "patternProperties": {"^_": {"description": "Any property starting with _ is valid.", "tsType": "any"}}, "properties": {"name": {"description": "The name of the package.", "type": "string", "maxLength": 214, "minLength": 1, "pattern": "^(?:(?:@(?:[a-z0-9-*~][a-z0-9-*._~]*)?/[a-z0-9-._~])|[a-z0-9-~])[a-z0-9-._~]*$"}, "version": {"description": "Version must be parsable by node-semver, which is bundled with npm as a dependency.", "type": "string"}, "description": {"description": "This helps people discover your package, as it's listed in 'npm search'.", "type": "string"}, "keywords": {"description": "This helps people discover your package as it's listed in 'npm search'.", "type": "array", "items": {"type": "string"}}, "homepage": {"description": "The url to the project homepage.", "type": "string"}, "bugs": {"description": "The url to your project's issue tracker and / or the email address to which issues should be reported. These are helpful for people who encounter issues with your package.", "type": ["object", "string"], "properties": {"url": {"type": "string", "description": "The url to your project's issue tracker.", "format": "uri"}, "email": {"type": "string", "description": "The email address to which issues should be reported.", "format": "email"}}}, "license": {"$ref": "#/definitions/license", "description": "You should specify a license for your package so that people know how they are permitted to use it, and any restrictions you're placing on it."}, "licenses": {"description": "DEPRECATED: Instead, use SPDX expressions, like this: { \"license\": \"ISC\" } or { \"license\": \"(MIT OR Apache-2.0)\" } see: 'https://docs.npmjs.com/files/package.json#license'.", "type": "array", "items": {"type": "object", "properties": {"type": {"$ref": "#/definitions/license"}, "url": {"type": "string", "format": "uri"}}}}, "author": {"$ref": "#/definitions/person"}, "contributors": {"description": "A list of people who contributed to this package.", "type": "array", "items": {"$ref": "#/definitions/person"}}, "maintainers": {"description": "A list of people who maintains this package.", "type": "array", "items": {"$ref": "#/definitions/person"}}, "files": {"description": "The 'files' field is an array of files to include in your project. If you name a folder in the array, then it will also include the files inside that folder.", "type": "array", "items": {"type": "string"}}, "main": {"description": "The main field is a module ID that is the primary entry point to your program.", "type": "string"}, "exports": {"description": "The \"exports\" field is used to restrict external access to non-exported module files, also enables a module to import itself using \"name\".", "oneOf": [{"$ref": "#/definitions/packageExportsEntryPath", "description": "The module path that is resolved when the module specifier matches \"name\", shadows the \"main\" field."}, {"type": "object", "properties": {".": {"$ref": "#/definitions/packageExportsEntryOrFallback", "description": "The module path that is resolved when the module specifier matches \"name\", shadows the \"main\" field."}}, "patternProperties": {"^\\./.+": {"$ref": "#/definitions/packageExportsEntryOrFallback", "description": "The module path prefix that is resolved when the module specifier starts with \"name/\", set to \"./*\" to allow external modules to import any subpath."}}, "additionalProperties": false}, {"$ref": "#/definitions/packageExportsEntryObject", "description": "The module path that is resolved when the module specifier matches \"name\", shadows the \"main\" field."}, {"$ref": "#/definitions/packageExportsFallback", "description": "The module path that is resolved when the module specifier matches \"name\", shadows the \"main\" field."}]}, "imports": {"description": "The \"imports\" field is used to create private mappings that only apply to import specifiers from within the package itself.", "type": "object", "patternProperties": {"^#.+$": {"$ref": "#/definitions/packageImportsEntryOrFallback", "description": "The module path that is resolved when this environment matches the property name."}}, "additionalProperties": false}, "bin": {"type": ["string", "object"], "additionalProperties": {"type": "string"}}, "type": {"description": "When set to \"module\", the type field allows a package to specify all .js files within are ES modules. If the \"type\" field is omitted or set to \"commonjs\", all .js files are treated as CommonJS.", "type": "string", "enum": ["commonjs", "module"], "default": "commonjs"}, "types": {"description": "Set the types property to point to your bundled declaration file.", "type": "string"}, "typings": {"description": "Note that the \"typings\" field is synonymous with \"types\", and could be used as well.", "type": "string"}, "typesVersions": {"description": "The \"typesVersions\" field is used since TypeScript 3.1 to support features that were only made available in newer TypeScript versions.", "type": "object", "additionalProperties": {"description": "Contains overrides for the TypeScript version that matches the version range matching the property key.", "type": "object", "properties": {"*": {"description": "Maps all file paths to the file paths specified in the array.", "type": "array", "items": {"type": "string", "pattern": "^[^*]*(?:\\*[^*]*)?$"}}}, "patternProperties": {"^[^*]+$": {"description": "Maps the file path matching the property key to the file paths specified in the array.", "type": "array", "items": {"type": "string"}}, "^[^*]*\\*[^*]*$": {"description": "Maps file paths matching the pattern specified in property key to file paths specified in the array.", "type": "array", "items": {"type": "string", "pattern": "^[^*]*(?:\\*[^*]*)?$"}}}, "additionalProperties": false}}, "man": {"type": ["array", "string"], "description": "Specify either a single file or an array of filenames to put in place for the man program to find.", "items": {"type": "string"}}, "directories": {"type": "object", "properties": {"bin": {"description": "If you specify a 'bin' directory, then all the files in that folder will be used as the 'bin' hash.", "type": "string"}, "doc": {"description": "Put markdown files in here. Eventually, these will be displayed nicely, maybe, someday.", "type": "string"}, "example": {"description": "Put example scripts in here. Someday, it might be exposed in some clever way.", "type": "string"}, "lib": {"description": "Tell people where the bulk of your library is. Nothing special is done with the lib folder in any way, but it's useful meta info.", "type": "string"}, "man": {"description": "A folder that is full of man pages. Sugar to generate a 'man' array by walking the folder.", "type": "string"}, "test": {"type": "string"}}}, "repository": {"description": "Specify the place where your code lives. This is helpful for people who want to contribute.", "type": ["object", "string"], "properties": {"type": {"type": "string"}, "url": {"type": "string"}, "directory": {"type": "string"}}}, "funding": {"oneOf": [{"$ref": "#/definitions/fundingUrl"}, {"$ref": "#/definitions/fundingWay"}, {"type": "array", "items": {"oneOf": [{"$ref": "#/definitions/fundingUrl"}, {"$ref": "#/definitions/fundingWay"}]}, "minItems": 1, "uniqueItems": true}]}, "scripts": {"description": "The 'scripts' member is an object hash of script commands that are run at various times in the lifecycle of your package. The key is the lifecycle event, and the value is the command to run at that point.", "type": "object", "properties": {"lint": {"type": "string", "description": "Run code quality tools, e.g. ESLint, TSLint, etc."}, "prepublish": {"type": "string", "description": "Run BEFORE the package is published (Also run on local npm install without any arguments)."}, "prepare": {"type": "string", "description": "Runs BEFORE the package is packed, i.e. during \"npm publish\" and \"npm pack\", and on local \"npm install\" without any arguments. This is run AFTER \"prepublish\", but BEFORE \"prepublishOnly\"."}, "prepublishOnly": {"type": "string", "description": "Run BEFORE the package is prepared and packed, ONLY on npm publish."}, "prepack": {"type": "string", "description": "run BEFORE a tarball is packed (on npm pack, npm publish, and when installing git dependencies)."}, "postpack": {"type": "string", "description": "Run AFTER the tarball has been generated and moved to its final destination."}, "publish": {"type": "string", "description": "Publishes a package to the registry so that it can be installed by name. See https://docs.npmjs.com/cli/v8/commands/npm-publish"}, "postpublish": {"$ref": "#/definitions/scriptsPublishAfter"}, "preinstall": {"type": "string", "description": "Run BEFORE the package is installed."}, "install": {"$ref": "#/definitions/scriptsInstallAfter"}, "postinstall": {"$ref": "#/definitions/scriptsInstallAfter"}, "preuninstall": {"$ref": "#/definitions/scriptsUninstallBefore"}, "uninstall": {"$ref": "#/definitions/scriptsUninstallBefore"}, "postuninstall": {"type": "string", "description": "Run AFTER the package is uninstalled."}, "preversion": {"$ref": "#/definitions/scriptsVersionBefore"}, "version": {"$ref": "#/definitions/scriptsVersionBefore"}, "postversion": {"type": "string", "description": "Run AFTER bump the package version."}, "pretest": {"$ref": "#/definitions/scriptsTest"}, "test": {"$ref": "#/definitions/scriptsTest"}, "posttest": {"$ref": "#/definitions/scriptsTest"}, "prestop": {"$ref": "#/definitions/scriptsStop"}, "stop": {"$ref": "#/definitions/scriptsStop"}, "poststop": {"$ref": "#/definitions/scriptsStop"}, "prestart": {"$ref": "#/definitions/scriptsStart"}, "start": {"$ref": "#/definitions/scriptsStart"}, "poststart": {"$ref": "#/definitions/scriptsStart"}, "prerestart": {"$ref": "#/definitions/scriptsRestart"}, "restart": {"$ref": "#/definitions/scriptsRestart"}, "postrestart": {"$ref": "#/definitions/scriptsRestart"}, "serve": {"type": "string", "description": "Start dev server to serve application files"}}, "additionalProperties": {"type": "string", "tsType": "string | undefined", "x-intellij-language-injection": "<PERSON> Script"}}, "config": {"description": "A 'config' hash can be used to set configuration parameters used in package scripts that persist across upgrades.", "type": "object", "additionalProperties": true}, "dependencies": {"$ref": "#/definitions/dependency"}, "devDependencies": {"$ref": "#/definitions/devDependency"}, "optionalDependencies": {"$ref": "#/definitions/optionalDependency"}, "peerDependencies": {"$ref": "#/definitions/peerDependency"}, "peerDependenciesMeta": {"$ref": "#/definitions/peerDependencyMeta"}, "bundleDependencies": {"description": "Array of package names that will be bundled when publishing the package.", "oneOf": [{"type": "array", "items": {"type": "string"}}, {"type": "boolean"}]}, "bundledDependencies": {"description": "DEPRECATED: This field is honored, but \"bundleDependencies\" is the correct field name.", "oneOf": [{"type": "array", "items": {"type": "string"}}, {"type": "boolean"}]}, "resolutions": {"description": "Resolutions is used to support selective version resolutions using yarn, which lets you define custom package versions or ranges inside your dependencies. For npm, use overrides instead. See: https://classic.yarnpkg.com/en/docs/selective-version-resolutions", "type": "object"}, "overrides": {"description": "Overrides is used to support selective version overrides using npm, which lets you define custom package versions or ranges inside your dependencies. For yarn, use resolutions instead. See: https://docs.npmjs.com/cli/v9/configuring-npm/package-json#overrides", "type": "object"}, "packageManager": {"description": "Defines which package manager is expected to be used when working on the current project. This field is currently experimental and needs to be opted-in; see https://nodejs.org/api/corepack.html", "type": "string", "pattern": "(npm|pnpm|yarn|bun)@\\d+\\.\\d+\\.\\d+(-.+)?"}, "engines": {"type": "object", "properties": {"node": {"type": "string"}}, "additionalProperties": {"type": "string"}}, "volta": {"description": "Defines which tools and versions are expected to be used when Volta is installed.", "type": "object", "properties": {"extends": {"description": "The value of that entry should be a path to another JSON file which also has a \"volta\" section", "type": "string"}}, "patternProperties": {"(node|npm|pnpm|yarn)": {"type": "string"}}}, "engineStrict": {"type": "boolean"}, "os": {"description": "Specify which operating systems your module will run on.", "type": "array", "items": {"type": "string"}}, "cpu": {"description": "Specify that your code only runs on certain cpu architectures.", "type": "array", "items": {"type": "string"}}, "devEngines": {"description": "Define the runtime and package manager for developing the current project.", "type": "object", "properties": {"os": {"oneOf": [{"$ref": "#/definitions/devEngineDependency"}, {"type": "array", "items": {"$ref": "#/definitions/devEngineDependency"}}], "description": "Specifies which operating systems are supported for development"}, "cpu": {"oneOf": [{"$ref": "#/definitions/devEngineDependency"}, {"type": "array", "items": {"$ref": "#/definitions/devEngineDependency"}}], "description": "Specifies which CPU architectures are supported for development"}, "libc": {"oneOf": [{"$ref": "#/definitions/devEngineDependency"}, {"type": "array", "items": {"$ref": "#/definitions/devEngineDependency"}}], "description": "Specifies which C standard libraries are supported for development"}, "runtime": {"oneOf": [{"$ref": "#/definitions/devEngineDependency"}, {"type": "array", "items": {"$ref": "#/definitions/devEngineDependency"}}], "description": "Specifies which JavaScript runtimes (like Node.js, <PERSON><PERSON>, <PERSON>un) are supported for development. Values should use WinterCG Runtime Keys (see https://runtime-keys.proposal.wintercg.org/)"}, "packageManager": {"oneOf": [{"$ref": "#/definitions/devEngineDependency"}, {"type": "array", "items": {"$ref": "#/definitions/devEngineDependency"}}], "description": "Specifies which package managers are supported for development"}}}, "preferGlobal": {"type": "boolean", "description": "DEPRECATED: This option used to trigger an npm warning, but it will no longer warn. It is purely there for informational purposes. It is now recommended that you install any binaries as local devDependencies wherever possible."}, "private": {"description": "If set to true, then npm will refuse to publish it.", "oneOf": [{"type": "boolean"}, {"enum": ["false", "true"]}]}, "publishConfig": {"type": "object", "properties": {"access": {"type": "string", "enum": ["public", "restricted"]}, "tag": {"type": "string"}, "registry": {"type": "string", "format": "uri"}, "provenance": {"type": "boolean"}}, "additionalProperties": true}, "dist": {"type": "object", "properties": {"shasum": {"type": "string"}, "tarball": {"type": "string"}}}, "readme": {"type": "string"}, "module": {"description": "An ECMAScript module ID that is the primary entry point to your program.", "type": "string"}, "esnext": {"description": "A module ID with untranspiled code that is the primary entry point to your program.", "type": ["string", "object"], "properties": {"main": {"type": "string"}, "browser": {"type": "string"}}, "additionalProperties": {"type": "string"}}, "workspaces": {"description": "Allows packages within a directory to depend on one another using direct linking of local files. Additionally, dependencies within a workspace are hoisted to the workspace root when possible to reduce duplication. Note: It's also a good idea to set \"private\" to true when using this feature.", "anyOf": [{"type": "array", "description": "Workspace package paths. Glob patterns are supported.", "items": {"type": "string"}}, {"type": "object", "properties": {"packages": {"type": "array", "description": "Workspace package paths. Glob patterns are supported.", "items": {"type": "string"}}, "nohoist": {"type": "array", "description": "Packages to block from hoisting to the workspace root. Currently only supported in Yarn only.", "items": {"type": "string"}}}}]}, "jspm": {"$ref": "#"}, "eslintConfig": {"$ref": "https://json.schemastore.org/eslintrc.json"}, "prettier": {"$ref": "https://json.schemastore.org/prettierrc.json"}, "stylelint": {"$ref": "https://json.schemastore.org/stylelintrc.json"}, "ava": {"$ref": "https://json.schemastore.org/ava.json"}, "release": {"$ref": "https://json.schemastore.org/semantic-release.json"}, "jscpd": {"$ref": "https://json.schemastore.org/jscpd.json"}, "pnpm": {"description": "Defines pnpm specific configuration.", "type": "object", "properties": {"overrides": {"description": "Used to override any dependency in the dependency graph.", "type": "object"}, "packageExtensions": {"description": "Used to extend the existing package definitions with additional information.", "type": "object", "patternProperties": {"^.+$": {"type": "object", "properties": {"dependencies": {"$ref": "#/definitions/dependency"}, "optionalDependencies": {"$ref": "#/definitions/optionalDependency"}, "peerDependencies": {"$ref": "#/definitions/peerDependency"}, "peerDependenciesMeta": {"$ref": "#/definitions/peerDependencyMeta"}}, "additionalProperties": false}}, "additionalProperties": false}, "peerDependencyRules": {"type": "object", "properties": {"ignoreMissing": {"description": "pnpm will not print warnings about missing peer dependencies from this list.", "type": "array", "items": {"type": "string"}}, "allowedVersions": {"description": "Unmet peer dependency warnings will not be printed for peer dependencies of the specified range.", "type": "object"}, "allowAny": {"description": "Any peer dependency matching the pattern will be resolved from any version, regardless of the range specified in \"peerDependencies\".", "type": "array", "items": {"type": "string"}}}, "additionalProperties": false}, "neverBuiltDependencies": {"description": "A list of dependencies to run builds for.", "type": "array", "items": {"type": "string"}}, "onlyBuiltDependencies": {"description": "A list of package names that are allowed to be executed during installation.", "type": "array", "items": {"type": "string"}}, "onlyBuiltDependenciesFile": {"description": "Specifies a JSON file that lists the only packages permitted to run installation scripts during the pnpm install process.", "type": "string"}, "ignoredBuiltDependencies": {"description": "A list of package names that should not be built during installation.", "type": "array", "items": {"type": "string"}}, "allowedDeprecatedVersions": {"description": "A list of deprecated versions that the warnings are suppressed.", "type": "object"}, "patchedDependencies": {"description": "A list of dependencies that are patched.", "type": "object"}, "allowNonAppliedPatches": {"description": "When true, installation won't fail if some of the patches from the \"patchedDependencies\" field were not applied.", "type": "boolean"}, "updateConfig": {"type": "object", "properties": {"ignoreDependencies": {"description": "A list of packages that should be ignored when running \"pnpm outdated\" or \"pnpm update --latest\".", "type": "array", "items": {"type": "string"}}}, "additionalProperties": false}, "configDependencies": {"type": "object", "description": "Configurational dependencies are installed before all the other types of dependencies (before 'dependencies', 'devDependencies', 'optionalDependencies')."}, "auditConfig": {"type": "object", "properties": {"ignoreCves": {"description": "A list of CVE IDs that will be ignored by \"pnpm audit\".", "type": "array", "items": {"type": "string", "pattern": "^CVE-\\d{4}-\\d{4,7}$"}}, "ignoreGhsas": {"description": "A list of GHSA Codes that will be ignored by \"pnpm audit\".", "type": "array", "items": {"type": "string", "pattern": "^GHSA(-[23456789cfghjmpqrvwx]{4}){3}$"}}}, "additionalProperties": false}, "requiredScripts": {"description": "A list of scripts that must exist in each project.", "type": "array", "items": {"type": "string"}}, "supportedArchitectures": {"description": "Specifies architectures for which you'd like to install optional dependencies, even if they don't match the architecture of the system running the install.", "type": "object", "properties": {"os": {"type": "array", "items": {"type": "string"}}, "cpu": {"type": "array", "items": {"type": "string"}}, "libc": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}, "ignoredOptionalDependencies": {"description": "A list of optional dependencies that the install should be skipped.", "type": "array", "items": {"type": "string"}}, "executionEnv": {"type": "object", "properties": {"nodeVersion": {"description": "Specifies which exact Node.js version should be used for the project's runtime.", "type": "string"}}, "additionalProperties": false}}, "additionalProperties": false}, "stackblitz": {"description": "Defines the StackBlitz configuration for the project.", "type": "object", "properties": {"installDependencies": {"description": "StackBlitz automatically installs npm dependencies when opening a project.", "type": "boolean"}, "startCommand": {"description": "A terminal command to be executed when opening the project, after installing npm dependencies.", "type": ["string", "boolean"]}, "compileTrigger": {"description": "The compileTrigger option controls how file changes in the editor are written to the WebContainers in-memory filesystem. ", "oneOf": [{"type": "string", "enum": ["auto", "keystroke", "save"]}]}, "env": {"description": "A map of default environment variables that will be set in each top-level shell process.", "type": "object"}}, "additionalProperties": false}}, "anyOf": [{"type": "object", "not": {"required": ["bundledDependencies", "bundleDependencies"]}}, {"type": "object", "not": {"required": ["bundleDependencies"]}, "required": ["bundledDependencies"]}, {"type": "object", "not": {"required": ["bundledDependencies"]}, "required": ["bundleDependencies"]}], "$id": "https://json.schemastore.org/package.json"}