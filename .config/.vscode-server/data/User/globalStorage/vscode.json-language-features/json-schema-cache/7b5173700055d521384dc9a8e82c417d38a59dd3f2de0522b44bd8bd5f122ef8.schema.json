{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://json.schemastore.org/prettierrc.json", "definitions": {"optionsDefinition": {"type": "object", "properties": {"arrowParens": {"description": "Include parentheses around a sole arrow function parameter.", "default": "always", "oneOf": [{"enum": ["always"], "description": "Always include parens. Example: `(x) => x`"}, {"enum": ["avoid"], "description": "Omit parens when possible. Example: `x => x`"}]}, "bracketSameLine": {"description": "Put > of opening tags on the last line instead of on a new line.", "default": false, "type": "boolean"}, "bracketSpacing": {"description": "Print spaces between brackets.", "default": true, "type": "boolean"}, "cursorOffset": {"description": "Print (to stderr) where a cursor at the given position would move to after formatting.", "default": -1, "type": "integer"}, "embeddedLanguageFormatting": {"description": "Control how Prettier formats quoted code embedded in the file.", "default": "auto", "oneOf": [{"enum": ["auto"], "description": "Format embedded code if <PERSON><PERSON><PERSON> can automatically identify it."}, {"enum": ["off"], "description": "Never automatically format embedded code."}]}, "endOfLine": {"description": "Which end of line characters to apply.", "default": "lf", "oneOf": [{"enum": ["lf"], "description": "Line Feed only (\\n), common on Linux and macOS as well as inside git repos"}, {"enum": ["crlf"], "description": "Carriage Return + Line Feed characters (\\r\\n), common on Windows"}, {"enum": ["cr"], "description": "Carriage Return character only (\\r), used very rarely"}, {"enum": ["auto"], "description": "Maintain existing\n(mixed values within one file are normalised by looking at what's used after the first line)"}]}, "experimentalOperatorPosition": {"description": "Where to print operators when binary expressions wrap lines.", "default": "end", "oneOf": [{"enum": ["start"], "description": "Print operators at the start of new lines."}, {"enum": ["end"], "description": "Print operators at the end of previous lines."}]}, "experimentalTernaries": {"description": "Use curious ternaries, with the question mark after the condition.", "default": false, "type": "boolean"}, "filepath": {"description": "Specify the input filepath. This will be used to do parser inference.", "type": "string"}, "htmlWhitespaceSensitivity": {"description": "How to handle whitespaces in HTML.", "default": "css", "oneOf": [{"enum": ["css"], "description": "Respect the default value of CSS display property."}, {"enum": ["strict"], "description": "Whitespaces are considered sensitive."}, {"enum": ["ignore"], "description": "Whitespaces are considered insensitive."}]}, "insertPragma": {"description": "Insert @format pragma into file's first docblock comment.", "default": false, "type": "boolean"}, "jsxSingleQuote": {"description": "Use single quotes in JSX.", "default": false, "type": "boolean"}, "objectWrap": {"description": "How to wrap object literals.", "default": "preserve", "oneOf": [{"enum": ["preserve"], "description": "Keep as multi-line, if there is a newline between the opening brace and first property."}, {"enum": ["collapse"], "description": "Fit to a single line when possible."}]}, "parser": {"description": "Which parser to use.", "anyOf": [{"enum": ["flow"], "description": "Flow"}, {"enum": ["babel"], "description": "JavaScript"}, {"enum": ["babel-flow"], "description": "Flow"}, {"enum": ["babel-ts"], "description": "TypeScript"}, {"enum": ["typescript"], "description": "TypeScript"}, {"enum": ["acorn"], "description": "JavaScript"}, {"enum": ["espree"], "description": "JavaScript"}, {"enum": ["<PERSON><PERSON>h"], "description": "JavaScript"}, {"enum": ["css"], "description": "CSS"}, {"enum": ["less"], "description": "Less"}, {"enum": ["scss"], "description": "SCSS"}, {"enum": ["json"], "description": "JSON"}, {"enum": ["json5"], "description": "JSON5"}, {"enum": ["jsonc"], "description": "JSON with Comments"}, {"enum": ["json-stringify"], "description": "JSON.stringify"}, {"enum": ["graphql"], "description": "GraphQL"}, {"enum": ["markdown"], "description": "<PERSON><PERSON>"}, {"enum": ["mdx"], "description": "MDX"}, {"enum": ["vue"], "description": "<PERSON><PERSON>"}, {"enum": ["yaml"], "description": "YAML"}, {"enum": ["glimmer"], "description": "Ember / Handlebars"}, {"enum": ["html"], "description": "HTML"}, {"enum": ["angular"], "description": "Angular"}, {"enum": ["lwc"], "description": "Lightning Web Components"}, {"type": "string", "description": "Custom parser"}]}, "plugins": {"description": "Add a plugin. Multiple plugins can be passed as separate `--plugin`s.", "default": [], "type": "array", "items": {"type": "string"}}, "printWidth": {"description": "The line length where <PERSON><PERSON><PERSON> will try wrap.", "default": 80, "type": "integer"}, "proseWrap": {"description": "How to wrap prose.", "default": "preserve", "oneOf": [{"enum": ["always"], "description": "Wrap prose if it exceeds the print width."}, {"enum": ["never"], "description": "Do not wrap prose."}, {"enum": ["preserve"], "description": "Wrap prose as-is."}]}, "quoteProps": {"description": "Change when properties in objects are quoted.", "default": "as-needed", "oneOf": [{"enum": ["as-needed"], "description": "Only add quotes around object properties where required."}, {"enum": ["consistent"], "description": "If at least one property in an object requires quotes, quote all properties."}, {"enum": ["preserve"], "description": "Respect the input use of quotes in object properties."}]}, "rangeEnd": {"description": "Format code ending at a given character offset (exclusive).\nThe range will extend forwards to the end of the selected statement.", "default": null, "type": "integer"}, "rangeStart": {"description": "Format code starting at a given character offset.\nThe range will extend backwards to the start of the first line containing the selected statement.", "default": 0, "type": "integer"}, "requirePragma": {"description": "Require either '@prettier' or '@format' to be present in the file's first docblock comment\nin order for it to be formatted.", "default": false, "type": "boolean"}, "semi": {"description": "Print semicolons.", "default": true, "type": "boolean"}, "singleAttributePerLine": {"description": "Enforce single attribute per line in HTML, Vue and JSX.", "default": false, "type": "boolean"}, "singleQuote": {"description": "Use single quotes instead of double quotes.", "default": false, "type": "boolean"}, "tabWidth": {"description": "Number of spaces per indentation level.", "default": 2, "type": "integer"}, "trailingComma": {"description": "Print trailing commas wherever possible when multi-line.", "default": "all", "oneOf": [{"enum": ["all"], "description": "Trailing commas wherever possible (including function arguments)."}, {"enum": ["es5"], "description": "Trailing commas where valid in ES5 (objects, arrays, etc.)"}, {"enum": ["none"], "description": "No trailing commas."}]}, "useTabs": {"description": "Indent with tabs instead of spaces.", "default": false, "type": "boolean"}, "vueIndentScriptAndStyle": {"description": "Indent script and style tags in Vue files.", "default": false, "type": "boolean"}}}, "overridesDefinition": {"type": "object", "properties": {"overrides": {"type": "array", "description": "Provide a list of patterns to override prettier configuration.", "items": {"type": "object", "required": ["files"], "properties": {"files": {"description": "Include these files in this override.", "oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "excludeFiles": {"description": "Exclude these files from this override.", "oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "options": {"$ref": "#/definitions/optionsDefinition", "type": "object", "description": "The options to apply for this override."}}, "additionalProperties": false}}}}}, "oneOf": [{"type": "object", "allOf": [{"$ref": "#/definitions/optionsDefinition"}, {"$ref": "#/definitions/overridesDefinition"}]}, {"type": "string"}], "title": "<PERSON><PERSON>a for .prettierrc"}