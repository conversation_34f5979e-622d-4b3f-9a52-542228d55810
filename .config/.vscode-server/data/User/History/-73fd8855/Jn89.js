import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'
import fs from 'fs'
import path from 'path'

// Plugin per verificare che i file siano stati generati correttamente
function verifyBuildAssets() {
  return {
    name: 'verify-build-assets',
    writeBundle(options, bundle) {
      const assets = Object.keys(bundle).filter(key =>
        key.endsWith('.js') || key.endsWith('.css')
      )

      const jsFile = assets.find(asset => asset === 'app.js')
      const cssFile = assets.find(asset => asset === 'index.css')
      const vendorFile = assets.find(asset => asset === 'vendor.js')

      console.log('✅ Build assets generati:')
      console.log(`   - CSS: ${cssFile || 'MANCANTE'}`)
      console.log(`   - App JS: ${jsFile || 'MANCANTE'}`)
      console.log(`   - Vendor JS: ${vendorFile || 'MANCANTE'}`)
      console.log('✅ Template Flask deve avere questi link:')
      console.log(`   <link rel="stylesheet" href="{{ url_for('static', filename='dist/assets/index.css') }}">`)
      console.log(`   <script type="module" src="{{ url_for('static', filename='dist/assets/app.js') }}"></script>`)
      console.log(`   <link rel="modulepreload" href="{{ url_for('static', filename='dist/assets/vendor.js') }}">`)

      // Non modifichiamo più il template automaticamente
    }
  }
}

export default defineConfig({
  plugins: [vue(), verifyBuildAssets()],
  build: {
    outDir: '../backend/static/dist',
    emptyOutDir: true,
    rollupOptions: {
      output: {
        entryFileNames: 'assets/app.js',
        chunkFileNames: 'assets/[name].js',
        assetFileNames: 'assets/[name].[ext]',
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia', 'axios', 'chart.js']
        }
      }
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true
      },
      '/auth': {
        target: 'http://localhost:5000',
        changeOrigin: true
      },
      '/admin': {
        target: 'http://localhost:5000',
        changeOrigin: true
      },
      '/dashboard': {
        target: 'http://localhost:5000',
        changeOrigin: true
      }
    }
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@components': fileURLToPath(new URL('./src/components', import.meta.url)),
      '@views': fileURLToPath(new URL('./src/views', import.meta.url)),
      '@stores': fileURLToPath(new URL('./src/stores', import.meta.url)),
      '@utils': fileURLToPath(new URL('./src/utils', import.meta.url))
    }
  }
})