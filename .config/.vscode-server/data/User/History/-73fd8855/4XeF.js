import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'
import fs from 'fs'
import path from 'path'

// Plugin per generare automaticamente il template Flask
function generateFlaskTemplate() {
  return {
    name: 'generate-flask-template',
    writeBundle(options, bundle) {
      const assets = Object.keys(bundle).filter(key =>
        key.endsWith('.js') || key.endsWith('.css')
      )

      const jsFile = assets.find(asset => asset === 'app.js')
      const cssFile = assets.find(asset => asset === 'index.css')
      const vendorFile = assets.find(asset => asset === 'vendor.js')

      const templateContent = `<!DOCTYPE html>
<html lang="it">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>DatPortal - Enterprise Intranet</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">

    <!-- Meta tags for SEO -->
    <meta name="description" content="DatPortal - Sistema di gestione progetti, task e risorse">
    <meta name="keywords" content="progetti, task, gestione, risorse, KPI, dashboard">
    <meta name="author" content="DatVinci">

    <!-- Open Graph meta tags -->
    <meta property="og:title" content="DatPortal">
    <meta property="og:description" content="Sistema di gestione progetti, task e risorse">
    <meta property="og:type" content="website">

    <!-- CSRF Token for API requests -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    ${jsFile ? `<script type="module" crossorigin src="{{ url_for('static', filename='dist/assets/${jsFile}') }}"></script>` : ''}
    ${vendorFile ? `<link rel="modulepreload" crossorigin href="{{ url_for('static', filename='dist/assets/${vendorFile}') }}">` : ''}
    ${cssFile ? `<link rel="stylesheet" crossorigin href="{{ url_for('static', filename='dist/assets/${cssFile}') }}">` : ''}
  </head>
  <body>
    <div id="app"></div>

    <!-- Global Configuration for Vue.js -->
    <script>
      // Global app configuration
      window.APP_CONFIG = {
        apiUrl: '/api',
        baseUrl: '{{ request.url_root }}',
        csrfToken: '{{ csrf_token() }}',
        user: {{ current_user.to_dict()|tojson if current_user.is_authenticated else 'null' }},
        isAuthenticated: {{ 'true' if current_user.is_authenticated else 'false' }},
        version: '1.0.0',
        environment: '{{ config.ENV }}',
        debug: {{ 'true' if config.DEBUG else 'false' }}
      };

      // La configurazione tenant verrà caricata dal frontend Vue.js

      // Global error handler
      window.addEventListener('error', function(event) {
        console.error('Global error:', event.error);
      });

      // Global unhandled promise rejection handler
      window.addEventListener('unhandledrejection', function(event) {
        console.error('Unhandled promise rejection:', event.reason);
      });
    </script>
  </body>
</html>`

      // Scrivi il template aggiornato
      const templatePath = path.resolve('../backend/templates/vue_app.html')
      fs.writeFileSync(templatePath, templateContent)

      console.log('✅ Template Flask aggiornato automaticamente con i nuovi asset paths')
    }
  }
}

export default defineConfig({
  plugins: [vue(), generateFlaskTemplate()],
  build: {
    outDir: '../backend/static/dist',
    emptyOutDir: true,
    rollupOptions: {
      output: {
        entryFileNames: 'assets/app.js',
        chunkFileNames: 'assets/[name].js',
        assetFileNames: 'assets/[name].[ext]',
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia', 'axios', 'chart.js']
        }
      }
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true
      },
      '/auth': {
        target: 'http://localhost:5000',
        changeOrigin: true
      },
      '/admin': {
        target: 'http://localhost:5000',
        changeOrigin: true
      },
      '/dashboard': {
        target: 'http://localhost:5000',
        changeOrigin: true
      }
    }
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@components': fileURLToPath(new URL('./src/components', import.meta.url)),
      '@views': fileURLToPath(new URL('./src/views', import.meta.url)),
      '@stores': fileURLToPath(new URL('./src/stores', import.meta.url)),
      '@utils': fileURLToPath(new URL('./src/utils', import.meta.url))
    }
  }
})