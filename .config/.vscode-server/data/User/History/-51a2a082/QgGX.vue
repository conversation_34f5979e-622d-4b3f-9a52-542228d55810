<template>
  <div class="project-team">
    <div class="bg-white shadow rounded-lg p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-6">Team del Progetto</h3>

      <!-- Team Members Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div v-for="member in teamMembers" :key="member.id" class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
          <div class="flex items-center space-x-4">
            <div class="flex-shrink-0">
              <img
                v-if="member.profile_image"
                :src="member.profile_image"
                :alt="member.full_name"
                class="w-12 h-12 rounded-full"
              >
              <div v-else class="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                <span class="text-sm font-medium text-gray-600">{{ getInitials(member.full_name) }}</span>
              </div>
            </div>
            <div class="flex-1">
              <h4 class="text-lg font-medium text-gray-900">{{ member.full_name }}</h4>
              <p class="text-sm text-gray-600">{{ member.role || 'Team Member' }}</p>
              <p class="text-xs text-gray-500">{{ member.email }}</p>
            </div>
          </div>
          <div class="mt-4 grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-gray-600">Ore Lavorate:</span>
              <span class="font-medium ml-1">{{ member.hours_worked || 0 }}h</span>
            </div>
            <div>
              <span class="text-gray-600">Task Assegnati:</span>
              <span class="font-medium ml-1">{{ getAssignedTasksCount(member.id) }}</span>
            </div>
          </div>
        </div>
        <div v-if="teamMembers.length === 0" class="col-span-full text-center py-8">
          <p class="text-gray-500">Nessun membro del team assegnato</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  project: { type: Object, default: null },
  loading: { type: Boolean, default: false }
})
</script>
