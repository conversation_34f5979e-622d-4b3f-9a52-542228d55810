<template>
  <div class="relative">
    <button
      @click="showUserMenu = !showUserMenu"
      class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
    >
      <span class="sr-only">Apri menu utente</span>
      <div class="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
        <span class="text-sm font-medium text-primary-700">
          {{ userInitials }}
        </span>
      </div>
    </button>

    <!-- Dropdown Menu -->
    <div
      v-if="showUserMenu"
      @click.away="showUserMenu = false"
      class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"
    >
      <div class="py-1">
        <div class="px-4 py-2 border-b border-gray-100">
          <p class="text-sm font-medium text-gray-900">{{ userName }}</p>
          <p class="text-xs text-gray-500">{{ userEmail }}</p>
        </div>

        <router-link
          to="/app/profile"
          class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
          @click="showUserMenu = false"
        >
          Il tuo profilo
        </router-link>
        <router-link
          to="/app/settings"
          class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
          @click="showUserMenu = false"
        >
          Impostazioni
        </router-link>

        <div class="border-t border-gray-100 my-1"></div>

        <button
          @click="toggleDarkMode"
          class="flex items-center justify-between w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
        >
          <span>{{ isDarkMode ? 'Modalità chiara' : 'Modalità scura' }}</span>
          <i :class="isDarkMode ? 'fas fa-sun' : 'fas fa-moon'" class="text-xs"></i>
        </button>

        <button
          @click="logout"
          class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
        >
          Esci
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useDarkMode } from '@/composables/useDarkMode'

const router = useRouter()
const authStore = useAuthStore()
const showUserMenu = ref(false)

// Dark mode composable
const { isDarkMode, toggleDarkMode } = useDarkMode()
const isDarkMode = ref(false)

const userName = computed(() => {
  if (!authStore.user) return 'Utente'
  return authStore.user.name || authStore.user.username || 'Utente'
})

const userEmail = computed(() => {
  return authStore.user?.email || ''
})

const userInitials = computed(() => {
  if (!authStore.user) return 'U'
  const name = userName.value
  return name.charAt(0).toUpperCase()
})

async function logout() {
  showUserMenu.value = false
  await authStore.logout()
  router.push('/auth/login')
}
</script>