import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/utils/api'

export const useAuthStore = defineStore('auth', () => {
  // Rimuoviamo la gestione del token per usare solo le sessioni Flask
  const storedUser = localStorage.getItem('user')
  const user = ref(storedUser ? JSON.parse(storedUser) : null)
  const loading = ref(false)
  const error = ref(null)
  const sessionChecked = ref(false)

  const isAuthenticated = computed(() => !!user.value && sessionChecked.value)

  async function login(credentials) {
    loading.value = true
    error.value = null

    try {
      const response = await api.post('/api/auth/login', credentials)

      if (response.data.success) {
        user.value = response.data.data.user
        localStorage.setItem('user', JSON.stringify(user.value))
        sessionChecked.value = true
        return { success: true }
      } else {
        error.value = response.data.message || 'Errore durante il login'
        return { success: false, error: error.value }
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore di connessione'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  async function register(userData) {
    loading.value = true
    error.value = null

    try {
      const response = await api.post('/api/auth/register', userData)

      if (response.data.success) {
        return { success: true, message: response.data.message }
      } else {
        error.value = response.data.message || 'Errore durante la registrazione'
        return { success: false, error: error.value }
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore di connessione'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  async function logout() {
    try {
      await api.post('/auth/logout')
    } catch (err) {
      console.warn('Errore durante il logout:', err)
    } finally {
      token.value = null
      user.value = null
      localStorage.removeItem('token')
    }
  }

  async function checkAuth() {
    if (!token.value) return false

    try {
      const response = await api.get('/auth/me')
      if (response.data.success) {
        user.value = response.data.user
        return true
      } else {
        logout()
        return false
      }
    } catch (err) {
      logout()
      return false
    }
  }

  return {
    user,
    token,
    loading,
    error,
    isAuthenticated,
    login,
    register,
    logout,
    checkAuth
  }
})