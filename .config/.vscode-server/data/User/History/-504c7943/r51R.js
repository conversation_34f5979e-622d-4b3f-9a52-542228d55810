import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/utils/api'

export const useAuthStore = defineStore('auth', () => {
  // Rimuoviamo la gestione del token per usare solo le sessioni Flask
  const storedUser = localStorage.getItem('user')
  const user = ref(storedUser ? JSON.parse(storedUser) : null)
  const loading = ref(false)
  const error = ref(null)
  const sessionChecked = ref(false)

  const isAuthenticated = computed(() => !!user.value && sessionChecked.value)

  // Permission system - mapping from backend
  const ROLE_PERMISSIONS = {
    admin: [
      'admin', 'manage_users', 'assign_roles', 'view_all_projects', 'create_project',
      'edit_project', 'delete_project', 'assign_to_project', 'manage_project_tasks',
      'manage_project_resources', 'approve_timesheets', 'view_personnel_data',
      'edit_personnel_data', 'view_contracts', 'manage_contracts', 'view_crm',
      'manage_clients', 'manage_proposals', 'view_reports', 'view_dashboard',
      'submit_timesheet', 'view_own_timesheets', 'view_funding', 'manage_funding',
      'view_products', 'manage_products', 'view_performance', 'manage_performance',
      'view_communications', 'manage_communications', 'view_startup', 'manage_startup'
    ],
    manager: [
      'view_dashboard', 'view_all_projects', 'edit_project', 'assign_to_project',
      'manage_project_tasks', 'manage_project_resources', 'approve_timesheets',
      'view_personnel_data', 'view_crm', 'view_reports', 'submit_timesheet',
      'view_own_timesheets', 'manage_clients', 'manage_proposals', 'view_funding',
      'manage_funding', 'view_products', 'manage_products', 'view_performance',
      'manage_performance', 'view_communications', 'manage_communications',
      'view_startup', 'manage_startup'
    ],
    employee: [
      'view_dashboard', 'view_own_timesheets', 'submit_timesheet'
    ],
    sales: [
      'view_dashboard', 'view_crm', 'manage_clients', 'manage_proposals',
      'submit_timesheet', 'view_own_timesheets', 'view_reports', 'view_funding',
      'view_products', 'manage_products'
    ],
    human_resources: [
      'view_dashboard', 'manage_users', 'view_personnel_data', 'edit_personnel_data',
      'view_contracts', 'manage_contracts', 'submit_timesheet', 'view_own_timesheets',
      'view_reports', 'view_funding', 'manage_funding', 'view_performance',
      'manage_performance', 'view_communications', 'manage_communications',
      'view_startup', 'manage_startup'
    ]
  }

  // Helper function to check permissions
  const hasPermission = (permission) => {
    if (!user.value || !user.value.role) return false

    // Admin has all permissions
    if (user.value.role === 'admin') return true

    const userPermissions = ROLE_PERMISSIONS[user.value.role] || []
    return userPermissions.includes(permission)
  }

  async function login(credentials) {
    loading.value = true
    error.value = null

    try {
      const response = await api.post('/api/auth/login', credentials)

      if (response.data.success) {
        user.value = response.data.data.user
        localStorage.setItem('user', JSON.stringify(user.value))
        sessionChecked.value = true
        return { success: true }
      } else {
        error.value = response.data.message || 'Errore durante il login'
        return { success: false, error: error.value }
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore di connessione'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  async function register(userData) {
    loading.value = true
    error.value = null

    try {
      const response = await api.post('/api/auth/register', userData)

      if (response.data.success) {
        return { success: true, message: response.data.message }
      } else {
        error.value = response.data.message || 'Errore durante la registrazione'
        return { success: false, error: error.value }
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore di connessione'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  async function logout() {
    try {
      await api.post('/api/auth/logout')
    } catch (err) {
      console.warn('Errore durante il logout:', err)
    } finally {
      user.value = null
      sessionChecked.value = false
      localStorage.removeItem('user')
    }
  }

  async function checkAuth() {
    if (sessionChecked.value) {
      return isAuthenticated.value
    }

    try {
      const response = await api.get('/api/auth/me')
      if (response.data.success) {
        user.value = response.data.data.user
        localStorage.setItem('user', JSON.stringify(user.value))
        sessionChecked.value = true
        return true
      } else {
        await logout()
        return false
      }
    } catch (err) {
      await logout()
      return false
    }
  }

  async function initializeAuth() {
    // Controlla se c'è un utente salvato e verifica la sessione
    if (user.value) {
      return await checkAuth()
    }
    sessionChecked.value = true
    return false
  }

  return {
    user,
    loading,
    error,
    sessionChecked,
    isAuthenticated,
    login,
    register,
    logout,
    checkAuth,
    initializeAuth
  }
})