<template>
  <div class="project-tasks">
    <div class="bg-white shadow rounded-lg p-6">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-lg font-medium text-gray-900">Task del Progetto</h3>
        <button class="btn-primary">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Nuovo Task
        </button>
      </div>

      <!-- Task List -->
      <div class="space-y-4">
        <div v-for="task in tasks" :key="task.id" class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <div class="w-3 h-3 rounded-full" :class="getStatusColor(task.status)"></div>
                </div>
                <h5 class="text-lg font-medium text-gray-900">{{ task.name }}</h5>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" :class="getPriorityClass(task.priority)">
                  {{ getPriorityLabel(task.priority) }}
                </span>
              </div>
              <p class="mt-2 text-sm text-gray-600">{{ task.description }}</p>
              <div class="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                <span v-if="task.assignee_id">Assegnato a: {{ getAssigneeName(task.assignee_id) }}</span>
                <span v-if="task.due_date">Scadenza: {{ formatDate(task.due_date) }}</span>
                <span v-if="task.estimated_hours">Stimate: {{ task.estimated_hours }}h</span>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <button class="text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>
            </div>
          </div>
        </div>
        <div v-if="tasks.length === 0" class="text-center py-8">
          <p class="text-gray-500">Nessun task trovato per questo progetto</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  project: { type: Object, default: null },
  loading: { type: Boolean, default: false }
})
</script>
