import axios from 'axios'

const api = axios.create({
  baseURL: import.meta.env.DEV ? 'http://localhost:5000' : '',
  timeout: 10000,
  withCredentials: true, // Importante per le sessioni Flask
  headers: {
    'Content-Type': 'application/json',
  }
})

// Request interceptor per aggiungere CSRF token
api.interceptors.request.use(
  (config) => {
    // Aggiungi CSRF token per richieste che ne hanno bisogno
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
    if (csrfToken && ['post', 'put', 'patch', 'delete'].includes(config.method?.toLowerCase())) {
      config.headers['X-CSRFToken'] = csrfToken
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor per gestire errori globali
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token scaduto, redirect al login
      localStorage.removeItem('token')
      window.location.href = '/auth/login'
    }
    return Promise.reject(error)
  }
)

export default api