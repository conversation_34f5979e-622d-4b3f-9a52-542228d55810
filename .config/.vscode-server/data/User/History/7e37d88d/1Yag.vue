<template>
  <div class="project-view">
    <!-- Project Header -->
    <ProjectHeader
      :project="project"
      :loading="loading"
      @edit="handleEdit"
      @delete="handleDelete"
    />

    <!-- Project Tabs -->
    <ProjectTabs
      v-model="activeTab"
      :tabs="availableTabs"
      class="mb-6"
    />

    <!-- Tab Content -->
    <div class="tab-content">
      <component
        :is="currentTabComponent"
        :project="project"
        :loading="loading"
        @refresh="loadProject"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProjectsStore } from '@/stores/projects'
import { useAuthStore } from '@/stores/auth'

// Components
import ProjectHeader from '@/components/projects/ProjectHeader.vue'
import TabNavigation from '@/components/ui/TabNavigation.vue'
import ProjectOverview from '@/components/projects/ProjectOverview.vue'
import ProjectTasks from '@/components/projects/ProjectTasks.vue'
import ProjectTeam from '@/components/projects/ProjectTeam.vue'
import ProjectFiles from '@/components/projects/ProjectFiles.vue'
import ProjectKPI from '@/components/projects/ProjectKPI.vue'
import ProjectGantt from '@/components/projects/ProjectGantt.vue'
import ProjectTimesheet from '@/components/projects/ProjectTimesheet.vue'

// Stores
const projectsStore = useProjectsStore()
const authStore = useAuthStore()
const route = useRoute()
const router = useRouter()

// State
const loading = ref(true)
const activeTab = ref('overview')

// Computed
const project = computed(() => projectsStore.currentProject)

const availableTabs = computed(() => {
  const baseTabs = [
    { id: 'overview', label: 'Panoramica', icon: 'chart-bar' },
    { id: 'tasks', label: 'Task', icon: 'clipboard-list' },
    { id: 'team', label: 'Team', icon: 'users' },
    { id: 'files', label: 'File', icon: 'folder' }
  ]

  // Add advanced tabs based on permissions
  if (authStore.hasPermission('view_reports')) {
    baseTabs.push({ id: 'kpi', label: 'KPI', icon: 'trending-up' })
  }

  if (authStore.hasPermission('view_gantt')) {
    baseTabs.push({ id: 'gantt', label: 'Gantt', icon: 'calendar' })
  }

  if (authStore.hasPermission('view_timesheets')) {
    baseTabs.push({ id: 'timesheet', label: 'Timesheet', icon: 'clock' })
  }

  return baseTabs
})

const currentTabComponent = computed(() => {
  const components = {
    overview: ProjectOverview,
    tasks: ProjectTasks,
    team: ProjectTeam,
    files: ProjectFiles,
    kpi: ProjectKPI,
    gantt: ProjectGantt,
    timesheet: ProjectTimesheet
  }
  return components[activeTab.value] || ProjectOverview
})

// Methods
const loadProject = async () => {
  loading.value = true
  try {
    const projectId = route.params.id
    await projectsStore.fetchProject(projectId)
  } catch (error) {
    console.error('Error loading project:', error)
    // Handle error (show toast, redirect, etc.)
  } finally {
    loading.value = false
  }
}

const handleEdit = () => {
  router.push(`/projects/${route.params.id}/edit`)
}

const handleDelete = async () => {
  if (confirm('Sei sicuro di voler eliminare questo progetto?')) {
    try {
      await projectsStore.deleteProject(route.params.id)
      router.push('/projects')
    } catch (error) {
      console.error('Error deleting project:', error)
    }
  }
}

// Watchers
watch(() => route.params.id, (newId) => {
  if (newId) {
    loadProject()
  }
})

watch(() => route.hash, (newHash) => {
  if (newHash) {
    const tab = newHash.replace('#', '')
    if (availableTabs.value.find(t => t.id === tab)) {
      activeTab.value = tab
    }
  }
})

watch(activeTab, (newTab) => {
  // Update URL hash without triggering navigation
  const newHash = `#${newTab}`
  if (route.hash !== newHash) {
    router.replace({ ...route, hash: newHash })
  }
})

// Lifecycle
onMounted(() => {
  // Set initial tab from URL hash
  if (route.hash) {
    const tab = route.hash.replace('#', '')
    if (availableTabs.value.find(t => t.id === tab)) {
      activeTab.value = tab
    }
  }

  loadProject()
})
</script>

<style scoped>
.project-view {
  @apply container mx-auto px-4 py-6;
}

.tab-content {
  @apply min-h-96;
}
</style>
