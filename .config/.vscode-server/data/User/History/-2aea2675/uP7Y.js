import { ref } from 'vue'

// Global dark mode state
const isDarkMode = ref(false)

// Apply dark mode to DOM
const applyDarkMode = (value) => {
  if (value) {
    document.documentElement.classList.add('dark')
    localStorage.setItem('darkMode', 'true')
  } else {
    document.documentElement.classList.remove('dark')
    localStorage.setItem('darkMode', 'false')
  }
}

export function useDarkMode() {
  const toggleDarkMode = () => {
    isDarkMode.value = !isDarkMode.value
    applyDarkMode(isDarkMode.value)
  }

  const setDarkMode = (value) => {
    isDarkMode.value = value
    applyDarkMode(value)
  }

  const initializeDarkMode = () => {
    const savedDarkMode = localStorage.getItem('darkMode')

    if (savedDarkMode === 'true') {
      isDarkMode.value = true
      applyDarkMode(true)
    } else if (savedDarkMode === 'false') {
      isDarkMode.value = false
      applyDarkMode(false)
    } else {
      // Default to system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      isDarkMode.value = prefersDark
      applyDarkMode(prefersDark)
    }
  }

  return {
    isDarkMode,
    toggleDarkMode,
    setDarkMode,
    initializeDarkMode
  }
}
