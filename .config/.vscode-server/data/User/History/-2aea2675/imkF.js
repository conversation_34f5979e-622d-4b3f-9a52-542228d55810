import { ref, watch } from 'vue'

// Global dark mode state
const isDarkMode = ref(false)
let isInitialized = false

// Apply dark mode to DOM
const applyDarkMode = (value) => {
  if (value) {
    document.documentElement.classList.add('dark')
    localStorage.setItem('darkMode', 'true')
  } else {
    document.documentElement.classList.remove('dark')
    localStorage.setItem('darkMode', 'false')
  }
}

// Initialize watcher only once
const initializeWatcher = () => {
  if (isInitialized) return

  watch(isDarkMode, (newValue) => {
    applyDarkMode(newValue)
  })

  isInitialized = true
}

export function useDarkMode() {
  // Initialize watcher on first use
  initializeWatcher()

  const toggleDarkMode = () => {
    isDarkMode.value = !isDarkMode.value
  }

  const setDarkMode = (value) => {
    isDarkMode.value = value
  }

  const initializeDarkMode = () => {
    const savedDarkMode = localStorage.getItem('darkMode')

    // Check if DOM already has dark class (from system preference)
    const isDomDark = document.documentElement.classList.contains('dark')

    if (savedDarkMode === 'true') {
      isDarkMode.value = true
    } else if (savedDarkMode === 'false') {
      isDarkMode.value = false
    } else {
      // Default to system preference or current DOM state
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      isDarkMode.value = isDomDark || prefersDark
    }

    // Apply immediately
    applyDarkMode(isDarkMode.value)

    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleSystemThemeChange = (e) => {
      // Only update if user hasn't manually set a preference
      const savedDarkMode = localStorage.getItem('darkMode')
      if (!savedDarkMode || savedDarkMode === 'null') {
        isDarkMode.value = e.matches
      }
    }
    mediaQuery.addEventListener('change', handleSystemThemeChange)
  }

  return {
    isDarkMode,
    toggleDarkMode,
    setDarkMode,
    initializeDarkMode
  }
}
