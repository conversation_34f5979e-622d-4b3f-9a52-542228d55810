import { ref, watch } from 'vue'

// Global dark mode state
const isDarkMode = ref(false)
let isInitialized = false

export function useDarkMode() {
  const toggleDarkMode = () => {
    isDarkMode.value = !isDarkMode.value
  }

  const setDarkMode = (value) => {
    isDarkMode.value = value
  }

  const initializeDarkMode = () => {
    if (isInitialized) return

    const savedDarkMode = localStorage.getItem('darkMode')

    if (savedDarkMode === 'true') {
      isDarkMode.value = true
    } else if (savedDarkMode === 'false') {
      isDarkMode.value = false
    } else {
      // Default to system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      isDarkMode.value = prefersDark
    }

    isInitialized = true
  }

  // Watch for changes and apply to DOM
  if (!isInitialized) {
    watch(isDarkMode, (newValue) => {
      if (newValue) {
        document.documentElement.classList.add('dark')
        localStorage.setItem('darkMode', 'true')
      } else {
        document.documentElement.classList.remove('dark')
        localStorage.setItem('darkMode', 'false')
      }
    }, { immediate: false })

    // Listen for system theme changes
    if (typeof window !== 'undefined') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      const handleSystemThemeChange = (e) => {
        // Only update if user hasn't manually set a preference
        const savedDarkMode = localStorage.getItem('darkMode')
        if (!savedDarkMode) {
          isDarkMode.value = e.matches
        }
      }
      mediaQuery.addEventListener('change', handleSystemThemeChange)
    }
  }

  return {
    isDarkMode,
    toggleDarkMode,
    setDarkMode,
    initializeDarkMode
  }
}
