import { ref, watch } from 'vue'

// Global dark mode state
const isDarkMode = ref(false)
let isInitialized = false

// Apply dark mode to DOM
const applyDarkMode = (value) => {
  if (value) {
    document.documentElement.classList.add('dark')
    localStorage.setItem('darkMode', 'true')
  } else {
    document.documentElement.classList.remove('dark')
    localStorage.setItem('darkMode', 'false')
  }
}

// Initialize watcher only once
const initializeWatcher = () => {
  if (isInitialized) return

  watch(isDarkMode, (newValue) => {
    console.log('Dark mode changed to:', newValue) // Debug log
    applyDarkMode(newValue)
  })

  isInitialized = true
}

export function useDarkMode() {
  // Initialize watcher on first use
  initializeWatcher()

  const toggleDarkMode = () => {
    console.log('Toggling dark mode from:', isDarkMode.value) // Debug log
    isDarkMode.value = !isDarkMode.value
  }

  const setDarkMode = (value) => {
    isDarkMode.value = value
  }

  const initializeDarkMode = () => {
    console.log('Initializing dark mode...') // Debug log
    const savedDarkMode = localStorage.getItem('darkMode')

    // Check if DOM already has dark class (from system preference)
    const isDomDark = document.documentElement.classList.contains('dark')
    console.log('DOM already has dark class:', isDomDark)

    if (savedDarkMode === 'true') {
      isDarkMode.value = true
    } else if (savedDarkMode === 'false') {
      isDarkMode.value = false
    } else {
      // Default to system preference or current DOM state
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      isDarkMode.value = isDomDark || prefersDark
      console.log('Setting to system/DOM preference:', isDarkMode.value)
    }

    // Apply immediately
    applyDarkMode(isDarkMode.value)

    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleSystemThemeChange = (e) => {
      console.log('System theme changed to:', e.matches ? 'dark' : 'light')
      // Only update if user hasn't manually set a preference
      const savedDarkMode = localStorage.getItem('darkMode')
      if (!savedDarkMode || savedDarkMode === 'null') {
        console.log('Updating to system preference:', e.matches)
        isDarkMode.value = e.matches
      } else {
        console.log('User has manual preference, ignoring system change')
      }
    }
    mediaQuery.addEventListener('change', handleSystemThemeChange)
  }

  return {
    isDarkMode,
    toggleDarkMode,
    setDarkMode,
    initializeDarkMode
  }
}
