<template>
  <div class="project-files">
    <div class="bg-white shadow rounded-lg p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-6">File del Progetto</h3>

      <!-- File Upload Area -->
      <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 mb-6 hover:border-gray-400 transition-colors">
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          <div class="mt-4">
            <label class="cursor-pointer">
              <span class="mt-2 block text-sm font-medium text-gray-900">
                Trascina file qui o clicca per selezionare
              </span>
              <input type="file" class="sr-only" multiple>
            </label>
            <p class="mt-2 text-xs text-gray-500">
              PNG, JPG, PDF, DOC, XLS fino a 10MB
            </p>
          </div>
        </div>
      </div>

      <!-- Files List -->
      <div class="space-y-3">
        <div v-for="file in files" :key="file.id" class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
          <div class="flex items-center space-x-4">
            <div class="flex-shrink-0">
              <svg class="h-8 w-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div class="flex-1">
              <h4 class="text-sm font-medium text-gray-900">{{ file.name }}</h4>
              <div class="flex items-center space-x-4 text-xs text-gray-500">
                <span>{{ formatFileSize(file.size) }}</span>
                <span>{{ formatDate(file.uploaded_at) }}</span>
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <button class="text-gray-400 hover:text-gray-600" title="Scarica">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </button>
          </div>
        </div>
        <div v-if="files.length === 0" class="text-center py-8">
          <p class="text-gray-500">Nessun file caricato per questo progetto</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  project: { type: Object, default: null },
  loading: { type: Boolean, default: false }
})
</script>
