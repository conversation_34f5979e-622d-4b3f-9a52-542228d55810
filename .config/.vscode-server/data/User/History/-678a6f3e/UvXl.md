# Vue.js Professional Migration - DatPortal

## 🎯 Obiettivo

Migrazione da setup ibrido Vue.js (CDN + template inline) a architettura professionale con Single File Components e build system Vite.

## 📊 Stato Attuale (Aggiornato)

### ✅ Completato
- ✅ Setup frontend Vue.js 3 + Vite + SFC (Single File Components)
- ✅ Routing Vue Router 4 con layout AppLayout e PublicLayout
- ✅ Tailwind CSS + PostCSS configurato
- ✅ Stores Pinia (auth, tenant) implementati
- ✅ Pagine pubbliche migrate (Home, About, Contact, Services)
- ✅ Autenticazione Login/Register migrata
- ✅ Layout responsive con sidebar funzionante
- ✅ Backend Flask completo (12 blueprints, 9 API)
- ✅ Build system configurato (vite.config.js, proxy Flask)
- ✅ **Dashboard.vue completamente funzionante** (KPI, grafici, statistiche)
- ✅ **Template Flask integrato** con build assets automatici
- ✅ **Navbar dinamica** (azioni contestuali per pagina)
- ✅ **API backend corrette** (auth, dashboard, KPI, progetti, task, ecc.)
- ✅ **SPA navigation** senza reload di pagina

### 🔄 In Corso - Prossime Viste da Migrare
- 🔄 Projects.vue (placeholder → vista completa con tab)
- 🔄 Personnel.vue (placeholder → vista completa con organigramma)
- 🔄 Tasks.vue (da creare)
- 🔄 Admin.vue (da creare)

### ❌ Da Completare - Piano Prioritario
1. **Projects.vue** (Alta priorità)
   - Migrazione da template legacy con 3000+ righe
   - Tab: Overview, Tasks, Team, Files, KPI, Gantt
   - Componenti: ProjectHeader, ProjectTabs, TaskList, TeamView

2. **Personnel.vue** (Alta priorità)
   - Migrazione modulo HR con 12 template
   - Tab: Directory, Orgchart, Skills, Departments
   - Componenti: PersonnelCard, OrgChart, SkillsMatrix

3. **Tasks.vue** (Media priorità)
   - Vista task standalone (non solo in progetti)
   - Kanban board, filtri, assegnazioni

4. **Admin.vue** (Bassa priorità)
   - Pannello amministrazione
   - Gestione utenti, permessi, configurazioni

### 🎯 Prossimo Step: Migrazione Projects.vue

## 🏗️ Architettura Target

```
project/
├── backend/                    # Flask app (MANTENIAMO TUTTO)
│   ├── app.py, main.py, models.py
│   ├── blueprints/ (12 moduli)
│   ├── utils/ (10 moduli)
│   ├── tests/ (19 test files)
│   ├── templates/index.html    # Solo entry point
│   └── static/dist/            # Build output
│
├── frontend/                   # Vue.js app (NUOVO)
│   ├── src/
│   │   ├── components/
│   │   │   ├── layout/         # Sidebar, Navigation, etc.
│   │   │   ├── ui/             # Modal, DataTable, etc.
│   │   │   └── forms/          # Form components
│   │   ├── views/
│   │   │   ├── public/         # Home, About, Contact
│   │   │   ├── auth/           # Login, Register
│   │   │   ├── dashboard/      # Dashboard views
│   │   │   ├── projects/       # Project management
│   │   │   └── personnel/      # HR management
│   │   ├── stores/             # Pinia stores
│   │   ├── router/             # Vue Router
│   │   ├── utils/              # API, helpers
│   │   └── assets/             # CSS, images
│   ├── package.json
│   ├── vite.config.js
│   └── index.html
```

## 📋 Piano Migrazione (5h 30min)

### Fase 1: Setup Build System (45min)
```bash
# 1. Inizializza frontend
cd project
npm create vue@latest frontend
cd frontend
npm install

# 2. Configura Vite
# vite.config.js con proxy Flask
# tailwind.config.js
# package.json scripts
```

### Fase 2: Core Migration (1h)
- Migra `templates/auth/` → `src/views/auth/`
- Migra `templates/components/` → `src/components/layout/`
- Setup stores Pinia principali

### Fase 3: Projects Module (1h)
- Migra `templates/projects/view.html` (3000+ righe) → componenti modulari
- Crea `ProjectView.vue`, `ProjectTasks.vue`, `ProjectGantt.vue`
- Setup `stores/projects.js`

### Fase 4: Personnel Module (1h)
- Migra 12 template personnel → Single File Components
- Orgchart, Skills, Directory views
- Setup `stores/personnel.js`

### Fase 5: Finalizzazione (45min)
- Migra moduli rimanenti (Admin, Products)
- Setup testing (Vitest, Cypress)
- Cleanup template obsoleti

## ⚙️ Configurazione Tecnica

### Vite Config
```javascript
// frontend/vite.config.js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  build: {
    outDir: '../backend/static/dist',
    emptyOutDir: true
  },
  server: {
    port: 3000,
    proxy: {
      '/api': 'http://localhost:5000',
      '/auth': 'http://localhost:5000'
    }
  },
  resolve: {
    alias: {
      '@': '/src',
      '@components': '/src/components',
      '@views': '/src/views'
    }
  }
})
```

### Package.json
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "test": "vitest",
    "lint": "eslint src --ext .vue,.js"
  },
  "dependencies": {
    "vue": "^3.4.0",
    "vue-router": "^4.2.0",
    "pinia": "^2.1.0",
    "axios": "^1.6.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.5.0",
    "vite": "^5.0.0",
    "vitest": "^1.0.0",
    "tailwindcss": "^3.4.0"
  }
}
```

## 🔄 Workflow Development

### Terminal 1: Backend Flask
```bash
cd backend
python main.py
# → http://localhost:5000
```

### Terminal 2: Frontend Vue.js
```bash
cd frontend
npm run dev
# → http://localhost:3000 (proxy to Flask)
```

### Flusso Richieste
```
Browser → http://localhost:3000 (Vite Dev Server)
    ↓ (HMR, Vue DevTools)
Vue.js Components
    ↓ (/api/* requests)
Proxy → http://localhost:5000 (Flask)
    ↓ (Database, Business Logic)
Response → Vue.js → Browser Update
```

## 💻 Esempi Migrazione

### Da Template HTML a Vue SFC
```html
<!-- PRIMA: templates/projects/view.html (3000+ righe) -->
<div class="project-view">
  <!-- HTML complesso con Alpine.js -->
</div>
```

```vue
<!-- DOPO: src/views/projects/ProjectView.vue -->
<template>
  <div class="project-view">
    <ProjectHeader :project="project" />
    <TabNavigation v-model="activeTab" />
    <component :is="currentComponent" :project="project" />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useProjectsStore } from '@/stores/projects'

const store = useProjectsStore()
const activeTab = ref('overview')
const project = computed(() => store.currentProject)
</script>

<style scoped>
.project-view {
  @apply container mx-auto p-6;
}
</style>
```

## ✅ Checklist Migrazione - STATO AGGIORNATO

### Setup ✅ COMPLETATO
- [x] Inizializza progetto Vue.js con Vite
- [x] Configura Tailwind CSS + PostCSS
- [x] Setup proxy development server
- [x] Configura alias path e build output

### Componenti Core ✅ COMPLETATO
- [x] Migra layout components (AppLayout, AppSidebar, AppHeader)
- [x] Sidebar collassabile con navigazione completa
- [x] Header modulare con breadcrumbs, notifiche, ricerca
- [x] Migra auth views (Login, Register)
- [x] Setup stores Pinia principali (auth, tenant)
- [x] Coerenza stile tra PublicLayout e AppLayout

### Moduli Business 🔄 IN CORSO
- [x] Dashboard view (✅ COMPLETATA - KPI, grafici, statistiche)
- [ ] Projects module (🔄 PROSSIMA - placeholder → componenti avanzati)
- [ ] Personnel module (⏳ PIANIFICATA - placeholder → componenti avanzati)
- [ ] Tasks module (⏳ PIANIFICATA - vista standalone)
- [ ] Admin interface (⏳ PIANIFICATA)
- [x] Integrazione API reali con backend Flask (✅ COMPLETATA)

### Testing & Quality ❌ NON INIZIATO
- [ ] Setup Vitest per unit tests
- [ ] Setup Cypress per E2E tests
- [ ] Configura ESLint + Prettier
- [ ] Test coverage frontend
- [ ] Test componenti layout modulari

### Cleanup ⏳ PIANIFICATO
- [ ] Rimuovi template HTML obsoleti
- [ ] Rimuovi file .js con template inline
- [ ] Aggiorna documentazione
- [ ] Test deployment production

## 📊 Stato Corrente (Maggio 2025)

### ✅ Completato (70%)
- Architettura Vue.js 3 + Vite funzionante
- Layout modulare professionale
- Navigazione completa con 10+ elementi
- Autenticazione integrata
- Gestione configurazioni tenant
- Build system ottimizzato

### 🔄 In Sviluppo (20%)
- Componenti business (Dashboard, Projects, Personnel)
- Integrazione API Flask
- Componenti UI avanzati

### ❌ Da Fare (10%)
- Testing suite completa
- Cleanup template legacy

## 🚀 Vantaggi Finali

✅ **Syntax highlighting** completo per HTML/CSS/JS
✅ **Hot Module Replacement** per sviluppo rapido
✅ **Vue DevTools** per debugging avanzato
✅ **Code splitting** automatico per performance
✅ **TypeScript support** (opzionale)
✅ **Testing integrato** con Vitest/Cypress
✅ **Build ottimizzato** per production
✅ **Architettura scalabile** per crescita futura

## 🔧 Troubleshooting

### Problemi Comuni
- **CORS errors**: Verificare configurazione proxy Vite
- **Asset loading**: Controllare path alias in vite.config.js
- **CSS conflicts**: Usare scoped styles nei componenti
- **Build errors**: Verificare import paths e dependencies

### Debug Tips
- Usare Vue DevTools per ispezionare componenti
- Console browser per errori JavaScript
- Network tab per verificare API calls
- Vite dev server logs per errori build

## 📈 Metriche Successo

### Performance
- Bundle size < 500KB (gzipped)
- First Contentful Paint < 2s
- Time to Interactive < 3s

### Developer Experience
- Hot reload < 100ms
- Build time < 30s
- Test execution < 10s

### Code Quality
- ESLint errors: 0
- Test coverage > 80%
- TypeScript errors: 0 (se abilitato)

## 📚 Riferimenti

- [Vue.js 3 Documentation](https://vuejs.org/)
- [Vite Documentation](https://vitejs.dev/)
- [Vue Router 4](https://router.vuejs.org/)
- [Pinia State Management](https://pinia.vuejs.org/)
- [Vitest Testing](https://vitest.dev/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Vue DevTools](https://devtools.vuejs.org/)
