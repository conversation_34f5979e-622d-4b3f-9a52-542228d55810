import os
import logging
from flask import Flask, session, redirect, url_for, request, flash
from flask_login import logout_user, current_user
from flask_cors import CORS
from werkzeug.middleware.proxy_fix import ProxyFix
from datetime import datetime, timedelta
from config import Config
import time
from extensions import db, login_manager, migrate, csrf

# Configure logging
logging.basicConfig(level=logging.INFO) # Modificato INFO per produzione, DEBUG per sviluppo
logger = logging.getLogger(__name__)

PUBLIC_ENDPOINTS = [
    'static', 'public_api.get_public_config', 'public_api.get_featured_services',
    'public_api.get_services', 'public_api.get_service_detail',
    'tenants_api.api_tenant_config',  # API per configurazione tenant
    'api.api_auth.login', 'api.api_auth.logout',  # API di autenticazione Vue.js
    'swagger_json.swagger_json',  # Swagger JSON
    'swagger_ui.show',  # Swagger UI
    'spa'  # SPA catch-all route - Vue.js gestisce tutto
]

# Fix MIME types for ES6 modules GLOBALLY
import mimetypes
mimetypes.add_type('application/javascript', '.js')
mimetypes.add_type('application/javascript', '.mjs')
mimetypes.add_type('application/javascript', '.vue')

def create_app(config_object='config.Config', config_overrides=None):
    """Factory function to create and configure the Flask app."""
    app = Flask(__name__)
    app.secret_key = os.environ.get("SESSION_SECRET", os.urandom(24))
    app.wsgi_app = ProxyFix(app.wsgi_app, x_proto=1, x_host=1)

    # Load configuration
    app.config.from_object(config_object)
    if config_overrides:
        app.config.from_mapping(config_overrides)

    # Initialize extensions with app
    db.init_app(app)
    login_manager.init_app(app)
    # login_manager.login_view = None  # Non serve più con Vue.js SPA
    migrate.init_app(app, db)

    # Configure CORS to allow credentials (cookies) for Vue.js SPA
    CORS(app,
         origins=['http://localhost:3000', 'http://localhost:5000', 'http://127.0.0.1:5000'],
         supports_credentials=True,
         allow_headers=['Content-Type', 'X-CSRFToken', 'Authorization'],
         methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])

    # Configure CSRF protection
    csrf.init_app(app)
    # Make csrf_token available in templates without function call
    app.jinja_env.globals['csrf_token'] = lambda: csrf.generate_csrf()

    with app.app_context():
        # Import models first to ensure they're registered
        from models import User # Spostato import qui per evitare importazioni circolari

        # Add datetime utility for templates
        @app.context_processor
        def utility_processor():
            return {'current_year': datetime.now().year}

        # Import blueprints for Vue.js SPA
        from blueprints.api import api_bp
        from blueprints.api.public import public_api_bp
        from blueprints.swagger import register_swagger_blueprints

        # Register blueprints - SOLO API per Vue.js SPA
        app.register_blueprint(api_bp, url_prefix='/api')
        app.register_blueprint(public_api_bp)

        # Auth API
        from blueprints.api.auth import api_auth
        app.register_blueprint(api_auth, url_prefix='/api/auth')

        # Dashboard API
        from blueprints.api.dashboard import api_dashboard
        app.register_blueprint(api_dashboard, url_prefix='/api/dashboard')

        # Tenant API
        from blueprints.api.tenants import tenants_api
        app.register_blueprint(tenants_api, url_prefix='/api')



        # Register Swagger blueprints
        register_swagger_blueprints(app)

        # Configure static file serving with correct MIME types
        @app.after_request
        def after_request(response):
            # Fix MIME type for JavaScript modules and Vue files
            if response.content_type == 'application/octet-stream':
                if request.path.endswith('.js') or request.path.endswith('.vue'):
                    response.content_type = 'application/javascript'
            return response



        # SPA Route - Catch-all for Vue.js routing
        @app.route('/')
        @app.route('/<path:path>')
        def spa(path=''):
            """
            Serve the Vue.js SPA for all routes except API and auth routes.
            This allows Vue Router to handle client-side routing.
            """
            # Don't serve SPA for API routes
            if path.startswith('api/'):
                from flask import abort
                abort(404)

            # Don't serve SPA for auth routes (keep traditional auth)
            if path.startswith('auth/'):
                from flask import abort
                abort(404)

            # Don't serve SPA for static files - let Flask serve them directly
            if path.startswith('static/'):
                from flask import abort
                abort(404)

            # Don't serve SPA for swagger routes
            if path.startswith('swagger') or path.startswith('docs') or path.startswith('api/swagger'):
                from flask import abort
                abort(404)

            # Serve the Vue.js SPA template for all other routes
            from flask import render_template
            return render_template('vue_app.html')

        # Setup user loader for Flask-Login
        @login_manager.user_loader
        def load_user(user_id):
            return User.query.get(int(user_id))

        # Create database tables if they don't exist
        db.create_all()

        logger.info("Flask app created and configured.")

        @app.before_request
        def session_management():
            # Simplified session management for Vue.js SPA
            if current_user.is_authenticated:
                session['last_activity'] = time.time()

        @app.before_request
        def global_auth_enforcement():
            endpoint = request.endpoint
            # Log ogni accesso
            user = getattr(current_user, 'username', 'anonymous')
            logger.info(f"Accesso: user={user}, endpoint={endpoint}, ip={request.remote_addr}")
            # Enforcement autenticazione globale
            if endpoint and not endpoint.startswith('static') and endpoint not in PUBLIC_ENDPOINTS:
                if not current_user.is_authenticated:
                    logger.warning(f"Tentativo accesso non autenticato a {endpoint} da IP {request.remote_addr}")

                    # Per le API, restituisci JSON 401 invece di redirect
                    if endpoint and endpoint.startswith('api.'):
                        from flask import jsonify
                        return jsonify({
                            'success': False,
                            'message': 'Autenticazione richiesta'
                        }), 401

                    # Per le pagine web, fai redirect
                    flash('Devi essere autenticato per accedere a questa pagina.', 'warning')
                    # Per Vue.js SPA, non redirect a login page
                    from flask import jsonify
                    return jsonify({'error': 'Authentication required'}), 401

        @app.errorhandler(403)
        def forbidden(e):
            user = getattr(current_user, 'username', 'anonymous')
            logger.warning(f"403 Forbidden: user={user}, endpoint={request.endpoint}, ip={request.remote_addr}")
            flash('Accesso negato: non hai i permessi necessari.', 'danger')
            return redirect('/')

        # Registra i filtri personalizzati
        from utils.filters import register_filters
        register_filters(app)

    return app

