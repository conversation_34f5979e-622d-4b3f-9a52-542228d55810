import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import { useAuthStore } from './stores/auth'
import './assets/css/main.css'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)

// Inizializza l'autenticazione prima di montare l'app
const authStore = useAuthStore()

// Monta l'app sempre, anche se l'auth fallisce
authStore.initializeAuth()
  .then(() => {
    console.log('Auth initialized successfully')
    app.mount('#app')
  })
  .catch((error) => {
    console.error('Auth initialization failed:', error)
    // Monta l'app comunque
    app.mount('#app')
  })