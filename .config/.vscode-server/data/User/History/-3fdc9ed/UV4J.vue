<template>
  <div class="project-kpi">
    <div v-if="loading" class="animate-pulse space-y-4">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div v-for="i in 4" :key="i" class="bg-gray-200 rounded-lg h-24"></div>
      </div>
      <div class="bg-gray-200 rounded-lg h-64"></div>
    </div>

    <div v-else-if="project" class="space-y-6">
      <!-- K<PERSON> Header -->
      <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-medium text-gray-900">KPI Progetto</h3>
            <p class="text-sm text-gray-600">Dashboard metriche e performance del progetto</p>
          </div>
          <button
            @click="refreshKPIs"
            :disabled="refreshing"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
          >
            <svg class="w-4 h-4 mr-2" :class="{ 'animate-spin': refreshing }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Aggiorna
          </button>
        </div>
      </div>

      <!-- KPI Overview Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Ore Totali -->
        <div class="bg-white shadow rounded-lg p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Ore Totali</dt>
                <dd class="text-lg font-medium text-gray-900">{{ formatHours(kpiData.totalHours) }}</dd>
                <dd class="text-xs text-gray-500">{{ kpiData.workDays }} giorni lavorati</dd>
              </dl>
            </div>
          </div>
        </div>

        <!-- Costi Totali -->
        <div class="bg-white shadow rounded-lg p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Costi Totali</dt>
                <dd class="text-lg font-medium text-gray-900">{{ formatCurrency(kpiData.totalCosts) }}</dd>
                <dd class="text-xs" :class="costVarianceClass">{{ formatCurrency(kpiData.costVariance) }} vs budget</dd>
              </dl>
            </div>
          </div>
        </div>

        <!-- Ricavi Potenziali -->
        <div class="bg-white shadow rounded-lg p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Ricavi Potenziali</dt>
                <dd class="text-lg font-medium text-gray-900">{{ formatCurrency(kpiData.potentialRevenue) }}</dd>
                <dd class="text-xs text-gray-500">{{ formatCurrency(kpiData.actualRevenue) }} fatturati</dd>
              </dl>
            </div>
          </div>
        </div>

        <!-- Margine Progetto -->
        <div class="bg-white shadow rounded-lg p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Margine</dt>
                <dd class="text-lg font-medium text-gray-900">{{ formatPercentage(kpiData.marginPercentage) }}</dd>
                <dd class="text-xs" :class="marginClass">{{ marginStatus }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Performance Charts -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Budget Progress Chart -->
        <div class="bg-white shadow rounded-lg p-6">
          <h4 class="text-lg font-medium text-gray-900 mb-4">Andamento Budget</h4>
          <div class="space-y-4">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Budget Totale</span>
              <span class="font-medium">{{ formatCurrency(project.budget || 0) }}</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3">
              <div
                class="bg-blue-600 h-3 rounded-full transition-all duration-300"
                :style="{ width: budgetUsagePercentage + '%' }"
              ></div>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Utilizzato: {{ formatCurrency(kpiData.totalCosts) }}</span>
              <span class="font-medium">{{ budgetUsagePercentage }}%</span>
            </div>
          </div>
        </div>

        <!-- Time Progress Chart -->
        <div class="bg-white shadow rounded-lg p-6">
          <h4 class="text-lg font-medium text-gray-900 mb-4">Andamento Tempo</h4>
          <div class="space-y-4">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Ore Stimate</span>
              <span class="font-medium">{{ formatHours(project.estimated_hours || 0) }}</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3">
              <div
                class="bg-green-600 h-3 rounded-full transition-all duration-300"
                :style="{ width: timeUsagePercentage + '%' }"
              ></div>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Lavorate: {{ formatHours(kpiData.totalHours) }}</span>
              <span class="font-medium">{{ timeUsagePercentage }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- KPI Thresholds Configuration -->
      <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <h4 class="text-lg font-medium text-gray-900">Soglie KPI</h4>
          <button
            @click="showThresholdModal = true"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            Configura
          </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="text-center p-4 border rounded-lg">
            <div class="text-2xl font-bold" :class="budgetThresholdClass">
              {{ budgetUsagePercentage }}%
            </div>
            <div class="text-sm text-gray-600">Budget Usage</div>
            <div class="text-xs text-gray-500">Soglia: {{ kpiThresholds.budget }}%</div>
          </div>

          <div class="text-center p-4 border rounded-lg">
            <div class="text-2xl font-bold" :class="timeThresholdClass">
              {{ timeUsagePercentage }}%
            </div>
            <div class="text-sm text-gray-600">Time Usage</div>
            <div class="text-xs text-gray-500">Soglia: {{ kpiThresholds.time }}%</div>
          </div>

          <div class="text-center p-4 border rounded-lg">
            <div class="text-2xl font-bold" :class="marginThresholdClass">
              {{ formatPercentage(kpiData.marginPercentage) }}
            </div>
            <div class="text-sm text-gray-600">Margine</div>
            <div class="text-xs text-gray-500">Soglia: {{ kpiThresholds.margin }}%</div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="text-center py-8">
      <p class="text-gray-500">Progetto non trovato</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import api from '@/utils/api'

// Props
const props = defineProps({
  project: { type: Object, default: null },
  loading: { type: Boolean, default: false }
})

// Emits
const emit = defineEmits(['refresh'])

// Reactive data
const refreshing = ref(false)
const showThresholdModal = ref(false)
const kpiData = ref({
  totalHours: 0,
  workDays: 0,
  totalCosts: 0,
  costVariance: 0,
  potentialRevenue: 0,
  actualRevenue: 0,
  marginPercentage: 0
})

const kpiThresholds = ref({
  budget: 80,
  time: 85,
  margin: 15
})

// Computed properties
const budgetUsagePercentage = computed(() => {
  if (!props.project?.budget || kpiData.value.totalCosts === 0) return 0
  return Math.round((kpiData.value.totalCosts / props.project.budget) * 100)
})

const timeUsagePercentage = computed(() => {
  if (!props.project?.estimated_hours || kpiData.value.totalHours === 0) return 0
  return Math.round((kpiData.value.totalHours / props.project.estimated_hours) * 100)
})

const costVarianceClass = computed(() => {
  const variance = kpiData.value.costVariance
  if (variance > 0) return 'text-red-600'
  if (variance < 0) return 'text-green-600'
  return 'text-gray-600'
})

const marginClass = computed(() => {
  const margin = kpiData.value.marginPercentage
  if (margin >= kpiThresholds.value.margin) return 'text-green-600'
  if (margin >= kpiThresholds.value.margin * 0.7) return 'text-yellow-600'
  return 'text-red-600'
})

const marginStatus = computed(() => {
  const margin = kpiData.value.marginPercentage
  if (margin >= kpiThresholds.value.margin) return 'Ottimo'
  if (margin >= kpiThresholds.value.margin * 0.7) return 'Accettabile'
  return 'Critico'
})

const budgetThresholdClass = computed(() => {
  const usage = budgetUsagePercentage.value
  if (usage >= kpiThresholds.value.budget) return 'text-red-600'
  if (usage >= kpiThresholds.value.budget * 0.8) return 'text-yellow-600'
  return 'text-green-600'
})

const timeThresholdClass = computed(() => {
  const usage = timeUsagePercentage.value
  if (usage >= kpiThresholds.value.time) return 'text-red-600'
  if (usage >= kpiThresholds.value.time * 0.8) return 'text-yellow-600'
  return 'text-green-600'
})

const marginThresholdClass = computed(() => {
  const margin = kpiData.value.marginPercentage
  if (margin >= kpiThresholds.value.margin) return 'text-green-600'
  if (margin >= kpiThresholds.value.margin * 0.7) return 'text-yellow-600'
  return 'text-red-600'
})

// Methods
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('it-IT', {
    style: 'currency',
    currency: 'EUR'
  }).format(amount || 0)
}

const formatHours = (hours) => {
  return `${hours || 0}h`
}

const formatPercentage = (percentage) => {
  return `${(percentage || 0).toFixed(1)}%`
}

const loadKPIData = async () => {
  if (!props.project?.id) return

  // API endpoint non esiste ancora, usa sempre calcoli fallback
  calculateFallbackKPIs()
}

const calculateFallbackKPIs = () => {
  // Calculate basic KPIs from project data
  const project = props.project
  if (!project) return

  // Basic calculations (these would normally come from API)
  kpiData.value = {
    totalHours: project.total_hours || 0,
    workDays: Math.ceil((project.total_hours || 0) / 8),
    totalCosts: (project.total_hours || 0) * 50, // Assuming 50€/hour average
    costVariance: ((project.total_hours || 0) * 50) - (project.budget || 0),
    potentialRevenue: project.budget || 0,
    actualRevenue: project.invoiced_amount || 0,
    marginPercentage: project.budget ?
      (((project.budget - ((project.total_hours || 0) * 50)) / project.budget) * 100) : 0
  }
}

const refreshKPIs = async () => {
  refreshing.value = true
  try {
    await loadKPIData()
    emit('refresh')
  } catch (error) {
    console.error('Error refreshing KPIs:', error)
  } finally {
    refreshing.value = false
  }
}

// Watchers
watch(() => props.project, (newProject) => {
  if (newProject) {
    loadKPIData()
  }
}, { immediate: true })

// Lifecycle
onMounted(() => {
  if (props.project) {
    loadKPIData()
  }
})
</script>