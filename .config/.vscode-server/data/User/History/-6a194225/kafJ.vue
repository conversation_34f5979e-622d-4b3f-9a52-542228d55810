<template>
  <div class="p-6">
    <h1 class="text-3xl font-bold text-red-500">PROGETTI FUNZIONA!</h1>
  </div>
</template>

<script setup>
// Componente minimo per test
console.log('Projects.vue loaded!')
</script>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Stato</label>
          <select
            v-model="filters.status"
            @change="applyFilters"
            class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">Tutti gli stati</option>
            <option value="planning">Pianificazione</option>
            <option value="active">Attivo</option>
            <option value="completed">Completato</option>
            <option value="on-hold">In Pausa</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Cliente</label>
          <select
            v-model="filters.client"
            @change="applyFilters"
            class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">Tutti i clienti</option>
            <option v-for="client in clients" :key="client.id" :value="client.id">
              {{ client.name }}
            </option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Ricerca</label>
          <input
            v-model="searchQuery"
            @input="search"
            type="text"
            placeholder="Cerca progetti..."
            class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
        </div>
        <div class="flex items-end">
          <button
            @click="resetFilters"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            Reset Filtri
          </button>
        </div>
      </div>
    </div>

    <!-- Projects List -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          Progetti ({{ filteredProjects.length }})
        </h3>
      </div>

      <div v-if="isLoading" class="p-6 text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"></div>
        <p class="mt-2 text-gray-600 dark:text-gray-400">Caricamento progetti...</p>
      </div>

      <div v-else-if="filteredProjects.length === 0" class="p-6 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessun progetto</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Inizia creando il tuo primo progetto.</p>
      </div>

      <div v-else class="divide-y divide-gray-200 dark:divide-gray-700">
        <div
          v-for="project in filteredProjects"
          :key="project.id"
          class="p-6 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
          @click="viewProject(project.id)"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                  {{ project.name }}
                </h4>
                <span
                  :class="getStatusClass(project.status)"
                  class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                >
                  {{ getStatusLabel(project.status) }}
                </span>
              </div>
              <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                {{ project.description }}
              </p>
              <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
                <span>Cliente: {{ project.client }}</span>
                <span class="mx-2">•</span>
                <span>Scadenza: {{ formatDate(project.deadline) }}</span>
                <span class="mx-2">•</span>
                <span>Budget: {{ formatCurrency(project.budget) }}</span>
              </div>
            </div>
            <div class="ml-4 flex items-center space-x-2">
              <div class="text-right">
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ project.progress }}%
                </div>
                <div class="w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1">
                  <div
                    class="bg-primary-600 h-2 rounded-full"
                    :style="{ width: project.progress + '%' }"
                  ></div>
                </div>
              </div>
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Project Modal -->
    <div v-if="showCreateModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showCreateModal = false"></div>

        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Nuovo Progetto</h3>
            <p class="text-gray-600 dark:text-gray-400">Funzionalità in fase di sviluppo...</p>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              @click="showCreateModal = false"
              class="w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Chiudi
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// Reactive data
const isLoading = ref(true)
const projects = ref([])
const clients = ref([])
const searchQuery = ref('')
const filters = ref({
  status: '',
  client: ''
})

// Mock data per ora
const mockProjects = [
  {
    id: 1,
    name: 'Progetto Alpha',
    description: 'Sviluppo piattaforma web per gestione clienti',
    client: 'Acme Corp',
    client_id: 1,
    status: 'active',
    progress: 75,
    deadline: '2024-03-15',
    budget: 50000,
    team_members: [
      { id: 1, full_name: 'Mario Rossi', profile_image: null }
    ]
  },
  {
    id: 2,
    name: 'Progetto Beta',
    description: 'Migrazione sistema legacy verso cloud',
    client: 'Tech Solutions',
    client_id: 2,
    status: 'planning',
    progress: 25,
    deadline: '2024-04-20',
    budget: 75000,
    team_members: [
      { id: 2, full_name: 'Laura Bianchi', profile_image: null }
    ]
  }
]

const mockClients = [
  { id: 1, name: 'Acme Corp' },
  { id: 2, name: 'Tech Solutions' }
]

// Computed
const filteredProjects = computed(() => {
  let filtered = projects.value

  if (filters.value.status) {
    filtered = filtered.filter(p => p.status === filters.value.status)
  }

  if (filters.value.client) {
    filtered = filtered.filter(p => p.client_id == filters.value.client)
  }

  if (searchQuery.value) {
    const search = searchQuery.value.toLowerCase()
    filtered = filtered.filter(p =>
      p.name.toLowerCase().includes(search) ||
      p.description.toLowerCase().includes(search) ||
      p.client.toLowerCase().includes(search)
    )
  }

  return filtered
})

// Methods
const loadProjects = async () => {
  isLoading.value = true
  try {
    // TODO: Sostituire con chiamata API reale
    await new Promise(resolve => setTimeout(resolve, 500))
    projects.value = mockProjects
    clients.value = mockClients
  } catch (error) {
    console.error('Error loading projects:', error)
  } finally {
    isLoading.value = false
  }
}

const search = () => {
  // La ricerca è reattiva tramite computed
}

const applyFilters = () => {
  // I filtri sono reattivi tramite computed
}

const resetFilters = () => {
  filters.value = {
    status: '',
    client: ''
  }
  searchQuery.value = ''
}

const createProject = () => {
  router.push('/app/projects/create')
}

const viewProject = (projectId) => {
  router.push(`/app/projects/${projectId}`)
}

const getStatusClass = (status) => {
  const classes = {
    planning: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    active: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    completed: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    'on-hold': 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
  }
  return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
}

const getStatusLabel = (status) => {
  const labels = {
    planning: 'Pianificazione',
    active: 'Attivo',
    completed: 'Completato',
    'on-hold': 'In Pausa'
  }
  return labels[status] || status
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('it-IT')
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('it-IT', {
    style: 'currency',
    currency: 'EUR'
  }).format(amount)
}

// Lifecycle
onMounted(() => {
  loadProjects()
})
</script>