<template>
  <div>
    <!-- Header -->
    <div class="mb-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white"><PERSON><PERSON><PERSON></h1>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Gestisci e monitora tutti i progetti aziendali
          </p>
        </div>
        <div class="mt-4 sm:mt-0">
          <button
            @click="createProject"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Nuovo Progetto
          </button>
        </div>
      </div>
    </div>

    <!-- Filtri e Ricerca -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Stato</label>
          <select
            v-model="filters.status"
            @change="applyFilters"
            class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">Tutti gli stati</option>
            <option value="planning">Pianificazione</option>
            <option value="active">Attivo</option>
            <option value="completed">Completato</option>
            <option value="on-hold">In Pausa</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Cliente</label>
          <select
            v-model="filters.client"
            @change="applyFilters"
            class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">Tutti i clienti</option>
            <option v-for="client in clients" :key="client.id" :value="client.id">
              {{ client.name }}
            </option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Ricerca</label>
          <input
            v-model="searchQuery"
            @input="search"
            type="text"
            placeholder="Cerca progetti..."
            class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
        </div>
        <div class="flex items-end">
          <button
            @click="resetFilters"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            Reset Filtri
          </button>
        </div>
      </div>
    </div>

    <!-- Projects List -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          Progetti ({{ filteredProjects.length }})
        </h3>
      </div>

      <div v-if="isLoading" class="p-6 text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"></div>
        <p class="mt-2 text-gray-600 dark:text-gray-400">Caricamento progetti...</p>
      </div>

      <div v-else-if="filteredProjects.length === 0" class="p-6 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessun progetto</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Inizia creando il tuo primo progetto.</p>
      </div>

      <div v-else class="divide-y divide-gray-200 dark:divide-gray-700">
        <div
          v-for="project in filteredProjects"
          :key="project.id"
          class="p-6 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
          @click="viewProject(project.id)"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                  {{ project.name }}
                </h4>
                <span
                  :class="getStatusClass(project.status)"
                  class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                >
                  {{ getStatusLabel(project.status) }}
                </span>
              </div>
              <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                {{ project.description }}
              </p>
              <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
                <span>Cliente: {{ project.client }}</span>
                <span class="mx-2">•</span>
                <span>Scadenza: {{ formatDate(project.deadline) }}</span>
                <span class="mx-2">•</span>
                <span>Budget: {{ formatCurrency(project.budget) }}</span>
              </div>
            </div>
            <div class="ml-4 flex items-center space-x-2">
              <div class="text-right">
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ project.progress }}%
                </div>
                <div class="w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1">
                  <div
                    class="bg-primary-600 h-2 rounded-full"
                    :style="{ width: project.progress + '%' }"
                  ></div>
                </div>
              </div>
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Project Modal -->
    <div v-if="showCreateModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showCreateModal = false"></div>

        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Nuovo Progetto</h3>
            <p class="text-gray-600 dark:text-gray-400">Funzionalità in fase di sviluppo...</p>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              @click="showCreateModal = false"
              class="w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Chiudi
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>