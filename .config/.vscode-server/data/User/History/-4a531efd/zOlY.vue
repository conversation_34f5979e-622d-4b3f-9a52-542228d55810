<template>
  <div class="project-gantt">
    <div class="bg-white shadow rounded-lg p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-6">Diagramma di Gantt</h3>

      <!-- Task Timeline List -->
      <div class="space-y-3">
        <div v-for="task in tasks" :key="task.id" class="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <div class="w-3 h-3 rounded-full" :class="getStatusColor(task.status)"></div>
                </div>
                <h4 class="text-sm font-medium text-gray-900">{{ task.name }}</h4>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" :class="getPriorityClass(task.priority)">
                  {{ getPriorityLabel(task.priority) }}
                </span>
              </div>
              <div class="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                <span v-if="task.start_date">Inizio: {{ formatDate(task.start_date) }}</span>
                <span v-if="task.due_date">Fine: {{ formatDate(task.due_date) }}</span>
                <span v-if="task.estimated_hours">Durata: {{ task.estimated_hours }}h</span>
              </div>
              <!-- Timeline Bar -->
              <div class="mt-3">
                <div class="flex items-center space-x-2">
                  <span class="text-xs text-gray-500 w-16">{{ formatDate(task.start_date) }}</span>
                  <div class="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      class="h-2 rounded-full transition-all duration-300"
                      :class="getTaskBarColor(task.status)"
                      :style="{ width: getTaskProgress(task) + '%' }"
                    ></div>
                  </div>
                  <span class="text-xs text-gray-500 w-16">{{ formatDate(task.due_date) }}</span>
                </div>
              </div>
            </div>
            <div class="flex items-center space-x-2 ml-4">
              <div class="text-right">
                <div class="text-xs text-gray-500">Progresso</div>
                <div class="text-sm font-medium text-gray-900">{{ getTaskProgress(task) }}%</div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="tasks.length === 0" class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <p class="text-gray-500 mt-2">Nessun task pianificato per questo progetto</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  project: { type: Object, default: null },
  loading: { type: Boolean, default: false }
})
</script>
