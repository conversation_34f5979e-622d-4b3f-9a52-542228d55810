*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[17:26:56] 




[17:26:57] Extension host agent started.
[17:26:57] [<unknown>][d673faba][ExtensionHostConnection] New connection established.
[17:26:57] [<unknown>][cebaa68c][ManagementConnection] New connection established.
[17:26:57] [<unknown>][d673faba][ExtensionHostConnection] <392> Launched Extension Host Process.
[17:26:57] ComputeTargetPlatform: linux-x64
[17:26:59] ComputeTargetPlatform: linux-x64
New EH opened, aborting shutdown
[17:31:57] New EH opened, aborting shutdown
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:5002
stack trace: Error: connect ECONNREFUSED 127.0.0.1:5002
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[17:50:08] Error: connect ECONNREFUSED 127.0.0.1:5002
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 5002
}
