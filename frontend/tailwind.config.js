/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        'brand-primary': {
          50: 'var(--brand-primary-50)',
          100: 'var(--brand-primary-100)',
          200: 'var(--brand-primary-200)',
          300: 'var(--brand-primary-300)',
          400: 'var(--brand-primary-400)',
          500: 'var(--brand-primary-500)',
          600: 'var(--brand-primary-600)',
          700: 'var(--brand-primary-700)',
          800: 'var(--brand-primary-800)',
          900: 'var(--brand-primary-900)',
        },
        'brand-secondary': {
          50: 'var(--brand-secondary-50)',
          100: 'var(--brand-secondary-100)',
          200: 'var(--brand-secondary-200)',
          300: 'var(--brand-secondary-300)',
          400: 'var(--brand-secondary-400)',
          500: 'var(--brand-secondary-500)',
          600: 'var(--brand-secondary-600)',
          700: 'var(--brand-secondary-700)',
          800: 'var(--brand-secondary-800)',
          900: 'var(--brand-secondary-900)',
        },
        'brand-accent': {
          50: 'var(--brand-accent-50)',
          100: 'var(--brand-accent-100)',
          200: 'var(--brand-accent-200)',
          300: 'var(--brand-accent-300)',
          400: 'var(--brand-accent-400)',
          500: 'var(--brand-accent-500)',
          600: 'var(--brand-accent-600)',
          700: 'var(--brand-accent-700)',
          800: 'var(--brand-accent-800)',
          900: 'var(--brand-accent-900)',
        },
        primary: {
          50: 'var(--brand-primary-50)',
          500: 'var(--brand-primary-500)',
          600: 'var(--brand-primary-600)',
          700: 'var(--brand-primary-700)',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      }
    },
  },
  plugins: [],
}