<template>
  <div class="flex">
    <!-- Desktop Sidebar -->
    <div class="hidden lg:flex lg:flex-shrink-0 lg:fixed lg:inset-y-0 z-10">
      <div
        class="flex flex-col transition-all duration-300"
        :class="[
          isCollapsed ? 'w-20' : 'w-64'
        ]"
      >
        <div class="flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm">
          <!-- Logo and Toggle -->
          <div class="flex items-center flex-shrink-0 px-4">
            <div class="flex items-center" :class="{ 'justify-center': isCollapsed }">
              <!-- Logo when sidebar is open -->
              <router-link
                to="/app/dashboard"
                class="flex items-center"
                :class="{ 'hidden': isCollapsed }"
              >
                <div class="w-10 h-10 bg-primary-600 rounded flex items-center justify-center mr-3">
                  <span class="text-white font-bold text-lg">{{ brandInitials }}</span>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">{{ companyName }}</h3>
              </router-link>
              <!-- Compact logo for collapsed sidebar -->
              <router-link
                to="/app/dashboard"
                class="flex items-center justify-center"
                :class="{ 'hidden': !isCollapsed }"
              >
                <div class="w-8 h-8 bg-primary-600 rounded flex items-center justify-center">
                  <span class="text-white font-bold text-sm">{{ brandInitials }}</span>
                </div>
              </router-link>
            </div>
            <button
              @click="toggleCollapsed"
              class="ml-auto text-gray-600 dark:text-gray-400 focus:outline-none hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded"
            >
              <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      :d="isCollapsed ? 'M13 5l7 7-7 7M5 5l7 7-7 7' : 'M11 19l-7-7 7-7m8 14l-7-7 7-7'" />
              </svg>
            </button>
          </div>

          <SidebarNavigation
            :is-collapsed="isCollapsed"
            @item-click="handleItemClick"
          />

          <SidebarFooter :is-collapsed="isCollapsed" />
        </div>
      </div>
    </div>

    <!-- Mobile Sidebar -->
    <div
      :class="[
        'fixed inset-y-0 left-0 z-30 w-64 bg-primary-700 transform transition-transform duration-300 ease-in-out lg:hidden',
        isMobileOpen ? 'translate-x-0' : '-translate-x-full'
      ]"
    >
      <div class="flex flex-col h-full pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="flex items-center justify-between px-4 mb-4">
          <router-link to="/app/dashboard" class="flex items-center">
            <div class="w-8 h-8 bg-primary-600 rounded flex items-center justify-center mr-3">
              <span class="text-white font-bold text-sm">{{ brandInitials }}</span>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">{{ companyName }}</h3>
          </router-link>
          <button
            @click="$emit('close')"
            class="p-2 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <SidebarNavigation
          :is-collapsed="false"
          @item-click="$emit('close')"
        />

        <SidebarFooter :is-collapsed="false" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useTenantStore } from '@/stores/tenant'
import SidebarNavigation from './SidebarNavigation.vue'
import SidebarFooter from './SidebarFooter.vue'

const props = defineProps({
  isMobileOpen: {
    type: Boolean,
    default: false
  }
})

defineEmits(['close'])

const tenantStore = useTenantStore()
const isCollapsed = ref(false)

const tenantConfig = computed(() => tenantStore.config || {})
const companyName = computed(() => tenantConfig.value.company?.name || 'DatPortal')
const brandInitials = computed(() => {
  const name = companyName.value
  return name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2)
})

function toggleCollapsed() {
  isCollapsed.value = !isCollapsed.value
}

function handleItemClick() {
  // Auto-expand sidebar when clicking items if collapsed
  if (isCollapsed.value) {
    isCollapsed.value = false
  }
}
</script>