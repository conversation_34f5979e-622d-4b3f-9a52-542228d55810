<template>
  <div>
    <!-- Main Navigation Item -->
    <router-link
      v-if="item.path !== '#'"
      :to="item.path"
      class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150"
      :class="[
        navItemClasses,
        { 'justify-center': isCollapsed }
      ]"
      active-class="text-primary-600 bg-primary-50 border-r-2 border-primary-600"
      @click="$emit('click')"
    >
      <SidebarIcon
        :icon="item.icon"
        :class="[
          'flex-shrink-0 h-6 w-6',
          { 'mr-0': isCollapsed, 'mr-3': !isCollapsed }
        ]"
      />
      <span
        v-if="!isCollapsed"
        class="truncate"
      >
        {{ item.name }}
      </span>
    </router-link>

    <!-- Disabled/Placeholder Item -->
    <div
      v-else
      class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 cursor-not-allowed opacity-75"
      :class="[
        'text-gray-400 hover:text-gray-500',
        { 'justify-center': isCollapsed }
      ]"
    >
      <SidebarIcon
        :icon="item.icon"
        :class="[
          'flex-shrink-0 h-6 w-6',
          { 'mr-0': isCollapsed, 'mr-3': !isCollapsed }
        ]"
      />
      <span
        v-if="!isCollapsed"
        class="truncate"
      >
        {{ item.name }}
      </span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import SidebarIcon from './SidebarIcon.vue'

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  isCollapsed: {
    type: Boolean,
    default: false
  }
})

defineEmits(['click'])

const navItemClasses = computed(() => [
  'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400'
])
</script>