<template>
  <span 
    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
    :class="badgeClasses"
  >
    <span v-if="showDot" class="w-1.5 h-1.5 rounded-full mr-1.5" :class="dotClasses"></span>
    {{ label }}
  </span>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  status: {
    type: String,
    required: true
  },
  type: {
    type: String,
    default: 'project', // project, task, user, etc.
    validator: (value) => ['project', 'task', 'user', 'generic'].includes(value)
  },
  showDot: {
    type: Boolean,
    default: false
  }
})

const statusConfig = {
  project: {
    planning: { label: 'Pianificazione', classes: 'bg-yellow-100 text-yellow-800', dot: 'bg-yellow-400' },
    active: { label: 'Attivo', classes: 'bg-green-100 text-green-800', dot: 'bg-green-400' },
    on_hold: { label: 'In Pausa', classes: 'bg-orange-100 text-orange-800', dot: 'bg-orange-400' },
    completed: { label: 'Completato', classes: 'bg-blue-100 text-blue-800', dot: 'bg-blue-400' },
    cancelled: { label: 'Annullato', classes: 'bg-red-100 text-red-800', dot: 'bg-red-400' }
  },
  task: {
    todo: { label: 'Da Fare', classes: 'bg-gray-100 text-gray-800', dot: 'bg-gray-400' },
    in_progress: { label: 'In Corso', classes: 'bg-blue-100 text-blue-800', dot: 'bg-blue-400' },
    review: { label: 'In Revisione', classes: 'bg-purple-100 text-purple-800', dot: 'bg-purple-400' },
    done: { label: 'Completato', classes: 'bg-green-100 text-green-800', dot: 'bg-green-400' },
    blocked: { label: 'Bloccato', classes: 'bg-red-100 text-red-800', dot: 'bg-red-400' }
  },
  user: {
    active: { label: 'Attivo', classes: 'bg-green-100 text-green-800', dot: 'bg-green-400' },
    inactive: { label: 'Inattivo', classes: 'bg-gray-100 text-gray-800', dot: 'bg-gray-400' },
    pending: { label: 'In Attesa', classes: 'bg-yellow-100 text-yellow-800', dot: 'bg-yellow-400' }
  },
  generic: {
    success: { label: 'Successo', classes: 'bg-green-100 text-green-800', dot: 'bg-green-400' },
    warning: { label: 'Attenzione', classes: 'bg-yellow-100 text-yellow-800', dot: 'bg-yellow-400' },
    error: { label: 'Errore', classes: 'bg-red-100 text-red-800', dot: 'bg-red-400' },
    info: { label: 'Info', classes: 'bg-blue-100 text-blue-800', dot: 'bg-blue-400' }
  }
}

const currentConfig = computed(() => {
  const typeConfig = statusConfig[props.type] || statusConfig.generic
  return typeConfig[props.status] || {
    label: props.status,
    classes: 'bg-gray-100 text-gray-800',
    dot: 'bg-gray-400'
  }
})

const label = computed(() => currentConfig.value.label)
const badgeClasses = computed(() => currentConfig.value.classes)
const dotClasses = computed(() => currentConfig.value.dot)
</script>

<style scoped>
/* Additional custom styles if needed */
</style>
