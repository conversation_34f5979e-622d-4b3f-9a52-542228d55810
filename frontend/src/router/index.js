import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Layout components
import AppLayout from '@/components/layout/AppLayout.vue'
import PublicLayout from '@/components/layout/PublicLayout.vue'

// Public views
import Home from '@/views/public/Home.vue'
import About from '@/views/public/About.vue'
import Contact from '@/views/public/Contact.vue'
import Services from '@/views/public/Services.vue'

// Auth views
import Login from '@/views/auth/Login.vue'
import Register from '@/views/auth/Register.vue'

// Protected views
import Dashboard from '@/views/dashboard/Dashboard.vue'
import Projects from '@/views/projects/Projects.vue'
import Personnel from '@/views/personnel/Personnel.vue'

const routes = [
  // Public routes
  {
    path: '/',
    component: PublicLayout,
    children: [
      { path: '', name: 'home', component: Home },
      { path: 'about', name: 'about', component: About },
      { path: 'contact', name: 'contact', component: Contact },
      { path: 'services', name: 'services', component: Services }
    ]
  },

  // Auth routes
  {
    path: '/auth',
    component: PublicLayout,
    children: [
      { path: 'login', name: 'login', component: Login },
      { path: 'register', name: 'register', component: Register }
    ]
  },

  // Protected routes
  {
    path: '/app',
    component: AppLayout,
    meta: { requiresAuth: true },
    children: [
      { path: '', redirect: '/app/dashboard' },
      { path: 'dashboard', name: 'dashboard', component: Dashboard },
      { path: 'projects', name: 'projects', component: Projects },
      { path: 'personnel', name: 'personnel', component: Personnel }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guard per autenticazione
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  if (to.meta.requiresAuth) {
    // Controlla l'autenticazione se non è già stata verificata
    if (!authStore.sessionChecked) {
      await authStore.initializeAuth()
    }

    if (!authStore.isAuthenticated) {
      next('/auth/login')
      return
    }
  }

  next()
})

export default router