/**
 * Brand Variables CSS
 * Sistema di variabili CSS per gestione branding configurabile
 * Utilizzato da Tailwind CSS per generare classi personalizzate
 */

:root {
  /* === BRAND COLORS === */
  /* Primary Color (Brand principale) */
  --brand-primary-50: #e6f4fb;
  --brand-primary-100: #b3e0f3;
  --brand-primary-200: #80cceb;
  --brand-primary-300: #4db7e3;
  --brand-primary-400: #1aa3dc;
  --brand-primary-500: #0080c0;
  --brand-primary-600: #006699;
  --brand-primary-700: #004d73;
  --brand-primary-800: #00334d;
  --brand-primary-900: #001a26;

  /* Secondary Color (Brand secondario) */
  --brand-secondary-50: #e6fff9;
  --brand-secondary-100: #b3ffec;
  --brand-secondary-200: #80ffdf;
  --brand-secondary-300: #4dffd3;
  --brand-secondary-400: #1affc6;
  --brand-secondary-500: #00cc99;
  --brand-secondary-600: #00a677;
  --brand-secondary-700: #007f59;
  --brand-secondary-800: #00533c;
  --brand-secondary-900: #00291e;

  /* Accent Color (Colore di accento) */
  --brand-accent-50: #fef7e6;
  --brand-accent-100: #fde8b3;
  --brand-accent-200: #fcd980;
  --brand-accent-300: #fbca4d;
  --brand-accent-400: #fabb1a;
  --brand-accent-500: #f59e0b;
  --brand-accent-600: #d97706;
  --brand-accent-700: #b45309;
  --brand-accent-800: #92400e;
  --brand-accent-900: #78350f;

  /* Success, Warning, Error Colors */
  --brand-success-50: #ecfdf5;
  --brand-success-500: #10b981;
  --brand-success-600: #059669;
  --brand-success-700: #047857;

  --brand-warning-50: #fffbeb;
  --brand-warning-500: #f59e0b;
  --brand-warning-600: #d97706;
  --brand-warning-700: #b45309;

  --brand-error-50: #fef2f2;
  --brand-error-500: #ef4444;
  --brand-error-600: #dc2626;
  --brand-error-700: #b91c1c;

  /* === TYPOGRAPHY === */
  /* Font Families */
  --brand-font-heading: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --brand-font-body: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --brand-font-mono: 'JetBrains Mono', 'Fira Code', Consolas, 'Courier New', monospace;

  /* Font Weights */
  --brand-font-weight-light: 300;
  --brand-font-weight-normal: 400;
  --brand-font-weight-medium: 500;
  --brand-font-weight-semibold: 600;
  --brand-font-weight-bold: 700;
  --brand-font-weight-extrabold: 800;

  /* Font Sizes */
  --brand-text-xs: 0.75rem;
  --brand-text-sm: 0.875rem;
  --brand-text-base: 1rem;
  --brand-text-lg: 1.125rem;
  --brand-text-xl: 1.25rem;
  --brand-text-2xl: 1.5rem;
  --brand-text-3xl: 1.875rem;
  --brand-text-4xl: 2.25rem;

  /* === SPACING === */
  --brand-spacing-xs: 0.25rem;
  --brand-spacing-sm: 0.5rem;
  --brand-spacing-md: 1rem;
  --brand-spacing-lg: 1.5rem;
  --brand-spacing-xl: 2rem;
  --brand-spacing-2xl: 3rem;
  --brand-spacing-3xl: 4rem;

  /* === BORDER RADIUS === */
  --brand-radius-none: 0;
  --brand-radius-sm: 0.125rem;
  --brand-radius-md: 0.375rem;
  --brand-radius-lg: 0.5rem;
  --brand-radius-xl: 0.75rem;
  --brand-radius-2xl: 1rem;
  --brand-radius-full: 9999px;

  /* === SHADOWS === */
  --brand-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --brand-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --brand-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --brand-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* === TRANSITIONS === */
  --brand-transition-fast: 150ms ease-in-out;
  --brand-transition-normal: 250ms ease-in-out;
  --brand-transition-slow: 350ms ease-in-out;

  /* === LAYOUT === */
  --brand-sidebar-width: 16rem;
  --brand-sidebar-collapsed-width: 5rem;
  --brand-header-height: 4rem;
  --brand-container-max-width: 1280px;

  /* === LOGO PATHS === */
  --brand-logo-main: url('/static/img/logo.svg');
  --brand-logo-compact: url('/static/img/logo_compact.svg');
  --brand-logo-white: url('/static/img/logo_white.svg');
  --brand-logo-dark: url('/static/img/logo_dark.svg');
}

/* Dark Mode Variables */
[data-theme="dark"], .dark {
  /* Override colors for dark mode */
  --brand-bg-primary: #111827;
  --brand-bg-secondary: #1f2937;
  --brand-bg-tertiary: #374151;
  --brand-text-primary: #f9fafb;
  --brand-text-secondary: #d1d5db;
  --brand-text-tertiary: #9ca3af;
  --brand-border-primary: #374151;
  --brand-border-secondary: #4b5563;
}

/* Light Mode Variables */
[data-theme="light"], :root {
  --brand-bg-primary: #ffffff;
  --brand-bg-secondary: #f9fafb;
  --brand-bg-tertiary: #f3f4f6;
  --brand-text-primary: #111827;
  --brand-text-secondary: #374151;
  --brand-text-tertiary: #6b7280;
  --brand-border-primary: #e5e7eb;
  --brand-border-secondary: #d1d5db;
}

/* === UTILITY CLASSES === */
.brand-primary {
  color: var(--brand-primary-500);
}

.brand-secondary {
  color: var(--brand-secondary-500);
}

.brand-accent {
  color: var(--brand-accent-500);
}

.bg-brand-primary {
  background-color: var(--brand-primary-500);
}

.bg-brand-secondary {
  background-color: var(--brand-secondary-500);
}

.bg-brand-accent {
  background-color: var(--brand-accent-500);
}

.border-brand-primary {
  border-color: var(--brand-primary-500);
}

.font-brand-heading {
  font-family: var(--brand-font-heading);
}

.font-brand-body {
  font-family: var(--brand-font-body);
}

.font-brand-mono {
  font-family: var(--brand-font-mono);
}

.transition-brand {
  transition: all var(--brand-transition-normal);
}

.shadow-brand {
  box-shadow: var(--brand-shadow-md);
}

.rounded-brand {
  border-radius: var(--brand-radius-md);
}

/* === COMPONENT SPECIFIC === */
.brand-button {
  background-color: var(--brand-primary-500);
  color: white;
  padding: var(--brand-spacing-sm) var(--brand-spacing-md);
  border-radius: var(--brand-radius-md);
  font-family: var(--brand-font-body);
  font-weight: var(--brand-font-weight-medium);
  transition: all var(--brand-transition-fast);
  box-shadow: var(--brand-shadow-sm);
}

.brand-button:hover {
  background-color: var(--brand-primary-600);
  box-shadow: var(--brand-shadow-md);
  transform: translateY(-1px);
}

.brand-card {
  background-color: var(--brand-bg-primary);
  border: 1px solid var(--brand-border-primary);
  border-radius: var(--brand-radius-lg);
  box-shadow: var(--brand-shadow-sm);
  transition: all var(--brand-transition-normal);
}

.brand-card:hover {
  box-shadow: var(--brand-shadow-md);
  transform: translateY(-2px);
}

.brand-input {
  background-color: var(--brand-bg-primary);
  border: 1px solid var(--brand-border-secondary);
  border-radius: var(--brand-radius-md);
  padding: var(--brand-spacing-sm) var(--brand-spacing-md);
  font-family: var(--brand-font-body);
  color: var(--brand-text-primary);
  transition: all var(--brand-transition-fast);
}

.brand-input:focus {
  outline: none;
  border-color: var(--brand-primary-500);
  box-shadow: 0 0 0 3px rgba(var(--brand-primary-500), 0.1);
}

/* === RESPONSIVE BREAKPOINTS === */
@media (max-width: 640px) {
  :root {
    --brand-sidebar-width: 100%;
    --brand-spacing-md: 0.75rem;
    --brand-spacing-lg: 1rem;
  }
}

@media (max-width: 768px) {
  :root {
    --brand-text-base: 0.875rem;
    --brand-text-lg: 1rem;
    --brand-text-xl: 1.125rem;
  }
}
