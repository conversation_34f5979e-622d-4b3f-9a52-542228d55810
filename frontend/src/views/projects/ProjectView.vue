<template>
  <div class="project-view">
    <!-- Project Header -->
    <ProjectHeader
      :project="project"
      :loading="loading"
      @edit="handleEdit"
      @delete="handleDelete"
    />

    <!-- Project Tabs -->
    <TabNavigation
      v-model="activeTab"
      :tabs="availableTabs"
      class="mb-6"
    />

    <!-- Tab Content -->
    <div class="tab-content">
      <keep-alive>
        <component
          :is="currentTabComponent"
          :project="project"
          :loading="loading"
        />
      </keep-alive>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProjectsStore } from '@/stores/projects'
import { useAuthStore } from '@/stores/auth'

// Components
import ProjectHeader from './components/ProjectHeader.vue'
import TabNavigation from '@/components/ui/TabNavigation.vue'
import ProjectOverview from './components/ProjectOverview.vue'
import ProjectTasks from './components/ProjectTasks.vue'
import ProjectTeam from './components/ProjectTeam.vue'
import ProjectFiles from './components/ProjectFiles.vue'
import ProjectKPI from './components/ProjectKPI.vue'
import ProjectGantt from './components/ProjectGantt.vue'
import ProjectTimesheet from './components/ProjectTimesheet.vue'

// Stores
const projectsStore = useProjectsStore()
const authStore = useAuthStore()
const route = useRoute()
const router = useRouter()

// State
const loading = ref(true)
const activeTab = ref('overview')

// Computed
const project = computed(() => projectsStore.currentProject)

const availableTabs = computed(() => {
  const tabs = [
    { id: 'overview', label: 'Panoramica', icon: 'chart-bar' },
    { id: 'tasks', label: 'Task', icon: 'clipboard-list' },
    { id: 'team', label: 'Team', icon: 'users' },
    { id: 'gantt', label: 'Gantt', icon: 'calendar' },
    { id: 'timesheet', label: 'Timesheet', icon: 'clock' },
    { id: 'files', label: 'File', icon: 'folder' },
    { id: 'kpi', label: 'KPI', icon: 'trending-up' }
  ]

  // Filter tabs based on permissions (keep all for now, add permission checks later if needed)
  return tabs.filter(tab => {
    // Basic tabs always visible
    if (['overview', 'tasks', 'team', 'files'].includes(tab.id)) {
      return true
    }

    // Advanced tabs based on permissions
    if (tab.id === 'kpi' && authStore.hasPermission('view_reports')) {
      return true
    }

    if (tab.id === 'gantt' && authStore.hasPermission('view_all_projects')) {
      return true
    }

    if (tab.id === 'timesheet' && authStore.hasPermission('view_own_timesheets')) {
      return true
    }

    return false
  })
})

const currentTabComponent = computed(() => {
  const components = {
    overview: ProjectOverview,
    tasks: ProjectTasks,
    team: ProjectTeam,
    files: ProjectFiles,
    kpi: ProjectKPI,
    gantt: ProjectGantt,
    timesheet: ProjectTimesheet
  }
  return components[activeTab.value] || ProjectOverview
})

// Methods
const loadProject = async () => {
  loading.value = true
  try {
    const projectId = route.params.id
    await projectsStore.fetchProject(projectId)
  } catch (error) {
    console.error('Error loading project:', error)
    // Handle error (show toast, redirect, etc.)
  } finally {
    loading.value = false
  }
}

const handleEdit = () => {
  router.push(`/projects/${route.params.id}/edit`)
}

const handleDelete = async () => {
  if (confirm('Sei sicuro di voler eliminare questo progetto?')) {
    try {
      await projectsStore.deleteProject(route.params.id)
      router.push('/projects')
    } catch (error) {
      console.error('Error deleting project:', error)
    }
  }
}

// Watchers
watch(() => route.params.id, (newId) => {
  if (newId) {
    loadProject()
  }
})

watch(() => route.hash, (newHash) => {
  if (newHash) {
    const tab = newHash.replace('#', '')
    if (availableTabs.value.find(t => t.id === tab)) {
      activeTab.value = tab
    }
  }
})

watch(activeTab, (newTab) => {
  // Update URL hash without triggering navigation
  const newHash = `#${newTab}`
  if (route.hash !== newHash) {
    router.replace({ ...route, hash: newHash })
  }
})

// Lifecycle
onMounted(() => {
  // Set initial tab from URL hash
  if (route.hash) {
    const tab = route.hash.replace('#', '')
    if (availableTabs.value.find(t => t.id === tab)) {
      activeTab.value = tab
    }
  }

  loadProject()
})
</script>

<style scoped>
.project-view {
  @apply container mx-auto px-4 py-6;
}

.tab-content {
  @apply min-h-96;
}
</style>
