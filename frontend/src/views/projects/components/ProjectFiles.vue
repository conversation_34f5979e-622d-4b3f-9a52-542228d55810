<template>
  <div class="project-files">
    <div class="bg-white shadow rounded-lg p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-6">File del Progetto</h3>
      
      <div class="text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">Gestione File in sviluppo</h3>
        <p class="mt-1 text-sm text-gray-500">Upload e gestione documenti in arrivo...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  project: { type: Object, default: null },
  loading: { type: Boolean, default: false }
})
</script>
