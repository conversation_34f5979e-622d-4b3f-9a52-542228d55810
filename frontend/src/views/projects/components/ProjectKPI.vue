<template>
  <div class="project-kpi">
    <div class="bg-white shadow rounded-lg p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-6">KPI del Progetto</h3>
      
      <div class="text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">KPI e Metriche in sviluppo</h3>
        <p class="mt-1 text-sm text-gray-500">Dashboard KPI progetto in arrivo...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  project: { type: Object, default: null },
  loading: { type: Boolean, default: false }
})
</script>
