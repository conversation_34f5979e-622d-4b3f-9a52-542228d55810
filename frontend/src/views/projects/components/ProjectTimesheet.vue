<template>
  <div class="project-timesheet">
    <div class="bg-white shadow rounded-lg p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-6">Timesheet del Progetto</h3>

      <!-- Timesheet Summary -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="bg-blue-50 p-4 rounded-lg">
          <div class="text-sm text-blue-600">Ore Totali Mese</div>
          <div class="text-2xl font-bold text-blue-900">{{ totalHours }}h</div>
        </div>
        <div class="bg-green-50 p-4 rounded-lg">
          <div class="text-sm text-green-600">Membri Attivi</div>
          <div class="text-2xl font-bold text-green-900">{{ teamMembers.length }}</div>
        </div>
        <div class="bg-purple-50 p-4 rounded-lg">
          <div class="text-sm text-purple-600">Media Giornaliera</div>
          <div class="text-2xl font-bold text-purple-900">{{ averageDaily }}h</div>
        </div>
      </div>

      <!-- Timesheet Entries -->
      <div class="space-y-4">
        <div v-for="entry in timesheetEntries" :key="entry.id" class="border border-gray-200 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <img v-if="entry.user.profile_image" :src="entry.user.profile_image" :alt="entry.user.full_name" class="h-8 w-8 rounded-full">
                <div v-else class="h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <span class="text-xs font-medium text-gray-600">{{ getInitials(entry.user.full_name) }}</span>
                </div>
              </div>
              <div>
                <h4 class="text-sm font-medium text-gray-900">{{ entry.user.full_name }}</h4>
                <p class="text-xs text-gray-500">{{ formatDate(entry.date) }}</p>
              </div>
            </div>
            <div class="flex items-center space-x-4">
              <div class="text-right">
                <div class="text-sm font-medium text-gray-900">{{ entry.hours }}h</div>
                <div class="text-xs text-gray-500">{{ entry.task?.name || 'Generale' }}</div>
              </div>
            </div>
          </div>
          <div v-if="entry.description" class="mt-2 text-sm text-gray-600">
            {{ entry.description }}
          </div>
        </div>
        <div v-if="timesheetEntries.length === 0" class="text-center py-8">
          <p class="text-gray-500">Nessuna registrazione timesheet per questo progetto</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  project: { type: Object, default: null },
  loading: { type: Boolean, default: false }
})

const teamMembers = computed(() => {
  return props.project?.team_members || []
})

const timesheetEntries = computed(() => {
  // TODO: Implementare caricamento timesheet reali dall'API
  return props.project?.timesheet_entries || []
})

const totalHours = computed(() => {
  return timesheetEntries.value.reduce((total, entry) => total + (entry.hours || 0), 0)
})

const averageDaily = computed(() => {
  const entries = timesheetEntries.value
  if (entries.length === 0) return 0

  const uniqueDays = new Set(entries.map(e => e.date))
  return (totalHours.value / uniqueDays.size).toFixed(1)
})

const getInitials = (fullName) => {
  if (!fullName) return '??'
  return fullName
    .split(' ')
    .map(name => name.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('')
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('it-IT')
}
</script>
