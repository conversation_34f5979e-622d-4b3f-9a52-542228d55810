import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import { useAuthStore } from './stores/auth'
import './assets/css/main.css'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)

// Inizializza l'autenticazione prima di montare l'app
const authStore = useAuthStore()
authStore.initializeAuth().then(() => {
  app.mount('#app')
})