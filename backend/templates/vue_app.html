<!DOCTYPE html>
<html lang="it">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>DatPortal - Enterprise Intranet</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    
    <!-- Meta tags for SEO -->
    <meta name="description" content="DatPortal - Sistema di gestione progetti, task e risorse">
    <meta name="keywords" content="progetti, task, gestione, risorse, KPI, dashboard">
    <meta name="author" content="DatVinci">
    
    <!-- Open Graph meta tags -->
    <meta property="og:title" content="DatPortal">
    <meta property="og:description" content="Sistema di gestione progetti, task e risorse">
    <meta property="og:type" content="website">
    
    <!-- CSRF Token for API requests -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    
    
    
  </head>
  <body>
    <div id="app"></div>
    
    <!-- Global Configuration for Vue.js -->
    <script>
      // Global app configuration
      window.APP_CONFIG = {
        apiUrl: '/api',
        baseUrl: '{{ request.url_root }}',
        csrfToken: '{{ csrf_token() }}',
        user: {{ current_user.to_dict()|tojson if current_user.is_authenticated else 'null' }},
        isAuthenticated: {{ 'true' if current_user.is_authenticated else 'false' }},
        version: '1.0.0',
        environment: '{{ config.ENV }}',
        debug: {{ 'true' if config.DEBUG else 'false' }}
      };

      // La configurazione tenant verrà caricata dal frontend Vue.js

      // Global error handler
      window.addEventListener('error', function(event) {
        console.error('Global error:', event.error);
      });

      // Global unhandled promise rejection handler
      window.addEventListener('unhandledrejection', function(event) {
        console.error('Unhandled promise rejection:', event.reason);
      });
    </script>
  </body>
</html>