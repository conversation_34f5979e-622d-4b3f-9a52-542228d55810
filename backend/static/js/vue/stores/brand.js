/**
 * Brand Store - Pinia Store per gestione branding
 * Gestisce colori, font, logo e altre impostazioni di brand
 */

import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'

export const useBrandStore = defineStore('brand', () => {
  // === STATE ===
  const brandConfig = ref({
    // Brand Identity (personalizzabile per ogni tenant)
    name: window.APP_CONFIG?.brand?.name || 'DatVinci',
    tagline: window.APP_CONFIG?.brand?.tagline || 'Innovazione per il futuro',
    description: window.APP_CONFIG?.brand?.description || 'Supportiamo le aziende nel loro percorso di innovazione attraverso soluzioni tecnologiche all\'avanguardia.',

    // Logo Configuration
    logos: {
      main: '/static/img/logo.svg',
      compact: '/static/img/logo_compact.svg',
      white: '/static/img/logo_white.svg',
      dark: '/static/img/logo_dark.svg',
      favicon: '/static/favicon.ico'
    },

    // Color Palette
    colors: {
      primary: {
        50: '#e6f4fb',
        100: '#b3e0f3',
        200: '#80cceb',
        300: '#4db7e3',
        400: '#1aa3dc',
        500: '#0080c0',
        600: '#006699',
        700: '#004d73',
        800: '#00334d',
        900: '#001a26'
      },
      secondary: {
        50: '#e6fff9',
        100: '#b3ffec',
        200: '#80ffdf',
        300: '#4dffd3',
        400: '#1affc6',
        500: '#00cc99',
        600: '#00a677',
        700: '#007f59',
        800: '#00533c',
        900: '#00291e'
      },
      accent: {
        50: '#fef7e6',
        100: '#fde8b3',
        200: '#fcd980',
        300: '#fbca4d',
        400: '#fabb1a',
        500: '#f59e0b',
        600: '#d97706',
        700: '#b45309',
        800: '#92400e',
        900: '#78350f'
      },
      success: {
        50: '#ecfdf5',
        500: '#10b981',
        600: '#059669',
        700: '#047857'
      },
      warning: {
        50: '#fffbeb',
        500: '#f59e0b',
        600: '#d97706',
        700: '#b45309'
      },
      error: {
        50: '#fef2f2',
        500: '#ef4444',
        600: '#dc2626',
        700: '#b91c1c'
      }
    },

    // Typography
    typography: {
      fonts: {
        heading: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
        body: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
        mono: "'JetBrains Mono', 'Fira Code', Consolas, 'Courier New', monospace"
      },
      weights: {
        light: 300,
        normal: 400,
        medium: 500,
        semibold: 600,
        bold: 700,
        extrabold: 800
      },
      sizes: {
        xs: '0.75rem',
        sm: '0.875rem',
        base: '1rem',
        lg: '1.125rem',
        xl: '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem',
        '4xl': '2.25rem'
      }
    },

    // Layout & Spacing
    layout: {
      spacing: {
        xs: '0.25rem',
        sm: '0.5rem',
        md: '1rem',
        lg: '1.5rem',
        xl: '2rem',
        '2xl': '3rem',
        '3xl': '4rem'
      },
      borderRadius: {
        none: '0',
        sm: '0.125rem',
        md: '0.375rem',
        lg: '0.5rem',
        xl: '0.75rem',
        '2xl': '1rem',
        full: '9999px'
      },
      shadows: {
        sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
      },
      transitions: {
        fast: '150ms ease-in-out',
        normal: '250ms ease-in-out',
        slow: '350ms ease-in-out'
      }
    },

    // Component Styles
    components: {
      sidebar: {
        width: '16rem',
        collapsedWidth: '5rem'
      },
      header: {
        height: '4rem'
      },
      container: {
        maxWidth: '1280px'
      }
    }
  })

  // Current theme (light/dark)
  const currentTheme = ref('light')

  // === COMPUTED ===
  const primaryColor = computed(() => brandConfig.value.colors.primary[500])
  const secondaryColor = computed(() => brandConfig.value.colors.secondary[500])
  const accentColor = computed(() => brandConfig.value.colors.accent[500])

  const currentLogo = computed(() => {
    if (currentTheme.value === 'dark') {
      return brandConfig.value.logos.white || brandConfig.value.logos.main
    }
    return brandConfig.value.logos.main
  })

  const compactLogo = computed(() => {
    if (currentTheme.value === 'dark') {
      return brandConfig.value.logos.white || brandConfig.value.logos.compact
    }
    return brandConfig.value.logos.compact
  })

  // === ACTIONS ===

  /**
   * Aggiorna la configurazione del brand
   * @param {Object} newConfig - Nuova configurazione (merge con quella esistente)
   */
  function updateBrandConfig(newConfig) {
    brandConfig.value = {
      ...brandConfig.value,
      ...newConfig,
      colors: { ...brandConfig.value.colors, ...newConfig.colors },
      typography: { ...brandConfig.value.typography, ...newConfig.typography },
      layout: { ...brandConfig.value.layout, ...newConfig.layout },
      components: { ...brandConfig.value.components, ...newConfig.components }
    }

    // Applica le modifiche al CSS
    applyBrandToCss()
  }

  /**
   * Cambia il tema corrente
   * @param {string} theme - 'light' o 'dark'
   */
  function setTheme(theme) {
    currentTheme.value = theme
    document.documentElement.setAttribute('data-theme', theme)

    if (theme === 'dark') {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }

    // Salva la preferenza
    localStorage.setItem('brand-theme', theme)
  }

  /**
   * Applica la configurazione brand alle variabili CSS
   */
  function applyBrandToCss() {
    const root = document.documentElement
    const config = brandConfig.value

    // Applica colori
    Object.entries(config.colors.primary).forEach(([key, value]) => {
      root.style.setProperty(`--brand-primary-${key}`, value)
    })

    Object.entries(config.colors.secondary).forEach(([key, value]) => {
      root.style.setProperty(`--brand-secondary-${key}`, value)
    })

    Object.entries(config.colors.accent).forEach(([key, value]) => {
      root.style.setProperty(`--brand-accent-${key}`, value)
    })

    // Applica tipografia
    root.style.setProperty('--brand-font-heading', config.typography.fonts.heading)
    root.style.setProperty('--brand-font-body', config.typography.fonts.body)
    root.style.setProperty('--brand-font-mono', config.typography.fonts.mono)

    Object.entries(config.typography.weights).forEach(([key, value]) => {
      root.style.setProperty(`--brand-font-weight-${key}`, value)
    })

    Object.entries(config.typography.sizes).forEach(([key, value]) => {
      root.style.setProperty(`--brand-text-${key}`, value)
    })

    // Applica layout
    Object.entries(config.layout.spacing).forEach(([key, value]) => {
      root.style.setProperty(`--brand-spacing-${key}`, value)
    })

    Object.entries(config.layout.borderRadius).forEach(([key, value]) => {
      root.style.setProperty(`--brand-radius-${key}`, value)
    })

    Object.entries(config.layout.shadows).forEach(([key, value]) => {
      root.style.setProperty(`--brand-shadow-${key}`, value)
    })

    Object.entries(config.layout.transitions).forEach(([key, value]) => {
      root.style.setProperty(`--brand-transition-${key}`, value)
    })

    // Applica logo paths
    Object.entries(config.logos).forEach(([key, value]) => {
      root.style.setProperty(`--brand-logo-${key}`, `url('${value}')`)
    })

    // Applica componenti
    root.style.setProperty('--brand-sidebar-width', config.components.sidebar.width)
    root.style.setProperty('--brand-sidebar-collapsed-width', config.components.sidebar.collapsedWidth)
    root.style.setProperty('--brand-header-height', config.components.header.height)
    root.style.setProperty('--brand-container-max-width', config.components.container.maxWidth)
  }

  /**
   * Carica la configurazione brand da API o localStorage
   */
  async function loadBrandConfig() {
    try {
      // Prova a caricare da API (per configurazioni salvate)
      const response = await fetch('/api/brand/config')
      if (response.ok) {
        const config = await response.json()
        updateBrandConfig(config.data)
      }
    } catch (error) {
      console.log('Using default brand configuration')
    }

    // Carica tema salvato
    const savedTheme = localStorage.getItem('brand-theme') || 'light'
    setTheme(savedTheme)

    // Applica configurazione iniziale
    applyBrandToCss()
  }

  /**
   * Salva la configurazione brand
   */
  async function saveBrandConfig() {
    try {
      await fetch('/api/brand/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': window.APP_CONFIG.csrfToken
        },
        body: JSON.stringify(brandConfig.value)
      })
    } catch (error) {
      console.error('Failed to save brand configuration:', error)
    }
  }

  /**
   * Reset alla configurazione di default
   */
  function resetBrandConfig() {
    // Ricarica la configurazione di default
    loadBrandConfig()
  }

  // Watch per auto-save quando cambia la configurazione
  watch(brandConfig, () => {
    applyBrandToCss()
  }, { deep: true })

  return {
    // State
    brandConfig,
    currentTheme,

    // Computed
    primaryColor,
    secondaryColor,
    accentColor,
    currentLogo,
    compactLogo,

    // Actions
    updateBrandConfig,
    setTheme,
    applyBrandToCss,
    loadBrandConfig,
    saveBrandConfig,
    resetBrandConfig
  }
})
