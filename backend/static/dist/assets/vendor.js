var Wd=Object.defineProperty;var $d=(e,t,n)=>t in e?Wd(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var N=(e,t,n)=>$d(e,typeof t!="symbol"?t+"":t,n);/**
* @vue/shared v3.5.16
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ca(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const pt={},Gn=[],Te=()=>{},Ud=()=>!1,xo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCode<PERSON>t(2)<97),ua=e=>e.startsWith("onUpdate:"),Dt=Object.assign,fa=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Kd=Object.prototype.hasOwnProperty,ut=(e,t)=>Kd.call(e,t),q=Array.isArray,Jn=e=>li(e)==="[object Map]",cs=e=>li(e)==="[object Set]",Za=e=>li(e)==="[object Date]",tt=e=>typeof e=="function",Ct=e=>typeof e=="string",be=e=>typeof e=="symbol",gt=e=>e!==null&&typeof e=="object",Nu=e=>(gt(e)||tt(e))&&tt(e.then)&&tt(e.catch),Bu=Object.prototype.toString,li=e=>Bu.call(e),qd=e=>li(e).slice(8,-1),zu=e=>li(e)==="[object Object]",ha=e=>Ct(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,As=ca(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),vo=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Yd=/-(\w)/g,de=vo(e=>e.replace(Yd,(t,n)=>n?n.toUpperCase():"")),Xd=/\B([A-Z])/g,bn=vo(e=>e.replace(Xd,"-$1").toLowerCase()),wo=vo(e=>e.charAt(0).toUpperCase()+e.slice(1)),qo=vo(e=>e?`on${wo(e)}`:""),fn=(e,t)=>!Object.is(e,t),Ii=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Hu=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},Zi=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Gd=e=>{const t=Ct(e)?Number(e):NaN;return isNaN(t)?e:t};let tl;const So=()=>tl||(tl=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function da(e){if(q(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],i=Ct(s)?tp(s):da(s);if(i)for(const o in i)t[o]=i[o]}return t}else if(Ct(e)||gt(e))return e}const Jd=/;(?![^(]*\))/g,Qd=/:([^]+)/,Zd=/\/\*[^]*?\*\//g;function tp(e){const t={};return e.replace(Zd,"").split(Jd).forEach(n=>{if(n){const s=n.split(Qd);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function pa(e){let t="";if(Ct(e))t=e;else if(q(e))for(let n=0;n<e.length;n++){const s=pa(e[n]);s&&(t+=s+" ")}else if(gt(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const ep="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",np=ca(ep);function ju(e){return!!e||e===""}function sp(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=ci(e[s],t[s]);return n}function ci(e,t){if(e===t)return!0;let n=Za(e),s=Za(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=be(e),s=be(t),n||s)return e===t;if(n=q(e),s=q(t),n||s)return n&&s?sp(e,t):!1;if(n=gt(e),s=gt(t),n||s){if(!n||!s)return!1;const i=Object.keys(e).length,o=Object.keys(t).length;if(i!==o)return!1;for(const r in e){const a=e.hasOwnProperty(r),l=t.hasOwnProperty(r);if(a&&!l||!a&&l||!ci(e[r],t[r]))return!1}}return String(e)===String(t)}function ga(e,t){return e.findIndex(n=>ci(n,t))}const Vu=e=>!!(e&&e.__v_isRef===!0),ip=e=>Ct(e)?e:e==null?"":q(e)||gt(e)&&(e.toString===Bu||!tt(e.toString))?Vu(e)?ip(e.value):JSON.stringify(e,Wu,2):String(e),Wu=(e,t)=>Vu(t)?Wu(e,t.value):Jn(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,i],o)=>(n[Yo(s,o)+" =>"]=i,n),{})}:cs(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Yo(n))}:be(t)?Yo(t):gt(t)&&!q(t)&&!zu(t)?String(t):t,Yo=(e,t="")=>{var n;return be(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Bt;class $u{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Bt,!t&&Bt&&(this.index=(Bt.scopes||(Bt.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Bt;try{return Bt=this,t()}finally{Bt=n}}}on(){++this._on===1&&(this.prevScope=Bt,Bt=this)}off(){this._on>0&&--this._on===0&&(Bt=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const i=this.parent.scopes.pop();i&&i!==this&&(this.parent.scopes[this.index]=i,i.index=this.index)}this.parent=void 0}}}function Uu(e){return new $u(e)}function Ku(){return Bt}function op(e,t=!1){Bt&&Bt.cleanups.push(e)}let _t;const Xo=new WeakSet;class qu{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Bt&&Bt.active&&Bt.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Xo.has(this)&&(Xo.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Xu(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,el(this),Gu(this);const t=_t,n=ge;_t=this,ge=!0;try{return this.fn()}finally{Ju(this),_t=t,ge=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)_a(t);this.deps=this.depsTail=void 0,el(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Xo.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Sr(this)&&this.run()}get dirty(){return Sr(this)}}let Yu=0,Os,Rs;function Xu(e,t=!1){if(e.flags|=8,t){e.next=Rs,Rs=e;return}e.next=Os,Os=e}function ma(){Yu++}function ba(){if(--Yu>0)return;if(Rs){let t=Rs;for(Rs=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Os;){let t=Os;for(Os=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Gu(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Ju(e){let t,n=e.depsTail,s=n;for(;s;){const i=s.prevDep;s.version===-1?(s===n&&(n=i),_a(s),rp(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=i}e.deps=t,e.depsTail=n}function Sr(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Qu(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Qu(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Ws)||(e.globalVersion=Ws,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Sr(e))))return;e.flags|=2;const t=e.dep,n=_t,s=ge;_t=e,ge=!0;try{Gu(e);const i=e.fn(e._value);(t.version===0||fn(i,e._value))&&(e.flags|=128,e._value=i,t.version++)}catch(i){throw t.version++,i}finally{_t=n,ge=s,Ju(e),e.flags&=-3}}function _a(e,t=!1){const{dep:n,prevSub:s,nextSub:i}=e;if(s&&(s.nextSub=i,e.prevSub=void 0),i&&(i.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)_a(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function rp(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let ge=!0;const Zu=[];function Ye(){Zu.push(ge),ge=!1}function Xe(){const e=Zu.pop();ge=e===void 0?!0:e}function el(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=_t;_t=void 0;try{t()}finally{_t=n}}}let Ws=0;class ap{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class ya{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!_t||!ge||_t===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==_t)n=this.activeLink=new ap(_t,this),_t.deps?(n.prevDep=_t.depsTail,_t.depsTail.nextDep=n,_t.depsTail=n):_t.deps=_t.depsTail=n,tf(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=_t.depsTail,n.nextDep=void 0,_t.depsTail.nextDep=n,_t.depsTail=n,_t.deps===n&&(_t.deps=s)}return n}trigger(t){this.version++,Ws++,this.notify(t)}notify(t){ma();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{ba()}}}function tf(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)tf(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const to=new WeakMap,Tn=Symbol(""),Mr=Symbol(""),$s=Symbol("");function zt(e,t,n){if(ge&&_t){let s=to.get(e);s||to.set(e,s=new Map);let i=s.get(n);i||(s.set(n,i=new ya),i.map=s,i.key=n),i.track()}}function Ve(e,t,n,s,i,o){const r=to.get(e);if(!r){Ws++;return}const a=l=>{l&&l.trigger()};if(ma(),t==="clear")r.forEach(a);else{const l=q(e),c=l&&ha(n);if(l&&n==="length"){const u=Number(s);r.forEach((f,h)=>{(h==="length"||h===$s||!be(h)&&h>=u)&&a(f)})}else switch((n!==void 0||r.has(void 0))&&a(r.get(n)),c&&a(r.get($s)),t){case"add":l?c&&a(r.get("length")):(a(r.get(Tn)),Jn(e)&&a(r.get(Mr)));break;case"delete":l||(a(r.get(Tn)),Jn(e)&&a(r.get(Mr)));break;case"set":Jn(e)&&a(r.get(Tn));break}}ba()}function lp(e,t){const n=to.get(e);return n&&n.get(t)}function Vn(e){const t=lt(e);return t===e?t:(zt(t,"iterate",$s),fe(e)?t:t.map(It))}function Mo(e){return zt(e=lt(e),"iterate",$s),e}const cp={__proto__:null,[Symbol.iterator](){return Go(this,Symbol.iterator,It)},concat(...e){return Vn(this).concat(...e.map(t=>q(t)?Vn(t):t))},entries(){return Go(this,"entries",e=>(e[1]=It(e[1]),e))},every(e,t){return Fe(this,"every",e,t,void 0,arguments)},filter(e,t){return Fe(this,"filter",e,t,n=>n.map(It),arguments)},find(e,t){return Fe(this,"find",e,t,It,arguments)},findIndex(e,t){return Fe(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Fe(this,"findLast",e,t,It,arguments)},findLastIndex(e,t){return Fe(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Fe(this,"forEach",e,t,void 0,arguments)},includes(...e){return Jo(this,"includes",e)},indexOf(...e){return Jo(this,"indexOf",e)},join(e){return Vn(this).join(e)},lastIndexOf(...e){return Jo(this,"lastIndexOf",e)},map(e,t){return Fe(this,"map",e,t,void 0,arguments)},pop(){return ds(this,"pop")},push(...e){return ds(this,"push",e)},reduce(e,...t){return nl(this,"reduce",e,t)},reduceRight(e,...t){return nl(this,"reduceRight",e,t)},shift(){return ds(this,"shift")},some(e,t){return Fe(this,"some",e,t,void 0,arguments)},splice(...e){return ds(this,"splice",e)},toReversed(){return Vn(this).toReversed()},toSorted(e){return Vn(this).toSorted(e)},toSpliced(...e){return Vn(this).toSpliced(...e)},unshift(...e){return ds(this,"unshift",e)},values(){return Go(this,"values",It)}};function Go(e,t,n){const s=Mo(e),i=s[t]();return s!==e&&!fe(e)&&(i._next=i.next,i.next=()=>{const o=i._next();return o.value&&(o.value=n(o.value)),o}),i}const up=Array.prototype;function Fe(e,t,n,s,i,o){const r=Mo(e),a=r!==e&&!fe(e),l=r[t];if(l!==up[t]){const f=l.apply(e,o);return a?It(f):f}let c=n;r!==e&&(a?c=function(f,h){return n.call(this,It(f),h,e)}:n.length>2&&(c=function(f,h){return n.call(this,f,h,e)}));const u=l.call(r,c,s);return a&&i?i(u):u}function nl(e,t,n,s){const i=Mo(e);let o=n;return i!==e&&(fe(e)?n.length>3&&(o=function(r,a,l){return n.call(this,r,a,l,e)}):o=function(r,a,l){return n.call(this,r,It(a),l,e)}),i[t](o,...s)}function Jo(e,t,n){const s=lt(e);zt(s,"iterate",$s);const i=s[t](...n);return(i===-1||i===!1)&&wa(n[0])?(n[0]=lt(n[0]),s[t](...n)):i}function ds(e,t,n=[]){Ye(),ma();const s=lt(e)[t].apply(e,n);return ba(),Xe(),s}const fp=ca("__proto__,__v_isRef,__isVue"),ef=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(be));function hp(e){be(e)||(e=String(e));const t=lt(this);return zt(t,"has",e),t.hasOwnProperty(e)}class nf{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const i=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!i;if(n==="__v_isReadonly")return i;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(i?o?wp:af:o?rf:of).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const r=q(t);if(!i){let l;if(r&&(l=cp[n]))return l;if(n==="hasOwnProperty")return hp}const a=Reflect.get(t,n,kt(t)?t:s);return(be(n)?ef.has(n):fp(n))||(i||zt(t,"get",n),o)?a:kt(a)?r&&ha(n)?a:a.value:gt(a)?i?cf(a):ui(a):a}}class sf extends nf{constructor(t=!1){super(!1,t)}set(t,n,s,i){let o=t[n];if(!this._isShallow){const l=pn(o);if(!fe(s)&&!pn(s)&&(o=lt(o),s=lt(s)),!q(t)&&kt(o)&&!kt(s))return l?!1:(o.value=s,!0)}const r=q(t)&&ha(n)?Number(n)<t.length:ut(t,n),a=Reflect.set(t,n,s,kt(t)?t:i);return t===lt(i)&&(r?fn(s,o)&&Ve(t,"set",n,s):Ve(t,"add",n,s)),a}deleteProperty(t,n){const s=ut(t,n);t[n];const i=Reflect.deleteProperty(t,n);return i&&s&&Ve(t,"delete",n,void 0),i}has(t,n){const s=Reflect.has(t,n);return(!be(n)||!ef.has(n))&&zt(t,"has",n),s}ownKeys(t){return zt(t,"iterate",q(t)?"length":Tn),Reflect.ownKeys(t)}}class dp extends nf{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const pp=new sf,gp=new dp,mp=new sf(!0);const Cr=e=>e,bi=e=>Reflect.getPrototypeOf(e);function bp(e,t,n){return function(...s){const i=this.__v_raw,o=lt(i),r=Jn(o),a=e==="entries"||e===Symbol.iterator&&r,l=e==="keys"&&r,c=i[e](...s),u=n?Cr:t?eo:It;return!t&&zt(o,"iterate",l?Mr:Tn),{next(){const{value:f,done:h}=c.next();return h?{value:f,done:h}:{value:a?[u(f[0]),u(f[1])]:u(f),done:h}},[Symbol.iterator](){return this}}}}function _i(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function _p(e,t){const n={get(i){const o=this.__v_raw,r=lt(o),a=lt(i);e||(fn(i,a)&&zt(r,"get",i),zt(r,"get",a));const{has:l}=bi(r),c=t?Cr:e?eo:It;if(l.call(r,i))return c(o.get(i));if(l.call(r,a))return c(o.get(a));o!==r&&o.get(i)},get size(){const i=this.__v_raw;return!e&&zt(lt(i),"iterate",Tn),Reflect.get(i,"size",i)},has(i){const o=this.__v_raw,r=lt(o),a=lt(i);return e||(fn(i,a)&&zt(r,"has",i),zt(r,"has",a)),i===a?o.has(i):o.has(i)||o.has(a)},forEach(i,o){const r=this,a=r.__v_raw,l=lt(a),c=t?Cr:e?eo:It;return!e&&zt(l,"iterate",Tn),a.forEach((u,f)=>i.call(o,c(u),c(f),r))}};return Dt(n,e?{add:_i("add"),set:_i("set"),delete:_i("delete"),clear:_i("clear")}:{add(i){!t&&!fe(i)&&!pn(i)&&(i=lt(i));const o=lt(this);return bi(o).has.call(o,i)||(o.add(i),Ve(o,"add",i,i)),this},set(i,o){!t&&!fe(o)&&!pn(o)&&(o=lt(o));const r=lt(this),{has:a,get:l}=bi(r);let c=a.call(r,i);c||(i=lt(i),c=a.call(r,i));const u=l.call(r,i);return r.set(i,o),c?fn(o,u)&&Ve(r,"set",i,o):Ve(r,"add",i,o),this},delete(i){const o=lt(this),{has:r,get:a}=bi(o);let l=r.call(o,i);l||(i=lt(i),l=r.call(o,i)),a&&a.call(o,i);const c=o.delete(i);return l&&Ve(o,"delete",i,void 0),c},clear(){const i=lt(this),o=i.size!==0,r=i.clear();return o&&Ve(i,"clear",void 0,void 0),r}}),["keys","values","entries",Symbol.iterator].forEach(i=>{n[i]=bp(i,e,t)}),n}function xa(e,t){const n=_p(e,t);return(s,i,o)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?s:Reflect.get(ut(n,i)&&i in s?n:s,i,o)}const yp={get:xa(!1,!1)},xp={get:xa(!1,!0)},vp={get:xa(!0,!1)};const of=new WeakMap,rf=new WeakMap,af=new WeakMap,wp=new WeakMap;function Sp(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Mp(e){return e.__v_skip||!Object.isExtensible(e)?0:Sp(qd(e))}function ui(e){return pn(e)?e:va(e,!1,pp,yp,of)}function lf(e){return va(e,!1,mp,xp,rf)}function cf(e){return va(e,!0,gp,vp,af)}function va(e,t,n,s,i){if(!gt(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=Mp(e);if(o===0)return e;const r=i.get(e);if(r)return r;const a=new Proxy(e,o===2?s:n);return i.set(e,a),a}function hn(e){return pn(e)?hn(e.__v_raw):!!(e&&e.__v_isReactive)}function pn(e){return!!(e&&e.__v_isReadonly)}function fe(e){return!!(e&&e.__v_isShallow)}function wa(e){return e?!!e.__v_raw:!1}function lt(e){const t=e&&e.__v_raw;return t?lt(t):e}function Sa(e){return!ut(e,"__v_skip")&&Object.isExtensible(e)&&Hu(e,"__v_skip",!0),e}const It=e=>gt(e)?ui(e):e,eo=e=>gt(e)?cf(e):e;function kt(e){return e?e.__v_isRef===!0:!1}function Ma(e){return uf(e,!1)}function Cp(e){return uf(e,!0)}function uf(e,t){return kt(e)?e:new Pp(e,t)}class Pp{constructor(t,n){this.dep=new ya,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:lt(t),this._value=n?t:It(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||fe(t)||pn(t);t=s?t:lt(t),fn(t,n)&&(this._rawValue=t,this._value=s?t:It(t),this.dep.trigger())}}function Qn(e){return kt(e)?e.value:e}const Ep={get:(e,t,n)=>t==="__v_raw"?e:Qn(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const i=e[t];return kt(i)&&!kt(n)?(i.value=n,!0):Reflect.set(e,t,n,s)}};function ff(e){return hn(e)?e:new Proxy(e,Ep)}function kp(e){const t=q(e)?new Array(e.length):{};for(const n in e)t[n]=Op(e,n);return t}class Ap{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return lp(lt(this._object),this._key)}}function Op(e,t,n){const s=e[t];return kt(s)?s:new Ap(e,t,n)}class Rp{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new ya(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Ws-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&_t!==this)return Xu(this,!0),!0}get value(){const t=this.dep.track();return Qu(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Tp(e,t,n=!1){let s,i;return tt(e)?s=e:(s=e.get,i=e.set),new Rp(s,i,n)}const yi={},no=new WeakMap;let Pn;function Dp(e,t=!1,n=Pn){if(n){let s=no.get(n);s||no.set(n,s=[]),s.push(e)}}function Lp(e,t,n=pt){const{immediate:s,deep:i,once:o,scheduler:r,augmentJob:a,call:l}=n,c=w=>i?w:fe(w)||i===!1||i===0?We(w,1):We(w);let u,f,h,d,p=!1,g=!1;if(kt(e)?(f=()=>e.value,p=fe(e)):hn(e)?(f=()=>c(e),p=!0):q(e)?(g=!0,p=e.some(w=>hn(w)||fe(w)),f=()=>e.map(w=>{if(kt(w))return w.value;if(hn(w))return c(w);if(tt(w))return l?l(w,2):w()})):tt(e)?t?f=l?()=>l(e,2):e:f=()=>{if(h){Ye();try{h()}finally{Xe()}}const w=Pn;Pn=u;try{return l?l(e,3,[d]):e(d)}finally{Pn=w}}:f=Te,t&&i){const w=f,S=i===!0?1/0:i;f=()=>We(w(),S)}const m=Ku(),_=()=>{u.stop(),m&&m.active&&fa(m.effects,u)};if(o&&t){const w=t;t=(...S)=>{w(...S),_()}}let x=g?new Array(e.length).fill(yi):yi;const v=w=>{if(!(!(u.flags&1)||!u.dirty&&!w))if(t){const S=u.run();if(i||p||(g?S.some((A,k)=>fn(A,x[k])):fn(S,x))){h&&h();const A=Pn;Pn=u;try{const k=[S,x===yi?void 0:g&&x[0]===yi?[]:x,d];x=S,l?l(t,3,k):t(...k)}finally{Pn=A}}}else u.run()};return a&&a(v),u=new qu(f),u.scheduler=r?()=>r(v,!1):v,d=w=>Dp(w,!1,u),h=u.onStop=()=>{const w=no.get(u);if(w){if(l)l(w,4);else for(const S of w)S();no.delete(u)}},t?s?v(!0):x=u.run():r?r(v.bind(null,!0),!0):u.run(),_.pause=u.pause.bind(u),_.resume=u.resume.bind(u),_.stop=_,_}function We(e,t=1/0,n){if(t<=0||!gt(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,kt(e))We(e.value,t,n);else if(q(e))for(let s=0;s<e.length;s++)We(e[s],t,n);else if(cs(e)||Jn(e))e.forEach(s=>{We(s,t,n)});else if(zu(e)){for(const s in e)We(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&We(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function fi(e,t,n,s){try{return s?e(...s):e()}catch(i){Co(i,t,n)}}function _e(e,t,n,s){if(tt(e)){const i=fi(e,t,n,s);return i&&Nu(i)&&i.catch(o=>{Co(o,t,n)}),i}if(q(e)){const i=[];for(let o=0;o<e.length;o++)i.push(_e(e[o],t,n,s));return i}}function Co(e,t,n,s=!0){const i=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:r}=t&&t.appContext.config||pt;if(t){let a=t.parent;const l=t.proxy,c=`https://vuejs.org/error-reference/#runtime-${n}`;for(;a;){const u=a.ec;if(u){for(let f=0;f<u.length;f++)if(u[f](e,l,c)===!1)return}a=a.parent}if(o){Ye(),fi(o,null,10,[e,l,c]),Xe();return}}Fp(e,n,i,s,r)}function Fp(e,t,n,s=!0,i=!1){if(i)throw e;console.error(e)}const qt=[];let Ae=-1;const Zn=[];let nn=null,qn=0;const hf=Promise.resolve();let so=null;function Po(e){const t=so||hf;return e?t.then(this?e.bind(this):e):t}function Ip(e){let t=Ae+1,n=qt.length;for(;t<n;){const s=t+n>>>1,i=qt[s],o=Us(i);o<e||o===e&&i.flags&2?t=s+1:n=s}return t}function Ca(e){if(!(e.flags&1)){const t=Us(e),n=qt[qt.length-1];!n||!(e.flags&2)&&t>=Us(n)?qt.push(e):qt.splice(Ip(t),0,e),e.flags|=1,df()}}function df(){so||(so=hf.then(gf))}function Np(e){q(e)?Zn.push(...e):nn&&e.id===-1?nn.splice(qn+1,0,e):e.flags&1||(Zn.push(e),e.flags|=1),df()}function sl(e,t,n=Ae+1){for(;n<qt.length;n++){const s=qt[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;qt.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function pf(e){if(Zn.length){const t=[...new Set(Zn)].sort((n,s)=>Us(n)-Us(s));if(Zn.length=0,nn){nn.push(...t);return}for(nn=t,qn=0;qn<nn.length;qn++){const n=nn[qn];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}nn=null,qn=0}}const Us=e=>e.id==null?e.flags&2?-1:1/0:e.id;function gf(e){try{for(Ae=0;Ae<qt.length;Ae++){const t=qt[Ae];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),fi(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Ae<qt.length;Ae++){const t=qt[Ae];t&&(t.flags&=-2)}Ae=-1,qt.length=0,pf(),so=null,(qt.length||Zn.length)&&gf()}}let Rt=null,mf=null;function io(e){const t=Rt;return Rt=e,mf=e&&e.type.__scopeId||null,t}function Bp(e,t=Rt,n){if(!t||e._n)return e;const s=(...i)=>{s._d&&dl(-1);const o=io(t);let r;try{r=e(...i)}finally{io(o),s._d&&dl(1)}return r};return s._n=!0,s._c=!0,s._d=!0,s}function gS(e,t){if(Rt===null)return e;const n=Oo(Rt),s=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[o,r,a,l=pt]=t[i];o&&(tt(o)&&(o={mounted:o,updated:o}),o.deep&&We(r),s.push({dir:o,instance:n,value:r,oldValue:void 0,arg:a,modifiers:l}))}return e}function yn(e,t,n,s){const i=e.dirs,o=t&&t.dirs;for(let r=0;r<i.length;r++){const a=i[r];o&&(a.oldValue=o[r].value);let l=a.dir[s];l&&(Ye(),_e(l,n,8,[e.el,a,e,t]),Xe())}}const zp=Symbol("_vte"),Hp=e=>e.__isTeleport,Wn=Symbol("_leaveCb"),xi=Symbol("_enterCb");function jp(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return wf(()=>{e.isMounted=!0}),Mf(()=>{e.isUnmounting=!0}),e}const le=[Function,Array],Vp={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:le,onEnter:le,onAfterEnter:le,onEnterCancelled:le,onBeforeLeave:le,onLeave:le,onAfterLeave:le,onLeaveCancelled:le,onBeforeAppear:le,onAppear:le,onAfterAppear:le,onAppearCancelled:le};function Wp(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function Pr(e,t,n,s,i){const{appear:o,mode:r,persisted:a=!1,onBeforeEnter:l,onEnter:c,onAfterEnter:u,onEnterCancelled:f,onBeforeLeave:h,onLeave:d,onAfterLeave:p,onLeaveCancelled:g,onBeforeAppear:m,onAppear:_,onAfterAppear:x,onAppearCancelled:v}=t,w=String(e.key),S=Wp(n,e),A=(P,I)=>{P&&_e(P,s,9,I)},k=(P,I)=>{const H=I[1];A(P,I),q(P)?P.every(L=>L.length<=1)&&H():P.length<=1&&H()},E={mode:r,persisted:a,beforeEnter(P){let I=l;if(!n.isMounted)if(o)I=m||l;else return;P[Wn]&&P[Wn](!0);const H=S[w];H&&Yn(e,H)&&H.el[Wn]&&H.el[Wn](),A(I,[P])},enter(P){let I=c,H=u,L=f;if(!n.isMounted)if(o)I=_||c,H=x||u,L=v||f;else return;let X=!1;const rt=P[xi]=Z=>{X||(X=!0,Z?A(L,[P]):A(H,[P]),E.delayedLeave&&E.delayedLeave(),P[xi]=void 0)};I?k(I,[P,rt]):rt()},leave(P,I){const H=String(e.key);if(P[xi]&&P[xi](!0),n.isUnmounting)return I();A(h,[P]);let L=!1;const X=P[Wn]=rt=>{L||(L=!0,I(),rt?A(g,[P]):A(p,[P]),P[Wn]=void 0,S[H]===e&&delete S[H])};S[H]=e,d?k(d,[P,X]):X()},clone(P){return Pr(P,t,n,s)}};return E}function Ks(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Ks(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function bf(e,t=!1,n){let s=[],i=0;for(let o=0;o<e.length;o++){let r=e[o];const a=n==null?r.key:String(n)+String(r.key!=null?r.key:o);r.type===Jt?(r.patchFlag&128&&i++,s=s.concat(bf(r.children,t,a))):(t||r.type!==Le)&&s.push(a!=null?Nn(r,{key:a}):r)}if(i>1)for(let o=0;o<s.length;o++)s[o].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function _f(e,t){return tt(e)?Dt({name:e.name},t,{setup:e}):e}function yf(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function oo(e,t,n,s,i=!1){if(q(e)){e.forEach((p,g)=>oo(p,t&&(q(t)?t[g]:t),n,s,i));return}if(ts(s)&&!i){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&oo(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?Oo(s.component):s.el,r=i?null:o,{i:a,r:l}=e,c=t&&t.r,u=a.refs===pt?a.refs={}:a.refs,f=a.setupState,h=lt(f),d=f===pt?()=>!1:p=>ut(h,p);if(c!=null&&c!==l&&(Ct(c)?(u[c]=null,d(c)&&(f[c]=null)):kt(c)&&(c.value=null)),tt(l))fi(l,a,12,[r,u]);else{const p=Ct(l),g=kt(l);if(p||g){const m=()=>{if(e.f){const _=p?d(l)?f[l]:u[l]:l.value;i?q(_)&&fa(_,o):q(_)?_.includes(o)||_.push(o):p?(u[l]=[o],d(l)&&(f[l]=u[l])):(l.value=[o],e.k&&(u[e.k]=l.value))}else p?(u[l]=r,d(l)&&(f[l]=r)):g&&(l.value=r,e.k&&(u[e.k]=r))};r?(m.id=-1,se(m,n)):m()}}}So().requestIdleCallback;So().cancelIdleCallback;const ts=e=>!!e.type.__asyncLoader,xf=e=>e.type.__isKeepAlive;function $p(e,t){vf(e,"a",t)}function Up(e,t){vf(e,"da",t)}function vf(e,t,n=Lt){const s=e.__wdc||(e.__wdc=()=>{let i=n;for(;i;){if(i.isDeactivated)return;i=i.parent}return e()});if(Eo(t,s,n),n){let i=n.parent;for(;i&&i.parent;)xf(i.parent.vnode)&&Kp(s,t,n,i),i=i.parent}}function Kp(e,t,n,s){const i=Eo(t,e,s,!0);Cf(()=>{fa(s[t],i)},n)}function Eo(e,t,n=Lt,s=!1){if(n){const i=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...r)=>{Ye();const a=hi(n),l=_e(t,n,e,r);return a(),Xe(),l});return s?i.unshift(o):i.push(o),o}}const Ge=e=>(t,n=Lt)=>{(!Xs||e==="sp")&&Eo(e,(...s)=>t(...s),n)},qp=Ge("bm"),wf=Ge("m"),Yp=Ge("bu"),Sf=Ge("u"),Mf=Ge("bum"),Cf=Ge("um"),Xp=Ge("sp"),Gp=Ge("rtg"),Jp=Ge("rtc");function Qp(e,t=Lt){Eo("ec",e,t)}const Pf="components";function mS(e,t){return kf(Pf,e,!0,t)||e}const Ef=Symbol.for("v-ndc");function bS(e){return Ct(e)?kf(Pf,e,!1)||e:e||Ef}function kf(e,t,n=!0,s=!1){const i=Rt||Lt;if(i){const o=i.type;{const a=jg(o,!1);if(a&&(a===t||a===de(t)||a===wo(de(t))))return o}const r=il(i[e]||o[e],t)||il(i.appContext[e],t);return!r&&s?o:r}}function il(e,t){return e&&(e[t]||e[de(t)]||e[wo(de(t))])}function _S(e,t,n,s){let i;const o=n,r=q(e);if(r||Ct(e)){const a=r&&hn(e);let l=!1,c=!1;a&&(l=!fe(e),c=pn(e),e=Mo(e)),i=new Array(e.length);for(let u=0,f=e.length;u<f;u++)i[u]=t(l?c?eo(It(e[u])):It(e[u]):e[u],u,void 0,o)}else if(typeof e=="number"){i=new Array(e);for(let a=0;a<e;a++)i[a]=t(a+1,a,void 0,o)}else if(gt(e))if(e[Symbol.iterator])i=Array.from(e,(a,l)=>t(a,l,void 0,o));else{const a=Object.keys(e);i=new Array(a.length);for(let l=0,c=a.length;l<c;l++){const u=a[l];i[l]=t(e[u],u,l,o)}}else i=[];return i}function yS(e,t,n={},s,i){if(Rt.ce||Rt.parent&&ts(Rt.parent)&&Rt.parent.ce)return n.name=t,Rr(),Tr(Jt,null,[jt("slot",n,s)],64);let o=e[t];o&&o._c&&(o._d=!1),Rr();const r=o&&Af(o(n)),a=n.key||r&&r.key,l=Tr(Jt,{key:(a&&!be(a)?a:`_${t}`)+""},r||[],r&&e._===1?64:-2);return l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),o&&o._c&&(o._d=!0),l}function Af(e){return e.some(t=>Ys(t)?!(t.type===Le||t.type===Jt&&!Af(t.children)):!0)?e:null}const Er=e=>e?Gf(e)?Oo(e):Er(e.parent):null,Ts=Dt(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Er(e.parent),$root:e=>Er(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Rf(e),$forceUpdate:e=>e.f||(e.f=()=>{Ca(e.update)}),$nextTick:e=>e.n||(e.n=Po.bind(e.proxy)),$watch:e=>xg.bind(e)}),Qo=(e,t)=>e!==pt&&!e.__isScriptSetup&&ut(e,t),Zp={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:i,props:o,accessCache:r,type:a,appContext:l}=e;let c;if(t[0]!=="$"){const d=r[t];if(d!==void 0)switch(d){case 1:return s[t];case 2:return i[t];case 4:return n[t];case 3:return o[t]}else{if(Qo(s,t))return r[t]=1,s[t];if(i!==pt&&ut(i,t))return r[t]=2,i[t];if((c=e.propsOptions[0])&&ut(c,t))return r[t]=3,o[t];if(n!==pt&&ut(n,t))return r[t]=4,n[t];kr&&(r[t]=0)}}const u=Ts[t];let f,h;if(u)return t==="$attrs"&&zt(e.attrs,"get",""),u(e);if((f=a.__cssModules)&&(f=f[t]))return f;if(n!==pt&&ut(n,t))return r[t]=4,n[t];if(h=l.config.globalProperties,ut(h,t))return h[t]},set({_:e},t,n){const{data:s,setupState:i,ctx:o}=e;return Qo(i,t)?(i[t]=n,!0):s!==pt&&ut(s,t)?(s[t]=n,!0):ut(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:i,propsOptions:o}},r){let a;return!!n[r]||e!==pt&&ut(e,r)||Qo(t,r)||(a=o[0])&&ut(a,r)||ut(s,r)||ut(Ts,r)||ut(i.config.globalProperties,r)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ut(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function ol(e){return q(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let kr=!0;function tg(e){const t=Rf(e),n=e.proxy,s=e.ctx;kr=!1,t.beforeCreate&&rl(t.beforeCreate,e,"bc");const{data:i,computed:o,methods:r,watch:a,provide:l,inject:c,created:u,beforeMount:f,mounted:h,beforeUpdate:d,updated:p,activated:g,deactivated:m,beforeDestroy:_,beforeUnmount:x,destroyed:v,unmounted:w,render:S,renderTracked:A,renderTriggered:k,errorCaptured:E,serverPrefetch:P,expose:I,inheritAttrs:H,components:L,directives:X,filters:rt}=t;if(c&&eg(c,s,null),r)for(const U in r){const et=r[U];tt(et)&&(s[U]=et.bind(n))}if(i){const U=i.call(n,n);gt(U)&&(e.data=ui(U))}if(kr=!0,o)for(const U in o){const et=o[U],mt=tt(et)?et.bind(n,n):tt(et.get)?et.get.bind(n,n):Te,$t=!tt(et)&&tt(et.set)?et.set.bind(n):Te,Ut=ue({get:mt,set:$t});Object.defineProperty(s,U,{enumerable:!0,configurable:!0,get:()=>Ut.value,set:Pt=>Ut.value=Pt})}if(a)for(const U in a)Of(a[U],s,n,U);if(l){const U=tt(l)?l.call(n):l;Reflect.ownKeys(U).forEach(et=>{Ni(et,U[et])})}u&&rl(u,e,"c");function G(U,et){q(et)?et.forEach(mt=>U(mt.bind(n))):et&&U(et.bind(n))}if(G(qp,f),G(wf,h),G(Yp,d),G(Sf,p),G($p,g),G(Up,m),G(Qp,E),G(Jp,A),G(Gp,k),G(Mf,x),G(Cf,w),G(Xp,P),q(I))if(I.length){const U=e.exposed||(e.exposed={});I.forEach(et=>{Object.defineProperty(U,et,{get:()=>n[et],set:mt=>n[et]=mt})})}else e.exposed||(e.exposed={});S&&e.render===Te&&(e.render=S),H!=null&&(e.inheritAttrs=H),L&&(e.components=L),X&&(e.directives=X),P&&yf(e)}function eg(e,t,n=Te){q(e)&&(e=Ar(e));for(const s in e){const i=e[s];let o;gt(i)?"default"in i?o=he(i.from||s,i.default,!0):o=he(i.from||s):o=he(i),kt(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:r=>o.value=r}):t[s]=o}}function rl(e,t,n){_e(q(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Of(e,t,n,s){let i=s.includes(".")?$f(n,s):()=>n[s];if(Ct(e)){const o=t[e];tt(o)&&Ds(i,o)}else if(tt(e))Ds(i,e.bind(n));else if(gt(e))if(q(e))e.forEach(o=>Of(o,t,n,s));else{const o=tt(e.handler)?e.handler.bind(n):t[e.handler];tt(o)&&Ds(i,o,e)}}function Rf(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:i,optionsCache:o,config:{optionMergeStrategies:r}}=e.appContext,a=o.get(t);let l;return a?l=a:!i.length&&!n&&!s?l=t:(l={},i.length&&i.forEach(c=>ro(l,c,r,!0)),ro(l,t,r)),gt(t)&&o.set(t,l),l}function ro(e,t,n,s=!1){const{mixins:i,extends:o}=t;o&&ro(e,o,n,!0),i&&i.forEach(r=>ro(e,r,n,!0));for(const r in t)if(!(s&&r==="expose")){const a=ng[r]||n&&n[r];e[r]=a?a(e[r],t[r]):t[r]}return e}const ng={data:al,props:ll,emits:ll,methods:ws,computed:ws,beforeCreate:Kt,created:Kt,beforeMount:Kt,mounted:Kt,beforeUpdate:Kt,updated:Kt,beforeDestroy:Kt,beforeUnmount:Kt,destroyed:Kt,unmounted:Kt,activated:Kt,deactivated:Kt,errorCaptured:Kt,serverPrefetch:Kt,components:ws,directives:ws,watch:ig,provide:al,inject:sg};function al(e,t){return t?e?function(){return Dt(tt(e)?e.call(this,this):e,tt(t)?t.call(this,this):t)}:t:e}function sg(e,t){return ws(Ar(e),Ar(t))}function Ar(e){if(q(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Kt(e,t){return e?[...new Set([].concat(e,t))]:t}function ws(e,t){return e?Dt(Object.create(null),e,t):t}function ll(e,t){return e?q(e)&&q(t)?[...new Set([...e,...t])]:Dt(Object.create(null),ol(e),ol(t??{})):t}function ig(e,t){if(!e)return t;if(!t)return e;const n=Dt(Object.create(null),e);for(const s in t)n[s]=Kt(e[s],t[s]);return n}function Tf(){return{app:null,config:{isNativeTag:Ud,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let og=0;function rg(e,t){return function(s,i=null){tt(s)||(s=Dt({},s)),i!=null&&!gt(i)&&(i=null);const o=Tf(),r=new WeakSet,a=[];let l=!1;const c=o.app={_uid:og++,_component:s,_props:i,_container:null,_context:o,_instance:null,version:Wg,get config(){return o.config},set config(u){},use(u,...f){return r.has(u)||(u&&tt(u.install)?(r.add(u),u.install(c,...f)):tt(u)&&(r.add(u),u(c,...f))),c},mixin(u){return o.mixins.includes(u)||o.mixins.push(u),c},component(u,f){return f?(o.components[u]=f,c):o.components[u]},directive(u,f){return f?(o.directives[u]=f,c):o.directives[u]},mount(u,f,h){if(!l){const d=c._ceVNode||jt(s,i);return d.appContext=o,h===!0?h="svg":h===!1&&(h=void 0),e(d,u,h),l=!0,c._container=u,u.__vue_app__=c,Oo(d.component)}},onUnmount(u){a.push(u)},unmount(){l&&(_e(a,c._instance,16),e(null,c._container),delete c._container.__vue_app__)},provide(u,f){return o.provides[u]=f,c},runWithContext(u){const f=Dn;Dn=c;try{return u()}finally{Dn=f}}};return c}}let Dn=null;function Ni(e,t){if(Lt){let n=Lt.provides;const s=Lt.parent&&Lt.parent.provides;s===n&&(n=Lt.provides=Object.create(s)),n[e]=t}}function he(e,t,n=!1){const s=Lt||Rt;if(s||Dn){let i=Dn?Dn._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(i&&e in i)return i[e];if(arguments.length>1)return n&&tt(t)?t.call(s&&s.proxy):t}}function ag(){return!!(Lt||Rt||Dn)}const Df={},Lf=()=>Object.create(Df),Ff=e=>Object.getPrototypeOf(e)===Df;function lg(e,t,n,s=!1){const i={},o=Lf();e.propsDefaults=Object.create(null),If(e,t,i,o);for(const r in e.propsOptions[0])r in i||(i[r]=void 0);n?e.props=s?i:lf(i):e.type.props?e.props=i:e.props=o,e.attrs=o}function cg(e,t,n,s){const{props:i,attrs:o,vnode:{patchFlag:r}}=e,a=lt(i),[l]=e.propsOptions;let c=!1;if((s||r>0)&&!(r&16)){if(r&8){const u=e.vnode.dynamicProps;for(let f=0;f<u.length;f++){let h=u[f];if(ko(e.emitsOptions,h))continue;const d=t[h];if(l)if(ut(o,h))d!==o[h]&&(o[h]=d,c=!0);else{const p=de(h);i[p]=Or(l,a,p,d,e,!1)}else d!==o[h]&&(o[h]=d,c=!0)}}}else{If(e,t,i,o)&&(c=!0);let u;for(const f in a)(!t||!ut(t,f)&&((u=bn(f))===f||!ut(t,u)))&&(l?n&&(n[f]!==void 0||n[u]!==void 0)&&(i[f]=Or(l,a,f,void 0,e,!0)):delete i[f]);if(o!==a)for(const f in o)(!t||!ut(t,f))&&(delete o[f],c=!0)}c&&Ve(e.attrs,"set","")}function If(e,t,n,s){const[i,o]=e.propsOptions;let r=!1,a;if(t)for(let l in t){if(As(l))continue;const c=t[l];let u;i&&ut(i,u=de(l))?!o||!o.includes(u)?n[u]=c:(a||(a={}))[u]=c:ko(e.emitsOptions,l)||(!(l in s)||c!==s[l])&&(s[l]=c,r=!0)}if(o){const l=lt(n),c=a||pt;for(let u=0;u<o.length;u++){const f=o[u];n[f]=Or(i,l,f,c[f],e,!ut(c,f))}}return r}function Or(e,t,n,s,i,o){const r=e[n];if(r!=null){const a=ut(r,"default");if(a&&s===void 0){const l=r.default;if(r.type!==Function&&!r.skipFactory&&tt(l)){const{propsDefaults:c}=i;if(n in c)s=c[n];else{const u=hi(i);s=c[n]=l.call(null,t),u()}}else s=l;i.ce&&i.ce._setProp(n,s)}r[0]&&(o&&!a?s=!1:r[1]&&(s===""||s===bn(n))&&(s=!0))}return s}const ug=new WeakMap;function Nf(e,t,n=!1){const s=n?ug:t.propsCache,i=s.get(e);if(i)return i;const o=e.props,r={},a=[];let l=!1;if(!tt(e)){const u=f=>{l=!0;const[h,d]=Nf(f,t,!0);Dt(r,h),d&&a.push(...d)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!o&&!l)return gt(e)&&s.set(e,Gn),Gn;if(q(o))for(let u=0;u<o.length;u++){const f=de(o[u]);cl(f)&&(r[f]=pt)}else if(o)for(const u in o){const f=de(u);if(cl(f)){const h=o[u],d=r[f]=q(h)||tt(h)?{type:h}:Dt({},h),p=d.type;let g=!1,m=!0;if(q(p))for(let _=0;_<p.length;++_){const x=p[_],v=tt(x)&&x.name;if(v==="Boolean"){g=!0;break}else v==="String"&&(m=!1)}else g=tt(p)&&p.name==="Boolean";d[0]=g,d[1]=m,(g||ut(d,"default"))&&a.push(f)}}const c=[r,a];return gt(e)&&s.set(e,c),c}function cl(e){return e[0]!=="$"&&!As(e)}const Pa=e=>e[0]==="_"||e==="$stable",Ea=e=>q(e)?e.map(Re):[Re(e)],fg=(e,t,n)=>{if(t._n)return t;const s=Bp((...i)=>Ea(t(...i)),n);return s._c=!1,s},Bf=(e,t,n)=>{const s=e._ctx;for(const i in e){if(Pa(i))continue;const o=e[i];if(tt(o))t[i]=fg(i,o,s);else if(o!=null){const r=Ea(o);t[i]=()=>r}}},zf=(e,t)=>{const n=Ea(t);e.slots.default=()=>n},Hf=(e,t,n)=>{for(const s in t)(n||!Pa(s))&&(e[s]=t[s])},hg=(e,t,n)=>{const s=e.slots=Lf();if(e.vnode.shapeFlag&32){const i=t._;i?(Hf(s,t,n),n&&Hu(s,"_",i,!0)):Bf(t,s)}else t&&zf(e,t)},dg=(e,t,n)=>{const{vnode:s,slots:i}=e;let o=!0,r=pt;if(s.shapeFlag&32){const a=t._;a?n&&a===1?o=!1:Hf(i,t,n):(o=!t.$stable,Bf(t,i)),r=t}else t&&(zf(e,t),r={default:1});if(o)for(const a in i)!Pa(a)&&r[a]==null&&delete i[a]},se=Eg;function pg(e){return gg(e)}function gg(e,t){const n=So();n.__VUE__=!0;const{insert:s,remove:i,patchProp:o,createElement:r,createText:a,createComment:l,setText:c,setElementText:u,parentNode:f,nextSibling:h,setScopeId:d=Te,insertStaticContent:p}=e,g=(b,y,M,O=null,D=null,T=null,j=void 0,z=null,B=!!y.dynamicChildren)=>{if(b===y)return;b&&!Yn(b,y)&&(O=R(b),Pt(b,D,T,!0),b=null),y.patchFlag===-2&&(B=!1,y.dynamicChildren=null);const{type:F,ref:J,shapeFlag:W}=y;switch(F){case Ao:m(b,y,M,O);break;case Le:_(b,y,M,O);break;case Bi:b==null&&x(y,M,O,j);break;case Jt:L(b,y,M,O,D,T,j,z,B);break;default:W&1?S(b,y,M,O,D,T,j,z,B):W&6?X(b,y,M,O,D,T,j,z,B):(W&64||W&128)&&F.process(b,y,M,O,D,T,j,z,B,K)}J!=null&&D&&oo(J,b&&b.ref,T,y||b,!y)},m=(b,y,M,O)=>{if(b==null)s(y.el=a(y.children),M,O);else{const D=y.el=b.el;y.children!==b.children&&c(D,y.children)}},_=(b,y,M,O)=>{b==null?s(y.el=l(y.children||""),M,O):y.el=b.el},x=(b,y,M,O)=>{[b.el,b.anchor]=p(b.children,y,M,O,b.el,b.anchor)},v=({el:b,anchor:y},M,O)=>{let D;for(;b&&b!==y;)D=h(b),s(b,M,O),b=D;s(y,M,O)},w=({el:b,anchor:y})=>{let M;for(;b&&b!==y;)M=h(b),i(b),b=M;i(y)},S=(b,y,M,O,D,T,j,z,B)=>{y.type==="svg"?j="svg":y.type==="math"&&(j="mathml"),b==null?A(y,M,O,D,T,j,z,B):P(b,y,D,T,j,z,B)},A=(b,y,M,O,D,T,j,z)=>{let B,F;const{props:J,shapeFlag:W,transition:Y,dirs:Q}=b;if(B=b.el=r(b.type,T,J&&J.is,J),W&8?u(B,b.children):W&16&&E(b.children,B,null,O,D,Zo(b,T),j,z),Q&&yn(b,null,O,"created"),k(B,b,b.scopeId,j,O),J){for(const bt in J)bt!=="value"&&!As(bt)&&o(B,bt,null,J[bt],T,O);"value"in J&&o(B,"value",null,J.value,T),(F=J.onVnodeBeforeMount)&&Ce(F,O,b)}Q&&yn(b,null,O,"beforeMount");const at=mg(D,Y);at&&Y.beforeEnter(B),s(B,y,M),((F=J&&J.onVnodeMounted)||at||Q)&&se(()=>{F&&Ce(F,O,b),at&&Y.enter(B),Q&&yn(b,null,O,"mounted")},D)},k=(b,y,M,O,D)=>{if(M&&d(b,M),O)for(let T=0;T<O.length;T++)d(b,O[T]);if(D){let T=D.subTree;if(y===T||Kf(T.type)&&(T.ssContent===y||T.ssFallback===y)){const j=D.vnode;k(b,j,j.scopeId,j.slotScopeIds,D.parent)}}},E=(b,y,M,O,D,T,j,z,B=0)=>{for(let F=B;F<b.length;F++){const J=b[F]=z?sn(b[F]):Re(b[F]);g(null,J,y,M,O,D,T,j,z)}},P=(b,y,M,O,D,T,j)=>{const z=y.el=b.el;let{patchFlag:B,dynamicChildren:F,dirs:J}=y;B|=b.patchFlag&16;const W=b.props||pt,Y=y.props||pt;let Q;if(M&&xn(M,!1),(Q=Y.onVnodeBeforeUpdate)&&Ce(Q,M,y,b),J&&yn(y,b,M,"beforeUpdate"),M&&xn(M,!0),(W.innerHTML&&Y.innerHTML==null||W.textContent&&Y.textContent==null)&&u(z,""),F?I(b.dynamicChildren,F,z,M,O,Zo(y,D),T):j||et(b,y,z,null,M,O,Zo(y,D),T,!1),B>0){if(B&16)H(z,W,Y,M,D);else if(B&2&&W.class!==Y.class&&o(z,"class",null,Y.class,D),B&4&&o(z,"style",W.style,Y.style,D),B&8){const at=y.dynamicProps;for(let bt=0;bt<at.length;bt++){const ft=at[bt],ee=W[ft],Xt=Y[ft];(Xt!==ee||ft==="value")&&o(z,ft,ee,Xt,D,M)}}B&1&&b.children!==y.children&&u(z,y.children)}else!j&&F==null&&H(z,W,Y,M,D);((Q=Y.onVnodeUpdated)||J)&&se(()=>{Q&&Ce(Q,M,y,b),J&&yn(y,b,M,"updated")},O)},I=(b,y,M,O,D,T,j)=>{for(let z=0;z<y.length;z++){const B=b[z],F=y[z],J=B.el&&(B.type===Jt||!Yn(B,F)||B.shapeFlag&198)?f(B.el):M;g(B,F,J,null,O,D,T,j,!0)}},H=(b,y,M,O,D)=>{if(y!==M){if(y!==pt)for(const T in y)!As(T)&&!(T in M)&&o(b,T,y[T],null,D,O);for(const T in M){if(As(T))continue;const j=M[T],z=y[T];j!==z&&T!=="value"&&o(b,T,z,j,D,O)}"value"in M&&o(b,"value",y.value,M.value,D)}},L=(b,y,M,O,D,T,j,z,B)=>{const F=y.el=b?b.el:a(""),J=y.anchor=b?b.anchor:a("");let{patchFlag:W,dynamicChildren:Y,slotScopeIds:Q}=y;Q&&(z=z?z.concat(Q):Q),b==null?(s(F,M,O),s(J,M,O),E(y.children||[],M,J,D,T,j,z,B)):W>0&&W&64&&Y&&b.dynamicChildren?(I(b.dynamicChildren,Y,M,D,T,j,z),(y.key!=null||D&&y===D.subTree)&&jf(b,y,!0)):et(b,y,M,J,D,T,j,z,B)},X=(b,y,M,O,D,T,j,z,B)=>{y.slotScopeIds=z,b==null?y.shapeFlag&512?D.ctx.activate(y,M,O,j,B):rt(y,M,O,D,T,j,B):Z(b,y,B)},rt=(b,y,M,O,D,T,j)=>{const z=b.component=Fg(b,O,D);if(xf(b)&&(z.ctx.renderer=K),Ng(z,!1,j),z.asyncDep){if(D&&D.registerDep(z,G,j),!b.el){const B=z.subTree=jt(Le);_(null,B,y,M)}}else G(z,b,y,M,D,T,j)},Z=(b,y,M)=>{const O=y.component=b.component;if(Cg(b,y,M))if(O.asyncDep&&!O.asyncResolved){U(O,y,M);return}else O.next=y,O.update();else y.el=b.el,O.vnode=y},G=(b,y,M,O,D,T,j)=>{const z=()=>{if(b.isMounted){let{next:W,bu:Y,u:Q,parent:at,vnode:bt}=b;{const Se=Vf(b);if(Se){W&&(W.el=bt.el,U(b,W,j)),Se.asyncDep.then(()=>{b.isUnmounted||z()});return}}let ft=W,ee;xn(b,!1),W?(W.el=bt.el,U(b,W,j)):W=bt,Y&&Ii(Y),(ee=W.props&&W.props.onVnodeBeforeUpdate)&&Ce(ee,at,W,bt),xn(b,!0);const Xt=fl(b),we=b.subTree;b.subTree=Xt,g(we,Xt,f(we.el),R(we),b,D,T),W.el=Xt.el,ft===null&&Pg(b,Xt.el),Q&&se(Q,D),(ee=W.props&&W.props.onVnodeUpdated)&&se(()=>Ce(ee,at,W,bt),D)}else{let W;const{el:Y,props:Q}=y,{bm:at,m:bt,parent:ft,root:ee,type:Xt}=b,we=ts(y);xn(b,!1),at&&Ii(at),!we&&(W=Q&&Q.onVnodeBeforeMount)&&Ce(W,ft,y),xn(b,!0);{ee.ce&&ee.ce._injectChildStyle(Xt);const Se=b.subTree=fl(b);g(null,Se,M,O,b,D,T),y.el=Se.el}if(bt&&se(bt,D),!we&&(W=Q&&Q.onVnodeMounted)){const Se=y;se(()=>Ce(W,ft,Se),D)}(y.shapeFlag&256||ft&&ts(ft.vnode)&&ft.vnode.shapeFlag&256)&&b.a&&se(b.a,D),b.isMounted=!0,y=M=O=null}};b.scope.on();const B=b.effect=new qu(z);b.scope.off();const F=b.update=B.run.bind(B),J=b.job=B.runIfDirty.bind(B);J.i=b,J.id=b.uid,B.scheduler=()=>Ca(J),xn(b,!0),F()},U=(b,y,M)=>{y.component=b;const O=b.vnode.props;b.vnode=y,b.next=null,cg(b,y.props,O,M),dg(b,y.children,M),Ye(),sl(b),Xe()},et=(b,y,M,O,D,T,j,z,B=!1)=>{const F=b&&b.children,J=b?b.shapeFlag:0,W=y.children,{patchFlag:Y,shapeFlag:Q}=y;if(Y>0){if(Y&128){$t(F,W,M,O,D,T,j,z,B);return}else if(Y&256){mt(F,W,M,O,D,T,j,z,B);return}}Q&8?(J&16&&At(F,D,T),W!==F&&u(M,W)):J&16?Q&16?$t(F,W,M,O,D,T,j,z,B):At(F,D,T,!0):(J&8&&u(M,""),Q&16&&E(W,M,O,D,T,j,z,B))},mt=(b,y,M,O,D,T,j,z,B)=>{b=b||Gn,y=y||Gn;const F=b.length,J=y.length,W=Math.min(F,J);let Y;for(Y=0;Y<W;Y++){const Q=y[Y]=B?sn(y[Y]):Re(y[Y]);g(b[Y],Q,M,null,D,T,j,z,B)}F>J?At(b,D,T,!0,!1,W):E(y,M,O,D,T,j,z,B,W)},$t=(b,y,M,O,D,T,j,z,B)=>{let F=0;const J=y.length;let W=b.length-1,Y=J-1;for(;F<=W&&F<=Y;){const Q=b[F],at=y[F]=B?sn(y[F]):Re(y[F]);if(Yn(Q,at))g(Q,at,M,null,D,T,j,z,B);else break;F++}for(;F<=W&&F<=Y;){const Q=b[W],at=y[Y]=B?sn(y[Y]):Re(y[Y]);if(Yn(Q,at))g(Q,at,M,null,D,T,j,z,B);else break;W--,Y--}if(F>W){if(F<=Y){const Q=Y+1,at=Q<J?y[Q].el:O;for(;F<=Y;)g(null,y[F]=B?sn(y[F]):Re(y[F]),M,at,D,T,j,z,B),F++}}else if(F>Y)for(;F<=W;)Pt(b[F],D,T,!0),F++;else{const Q=F,at=F,bt=new Map;for(F=at;F<=Y;F++){const ne=y[F]=B?sn(y[F]):Re(y[F]);ne.key!=null&&bt.set(ne.key,F)}let ft,ee=0;const Xt=Y-at+1;let we=!1,Se=0;const hs=new Array(Xt);for(F=0;F<Xt;F++)hs[F]=0;for(F=Q;F<=W;F++){const ne=b[F];if(ee>=Xt){Pt(ne,D,T,!0);continue}let Me;if(ne.key!=null)Me=bt.get(ne.key);else for(ft=at;ft<=Y;ft++)if(hs[ft-at]===0&&Yn(ne,y[ft])){Me=ft;break}Me===void 0?Pt(ne,D,T,!0):(hs[Me-at]=F+1,Me>=Se?Se=Me:we=!0,g(ne,y[Me],M,null,D,T,j,z,B),ee++)}const Ja=we?bg(hs):Gn;for(ft=Ja.length-1,F=Xt-1;F>=0;F--){const ne=at+F,Me=y[ne],Qa=ne+1<J?y[ne+1].el:O;hs[F]===0?g(null,Me,M,Qa,D,T,j,z,B):we&&(ft<0||F!==Ja[ft]?Ut(Me,M,Qa,2):ft--)}}},Ut=(b,y,M,O,D=null)=>{const{el:T,type:j,transition:z,children:B,shapeFlag:F}=b;if(F&6){Ut(b.component.subTree,y,M,O);return}if(F&128){b.suspense.move(y,M,O);return}if(F&64){j.move(b,y,M,K);return}if(j===Jt){s(T,y,M);for(let W=0;W<B.length;W++)Ut(B[W],y,M,O);s(b.anchor,y,M);return}if(j===Bi){v(b,y,M);return}if(O!==2&&F&1&&z)if(O===0)z.beforeEnter(T),s(T,y,M),se(()=>z.enter(T),D);else{const{leave:W,delayLeave:Y,afterLeave:Q}=z,at=()=>{b.ctx.isUnmounted?i(T):s(T,y,M)},bt=()=>{W(T,()=>{at(),Q&&Q()})};Y?Y(T,at,bt):bt()}else s(T,y,M)},Pt=(b,y,M,O=!1,D=!1)=>{const{type:T,props:j,ref:z,children:B,dynamicChildren:F,shapeFlag:J,patchFlag:W,dirs:Y,cacheIndex:Q}=b;if(W===-2&&(D=!1),z!=null&&(Ye(),oo(z,null,M,b,!0),Xe()),Q!=null&&(y.renderCache[Q]=void 0),J&256){y.ctx.deactivate(b);return}const at=J&1&&Y,bt=!ts(b);let ft;if(bt&&(ft=j&&j.onVnodeBeforeUnmount)&&Ce(ft,y,b),J&6)ve(b.component,M,O);else{if(J&128){b.suspense.unmount(M,O);return}at&&yn(b,null,y,"beforeUnmount"),J&64?b.type.remove(b,y,M,K,O):F&&!F.hasOnce&&(T!==Jt||W>0&&W&64)?At(F,y,M,!1,!0):(T===Jt&&W&384||!D&&J&16)&&At(B,y,M),O&&ae(b)}(bt&&(ft=j&&j.onVnodeUnmounted)||at)&&se(()=>{ft&&Ce(ft,y,b),at&&yn(b,null,y,"unmounted")},M)},ae=b=>{const{type:y,el:M,anchor:O,transition:D}=b;if(y===Jt){Yt(M,O);return}if(y===Bi){w(b);return}const T=()=>{i(M),D&&!D.persisted&&D.afterLeave&&D.afterLeave()};if(b.shapeFlag&1&&D&&!D.persisted){const{leave:j,delayLeave:z}=D,B=()=>j(M,T);z?z(b.el,T,B):B()}else T()},Yt=(b,y)=>{let M;for(;b!==y;)M=h(b),i(b),b=M;i(y)},ve=(b,y,M)=>{const{bum:O,scope:D,job:T,subTree:j,um:z,m:B,a:F,parent:J,slots:{__:W}}=b;ul(B),ul(F),O&&Ii(O),J&&q(W)&&W.forEach(Y=>{J.renderCache[Y]=void 0}),D.stop(),T&&(T.flags|=8,Pt(j,b,y,M)),z&&se(z,y),se(()=>{b.isUnmounted=!0},y),y&&y.pendingBranch&&!y.isUnmounted&&b.asyncDep&&!b.asyncResolved&&b.suspenseId===y.pendingId&&(y.deps--,y.deps===0&&y.resolve())},At=(b,y,M,O=!1,D=!1,T=0)=>{for(let j=T;j<b.length;j++)Pt(b[j],y,M,O,D)},R=b=>{if(b.shapeFlag&6)return R(b.component.subTree);if(b.shapeFlag&128)return b.suspense.next();const y=h(b.anchor||b.el),M=y&&y[zp];return M?h(M):y};let $=!1;const V=(b,y,M)=>{b==null?y._vnode&&Pt(y._vnode,null,null,!0):g(y._vnode||null,b,y,null,null,null,M),y._vnode=b,$||($=!0,sl(),pf(),$=!1)},K={p:g,um:Pt,m:Ut,r:ae,mt:rt,mc:E,pc:et,pbc:I,n:R,o:e};return{render:V,hydrate:void 0,createApp:rg(V)}}function Zo({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function xn({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function mg(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function jf(e,t,n=!1){const s=e.children,i=t.children;if(q(s)&&q(i))for(let o=0;o<s.length;o++){const r=s[o];let a=i[o];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=i[o]=sn(i[o]),a.el=r.el),!n&&a.patchFlag!==-2&&jf(r,a)),a.type===Ao&&(a.el=r.el),a.type===Le&&!a.el&&(a.el=r.el)}}function bg(e){const t=e.slice(),n=[0];let s,i,o,r,a;const l=e.length;for(s=0;s<l;s++){const c=e[s];if(c!==0){if(i=n[n.length-1],e[i]<c){t[s]=i,n.push(s);continue}for(o=0,r=n.length-1;o<r;)a=o+r>>1,e[n[a]]<c?o=a+1:r=a;c<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,r=n[o-1];o-- >0;)n[o]=r,r=t[r];return n}function Vf(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Vf(t)}function ul(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const _g=Symbol.for("v-scx"),yg=()=>he(_g);function Ds(e,t,n){return Wf(e,t,n)}function Wf(e,t,n=pt){const{immediate:s,deep:i,flush:o,once:r}=n,a=Dt({},n),l=t&&s||!t&&o!=="post";let c;if(Xs){if(o==="sync"){const d=yg();c=d.__watcherHandles||(d.__watcherHandles=[])}else if(!l){const d=()=>{};return d.stop=Te,d.resume=Te,d.pause=Te,d}}const u=Lt;a.call=(d,p,g)=>_e(d,u,p,g);let f=!1;o==="post"?a.scheduler=d=>{se(d,u&&u.suspense)}:o!=="sync"&&(f=!0,a.scheduler=(d,p)=>{p?d():Ca(d)}),a.augmentJob=d=>{t&&(d.flags|=4),f&&(d.flags|=2,u&&(d.id=u.uid,d.i=u))};const h=Lp(e,t,a);return Xs&&(c?c.push(h):l&&h()),h}function xg(e,t,n){const s=this.proxy,i=Ct(e)?e.includes(".")?$f(s,e):()=>s[e]:e.bind(s,s);let o;tt(t)?o=t:(o=t.handler,n=t);const r=hi(this),a=Wf(i,o.bind(s),n);return r(),a}function $f(e,t){const n=t.split(".");return()=>{let s=e;for(let i=0;i<n.length&&s;i++)s=s[n[i]];return s}}const vg=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${de(t)}Modifiers`]||e[`${bn(t)}Modifiers`];function wg(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||pt;let i=n;const o=t.startsWith("update:"),r=o&&vg(s,t.slice(7));r&&(r.trim&&(i=n.map(u=>Ct(u)?u.trim():u)),r.number&&(i=n.map(Zi)));let a,l=s[a=qo(t)]||s[a=qo(de(t))];!l&&o&&(l=s[a=qo(bn(t))]),l&&_e(l,e,6,i);const c=s[a+"Once"];if(c){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,_e(c,e,6,i)}}function Uf(e,t,n=!1){const s=t.emitsCache,i=s.get(e);if(i!==void 0)return i;const o=e.emits;let r={},a=!1;if(!tt(e)){const l=c=>{const u=Uf(c,t,!0);u&&(a=!0,Dt(r,u))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!o&&!a?(gt(e)&&s.set(e,null),null):(q(o)?o.forEach(l=>r[l]=null):Dt(r,o),gt(e)&&s.set(e,r),r)}function ko(e,t){return!e||!xo(t)?!1:(t=t.slice(2).replace(/Once$/,""),ut(e,t[0].toLowerCase()+t.slice(1))||ut(e,bn(t))||ut(e,t))}function fl(e){const{type:t,vnode:n,proxy:s,withProxy:i,propsOptions:[o],slots:r,attrs:a,emit:l,render:c,renderCache:u,props:f,data:h,setupState:d,ctx:p,inheritAttrs:g}=e,m=io(e);let _,x;try{if(n.shapeFlag&4){const w=i||s,S=w;_=Re(c.call(S,w,u,f,d,h,p)),x=a}else{const w=t;_=Re(w.length>1?w(f,{attrs:a,slots:r,emit:l}):w(f,null)),x=t.props?a:Sg(a)}}catch(w){Ls.length=0,Co(w,e,1),_=jt(Le)}let v=_;if(x&&g!==!1){const w=Object.keys(x),{shapeFlag:S}=v;w.length&&S&7&&(o&&w.some(ua)&&(x=Mg(x,o)),v=Nn(v,x,!1,!0))}return n.dirs&&(v=Nn(v,null,!1,!0),v.dirs=v.dirs?v.dirs.concat(n.dirs):n.dirs),n.transition&&Ks(v,n.transition),_=v,io(m),_}const Sg=e=>{let t;for(const n in e)(n==="class"||n==="style"||xo(n))&&((t||(t={}))[n]=e[n]);return t},Mg=(e,t)=>{const n={};for(const s in e)(!ua(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function Cg(e,t,n){const{props:s,children:i,component:o}=e,{props:r,children:a,patchFlag:l}=t,c=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return s?hl(s,r,c):!!r;if(l&8){const u=t.dynamicProps;for(let f=0;f<u.length;f++){const h=u[f];if(r[h]!==s[h]&&!ko(c,h))return!0}}}else return(i||a)&&(!a||!a.$stable)?!0:s===r?!1:s?r?hl(s,r,c):!0:!!r;return!1}function hl(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let i=0;i<s.length;i++){const o=s[i];if(t[o]!==e[o]&&!ko(n,o))return!0}return!1}function Pg({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Kf=e=>e.__isSuspense;function Eg(e,t){t&&t.pendingBranch?q(e)?t.effects.push(...e):t.effects.push(e):Np(e)}const Jt=Symbol.for("v-fgt"),Ao=Symbol.for("v-txt"),Le=Symbol.for("v-cmt"),Bi=Symbol.for("v-stc"),Ls=[];let re=null;function Rr(e=!1){Ls.push(re=e?null:[])}function kg(){Ls.pop(),re=Ls[Ls.length-1]||null}let qs=1;function dl(e,t=!1){qs+=e,e<0&&re&&t&&(re.hasOnce=!0)}function qf(e){return e.dynamicChildren=qs>0?re||Gn:null,kg(),qs>0&&re&&re.push(e),e}function xS(e,t,n,s,i,o){return qf(Xf(e,t,n,s,i,o,!0))}function Tr(e,t,n,s,i){return qf(jt(e,t,n,s,i,!0))}function Ys(e){return e?e.__v_isVNode===!0:!1}function Yn(e,t){return e.type===t.type&&e.key===t.key}const Yf=({key:e})=>e??null,zi=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Ct(e)||kt(e)||tt(e)?{i:Rt,r:e,k:t,f:!!n}:e:null);function Xf(e,t=null,n=null,s=0,i=null,o=e===Jt?0:1,r=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Yf(t),ref:t&&zi(t),scopeId:mf,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:Rt};return a?(ka(l,n),o&128&&e.normalize(l)):n&&(l.shapeFlag|=Ct(n)?8:16),qs>0&&!r&&re&&(l.patchFlag>0||o&6)&&l.patchFlag!==32&&re.push(l),l}const jt=Ag;function Ag(e,t=null,n=null,s=0,i=null,o=!1){if((!e||e===Ef)&&(e=Le),Ys(e)){const a=Nn(e,t,!0);return n&&ka(a,n),qs>0&&!o&&re&&(a.shapeFlag&6?re[re.indexOf(e)]=a:re.push(a)),a.patchFlag=-2,a}if(Vg(e)&&(e=e.__vccOpts),t){t=Og(t);let{class:a,style:l}=t;a&&!Ct(a)&&(t.class=pa(a)),gt(l)&&(wa(l)&&!q(l)&&(l=Dt({},l)),t.style=da(l))}const r=Ct(e)?1:Kf(e)?128:Hp(e)?64:gt(e)?4:tt(e)?2:0;return Xf(e,t,n,s,i,r,o,!0)}function Og(e){return e?wa(e)||Ff(e)?Dt({},e):e:null}function Nn(e,t,n=!1,s=!1){const{props:i,ref:o,patchFlag:r,children:a,transition:l}=e,c=t?Tg(i||{},t):i,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:c,key:c&&Yf(c),ref:t&&t.ref?n&&o?q(o)?o.concat(zi(t)):[o,zi(t)]:zi(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Jt?r===-1?16:r|16:r,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Nn(e.ssContent),ssFallback:e.ssFallback&&Nn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&s&&Ks(u,l.clone(u)),u}function Rg(e=" ",t=0){return jt(Ao,null,e,t)}function vS(e,t){const n=jt(Bi,null,e);return n.staticCount=t,n}function wS(e="",t=!1){return t?(Rr(),Tr(Le,null,e)):jt(Le,null,e)}function Re(e){return e==null||typeof e=="boolean"?jt(Le):q(e)?jt(Jt,null,e.slice()):Ys(e)?sn(e):jt(Ao,null,String(e))}function sn(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Nn(e)}function ka(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(q(t))n=16;else if(typeof t=="object")if(s&65){const i=t.default;i&&(i._c&&(i._d=!1),ka(e,i()),i._c&&(i._d=!0));return}else{n=32;const i=t._;!i&&!Ff(t)?t._ctx=Rt:i===3&&Rt&&(Rt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else tt(t)?(t={default:t,_ctx:Rt},n=32):(t=String(t),s&64?(n=16,t=[Rg(t)]):n=8);e.children=t,e.shapeFlag|=n}function Tg(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const i in s)if(i==="class")t.class!==s.class&&(t.class=pa([t.class,s.class]));else if(i==="style")t.style=da([t.style,s.style]);else if(xo(i)){const o=t[i],r=s[i];r&&o!==r&&!(q(o)&&o.includes(r))&&(t[i]=o?[].concat(o,r):r)}else i!==""&&(t[i]=s[i])}return t}function Ce(e,t,n,s=null){_e(e,t,7,[n,s])}const Dg=Tf();let Lg=0;function Fg(e,t,n){const s=e.type,i=(t?t.appContext:e.appContext)||Dg,o={uid:Lg++,vnode:e,type:s,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new $u(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Nf(s,i),emitsOptions:Uf(s,i),emit:null,emitted:null,propsDefaults:pt,inheritAttrs:s.inheritAttrs,ctx:pt,data:pt,props:pt,attrs:pt,slots:pt,refs:pt,setupState:pt,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=wg.bind(null,o),e.ce&&e.ce(o),o}let Lt=null;const Ig=()=>Lt||Rt;let ao,Dr;{const e=So(),t=(n,s)=>{let i;return(i=e[n])||(i=e[n]=[]),i.push(s),o=>{i.length>1?i.forEach(r=>r(o)):i[0](o)}};ao=t("__VUE_INSTANCE_SETTERS__",n=>Lt=n),Dr=t("__VUE_SSR_SETTERS__",n=>Xs=n)}const hi=e=>{const t=Lt;return ao(e),e.scope.on(),()=>{e.scope.off(),ao(t)}},pl=()=>{Lt&&Lt.scope.off(),ao(null)};function Gf(e){return e.vnode.shapeFlag&4}let Xs=!1;function Ng(e,t=!1,n=!1){t&&Dr(t);const{props:s,children:i}=e.vnode,o=Gf(e);lg(e,s,o,t),hg(e,i,n||t);const r=o?Bg(e,t):void 0;return t&&Dr(!1),r}function Bg(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Zp);const{setup:s}=n;if(s){Ye();const i=e.setupContext=s.length>1?Hg(e):null,o=hi(e),r=fi(s,e,0,[e.props,i]),a=Nu(r);if(Xe(),o(),(a||e.sp)&&!ts(e)&&yf(e),a){if(r.then(pl,pl),t)return r.then(l=>{gl(e,l)}).catch(l=>{Co(l,e,0)});e.asyncDep=r}else gl(e,r)}else Jf(e)}function gl(e,t,n){tt(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:gt(t)&&(e.setupState=ff(t)),Jf(e)}function Jf(e,t,n){const s=e.type;e.render||(e.render=s.render||Te);{const i=hi(e);Ye();try{tg(e)}finally{Xe(),i()}}}const zg={get(e,t){return zt(e,"get",""),e[t]}};function Hg(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,zg),slots:e.slots,emit:e.emit,expose:t}}function Oo(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(ff(Sa(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Ts)return Ts[n](e)},has(t,n){return n in t||n in Ts}})):e.proxy}function jg(e,t=!0){return tt(e)?e.displayName||e.name:e.name||t&&e.__name}function Vg(e){return tt(e)&&"__vccOpts"in e}const ue=(e,t)=>Tp(e,t,Xs);function Qf(e,t,n){const s=arguments.length;return s===2?gt(t)&&!q(t)?Ys(t)?jt(e,null,[t]):jt(e,t):jt(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&Ys(n)&&(n=[n]),jt(e,t,n))}const Wg="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Lr;const ml=typeof window<"u"&&window.trustedTypes;if(ml)try{Lr=ml.createPolicy("vue",{createHTML:e=>e})}catch{}const Zf=Lr?e=>Lr.createHTML(e):e=>e,$g="http://www.w3.org/2000/svg",Ug="http://www.w3.org/1998/Math/MathML",He=typeof document<"u"?document:null,bl=He&&He.createElement("template"),Kg={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const i=t==="svg"?He.createElementNS($g,e):t==="mathml"?He.createElementNS(Ug,e):n?He.createElement(e,{is:n}):He.createElement(e);return e==="select"&&s&&s.multiple!=null&&i.setAttribute("multiple",s.multiple),i},createText:e=>He.createTextNode(e),createComment:e=>He.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>He.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,i,o){const r=n?n.previousSibling:t.lastChild;if(i&&(i===o||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),n),!(i===o||!(i=i.nextSibling)););else{bl.innerHTML=Zf(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const a=bl.content;if(s==="svg"||s==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,n)}return[r?r.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Qe="transition",ps="animation",ns=Symbol("_vtc"),th={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},qg=Dt({},Vp,th),vn=(e,t=[])=>{q(e)?e.forEach(n=>n(...t)):e&&e(...t)},_l=e=>e?q(e)?e.some(t=>t.length>1):e.length>1:!1;function Yg(e){const t={};for(const L in e)L in th||(t[L]=e[L]);if(e.css===!1)return t;const{name:n="v",type:s,duration:i,enterFromClass:o=`${n}-enter-from`,enterActiveClass:r=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=o,appearActiveClass:c=r,appearToClass:u=a,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:h=`${n}-leave-active`,leaveToClass:d=`${n}-leave-to`}=e,p=Xg(i),g=p&&p[0],m=p&&p[1],{onBeforeEnter:_,onEnter:x,onEnterCancelled:v,onLeave:w,onLeaveCancelled:S,onBeforeAppear:A=_,onAppear:k=x,onAppearCancelled:E=v}=t,P=(L,X,rt,Z)=>{L._enterCancelled=Z,tn(L,X?u:a),tn(L,X?c:r),rt&&rt()},I=(L,X)=>{L._isLeaving=!1,tn(L,f),tn(L,d),tn(L,h),X&&X()},H=L=>(X,rt)=>{const Z=L?k:x,G=()=>P(X,L,rt);vn(Z,[X,G]),yl(()=>{tn(X,L?l:o),Ee(X,L?u:a),_l(Z)||xl(X,s,g,G)})};return Dt(t,{onBeforeEnter(L){vn(_,[L]),Ee(L,o),Ee(L,r)},onBeforeAppear(L){vn(A,[L]),Ee(L,l),Ee(L,c)},onEnter:H(!1),onAppear:H(!0),onLeave(L,X){L._isLeaving=!0;const rt=()=>I(L,X);Ee(L,f),L._enterCancelled?(Ee(L,h),Fr()):(Fr(),Ee(L,h)),yl(()=>{L._isLeaving&&(tn(L,f),Ee(L,d),_l(w)||xl(L,s,m,rt))}),vn(w,[L,rt])},onEnterCancelled(L){P(L,!1,void 0,!0),vn(v,[L])},onAppearCancelled(L){P(L,!0,void 0,!0),vn(E,[L])},onLeaveCancelled(L){I(L),vn(S,[L])}})}function Xg(e){if(e==null)return null;if(gt(e))return[tr(e.enter),tr(e.leave)];{const t=tr(e);return[t,t]}}function tr(e){return Gd(e)}function Ee(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[ns]||(e[ns]=new Set)).add(t)}function tn(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const n=e[ns];n&&(n.delete(t),n.size||(e[ns]=void 0))}function yl(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Gg=0;function xl(e,t,n,s){const i=e._endId=++Gg,o=()=>{i===e._endId&&s()};if(n!=null)return setTimeout(o,n);const{type:r,timeout:a,propCount:l}=eh(e,t);if(!r)return s();const c=r+"end";let u=0;const f=()=>{e.removeEventListener(c,h),o()},h=d=>{d.target===e&&++u>=l&&f()};setTimeout(()=>{u<l&&f()},a+1),e.addEventListener(c,h)}function eh(e,t){const n=window.getComputedStyle(e),s=p=>(n[p]||"").split(", "),i=s(`${Qe}Delay`),o=s(`${Qe}Duration`),r=vl(i,o),a=s(`${ps}Delay`),l=s(`${ps}Duration`),c=vl(a,l);let u=null,f=0,h=0;t===Qe?r>0&&(u=Qe,f=r,h=o.length):t===ps?c>0&&(u=ps,f=c,h=l.length):(f=Math.max(r,c),u=f>0?r>c?Qe:ps:null,h=u?u===Qe?o.length:l.length:0);const d=u===Qe&&/\b(transform|all)(,|$)/.test(s(`${Qe}Property`).toString());return{type:u,timeout:f,propCount:h,hasTransform:d}}function vl(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>wl(n)+wl(e[s])))}function wl(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Fr(){return document.body.offsetHeight}function Jg(e,t,n){const s=e[ns];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Sl=Symbol("_vod"),Qg=Symbol("_vsh"),Zg=Symbol(""),tm=/(^|;)\s*display\s*:/;function em(e,t,n){const s=e.style,i=Ct(n);let o=!1;if(n&&!i){if(t)if(Ct(t))for(const r of t.split(";")){const a=r.slice(0,r.indexOf(":")).trim();n[a]==null&&Hi(s,a,"")}else for(const r in t)n[r]==null&&Hi(s,r,"");for(const r in n)r==="display"&&(o=!0),Hi(s,r,n[r])}else if(i){if(t!==n){const r=s[Zg];r&&(n+=";"+r),s.cssText=n,o=tm.test(n)}}else t&&e.removeAttribute("style");Sl in e&&(e[Sl]=o?s.display:"",e[Qg]&&(s.display="none"))}const Ml=/\s*!important$/;function Hi(e,t,n){if(q(n))n.forEach(s=>Hi(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=nm(e,t);Ml.test(n)?e.setProperty(bn(s),n.replace(Ml,""),"important"):e[s]=n}}const Cl=["Webkit","Moz","ms"],er={};function nm(e,t){const n=er[t];if(n)return n;let s=de(t);if(s!=="filter"&&s in e)return er[t]=s;s=wo(s);for(let i=0;i<Cl.length;i++){const o=Cl[i]+s;if(o in e)return er[t]=o}return t}const Pl="http://www.w3.org/1999/xlink";function El(e,t,n,s,i,o=np(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Pl,t.slice(6,t.length)):e.setAttributeNS(Pl,t,n):n==null||o&&!ju(n)?e.removeAttribute(t):e.setAttribute(t,o?"":be(n)?String(n):n)}function kl(e,t,n,s,i){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Zf(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const a=o==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(a!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let r=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=ju(n):n==null&&a==="string"?(n="",r=!0):a==="number"&&(n=0,r=!0)}try{e[t]=n}catch{}r&&e.removeAttribute(i||t)}function rn(e,t,n,s){e.addEventListener(t,n,s)}function sm(e,t,n,s){e.removeEventListener(t,n,s)}const Al=Symbol("_vei");function im(e,t,n,s,i=null){const o=e[Al]||(e[Al]={}),r=o[t];if(s&&r)r.value=s;else{const[a,l]=om(t);if(s){const c=o[t]=lm(s,i);rn(e,a,c,l)}else r&&(sm(e,a,r,l),o[t]=void 0)}}const Ol=/(?:Once|Passive|Capture)$/;function om(e){let t;if(Ol.test(e)){t={};let s;for(;s=e.match(Ol);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):bn(e.slice(2)),t]}let nr=0;const rm=Promise.resolve(),am=()=>nr||(rm.then(()=>nr=0),nr=Date.now());function lm(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;_e(cm(s,n.value),t,5,[s])};return n.value=e,n.attached=am(),n}function cm(e,t){if(q(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>i=>!i._stopped&&s&&s(i))}else return t}const Rl=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,um=(e,t,n,s,i,o)=>{const r=i==="svg";t==="class"?Jg(e,s,r):t==="style"?em(e,n,s):xo(t)?ua(t)||im(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):fm(e,t,s,r))?(kl(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&El(e,t,s,r,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Ct(s))?kl(e,de(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),El(e,t,s,r))};function fm(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Rl(t)&&tt(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const i=e.tagName;if(i==="IMG"||i==="VIDEO"||i==="CANVAS"||i==="SOURCE")return!1}return Rl(t)&&Ct(n)?!1:t in e}const nh=new WeakMap,sh=new WeakMap,lo=Symbol("_moveCb"),Tl=Symbol("_enterCb"),hm=e=>(delete e.props.mode,e),dm=hm({name:"TransitionGroup",props:Dt({},qg,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=Ig(),s=jp();let i,o;return Sf(()=>{if(!i.length)return;const r=e.moveClass||`${e.name||"v"}-move`;if(!bm(i[0].el,n.vnode.el,r)){i=[];return}i.forEach(pm),i.forEach(gm);const a=i.filter(mm);Fr(),a.forEach(l=>{const c=l.el,u=c.style;Ee(c,r),u.transform=u.webkitTransform=u.transitionDuration="";const f=c[lo]=h=>{h&&h.target!==c||(!h||/transform$/.test(h.propertyName))&&(c.removeEventListener("transitionend",f),c[lo]=null,tn(c,r))};c.addEventListener("transitionend",f)}),i=[]}),()=>{const r=lt(e),a=Yg(r);let l=r.tag||Jt;if(i=[],o)for(let c=0;c<o.length;c++){const u=o[c];u.el&&u.el instanceof Element&&(i.push(u),Ks(u,Pr(u,a,s,n)),nh.set(u,u.el.getBoundingClientRect()))}o=t.default?bf(t.default()):[];for(let c=0;c<o.length;c++){const u=o[c];u.key!=null&&Ks(u,Pr(u,a,s,n))}return jt(l,null,o)}}}),SS=dm;function pm(e){const t=e.el;t[lo]&&t[lo](),t[Tl]&&t[Tl]()}function gm(e){sh.set(e,e.el.getBoundingClientRect())}function mm(e){const t=nh.get(e),n=sh.get(e),s=t.left-n.left,i=t.top-n.top;if(s||i){const o=e.el.style;return o.transform=o.webkitTransform=`translate(${s}px,${i}px)`,o.transitionDuration="0s",e}}function bm(e,t,n){const s=e.cloneNode(),i=e[ns];i&&i.forEach(a=>{a.split(/\s+/).forEach(l=>l&&s.classList.remove(l))}),n.split(/\s+/).forEach(a=>a&&s.classList.add(a)),s.style.display="none";const o=t.nodeType===1?t:t.parentNode;o.appendChild(s);const{hasTransform:r}=eh(s);return o.removeChild(s),r}const ss=e=>{const t=e.props["onUpdate:modelValue"]||!1;return q(t)?n=>Ii(t,n):t};function _m(e){e.target.composing=!0}function Dl(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const qe=Symbol("_assign"),MS={created(e,{modifiers:{lazy:t,trim:n,number:s}},i){e[qe]=ss(i);const o=s||i.props&&i.props.type==="number";rn(e,t?"change":"input",r=>{if(r.target.composing)return;let a=e.value;n&&(a=a.trim()),o&&(a=Zi(a)),e[qe](a)}),n&&rn(e,"change",()=>{e.value=e.value.trim()}),t||(rn(e,"compositionstart",_m),rn(e,"compositionend",Dl),rn(e,"change",Dl))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:i,number:o}},r){if(e[qe]=ss(r),e.composing)return;const a=(o||e.type==="number")&&!/^0\d/.test(e.value)?Zi(e.value):e.value,l=t??"";a!==l&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||i&&e.value.trim()===l)||(e.value=l))}},CS={deep:!0,created(e,t,n){e[qe]=ss(n),rn(e,"change",()=>{const s=e._modelValue,i=Gs(e),o=e.checked,r=e[qe];if(q(s)){const a=ga(s,i),l=a!==-1;if(o&&!l)r(s.concat(i));else if(!o&&l){const c=[...s];c.splice(a,1),r(c)}}else if(cs(s)){const a=new Set(s);o?a.add(i):a.delete(i),r(a)}else r(ih(e,o))})},mounted:Ll,beforeUpdate(e,t,n){e[qe]=ss(n),Ll(e,t,n)}};function Ll(e,{value:t,oldValue:n},s){e._modelValue=t;let i;if(q(t))i=ga(t,s.props.value)>-1;else if(cs(t))i=t.has(s.props.value);else{if(t===n)return;i=ci(t,ih(e,!0))}e.checked!==i&&(e.checked=i)}const PS={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const i=cs(t);rn(e,"change",()=>{const o=Array.prototype.filter.call(e.options,r=>r.selected).map(r=>n?Zi(Gs(r)):Gs(r));e[qe](e.multiple?i?new Set(o):o:o[0]),e._assigning=!0,Po(()=>{e._assigning=!1})}),e[qe]=ss(s)},mounted(e,{value:t}){Fl(e,t)},beforeUpdate(e,t,n){e[qe]=ss(n)},updated(e,{value:t}){e._assigning||Fl(e,t)}};function Fl(e,t){const n=e.multiple,s=q(t);if(!(n&&!s&&!cs(t))){for(let i=0,o=e.options.length;i<o;i++){const r=e.options[i],a=Gs(r);if(n)if(s){const l=typeof a;l==="string"||l==="number"?r.selected=t.some(c=>String(c)===String(a)):r.selected=ga(t,a)>-1}else r.selected=t.has(a);else if(ci(Gs(r),t)){e.selectedIndex!==i&&(e.selectedIndex=i);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function Gs(e){return"_value"in e?e._value:e.value}function ih(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const ym=["ctrl","shift","alt","meta"],xm={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>ym.some(n=>e[`${n}Key`]&&!t.includes(n))},ES=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(i,...o)=>{for(let r=0;r<t.length;r++){const a=xm[t[r]];if(a&&a(i,t))return}return e(i,...o)})},vm={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},kS=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=i=>{if(!("key"in i))return;const o=bn(i.key);if(t.some(r=>r===o||vm[r]===o))return e(i)})},wm=Dt({patchProp:um},Kg);let Il;function Sm(){return Il||(Il=pg(wm))}const AS=(...e)=>{const t=Sm().createApp(...e),{mount:n}=t;return t.mount=s=>{const i=Cm(s);if(!i)return;const o=t._component;!tt(o)&&!o.render&&!o.template&&(o.template=i.innerHTML),i.nodeType===1&&(i.textContent="");const r=n(i,!1,Mm(i));return i instanceof Element&&(i.removeAttribute("v-cloak"),i.setAttribute("data-v-app","")),r},t};function Mm(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Cm(e){return Ct(e)?document.querySelector(e):e}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let oh;const Ro=e=>oh=e,rh=Symbol();function Ir(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var Fs;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Fs||(Fs={}));function OS(){const e=Uu(!0),t=e.run(()=>Ma({}));let n=[],s=[];const i=Sa({install(o){Ro(i),i._a=o,o.provide(rh,i),o.config.globalProperties.$pinia=i,s.forEach(r=>n.push(r)),s=[]},use(o){return this._a?n.push(o):s.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return i}const ah=()=>{};function Nl(e,t,n,s=ah){e.push(t);const i=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),s())};return!n&&Ku()&&op(i),i}function $n(e,...t){e.slice().forEach(n=>{n(...t)})}const Pm=e=>e(),Bl=Symbol(),sr=Symbol();function Nr(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,s)=>e.set(s,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const s=t[n],i=e[n];Ir(i)&&Ir(s)&&e.hasOwnProperty(n)&&!kt(s)&&!hn(s)?e[n]=Nr(i,s):e[n]=s}return e}const Em=Symbol();function km(e){return!Ir(e)||!e.hasOwnProperty(Em)}const{assign:en}=Object;function Am(e){return!!(kt(e)&&e.effect)}function Om(e,t,n,s){const{state:i,actions:o,getters:r}=t,a=n.state.value[e];let l;function c(){a||(n.state.value[e]=i?i():{});const u=kp(n.state.value[e]);return en(u,o,Object.keys(r||{}).reduce((f,h)=>(f[h]=Sa(ue(()=>{Ro(n);const d=n._s.get(e);return r[h].call(d,d)})),f),{}))}return l=lh(e,c,t,n,s,!0),l}function lh(e,t,n={},s,i,o){let r;const a=en({actions:{}},n),l={deep:!0};let c,u,f=[],h=[],d;const p=s.state.value[e];!o&&!p&&(s.state.value[e]={}),Ma({});let g;function m(E){let P;c=u=!1,typeof E=="function"?(E(s.state.value[e]),P={type:Fs.patchFunction,storeId:e,events:d}):(Nr(s.state.value[e],E),P={type:Fs.patchObject,payload:E,storeId:e,events:d});const I=g=Symbol();Po().then(()=>{g===I&&(c=!0)}),u=!0,$n(f,P,s.state.value[e])}const _=o?function(){const{state:P}=n,I=P?P():{};this.$patch(H=>{en(H,I)})}:ah;function x(){r.stop(),f=[],h=[],s._s.delete(e)}const v=(E,P="")=>{if(Bl in E)return E[sr]=P,E;const I=function(){Ro(s);const H=Array.from(arguments),L=[],X=[];function rt(U){L.push(U)}function Z(U){X.push(U)}$n(h,{args:H,name:I[sr],store:S,after:rt,onError:Z});let G;try{G=E.apply(this&&this.$id===e?this:S,H)}catch(U){throw $n(X,U),U}return G instanceof Promise?G.then(U=>($n(L,U),U)).catch(U=>($n(X,U),Promise.reject(U))):($n(L,G),G)};return I[Bl]=!0,I[sr]=P,I},w={_p:s,$id:e,$onAction:Nl.bind(null,h),$patch:m,$reset:_,$subscribe(E,P={}){const I=Nl(f,E,P.detached,()=>H()),H=r.run(()=>Ds(()=>s.state.value[e],L=>{(P.flush==="sync"?u:c)&&E({storeId:e,type:Fs.direct,events:d},L)},en({},l,P)));return I},$dispose:x},S=ui(w);s._s.set(e,S);const k=(s._a&&s._a.runWithContext||Pm)(()=>s._e.run(()=>(r=Uu()).run(()=>t({action:v}))));for(const E in k){const P=k[E];if(kt(P)&&!Am(P)||hn(P))o||(p&&km(P)&&(kt(P)?P.value=p[E]:Nr(P,p[E])),s.state.value[e][E]=P);else if(typeof P=="function"){const I=v(P,E);k[E]=I,a.actions[E]=P}}return en(S,k),en(lt(S),k),Object.defineProperty(S,"$state",{get:()=>s.state.value[e],set:E=>{m(P=>{en(P,E)})}}),s._p.forEach(E=>{en(S,r.run(()=>E({store:S,app:s._a,pinia:s,options:a})))}),p&&o&&n.hydrate&&n.hydrate(S.$state,p),c=!0,u=!0,S}/*! #__NO_SIDE_EFFECTS__ */function RS(e,t,n){let s,i;const o=typeof t=="function";typeof e=="string"?(s=e,i=o?n:t):(i=e,s=e.id);function r(a,l){const c=ag();return a=a||(c?he(rh,null):null),a&&Ro(a),a=oh,a._s.has(s)||(o?lh(s,t,i,a):Om(s,i,a)),a._s.get(s)}return r.$id=s,r}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Xn=typeof document<"u";function ch(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Rm(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&ch(e.default)}const ct=Object.assign;function ir(e,t){const n={};for(const s in t){const i=t[s];n[s]=ye(i)?i.map(e):e(i)}return n}const Is=()=>{},ye=Array.isArray,uh=/#/g,Tm=/&/g,Dm=/\//g,Lm=/=/g,Fm=/\?/g,fh=/\+/g,Im=/%5B/g,Nm=/%5D/g,hh=/%5E/g,Bm=/%60/g,dh=/%7B/g,zm=/%7C/g,ph=/%7D/g,Hm=/%20/g;function Aa(e){return encodeURI(""+e).replace(zm,"|").replace(Im,"[").replace(Nm,"]")}function jm(e){return Aa(e).replace(dh,"{").replace(ph,"}").replace(hh,"^")}function Br(e){return Aa(e).replace(fh,"%2B").replace(Hm,"+").replace(uh,"%23").replace(Tm,"%26").replace(Bm,"`").replace(dh,"{").replace(ph,"}").replace(hh,"^")}function Vm(e){return Br(e).replace(Lm,"%3D")}function Wm(e){return Aa(e).replace(uh,"%23").replace(Fm,"%3F")}function $m(e){return e==null?"":Wm(e).replace(Dm,"%2F")}function Js(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Um=/\/$/,Km=e=>e.replace(Um,"");function or(e,t,n="/"){let s,i={},o="",r="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(s=t.slice(0,l),o=t.slice(l+1,a>-1?a:t.length),i=e(o)),a>-1&&(s=s||t.slice(0,a),r=t.slice(a,t.length)),s=Gm(s??t,n),{fullPath:s+(o&&"?")+o+r,path:s,query:i,hash:Js(r)}}function qm(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function zl(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Ym(e,t,n){const s=t.matched.length-1,i=n.matched.length-1;return s>-1&&s===i&&is(t.matched[s],n.matched[i])&&gh(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function is(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function gh(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Xm(e[n],t[n]))return!1;return!0}function Xm(e,t){return ye(e)?Hl(e,t):ye(t)?Hl(t,e):e===t}function Hl(e,t){return ye(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function Gm(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),i=s[s.length-1];(i===".."||i===".")&&s.push("");let o=n.length-1,r,a;for(r=0;r<s.length;r++)if(a=s[r],a!==".")if(a==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(r).join("/")}const Ze={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Qs;(function(e){e.pop="pop",e.push="push"})(Qs||(Qs={}));var Ns;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Ns||(Ns={}));function Jm(e){if(!e)if(Xn){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Km(e)}const Qm=/^[^#]+#/;function Zm(e,t){return e.replace(Qm,"#")+t}function tb(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const To=()=>({left:window.scrollX,top:window.scrollY});function eb(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),i=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!i)return;t=tb(i,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function jl(e,t){return(history.state?history.state.position-t:-1)+e}const zr=new Map;function nb(e,t){zr.set(e,t)}function sb(e){const t=zr.get(e);return zr.delete(e),t}let ib=()=>location.protocol+"//"+location.host;function mh(e,t){const{pathname:n,search:s,hash:i}=t,o=e.indexOf("#");if(o>-1){let a=i.includes(e.slice(o))?e.slice(o).length:1,l=i.slice(a);return l[0]!=="/"&&(l="/"+l),zl(l,"")}return zl(n,e)+s+i}function ob(e,t,n,s){let i=[],o=[],r=null;const a=({state:h})=>{const d=mh(e,location),p=n.value,g=t.value;let m=0;if(h){if(n.value=d,t.value=h,r&&r===p){r=null;return}m=g?h.position-g.position:0}else s(d);i.forEach(_=>{_(n.value,p,{delta:m,type:Qs.pop,direction:m?m>0?Ns.forward:Ns.back:Ns.unknown})})};function l(){r=n.value}function c(h){i.push(h);const d=()=>{const p=i.indexOf(h);p>-1&&i.splice(p,1)};return o.push(d),d}function u(){const{history:h}=window;h.state&&h.replaceState(ct({},h.state,{scroll:To()}),"")}function f(){for(const h of o)h();o=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:l,listen:c,destroy:f}}function Vl(e,t,n,s=!1,i=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:i?To():null}}function rb(e){const{history:t,location:n}=window,s={value:mh(e,n)},i={value:t.state};i.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(l,c,u){const f=e.indexOf("#"),h=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+l:ib()+e+l;try{t[u?"replaceState":"pushState"](c,"",h),i.value=c}catch(d){console.error(d),n[u?"replace":"assign"](h)}}function r(l,c){const u=ct({},t.state,Vl(i.value.back,l,i.value.forward,!0),c,{position:i.value.position});o(l,u,!0),s.value=l}function a(l,c){const u=ct({},i.value,t.state,{forward:l,scroll:To()});o(u.current,u,!0);const f=ct({},Vl(s.value,l,null),{position:u.position+1},c);o(l,f,!1),s.value=l}return{location:s,state:i,push:a,replace:r}}function TS(e){e=Jm(e);const t=rb(e),n=ob(e,t.state,t.location,t.replace);function s(o,r=!0){r||n.pauseListeners(),history.go(o)}const i=ct({location:"",base:e,go:s,createHref:Zm.bind(null,e)},t,n);return Object.defineProperty(i,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(i,"state",{enumerable:!0,get:()=>t.state.value}),i}function ab(e){return typeof e=="string"||e&&typeof e=="object"}function bh(e){return typeof e=="string"||typeof e=="symbol"}const _h=Symbol("");var Wl;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Wl||(Wl={}));function os(e,t){return ct(new Error,{type:e,[_h]:!0},t)}function Ie(e,t){return e instanceof Error&&_h in e&&(t==null||!!(e.type&t))}const $l="[^/]+?",lb={sensitive:!1,strict:!1,start:!0,end:!0},cb=/[.+*?^${}()[\]/\\]/g;function ub(e,t){const n=ct({},lb,t),s=[];let i=n.start?"^":"";const o=[];for(const c of e){const u=c.length?[]:[90];n.strict&&!c.length&&(i+="/");for(let f=0;f<c.length;f++){const h=c[f];let d=40+(n.sensitive?.25:0);if(h.type===0)f||(i+="/"),i+=h.value.replace(cb,"\\$&"),d+=40;else if(h.type===1){const{value:p,repeatable:g,optional:m,regexp:_}=h;o.push({name:p,repeatable:g,optional:m});const x=_||$l;if(x!==$l){d+=10;try{new RegExp(`(${x})`)}catch(w){throw new Error(`Invalid custom RegExp for param "${p}" (${x}): `+w.message)}}let v=g?`((?:${x})(?:/(?:${x}))*)`:`(${x})`;f||(v=m&&c.length<2?`(?:/${v})`:"/"+v),m&&(v+="?"),i+=v,d+=20,m&&(d+=-8),g&&(d+=-20),x===".*"&&(d+=-50)}u.push(d)}s.push(u)}if(n.strict&&n.end){const c=s.length-1;s[c][s[c].length-1]+=.7000000000000001}n.strict||(i+="/?"),n.end?i+="$":n.strict&&!i.endsWith("/")&&(i+="(?:/|$)");const r=new RegExp(i,n.sensitive?"":"i");function a(c){const u=c.match(r),f={};if(!u)return null;for(let h=1;h<u.length;h++){const d=u[h]||"",p=o[h-1];f[p.name]=d&&p.repeatable?d.split("/"):d}return f}function l(c){let u="",f=!1;for(const h of e){(!f||!u.endsWith("/"))&&(u+="/"),f=!1;for(const d of h)if(d.type===0)u+=d.value;else if(d.type===1){const{value:p,repeatable:g,optional:m}=d,_=p in c?c[p]:"";if(ye(_)&&!g)throw new Error(`Provided param "${p}" is an array but it is not repeatable (* or + modifiers)`);const x=ye(_)?_.join("/"):_;if(!x)if(m)h.length<2&&(u.endsWith("/")?u=u.slice(0,-1):f=!0);else throw new Error(`Missing required param "${p}"`);u+=x}}return u||"/"}return{re:r,score:s,keys:o,parse:a,stringify:l}}function fb(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function yh(e,t){let n=0;const s=e.score,i=t.score;for(;n<s.length&&n<i.length;){const o=fb(s[n],i[n]);if(o)return o;n++}if(Math.abs(i.length-s.length)===1){if(Ul(s))return 1;if(Ul(i))return-1}return i.length-s.length}function Ul(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const hb={type:0,value:""},db=/[a-zA-Z0-9_]/;function pb(e){if(!e)return[[]];if(e==="/")return[[hb]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(d){throw new Error(`ERR (${n})/"${c}": ${d}`)}let n=0,s=n;const i=[];let o;function r(){o&&i.push(o),o=[]}let a=0,l,c="",u="";function f(){c&&(n===0?o.push({type:0,value:c}):n===1||n===2||n===3?(o.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:c,regexp:u,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),c="")}function h(){c+=l}for(;a<e.length;){if(l=e[a++],l==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:l==="/"?(c&&f(),r()):l===":"?(f(),n=1):h();break;case 4:h(),n=s;break;case 1:l==="("?n=2:db.test(l)?h():(f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+l:n=3:u+=l;break;case 3:f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,u="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${c}"`),f(),r(),i}function gb(e,t,n){const s=ub(pb(e.path),n),i=ct(s,{record:e,parent:t,children:[],alias:[]});return t&&!i.record.aliasOf==!t.record.aliasOf&&t.children.push(i),i}function mb(e,t){const n=[],s=new Map;t=Xl({strict:!1,end:!0,sensitive:!1},t);function i(f){return s.get(f)}function o(f,h,d){const p=!d,g=ql(f);g.aliasOf=d&&d.record;const m=Xl(t,f),_=[g];if("alias"in f){const w=typeof f.alias=="string"?[f.alias]:f.alias;for(const S of w)_.push(ql(ct({},g,{components:d?d.record.components:g.components,path:S,aliasOf:d?d.record:g})))}let x,v;for(const w of _){const{path:S}=w;if(h&&S[0]!=="/"){const A=h.record.path,k=A[A.length-1]==="/"?"":"/";w.path=h.record.path+(S&&k+S)}if(x=gb(w,h,m),d?d.alias.push(x):(v=v||x,v!==x&&v.alias.push(x),p&&f.name&&!Yl(x)&&r(f.name)),xh(x)&&l(x),g.children){const A=g.children;for(let k=0;k<A.length;k++)o(A[k],x,d&&d.children[k])}d=d||x}return v?()=>{r(v)}:Is}function r(f){if(bh(f)){const h=s.get(f);h&&(s.delete(f),n.splice(n.indexOf(h),1),h.children.forEach(r),h.alias.forEach(r))}else{const h=n.indexOf(f);h>-1&&(n.splice(h,1),f.record.name&&s.delete(f.record.name),f.children.forEach(r),f.alias.forEach(r))}}function a(){return n}function l(f){const h=yb(f,n);n.splice(h,0,f),f.record.name&&!Yl(f)&&s.set(f.record.name,f)}function c(f,h){let d,p={},g,m;if("name"in f&&f.name){if(d=s.get(f.name),!d)throw os(1,{location:f});m=d.record.name,p=ct(Kl(h.params,d.keys.filter(v=>!v.optional).concat(d.parent?d.parent.keys.filter(v=>v.optional):[]).map(v=>v.name)),f.params&&Kl(f.params,d.keys.map(v=>v.name))),g=d.stringify(p)}else if(f.path!=null)g=f.path,d=n.find(v=>v.re.test(g)),d&&(p=d.parse(g),m=d.record.name);else{if(d=h.name?s.get(h.name):n.find(v=>v.re.test(h.path)),!d)throw os(1,{location:f,currentLocation:h});m=d.record.name,p=ct({},h.params,f.params),g=d.stringify(p)}const _=[];let x=d;for(;x;)_.unshift(x.record),x=x.parent;return{name:m,path:g,params:p,matched:_,meta:_b(_)}}e.forEach(f=>o(f));function u(){n.length=0,s.clear()}return{addRoute:o,resolve:c,removeRoute:r,clearRoutes:u,getRoutes:a,getRecordMatcher:i}}function Kl(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function ql(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:bb(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function bb(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Yl(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function _b(e){return e.reduce((t,n)=>ct(t,n.meta),{})}function Xl(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function yb(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;yh(e,t[o])<0?s=o:n=o+1}const i=xb(e);return i&&(s=t.lastIndexOf(i,s-1)),s}function xb(e){let t=e;for(;t=t.parent;)if(xh(t)&&yh(e,t)===0)return t}function xh({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function vb(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let i=0;i<s.length;++i){const o=s[i].replace(fh," "),r=o.indexOf("="),a=Js(r<0?o:o.slice(0,r)),l=r<0?null:Js(o.slice(r+1));if(a in t){let c=t[a];ye(c)||(c=t[a]=[c]),c.push(l)}else t[a]=l}return t}function Gl(e){let t="";for(let n in e){const s=e[n];if(n=Vm(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(ye(s)?s.map(o=>o&&Br(o)):[s&&Br(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function wb(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=ye(s)?s.map(i=>i==null?null:""+i):s==null?s:""+s)}return t}const Sb=Symbol(""),Jl=Symbol(""),Do=Symbol(""),Oa=Symbol(""),Hr=Symbol("");function gs(){let e=[];function t(s){return e.push(s),()=>{const i=e.indexOf(s);i>-1&&e.splice(i,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function on(e,t,n,s,i,o=r=>r()){const r=s&&(s.enterCallbacks[i]=s.enterCallbacks[i]||[]);return()=>new Promise((a,l)=>{const c=h=>{h===!1?l(os(4,{from:n,to:t})):h instanceof Error?l(h):ab(h)?l(os(2,{from:t,to:h})):(r&&s.enterCallbacks[i]===r&&typeof h=="function"&&r.push(h),a())},u=o(()=>e.call(s&&s.instances[i],t,n,c));let f=Promise.resolve(u);e.length<3&&(f=f.then(c)),f.catch(h=>l(h))})}function rr(e,t,n,s,i=o=>o()){const o=[];for(const r of e)for(const a in r.components){let l=r.components[a];if(!(t!=="beforeRouteEnter"&&!r.instances[a]))if(ch(l)){const u=(l.__vccOpts||l)[t];u&&o.push(on(u,n,s,r,a,i))}else{let c=l();o.push(()=>c.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${a}" at "${r.path}"`);const f=Rm(u)?u.default:u;r.mods[a]=u,r.components[a]=f;const d=(f.__vccOpts||f)[t];return d&&on(d,n,s,r,a,i)()}))}}return o}function Ql(e){const t=he(Do),n=he(Oa),s=ue(()=>{const l=Qn(e.to);return t.resolve(l)}),i=ue(()=>{const{matched:l}=s.value,{length:c}=l,u=l[c-1],f=n.matched;if(!u||!f.length)return-1;const h=f.findIndex(is.bind(null,u));if(h>-1)return h;const d=Zl(l[c-2]);return c>1&&Zl(u)===d&&f[f.length-1].path!==d?f.findIndex(is.bind(null,l[c-2])):h}),o=ue(()=>i.value>-1&&kb(n.params,s.value.params)),r=ue(()=>i.value>-1&&i.value===n.matched.length-1&&gh(n.params,s.value.params));function a(l={}){if(Eb(l)){const c=t[Qn(e.replace)?"replace":"push"](Qn(e.to)).catch(Is);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>c),c}return Promise.resolve()}return{route:s,href:ue(()=>s.value.href),isActive:o,isExactActive:r,navigate:a}}function Mb(e){return e.length===1?e[0]:e}const Cb=_f({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Ql,setup(e,{slots:t}){const n=ui(Ql(e)),{options:s}=he(Do),i=ue(()=>({[tc(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[tc(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&Mb(t.default(n));return e.custom?o:Qf("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:i.value},o)}}}),Pb=Cb;function Eb(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function kb(e,t){for(const n in t){const s=t[n],i=e[n];if(typeof s=="string"){if(s!==i)return!1}else if(!ye(i)||i.length!==s.length||s.some((o,r)=>o!==i[r]))return!1}return!0}function Zl(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const tc=(e,t,n)=>e??t??n,Ab=_f({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=he(Hr),i=ue(()=>e.route||s.value),o=he(Jl,0),r=ue(()=>{let c=Qn(o);const{matched:u}=i.value;let f;for(;(f=u[c])&&!f.components;)c++;return c}),a=ue(()=>i.value.matched[r.value]);Ni(Jl,ue(()=>r.value+1)),Ni(Sb,a),Ni(Hr,i);const l=Ma();return Ds(()=>[l.value,a.value,e.name],([c,u,f],[h,d,p])=>{u&&(u.instances[f]=c,d&&d!==u&&c&&c===h&&(u.leaveGuards.size||(u.leaveGuards=d.leaveGuards),u.updateGuards.size||(u.updateGuards=d.updateGuards))),c&&u&&(!d||!is(u,d)||!h)&&(u.enterCallbacks[f]||[]).forEach(g=>g(c))},{flush:"post"}),()=>{const c=i.value,u=e.name,f=a.value,h=f&&f.components[u];if(!h)return ec(n.default,{Component:h,route:c});const d=f.props[u],p=d?d===!0?c.params:typeof d=="function"?d(c):d:null,m=Qf(h,ct({},p,t,{onVnodeUnmounted:_=>{_.component.isUnmounted&&(f.instances[u]=null)},ref:l}));return ec(n.default,{Component:m,route:c})||m}}});function ec(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Ob=Ab;function DS(e){const t=mb(e.routes,e),n=e.parseQuery||vb,s=e.stringifyQuery||Gl,i=e.history,o=gs(),r=gs(),a=gs(),l=Cp(Ze);let c=Ze;Xn&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=ir.bind(null,R=>""+R),f=ir.bind(null,$m),h=ir.bind(null,Js);function d(R,$){let V,K;return bh(R)?(V=t.getRecordMatcher(R),K=$):K=R,t.addRoute(K,V)}function p(R){const $=t.getRecordMatcher(R);$&&t.removeRoute($)}function g(){return t.getRoutes().map(R=>R.record)}function m(R){return!!t.getRecordMatcher(R)}function _(R,$){if($=ct({},$||l.value),typeof R=="string"){const M=or(n,R,$.path),O=t.resolve({path:M.path},$),D=i.createHref(M.fullPath);return ct(M,O,{params:h(O.params),hash:Js(M.hash),redirectedFrom:void 0,href:D})}let V;if(R.path!=null)V=ct({},R,{path:or(n,R.path,$.path).path});else{const M=ct({},R.params);for(const O in M)M[O]==null&&delete M[O];V=ct({},R,{params:f(M)}),$.params=f($.params)}const K=t.resolve(V,$),dt=R.hash||"";K.params=u(h(K.params));const b=qm(s,ct({},R,{hash:jm(dt),path:K.path})),y=i.createHref(b);return ct({fullPath:b,hash:dt,query:s===Gl?wb(R.query):R.query||{}},K,{redirectedFrom:void 0,href:y})}function x(R){return typeof R=="string"?or(n,R,l.value.path):ct({},R)}function v(R,$){if(c!==R)return os(8,{from:$,to:R})}function w(R){return k(R)}function S(R){return w(ct(x(R),{replace:!0}))}function A(R){const $=R.matched[R.matched.length-1];if($&&$.redirect){const{redirect:V}=$;let K=typeof V=="function"?V(R):V;return typeof K=="string"&&(K=K.includes("?")||K.includes("#")?K=x(K):{path:K},K.params={}),ct({query:R.query,hash:R.hash,params:K.path!=null?{}:R.params},K)}}function k(R,$){const V=c=_(R),K=l.value,dt=R.state,b=R.force,y=R.replace===!0,M=A(V);if(M)return k(ct(x(M),{state:typeof M=="object"?ct({},dt,M.state):dt,force:b,replace:y}),$||V);const O=V;O.redirectedFrom=$;let D;return!b&&Ym(s,K,V)&&(D=os(16,{to:O,from:K}),Ut(K,K,!0,!1)),(D?Promise.resolve(D):I(O,K)).catch(T=>Ie(T)?Ie(T,2)?T:$t(T):et(T,O,K)).then(T=>{if(T){if(Ie(T,2))return k(ct({replace:y},x(T.to),{state:typeof T.to=="object"?ct({},dt,T.to.state):dt,force:b}),$||O)}else T=L(O,K,!0,y,dt);return H(O,K,T),T})}function E(R,$){const V=v(R,$);return V?Promise.reject(V):Promise.resolve()}function P(R){const $=Yt.values().next().value;return $&&typeof $.runWithContext=="function"?$.runWithContext(R):R()}function I(R,$){let V;const[K,dt,b]=Rb(R,$);V=rr(K.reverse(),"beforeRouteLeave",R,$);for(const M of K)M.leaveGuards.forEach(O=>{V.push(on(O,R,$))});const y=E.bind(null,R,$);return V.push(y),At(V).then(()=>{V=[];for(const M of o.list())V.push(on(M,R,$));return V.push(y),At(V)}).then(()=>{V=rr(dt,"beforeRouteUpdate",R,$);for(const M of dt)M.updateGuards.forEach(O=>{V.push(on(O,R,$))});return V.push(y),At(V)}).then(()=>{V=[];for(const M of b)if(M.beforeEnter)if(ye(M.beforeEnter))for(const O of M.beforeEnter)V.push(on(O,R,$));else V.push(on(M.beforeEnter,R,$));return V.push(y),At(V)}).then(()=>(R.matched.forEach(M=>M.enterCallbacks={}),V=rr(b,"beforeRouteEnter",R,$,P),V.push(y),At(V))).then(()=>{V=[];for(const M of r.list())V.push(on(M,R,$));return V.push(y),At(V)}).catch(M=>Ie(M,8)?M:Promise.reject(M))}function H(R,$,V){a.list().forEach(K=>P(()=>K(R,$,V)))}function L(R,$,V,K,dt){const b=v(R,$);if(b)return b;const y=$===Ze,M=Xn?history.state:{};V&&(K||y?i.replace(R.fullPath,ct({scroll:y&&M&&M.scroll},dt)):i.push(R.fullPath,dt)),l.value=R,Ut(R,$,V,y),$t()}let X;function rt(){X||(X=i.listen((R,$,V)=>{if(!ve.listening)return;const K=_(R),dt=A(K);if(dt){k(ct(dt,{replace:!0,force:!0}),K).catch(Is);return}c=K;const b=l.value;Xn&&nb(jl(b.fullPath,V.delta),To()),I(K,b).catch(y=>Ie(y,12)?y:Ie(y,2)?(k(ct(x(y.to),{force:!0}),K).then(M=>{Ie(M,20)&&!V.delta&&V.type===Qs.pop&&i.go(-1,!1)}).catch(Is),Promise.reject()):(V.delta&&i.go(-V.delta,!1),et(y,K,b))).then(y=>{y=y||L(K,b,!1),y&&(V.delta&&!Ie(y,8)?i.go(-V.delta,!1):V.type===Qs.pop&&Ie(y,20)&&i.go(-1,!1)),H(K,b,y)}).catch(Is)}))}let Z=gs(),G=gs(),U;function et(R,$,V){$t(R);const K=G.list();return K.length?K.forEach(dt=>dt(R,$,V)):console.error(R),Promise.reject(R)}function mt(){return U&&l.value!==Ze?Promise.resolve():new Promise((R,$)=>{Z.add([R,$])})}function $t(R){return U||(U=!R,rt(),Z.list().forEach(([$,V])=>R?V(R):$()),Z.reset()),R}function Ut(R,$,V,K){const{scrollBehavior:dt}=e;if(!Xn||!dt)return Promise.resolve();const b=!V&&sb(jl(R.fullPath,0))||(K||!V)&&history.state&&history.state.scroll||null;return Po().then(()=>dt(R,$,b)).then(y=>y&&eb(y)).catch(y=>et(y,R,$))}const Pt=R=>i.go(R);let ae;const Yt=new Set,ve={currentRoute:l,listening:!0,addRoute:d,removeRoute:p,clearRoutes:t.clearRoutes,hasRoute:m,getRoutes:g,resolve:_,options:e,push:w,replace:S,go:Pt,back:()=>Pt(-1),forward:()=>Pt(1),beforeEach:o.add,beforeResolve:r.add,afterEach:a.add,onError:G.add,isReady:mt,install(R){const $=this;R.component("RouterLink",Pb),R.component("RouterView",Ob),R.config.globalProperties.$router=$,Object.defineProperty(R.config.globalProperties,"$route",{enumerable:!0,get:()=>Qn(l)}),Xn&&!ae&&l.value===Ze&&(ae=!0,w(i.location).catch(dt=>{}));const V={};for(const dt in Ze)Object.defineProperty(V,dt,{get:()=>l.value[dt],enumerable:!0});R.provide(Do,$),R.provide(Oa,lf(V)),R.provide(Hr,l);const K=R.unmount;Yt.add(R),R.unmount=function(){Yt.delete(R),Yt.size<1&&(c=Ze,X&&X(),X=null,l.value=Ze,ae=!1,U=!1),K()}}};function At(R){return R.reduce(($,V)=>$.then(()=>P(V)),Promise.resolve())}return ve}function Rb(e,t){const n=[],s=[],i=[],o=Math.max(t.matched.length,e.matched.length);for(let r=0;r<o;r++){const a=t.matched[r];a&&(e.matched.find(c=>is(c,a))?s.push(a):n.push(a));const l=e.matched[r];l&&(t.matched.find(c=>is(c,l))||i.push(l))}return[n,s,i]}function LS(){return he(Do)}function FS(e){return he(Oa)}function vh(e,t){return function(){return e.apply(t,arguments)}}const{toString:Tb}=Object.prototype,{getPrototypeOf:Ra}=Object,{iterator:Lo,toStringTag:wh}=Symbol,Fo=(e=>t=>{const n=Tb.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),xe=e=>(e=e.toLowerCase(),t=>Fo(t)===e),Io=e=>t=>typeof t===e,{isArray:us}=Array,Zs=Io("undefined");function Db(e){return e!==null&&!Zs(e)&&e.constructor!==null&&!Zs(e.constructor)&&Zt(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Sh=xe("ArrayBuffer");function Lb(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Sh(e.buffer),t}const Fb=Io("string"),Zt=Io("function"),Mh=Io("number"),No=e=>e!==null&&typeof e=="object",Ib=e=>e===!0||e===!1,ji=e=>{if(Fo(e)!=="object")return!1;const t=Ra(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(wh in e)&&!(Lo in e)},Nb=xe("Date"),Bb=xe("File"),zb=xe("Blob"),Hb=xe("FileList"),jb=e=>No(e)&&Zt(e.pipe),Vb=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Zt(e.append)&&((t=Fo(e))==="formdata"||t==="object"&&Zt(e.toString)&&e.toString()==="[object FormData]"))},Wb=xe("URLSearchParams"),[$b,Ub,Kb,qb]=["ReadableStream","Request","Response","Headers"].map(xe),Yb=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function di(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,i;if(typeof e!="object"&&(e=[e]),us(e))for(s=0,i=e.length;s<i;s++)t.call(null,e[s],s,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),r=o.length;let a;for(s=0;s<r;s++)a=o[s],t.call(null,e[a],a,e)}}function Ch(e,t){t=t.toLowerCase();const n=Object.keys(e);let s=n.length,i;for(;s-- >0;)if(i=n[s],t===i.toLowerCase())return i;return null}const On=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Ph=e=>!Zs(e)&&e!==On;function jr(){const{caseless:e}=Ph(this)&&this||{},t={},n=(s,i)=>{const o=e&&Ch(t,i)||i;ji(t[o])&&ji(s)?t[o]=jr(t[o],s):ji(s)?t[o]=jr({},s):us(s)?t[o]=s.slice():t[o]=s};for(let s=0,i=arguments.length;s<i;s++)arguments[s]&&di(arguments[s],n);return t}const Xb=(e,t,n,{allOwnKeys:s}={})=>(di(t,(i,o)=>{n&&Zt(i)?e[o]=vh(i,n):e[o]=i},{allOwnKeys:s}),e),Gb=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Jb=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Qb=(e,t,n,s)=>{let i,o,r;const a={};if(t=t||{},e==null)return t;do{for(i=Object.getOwnPropertyNames(e),o=i.length;o-- >0;)r=i[o],(!s||s(r,e,t))&&!a[r]&&(t[r]=e[r],a[r]=!0);e=n!==!1&&Ra(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Zb=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},t_=e=>{if(!e)return null;if(us(e))return e;let t=e.length;if(!Mh(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},e_=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Ra(Uint8Array)),n_=(e,t)=>{const s=(e&&e[Lo]).call(e);let i;for(;(i=s.next())&&!i.done;){const o=i.value;t.call(e,o[0],o[1])}},s_=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},i_=xe("HTMLFormElement"),o_=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,i){return s.toUpperCase()+i}),nc=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),r_=xe("RegExp"),Eh=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};di(n,(i,o)=>{let r;(r=t(i,o,e))!==!1&&(s[o]=r||i)}),Object.defineProperties(e,s)},a_=e=>{Eh(e,(t,n)=>{if(Zt(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(Zt(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},l_=(e,t)=>{const n={},s=i=>{i.forEach(o=>{n[o]=!0})};return us(e)?s(e):s(String(e).split(t)),n},c_=()=>{},u_=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function f_(e){return!!(e&&Zt(e.append)&&e[wh]==="FormData"&&e[Lo])}const h_=e=>{const t=new Array(10),n=(s,i)=>{if(No(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[i]=s;const o=us(s)?[]:{};return di(s,(r,a)=>{const l=n(r,i+1);!Zs(l)&&(o[a]=l)}),t[i]=void 0,o}}return s};return n(e,0)},d_=xe("AsyncFunction"),p_=e=>e&&(No(e)||Zt(e))&&Zt(e.then)&&Zt(e.catch),kh=((e,t)=>e?setImmediate:t?((n,s)=>(On.addEventListener("message",({source:i,data:o})=>{i===On&&o===n&&s.length&&s.shift()()},!1),i=>{s.push(i),On.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",Zt(On.postMessage)),g_=typeof queueMicrotask<"u"?queueMicrotask.bind(On):typeof process<"u"&&process.nextTick||kh,m_=e=>e!=null&&Zt(e[Lo]),C={isArray:us,isArrayBuffer:Sh,isBuffer:Db,isFormData:Vb,isArrayBufferView:Lb,isString:Fb,isNumber:Mh,isBoolean:Ib,isObject:No,isPlainObject:ji,isReadableStream:$b,isRequest:Ub,isResponse:Kb,isHeaders:qb,isUndefined:Zs,isDate:Nb,isFile:Bb,isBlob:zb,isRegExp:r_,isFunction:Zt,isStream:jb,isURLSearchParams:Wb,isTypedArray:e_,isFileList:Hb,forEach:di,merge:jr,extend:Xb,trim:Yb,stripBOM:Gb,inherits:Jb,toFlatObject:Qb,kindOf:Fo,kindOfTest:xe,endsWith:Zb,toArray:t_,forEachEntry:n_,matchAll:s_,isHTMLForm:i_,hasOwnProperty:nc,hasOwnProp:nc,reduceDescriptors:Eh,freezeMethods:a_,toObjectSet:l_,toCamelCase:o_,noop:c_,toFiniteNumber:u_,findKey:Ch,global:On,isContextDefined:Ph,isSpecCompliantForm:f_,toJSONObject:h_,isAsyncFn:d_,isThenable:p_,setImmediate:kh,asap:g_,isIterable:m_};function nt(e,t,n,s,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),i&&(this.response=i,this.status=i.status?i.status:null)}C.inherits(nt,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:C.toJSONObject(this.config),code:this.code,status:this.status}}});const Ah=nt.prototype,Oh={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Oh[e]={value:e}});Object.defineProperties(nt,Oh);Object.defineProperty(Ah,"isAxiosError",{value:!0});nt.from=(e,t,n,s,i,o)=>{const r=Object.create(Ah);return C.toFlatObject(e,r,function(l){return l!==Error.prototype},a=>a!=="isAxiosError"),nt.call(r,e.message,t,n,s,i),r.cause=e,r.name=e.name,o&&Object.assign(r,o),r};const b_=null;function Vr(e){return C.isPlainObject(e)||C.isArray(e)}function Rh(e){return C.endsWith(e,"[]")?e.slice(0,-2):e}function sc(e,t,n){return e?e.concat(t).map(function(i,o){return i=Rh(i),!n&&o?"["+i+"]":i}).join(n?".":""):t}function __(e){return C.isArray(e)&&!e.some(Vr)}const y_=C.toFlatObject(C,{},null,function(t){return/^is[A-Z]/.test(t)});function Bo(e,t,n){if(!C.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=C.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(g,m){return!C.isUndefined(m[g])});const s=n.metaTokens,i=n.visitor||u,o=n.dots,r=n.indexes,l=(n.Blob||typeof Blob<"u"&&Blob)&&C.isSpecCompliantForm(t);if(!C.isFunction(i))throw new TypeError("visitor must be a function");function c(p){if(p===null)return"";if(C.isDate(p))return p.toISOString();if(!l&&C.isBlob(p))throw new nt("Blob is not supported. Use a Buffer instead.");return C.isArrayBuffer(p)||C.isTypedArray(p)?l&&typeof Blob=="function"?new Blob([p]):Buffer.from(p):p}function u(p,g,m){let _=p;if(p&&!m&&typeof p=="object"){if(C.endsWith(g,"{}"))g=s?g:g.slice(0,-2),p=JSON.stringify(p);else if(C.isArray(p)&&__(p)||(C.isFileList(p)||C.endsWith(g,"[]"))&&(_=C.toArray(p)))return g=Rh(g),_.forEach(function(v,w){!(C.isUndefined(v)||v===null)&&t.append(r===!0?sc([g],w,o):r===null?g:g+"[]",c(v))}),!1}return Vr(p)?!0:(t.append(sc(m,g,o),c(p)),!1)}const f=[],h=Object.assign(y_,{defaultVisitor:u,convertValue:c,isVisitable:Vr});function d(p,g){if(!C.isUndefined(p)){if(f.indexOf(p)!==-1)throw Error("Circular reference detected in "+g.join("."));f.push(p),C.forEach(p,function(_,x){(!(C.isUndefined(_)||_===null)&&i.call(t,_,C.isString(x)?x.trim():x,g,h))===!0&&d(_,g?g.concat(x):[x])}),f.pop()}}if(!C.isObject(e))throw new TypeError("data must be an object");return d(e),t}function ic(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function Ta(e,t){this._pairs=[],e&&Bo(e,this,t)}const Th=Ta.prototype;Th.append=function(t,n){this._pairs.push([t,n])};Th.toString=function(t){const n=t?function(s){return t.call(this,s,ic)}:ic;return this._pairs.map(function(i){return n(i[0])+"="+n(i[1])},"").join("&")};function x_(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Dh(e,t,n){if(!t)return e;const s=n&&n.encode||x_;C.isFunction(n)&&(n={serialize:n});const i=n&&n.serialize;let o;if(i?o=i(t,n):o=C.isURLSearchParams(t)?t.toString():new Ta(t,n).toString(s),o){const r=e.indexOf("#");r!==-1&&(e=e.slice(0,r)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class oc{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){C.forEach(this.handlers,function(s){s!==null&&t(s)})}}const Lh={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},v_=typeof URLSearchParams<"u"?URLSearchParams:Ta,w_=typeof FormData<"u"?FormData:null,S_=typeof Blob<"u"?Blob:null,M_={isBrowser:!0,classes:{URLSearchParams:v_,FormData:w_,Blob:S_},protocols:["http","https","file","blob","url","data"]},Da=typeof window<"u"&&typeof document<"u",Wr=typeof navigator=="object"&&navigator||void 0,C_=Da&&(!Wr||["ReactNative","NativeScript","NS"].indexOf(Wr.product)<0),P_=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",E_=Da&&window.location.href||"http://localhost",k_=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Da,hasStandardBrowserEnv:C_,hasStandardBrowserWebWorkerEnv:P_,navigator:Wr,origin:E_},Symbol.toStringTag,{value:"Module"})),Ht={...k_,...M_};function A_(e,t){return Bo(e,new Ht.classes.URLSearchParams,Object.assign({visitor:function(n,s,i,o){return Ht.isNode&&C.isBuffer(n)?(this.append(s,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function O_(e){return C.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function R_(e){const t={},n=Object.keys(e);let s;const i=n.length;let o;for(s=0;s<i;s++)o=n[s],t[o]=e[o];return t}function Fh(e){function t(n,s,i,o){let r=n[o++];if(r==="__proto__")return!0;const a=Number.isFinite(+r),l=o>=n.length;return r=!r&&C.isArray(i)?i.length:r,l?(C.hasOwnProp(i,r)?i[r]=[i[r],s]:i[r]=s,!a):((!i[r]||!C.isObject(i[r]))&&(i[r]=[]),t(n,s,i[r],o)&&C.isArray(i[r])&&(i[r]=R_(i[r])),!a)}if(C.isFormData(e)&&C.isFunction(e.entries)){const n={};return C.forEachEntry(e,(s,i)=>{t(O_(s),i,n,0)}),n}return null}function T_(e,t,n){if(C.isString(e))try{return(t||JSON.parse)(e),C.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(e)}const pi={transitional:Lh,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const s=n.getContentType()||"",i=s.indexOf("application/json")>-1,o=C.isObject(t);if(o&&C.isHTMLForm(t)&&(t=new FormData(t)),C.isFormData(t))return i?JSON.stringify(Fh(t)):t;if(C.isArrayBuffer(t)||C.isBuffer(t)||C.isStream(t)||C.isFile(t)||C.isBlob(t)||C.isReadableStream(t))return t;if(C.isArrayBufferView(t))return t.buffer;if(C.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(o){if(s.indexOf("application/x-www-form-urlencoded")>-1)return A_(t,this.formSerializer).toString();if((a=C.isFileList(t))||s.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return Bo(a?{"files[]":t}:t,l&&new l,this.formSerializer)}}return o||i?(n.setContentType("application/json",!1),T_(t)):t}],transformResponse:[function(t){const n=this.transitional||pi.transitional,s=n&&n.forcedJSONParsing,i=this.responseType==="json";if(C.isResponse(t)||C.isReadableStream(t))return t;if(t&&C.isString(t)&&(s&&!this.responseType||i)){const r=!(n&&n.silentJSONParsing)&&i;try{return JSON.parse(t)}catch(a){if(r)throw a.name==="SyntaxError"?nt.from(a,nt.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ht.classes.FormData,Blob:Ht.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};C.forEach(["delete","get","head","post","put","patch"],e=>{pi.headers[e]={}});const D_=C.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),L_=e=>{const t={};let n,s,i;return e&&e.split(`
`).forEach(function(r){i=r.indexOf(":"),n=r.substring(0,i).trim().toLowerCase(),s=r.substring(i+1).trim(),!(!n||t[n]&&D_[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},rc=Symbol("internals");function ms(e){return e&&String(e).trim().toLowerCase()}function Vi(e){return e===!1||e==null?e:C.isArray(e)?e.map(Vi):String(e)}function F_(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const I_=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function ar(e,t,n,s,i){if(C.isFunction(s))return s.call(this,t,n);if(i&&(t=n),!!C.isString(t)){if(C.isString(s))return t.indexOf(s)!==-1;if(C.isRegExp(s))return s.test(t)}}function N_(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function B_(e,t){const n=C.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(i,o,r){return this[s].call(this,t,i,o,r)},configurable:!0})})}let te=class{constructor(t){t&&this.set(t)}set(t,n,s){const i=this;function o(a,l,c){const u=ms(l);if(!u)throw new Error("header name must be a non-empty string");const f=C.findKey(i,u);(!f||i[f]===void 0||c===!0||c===void 0&&i[f]!==!1)&&(i[f||l]=Vi(a))}const r=(a,l)=>C.forEach(a,(c,u)=>o(c,u,l));if(C.isPlainObject(t)||t instanceof this.constructor)r(t,n);else if(C.isString(t)&&(t=t.trim())&&!I_(t))r(L_(t),n);else if(C.isObject(t)&&C.isIterable(t)){let a={},l,c;for(const u of t){if(!C.isArray(u))throw TypeError("Object iterator must return a key-value pair");a[c=u[0]]=(l=a[c])?C.isArray(l)?[...l,u[1]]:[l,u[1]]:u[1]}r(a,n)}else t!=null&&o(n,t,s);return this}get(t,n){if(t=ms(t),t){const s=C.findKey(this,t);if(s){const i=this[s];if(!n)return i;if(n===!0)return F_(i);if(C.isFunction(n))return n.call(this,i,s);if(C.isRegExp(n))return n.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=ms(t),t){const s=C.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||ar(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let i=!1;function o(r){if(r=ms(r),r){const a=C.findKey(s,r);a&&(!n||ar(s,s[a],a,n))&&(delete s[a],i=!0)}}return C.isArray(t)?t.forEach(o):o(t),i}clear(t){const n=Object.keys(this);let s=n.length,i=!1;for(;s--;){const o=n[s];(!t||ar(this,this[o],o,t,!0))&&(delete this[o],i=!0)}return i}normalize(t){const n=this,s={};return C.forEach(this,(i,o)=>{const r=C.findKey(s,o);if(r){n[r]=Vi(i),delete n[o];return}const a=t?N_(o):String(o).trim();a!==o&&delete n[o],n[a]=Vi(i),s[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return C.forEach(this,(s,i)=>{s!=null&&s!==!1&&(n[i]=t&&C.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(i=>s.set(i)),s}static accessor(t){const s=(this[rc]=this[rc]={accessors:{}}).accessors,i=this.prototype;function o(r){const a=ms(r);s[a]||(B_(i,r),s[a]=!0)}return C.isArray(t)?t.forEach(o):o(t),this}};te.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);C.reduceDescriptors(te.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[n]=s}}});C.freezeMethods(te);function lr(e,t){const n=this||pi,s=t||n,i=te.from(s.headers);let o=s.data;return C.forEach(e,function(a){o=a.call(n,o,i.normalize(),t?t.status:void 0)}),i.normalize(),o}function Ih(e){return!!(e&&e.__CANCEL__)}function fs(e,t,n){nt.call(this,e??"canceled",nt.ERR_CANCELED,t,n),this.name="CanceledError"}C.inherits(fs,nt,{__CANCEL__:!0});function Nh(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new nt("Request failed with status code "+n.status,[nt.ERR_BAD_REQUEST,nt.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function z_(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function H_(e,t){e=e||10;const n=new Array(e),s=new Array(e);let i=0,o=0,r;return t=t!==void 0?t:1e3,function(l){const c=Date.now(),u=s[o];r||(r=c),n[i]=l,s[i]=c;let f=o,h=0;for(;f!==i;)h+=n[f++],f=f%e;if(i=(i+1)%e,i===o&&(o=(o+1)%e),c-r<t)return;const d=u&&c-u;return d?Math.round(h*1e3/d):void 0}}function j_(e,t){let n=0,s=1e3/t,i,o;const r=(c,u=Date.now())=>{n=u,i=null,o&&(clearTimeout(o),o=null),e.apply(null,c)};return[(...c)=>{const u=Date.now(),f=u-n;f>=s?r(c,u):(i=c,o||(o=setTimeout(()=>{o=null,r(i)},s-f)))},()=>i&&r(i)]}const co=(e,t,n=3)=>{let s=0;const i=H_(50,250);return j_(o=>{const r=o.loaded,a=o.lengthComputable?o.total:void 0,l=r-s,c=i(l),u=r<=a;s=r;const f={loaded:r,total:a,progress:a?r/a:void 0,bytes:l,rate:c||void 0,estimated:c&&a&&u?(a-r)/c:void 0,event:o,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(f)},n)},ac=(e,t)=>{const n=e!=null;return[s=>t[0]({lengthComputable:n,total:e,loaded:s}),t[1]]},lc=e=>(...t)=>C.asap(()=>e(...t)),V_=Ht.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Ht.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Ht.origin),Ht.navigator&&/(msie|trident)/i.test(Ht.navigator.userAgent)):()=>!0,W_=Ht.hasStandardBrowserEnv?{write(e,t,n,s,i,o){const r=[e+"="+encodeURIComponent(t)];C.isNumber(n)&&r.push("expires="+new Date(n).toGMTString()),C.isString(s)&&r.push("path="+s),C.isString(i)&&r.push("domain="+i),o===!0&&r.push("secure"),document.cookie=r.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function $_(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function U_(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Bh(e,t,n){let s=!$_(t);return e&&(s||n==!1)?U_(e,t):t}const cc=e=>e instanceof te?{...e}:e;function Bn(e,t){t=t||{};const n={};function s(c,u,f,h){return C.isPlainObject(c)&&C.isPlainObject(u)?C.merge.call({caseless:h},c,u):C.isPlainObject(u)?C.merge({},u):C.isArray(u)?u.slice():u}function i(c,u,f,h){if(C.isUndefined(u)){if(!C.isUndefined(c))return s(void 0,c,f,h)}else return s(c,u,f,h)}function o(c,u){if(!C.isUndefined(u))return s(void 0,u)}function r(c,u){if(C.isUndefined(u)){if(!C.isUndefined(c))return s(void 0,c)}else return s(void 0,u)}function a(c,u,f){if(f in t)return s(c,u);if(f in e)return s(void 0,c)}const l={url:o,method:o,data:o,baseURL:r,transformRequest:r,transformResponse:r,paramsSerializer:r,timeout:r,timeoutMessage:r,withCredentials:r,withXSRFToken:r,adapter:r,responseType:r,xsrfCookieName:r,xsrfHeaderName:r,onUploadProgress:r,onDownloadProgress:r,decompress:r,maxContentLength:r,maxBodyLength:r,beforeRedirect:r,transport:r,httpAgent:r,httpsAgent:r,cancelToken:r,socketPath:r,responseEncoding:r,validateStatus:a,headers:(c,u,f)=>i(cc(c),cc(u),f,!0)};return C.forEach(Object.keys(Object.assign({},e,t)),function(u){const f=l[u]||i,h=f(e[u],t[u],u);C.isUndefined(h)&&f!==a||(n[u]=h)}),n}const zh=e=>{const t=Bn({},e);let{data:n,withXSRFToken:s,xsrfHeaderName:i,xsrfCookieName:o,headers:r,auth:a}=t;t.headers=r=te.from(r),t.url=Dh(Bh(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&r.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let l;if(C.isFormData(n)){if(Ht.hasStandardBrowserEnv||Ht.hasStandardBrowserWebWorkerEnv)r.setContentType(void 0);else if((l=r.getContentType())!==!1){const[c,...u]=l?l.split(";").map(f=>f.trim()).filter(Boolean):[];r.setContentType([c||"multipart/form-data",...u].join("; "))}}if(Ht.hasStandardBrowserEnv&&(s&&C.isFunction(s)&&(s=s(t)),s||s!==!1&&V_(t.url))){const c=i&&o&&W_.read(o);c&&r.set(i,c)}return t},K_=typeof XMLHttpRequest<"u",q_=K_&&function(e){return new Promise(function(n,s){const i=zh(e);let o=i.data;const r=te.from(i.headers).normalize();let{responseType:a,onUploadProgress:l,onDownloadProgress:c}=i,u,f,h,d,p;function g(){d&&d(),p&&p(),i.cancelToken&&i.cancelToken.unsubscribe(u),i.signal&&i.signal.removeEventListener("abort",u)}let m=new XMLHttpRequest;m.open(i.method.toUpperCase(),i.url,!0),m.timeout=i.timeout;function _(){if(!m)return;const v=te.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders()),S={data:!a||a==="text"||a==="json"?m.responseText:m.response,status:m.status,statusText:m.statusText,headers:v,config:e,request:m};Nh(function(k){n(k),g()},function(k){s(k),g()},S),m=null}"onloadend"in m?m.onloadend=_:m.onreadystatechange=function(){!m||m.readyState!==4||m.status===0&&!(m.responseURL&&m.responseURL.indexOf("file:")===0)||setTimeout(_)},m.onabort=function(){m&&(s(new nt("Request aborted",nt.ECONNABORTED,e,m)),m=null)},m.onerror=function(){s(new nt("Network Error",nt.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let w=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const S=i.transitional||Lh;i.timeoutErrorMessage&&(w=i.timeoutErrorMessage),s(new nt(w,S.clarifyTimeoutError?nt.ETIMEDOUT:nt.ECONNABORTED,e,m)),m=null},o===void 0&&r.setContentType(null),"setRequestHeader"in m&&C.forEach(r.toJSON(),function(w,S){m.setRequestHeader(S,w)}),C.isUndefined(i.withCredentials)||(m.withCredentials=!!i.withCredentials),a&&a!=="json"&&(m.responseType=i.responseType),c&&([h,p]=co(c,!0),m.addEventListener("progress",h)),l&&m.upload&&([f,d]=co(l),m.upload.addEventListener("progress",f),m.upload.addEventListener("loadend",d)),(i.cancelToken||i.signal)&&(u=v=>{m&&(s(!v||v.type?new fs(null,e,m):v),m.abort(),m=null)},i.cancelToken&&i.cancelToken.subscribe(u),i.signal&&(i.signal.aborted?u():i.signal.addEventListener("abort",u)));const x=z_(i.url);if(x&&Ht.protocols.indexOf(x)===-1){s(new nt("Unsupported protocol "+x+":",nt.ERR_BAD_REQUEST,e));return}m.send(o||null)})},Y_=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let s=new AbortController,i;const o=function(c){if(!i){i=!0,a();const u=c instanceof Error?c:this.reason;s.abort(u instanceof nt?u:new fs(u instanceof Error?u.message:u))}};let r=t&&setTimeout(()=>{r=null,o(new nt(`timeout ${t} of ms exceeded`,nt.ETIMEDOUT))},t);const a=()=>{e&&(r&&clearTimeout(r),r=null,e.forEach(c=>{c.unsubscribe?c.unsubscribe(o):c.removeEventListener("abort",o)}),e=null)};e.forEach(c=>c.addEventListener("abort",o));const{signal:l}=s;return l.unsubscribe=()=>C.asap(a),l}},X_=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let s=0,i;for(;s<n;)i=s+t,yield e.slice(s,i),s=i},G_=async function*(e,t){for await(const n of J_(e))yield*X_(n,t)},J_=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:s}=await t.read();if(n)break;yield s}}finally{await t.cancel()}},uc=(e,t,n,s)=>{const i=G_(e,t);let o=0,r,a=l=>{r||(r=!0,s&&s(l))};return new ReadableStream({async pull(l){try{const{done:c,value:u}=await i.next();if(c){a(),l.close();return}let f=u.byteLength;if(n){let h=o+=f;n(h)}l.enqueue(new Uint8Array(u))}catch(c){throw a(c),c}},cancel(l){return a(l),i.return()}},{highWaterMark:2})},zo=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Hh=zo&&typeof ReadableStream=="function",Q_=zo&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),jh=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Z_=Hh&&jh(()=>{let e=!1;const t=new Request(Ht.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),fc=64*1024,$r=Hh&&jh(()=>C.isReadableStream(new Response("").body)),uo={stream:$r&&(e=>e.body)};zo&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!uo[t]&&(uo[t]=C.isFunction(e[t])?n=>n[t]():(n,s)=>{throw new nt(`Response type '${t}' is not supported`,nt.ERR_NOT_SUPPORT,s)})})})(new Response);const ty=async e=>{if(e==null)return 0;if(C.isBlob(e))return e.size;if(C.isSpecCompliantForm(e))return(await new Request(Ht.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(C.isArrayBufferView(e)||C.isArrayBuffer(e))return e.byteLength;if(C.isURLSearchParams(e)&&(e=e+""),C.isString(e))return(await Q_(e)).byteLength},ey=async(e,t)=>{const n=C.toFiniteNumber(e.getContentLength());return n??ty(t)},ny=zo&&(async e=>{let{url:t,method:n,data:s,signal:i,cancelToken:o,timeout:r,onDownloadProgress:a,onUploadProgress:l,responseType:c,headers:u,withCredentials:f="same-origin",fetchOptions:h}=zh(e);c=c?(c+"").toLowerCase():"text";let d=Y_([i,o&&o.toAbortSignal()],r),p;const g=d&&d.unsubscribe&&(()=>{d.unsubscribe()});let m;try{if(l&&Z_&&n!=="get"&&n!=="head"&&(m=await ey(u,s))!==0){let S=new Request(t,{method:"POST",body:s,duplex:"half"}),A;if(C.isFormData(s)&&(A=S.headers.get("content-type"))&&u.setContentType(A),S.body){const[k,E]=ac(m,co(lc(l)));s=uc(S.body,fc,k,E)}}C.isString(f)||(f=f?"include":"omit");const _="credentials"in Request.prototype;p=new Request(t,{...h,signal:d,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:s,duplex:"half",credentials:_?f:void 0});let x=await fetch(p);const v=$r&&(c==="stream"||c==="response");if($r&&(a||v&&g)){const S={};["status","statusText","headers"].forEach(P=>{S[P]=x[P]});const A=C.toFiniteNumber(x.headers.get("content-length")),[k,E]=a&&ac(A,co(lc(a),!0))||[];x=new Response(uc(x.body,fc,k,()=>{E&&E(),g&&g()}),S)}c=c||"text";let w=await uo[C.findKey(uo,c)||"text"](x,e);return!v&&g&&g(),await new Promise((S,A)=>{Nh(S,A,{data:w,headers:te.from(x.headers),status:x.status,statusText:x.statusText,config:e,request:p})})}catch(_){throw g&&g(),_&&_.name==="TypeError"&&/Load failed|fetch/i.test(_.message)?Object.assign(new nt("Network Error",nt.ERR_NETWORK,e,p),{cause:_.cause||_}):nt.from(_,_&&_.code,e,p)}}),Ur={http:b_,xhr:q_,fetch:ny};C.forEach(Ur,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const hc=e=>`- ${e}`,sy=e=>C.isFunction(e)||e===null||e===!1,Vh={getAdapter:e=>{e=C.isArray(e)?e:[e];const{length:t}=e;let n,s;const i={};for(let o=0;o<t;o++){n=e[o];let r;if(s=n,!sy(n)&&(s=Ur[(r=String(n)).toLowerCase()],s===void 0))throw new nt(`Unknown adapter '${r}'`);if(s)break;i[r||"#"+o]=s}if(!s){const o=Object.entries(i).map(([a,l])=>`adapter ${a} `+(l===!1?"is not supported by the environment":"is not available in the build"));let r=t?o.length>1?`since :
`+o.map(hc).join(`
`):" "+hc(o[0]):"as no adapter specified";throw new nt("There is no suitable adapter to dispatch the request "+r,"ERR_NOT_SUPPORT")}return s},adapters:Ur};function cr(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new fs(null,e)}function dc(e){return cr(e),e.headers=te.from(e.headers),e.data=lr.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Vh.getAdapter(e.adapter||pi.adapter)(e).then(function(s){return cr(e),s.data=lr.call(e,e.transformResponse,s),s.headers=te.from(s.headers),s},function(s){return Ih(s)||(cr(e),s&&s.response&&(s.response.data=lr.call(e,e.transformResponse,s.response),s.response.headers=te.from(s.response.headers))),Promise.reject(s)})}const Wh="1.9.0",Ho={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Ho[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const pc={};Ho.transitional=function(t,n,s){function i(o,r){return"[Axios v"+Wh+"] Transitional option '"+o+"'"+r+(s?". "+s:"")}return(o,r,a)=>{if(t===!1)throw new nt(i(r," has been removed"+(n?" in "+n:"")),nt.ERR_DEPRECATED);return n&&!pc[r]&&(pc[r]=!0,console.warn(i(r," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,r,a):!0}};Ho.spelling=function(t){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function iy(e,t,n){if(typeof e!="object")throw new nt("options must be an object",nt.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let i=s.length;for(;i-- >0;){const o=s[i],r=t[o];if(r){const a=e[o],l=a===void 0||r(a,o,e);if(l!==!0)throw new nt("option "+o+" must be "+l,nt.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new nt("Unknown option "+o,nt.ERR_BAD_OPTION)}}const Wi={assertOptions:iy,validators:Ho},Pe=Wi.validators;let Ln=class{constructor(t){this.defaults=t||{},this.interceptors={request:new oc,response:new oc}}async request(t,n){try{return await this._request(t,n)}catch(s){if(s instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const o=i.stack?i.stack.replace(/^.+\n/,""):"";try{s.stack?o&&!String(s.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+o):s.stack=o}catch{}}throw s}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Bn(this.defaults,n);const{transitional:s,paramsSerializer:i,headers:o}=n;s!==void 0&&Wi.assertOptions(s,{silentJSONParsing:Pe.transitional(Pe.boolean),forcedJSONParsing:Pe.transitional(Pe.boolean),clarifyTimeoutError:Pe.transitional(Pe.boolean)},!1),i!=null&&(C.isFunction(i)?n.paramsSerializer={serialize:i}:Wi.assertOptions(i,{encode:Pe.function,serialize:Pe.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Wi.assertOptions(n,{baseUrl:Pe.spelling("baseURL"),withXsrfToken:Pe.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let r=o&&C.merge(o.common,o[n.method]);o&&C.forEach(["delete","get","head","post","put","patch","common"],p=>{delete o[p]}),n.headers=te.concat(r,o);const a=[];let l=!0;this.interceptors.request.forEach(function(g){typeof g.runWhen=="function"&&g.runWhen(n)===!1||(l=l&&g.synchronous,a.unshift(g.fulfilled,g.rejected))});const c=[];this.interceptors.response.forEach(function(g){c.push(g.fulfilled,g.rejected)});let u,f=0,h;if(!l){const p=[dc.bind(this),void 0];for(p.unshift.apply(p,a),p.push.apply(p,c),h=p.length,u=Promise.resolve(n);f<h;)u=u.then(p[f++],p[f++]);return u}h=a.length;let d=n;for(f=0;f<h;){const p=a[f++],g=a[f++];try{d=p(d)}catch(m){g.call(this,m);break}}try{u=dc.call(this,d)}catch(p){return Promise.reject(p)}for(f=0,h=c.length;f<h;)u=u.then(c[f++],c[f++]);return u}getUri(t){t=Bn(this.defaults,t);const n=Bh(t.baseURL,t.url,t.allowAbsoluteUrls);return Dh(n,t.params,t.paramsSerializer)}};C.forEach(["delete","get","head","options"],function(t){Ln.prototype[t]=function(n,s){return this.request(Bn(s||{},{method:t,url:n,data:(s||{}).data}))}});C.forEach(["post","put","patch"],function(t){function n(s){return function(o,r,a){return this.request(Bn(a||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:o,data:r}))}}Ln.prototype[t]=n(),Ln.prototype[t+"Form"]=n(!0)});let oy=class $h{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const s=this;this.promise.then(i=>{if(!s._listeners)return;let o=s._listeners.length;for(;o-- >0;)s._listeners[o](i);s._listeners=null}),this.promise.then=i=>{let o;const r=new Promise(a=>{s.subscribe(a),o=a}).then(i);return r.cancel=function(){s.unsubscribe(o)},r},t(function(o,r,a){s.reason||(s.reason=new fs(o,r,a),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=s=>{t.abort(s)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new $h(function(i){t=i}),cancel:t}}};function ry(e){return function(n){return e.apply(null,n)}}function ay(e){return C.isObject(e)&&e.isAxiosError===!0}const Kr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Kr).forEach(([e,t])=>{Kr[t]=e});function Uh(e){const t=new Ln(e),n=vh(Ln.prototype.request,t);return C.extend(n,Ln.prototype,t,{allOwnKeys:!0}),C.extend(n,t,null,{allOwnKeys:!0}),n.create=function(i){return Uh(Bn(e,i))},n}const Ot=Uh(pi);Ot.Axios=Ln;Ot.CanceledError=fs;Ot.CancelToken=oy;Ot.isCancel=Ih;Ot.VERSION=Wh;Ot.toFormData=Bo;Ot.AxiosError=nt;Ot.Cancel=Ot.CanceledError;Ot.all=function(t){return Promise.all(t)};Ot.spread=ry;Ot.isAxiosError=ay;Ot.mergeConfig=Bn;Ot.AxiosHeaders=te;Ot.formToJSON=e=>Fh(C.isHTMLForm(e)?new FormData(e):e);Ot.getAdapter=Vh.getAdapter;Ot.HttpStatusCode=Kr;Ot.default=Ot;const{Axios:BS,AxiosError:zS,CanceledError:HS,isCancel:jS,CancelToken:VS,VERSION:WS,all:$S,Cancel:US,isAxiosError:KS,spread:qS,toFormData:YS,AxiosHeaders:XS,HttpStatusCode:GS,formToJSON:JS,getAdapter:QS,mergeConfig:ZS}=Ot;/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function gi(e){return e+.5|0}const an=(e,t,n)=>Math.max(Math.min(e,n),t);function Ss(e){return an(gi(e*2.55),0,255)}function dn(e){return an(gi(e*255),0,255)}function je(e){return an(gi(e/2.55)/100,0,1)}function gc(e){return an(gi(e*100),0,100)}const ce={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},qr=[..."0123456789ABCDEF"],ly=e=>qr[e&15],cy=e=>qr[(e&240)>>4]+qr[e&15],vi=e=>(e&240)>>4===(e&15),uy=e=>vi(e.r)&&vi(e.g)&&vi(e.b)&&vi(e.a);function fy(e){var t=e.length,n;return e[0]==="#"&&(t===4||t===5?n={r:255&ce[e[1]]*17,g:255&ce[e[2]]*17,b:255&ce[e[3]]*17,a:t===5?ce[e[4]]*17:255}:(t===7||t===9)&&(n={r:ce[e[1]]<<4|ce[e[2]],g:ce[e[3]]<<4|ce[e[4]],b:ce[e[5]]<<4|ce[e[6]],a:t===9?ce[e[7]]<<4|ce[e[8]]:255})),n}const hy=(e,t)=>e<255?t(e):"";function dy(e){var t=uy(e)?ly:cy;return e?"#"+t(e.r)+t(e.g)+t(e.b)+hy(e.a,t):void 0}const py=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Kh(e,t,n){const s=t*Math.min(n,1-n),i=(o,r=(o+e/30)%12)=>n-s*Math.max(Math.min(r-3,9-r,1),-1);return[i(0),i(8),i(4)]}function gy(e,t,n){const s=(i,o=(i+e/60)%6)=>n-n*t*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function my(e,t,n){const s=Kh(e,1,.5);let i;for(t+n>1&&(i=1/(t+n),t*=i,n*=i),i=0;i<3;i++)s[i]*=1-t-n,s[i]+=t;return s}function by(e,t,n,s,i){return e===i?(t-n)/s+(t<n?6:0):t===i?(n-e)/s+2:(e-t)/s+4}function La(e){const n=e.r/255,s=e.g/255,i=e.b/255,o=Math.max(n,s,i),r=Math.min(n,s,i),a=(o+r)/2;let l,c,u;return o!==r&&(u=o-r,c=a>.5?u/(2-o-r):u/(o+r),l=by(n,s,i,u,o),l=l*60+.5),[l|0,c||0,a]}function Fa(e,t,n,s){return(Array.isArray(t)?e(t[0],t[1],t[2]):e(t,n,s)).map(dn)}function Ia(e,t,n){return Fa(Kh,e,t,n)}function _y(e,t,n){return Fa(my,e,t,n)}function yy(e,t,n){return Fa(gy,e,t,n)}function qh(e){return(e%360+360)%360}function xy(e){const t=py.exec(e);let n=255,s;if(!t)return;t[5]!==s&&(n=t[6]?Ss(+t[5]):dn(+t[5]));const i=qh(+t[2]),o=+t[3]/100,r=+t[4]/100;return t[1]==="hwb"?s=_y(i,o,r):t[1]==="hsv"?s=yy(i,o,r):s=Ia(i,o,r),{r:s[0],g:s[1],b:s[2],a:n}}function vy(e,t){var n=La(e);n[0]=qh(n[0]+t),n=Ia(n),e.r=n[0],e.g=n[1],e.b=n[2]}function wy(e){if(!e)return;const t=La(e),n=t[0],s=gc(t[1]),i=gc(t[2]);return e.a<255?`hsla(${n}, ${s}%, ${i}%, ${je(e.a)})`:`hsl(${n}, ${s}%, ${i}%)`}const mc={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},bc={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function Sy(){const e={},t=Object.keys(bc),n=Object.keys(mc);let s,i,o,r,a;for(s=0;s<t.length;s++){for(r=a=t[s],i=0;i<n.length;i++)o=n[i],a=a.replace(o,mc[o]);o=parseInt(bc[r],16),e[a]=[o>>16&255,o>>8&255,o&255]}return e}let wi;function My(e){wi||(wi=Sy(),wi.transparent=[0,0,0,0]);const t=wi[e.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const Cy=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function Py(e){const t=Cy.exec(e);let n=255,s,i,o;if(t){if(t[7]!==s){const r=+t[7];n=t[8]?Ss(r):an(r*255,0,255)}return s=+t[1],i=+t[3],o=+t[5],s=255&(t[2]?Ss(s):an(s,0,255)),i=255&(t[4]?Ss(i):an(i,0,255)),o=255&(t[6]?Ss(o):an(o,0,255)),{r:s,g:i,b:o,a:n}}}function Ey(e){return e&&(e.a<255?`rgba(${e.r}, ${e.g}, ${e.b}, ${je(e.a)})`:`rgb(${e.r}, ${e.g}, ${e.b})`)}const ur=e=>e<=.0031308?e*12.92:Math.pow(e,1/2.4)*1.055-.055,Un=e=>e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4);function ky(e,t,n){const s=Un(je(e.r)),i=Un(je(e.g)),o=Un(je(e.b));return{r:dn(ur(s+n*(Un(je(t.r))-s))),g:dn(ur(i+n*(Un(je(t.g))-i))),b:dn(ur(o+n*(Un(je(t.b))-o))),a:e.a+n*(t.a-e.a)}}function Si(e,t,n){if(e){let s=La(e);s[t]=Math.max(0,Math.min(s[t]+s[t]*n,t===0?360:1)),s=Ia(s),e.r=s[0],e.g=s[1],e.b=s[2]}}function Yh(e,t){return e&&Object.assign(t||{},e)}function _c(e){var t={r:0,g:0,b:0,a:255};return Array.isArray(e)?e.length>=3&&(t={r:e[0],g:e[1],b:e[2],a:255},e.length>3&&(t.a=dn(e[3]))):(t=Yh(e,{r:0,g:0,b:0,a:1}),t.a=dn(t.a)),t}function Ay(e){return e.charAt(0)==="r"?Py(e):xy(e)}class ti{constructor(t){if(t instanceof ti)return t;const n=typeof t;let s;n==="object"?s=_c(t):n==="string"&&(s=fy(t)||My(t)||Ay(t)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var t=Yh(this._rgb);return t&&(t.a=je(t.a)),t}set rgb(t){this._rgb=_c(t)}rgbString(){return this._valid?Ey(this._rgb):void 0}hexString(){return this._valid?dy(this._rgb):void 0}hslString(){return this._valid?wy(this._rgb):void 0}mix(t,n){if(t){const s=this.rgb,i=t.rgb;let o;const r=n===o?.5:n,a=2*r-1,l=s.a-i.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;o=1-c,s.r=255&c*s.r+o*i.r+.5,s.g=255&c*s.g+o*i.g+.5,s.b=255&c*s.b+o*i.b+.5,s.a=r*s.a+(1-r)*i.a,this.rgb=s}return this}interpolate(t,n){return t&&(this._rgb=ky(this._rgb,t._rgb,n)),this}clone(){return new ti(this.rgb)}alpha(t){return this._rgb.a=dn(t),this}clearer(t){const n=this._rgb;return n.a*=1-t,this}greyscale(){const t=this._rgb,n=gi(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=n,this}opaquer(t){const n=this._rgb;return n.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return Si(this._rgb,2,t),this}darken(t){return Si(this._rgb,2,-t),this}saturate(t){return Si(this._rgb,1,t),this}desaturate(t){return Si(this._rgb,1,-t),this}rotate(t){return vy(this._rgb,t),this}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function Ne(){}const Oy=(()=>{let e=0;return()=>e++})();function it(e){return e==null}function wt(e){if(Array.isArray&&Array.isArray(e))return!0;const t=Object.prototype.toString.call(e);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function ot(e){return e!==null&&Object.prototype.toString.call(e)==="[object Object]"}function Mt(e){return(typeof e=="number"||e instanceof Number)&&isFinite(+e)}function ie(e,t){return Mt(e)?e:t}function st(e,t){return typeof e>"u"?t:e}const Ry=(e,t)=>typeof e=="string"&&e.endsWith("%")?parseFloat(e)/100:+e/t,Xh=(e,t)=>typeof e=="string"&&e.endsWith("%")?parseFloat(e)/100*t:+e;function yt(e,t,n){if(e&&typeof e.call=="function")return e.apply(n,t)}function ht(e,t,n,s){let i,o,r;if(wt(e))for(o=e.length,i=0;i<o;i++)t.call(n,e[i],i);else if(ot(e))for(r=Object.keys(e),o=r.length,i=0;i<o;i++)t.call(n,e[r[i]],r[i])}function fo(e,t){let n,s,i,o;if(!e||!t||e.length!==t.length)return!1;for(n=0,s=e.length;n<s;++n)if(i=e[n],o=t[n],i.datasetIndex!==o.datasetIndex||i.index!==o.index)return!1;return!0}function ho(e){if(wt(e))return e.map(ho);if(ot(e)){const t=Object.create(null),n=Object.keys(e),s=n.length;let i=0;for(;i<s;++i)t[n[i]]=ho(e[n[i]]);return t}return e}function Gh(e){return["__proto__","prototype","constructor"].indexOf(e)===-1}function Ty(e,t,n,s){if(!Gh(e))return;const i=t[e],o=n[e];ot(i)&&ot(o)?ei(i,o,s):t[e]=ho(o)}function ei(e,t,n){const s=wt(t)?t:[t],i=s.length;if(!ot(e))return e;n=n||{};const o=n.merger||Ty;let r;for(let a=0;a<i;++a){if(r=s[a],!ot(r))continue;const l=Object.keys(r);for(let c=0,u=l.length;c<u;++c)o(l[c],e,r,n)}return e}function Bs(e,t){return ei(e,t,{merger:Dy})}function Dy(e,t,n){if(!Gh(e))return;const s=t[e],i=n[e];ot(s)&&ot(i)?Bs(s,i):Object.prototype.hasOwnProperty.call(t,e)||(t[e]=ho(i))}const yc={"":e=>e,x:e=>e.x,y:e=>e.y};function Ly(e){const t=e.split("."),n=[];let s="";for(const i of t)s+=i,s.endsWith("\\")?s=s.slice(0,-1)+".":(n.push(s),s="");return n}function Fy(e){const t=Ly(e);return n=>{for(const s of t){if(s==="")break;n=n&&n[s]}return n}}function gn(e,t){return(yc[t]||(yc[t]=Fy(t)))(e)}function Na(e){return e.charAt(0).toUpperCase()+e.slice(1)}const ni=e=>typeof e<"u",mn=e=>typeof e=="function",xc=(e,t)=>{if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0};function Iy(e){return e.type==="mouseup"||e.type==="click"||e.type==="contextmenu"}const vt=Math.PI,xt=2*vt,Ny=xt+vt,po=Number.POSITIVE_INFINITY,By=vt/180,Et=vt/2,wn=vt/4,vc=vt*2/3,ln=Math.log10,De=Math.sign;function zs(e,t,n){return Math.abs(e-t)<n}function wc(e){const t=Math.round(e);e=zs(e,t,e/1e3)?t:e;const n=Math.pow(10,Math.floor(ln(e))),s=e/n;return(s<=1?1:s<=2?2:s<=5?5:10)*n}function zy(e){const t=[],n=Math.sqrt(e);let s;for(s=1;s<n;s++)e%s===0&&(t.push(s),t.push(e/s));return n===(n|0)&&t.push(n),t.sort((i,o)=>i-o).pop(),t}function Hy(e){return typeof e=="symbol"||typeof e=="object"&&e!==null&&!(Symbol.toPrimitive in e||"toString"in e||"valueOf"in e)}function rs(e){return!Hy(e)&&!isNaN(parseFloat(e))&&isFinite(e)}function jy(e,t){const n=Math.round(e);return n-t<=e&&n+t>=e}function Jh(e,t,n){let s,i,o;for(s=0,i=e.length;s<i;s++)o=e[s][n],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function pe(e){return e*(vt/180)}function Ba(e){return e*(180/vt)}function Sc(e){if(!Mt(e))return;let t=1,n=0;for(;Math.round(e*t)/t!==e;)t*=10,n++;return n}function Qh(e,t){const n=t.x-e.x,s=t.y-e.y,i=Math.sqrt(n*n+s*s);let o=Math.atan2(s,n);return o<-.5*vt&&(o+=xt),{angle:o,distance:i}}function Yr(e,t){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}function Vy(e,t){return(e-t+Ny)%xt-vt}function oe(e){return(e%xt+xt)%xt}function si(e,t,n,s){const i=oe(e),o=oe(t),r=oe(n),a=oe(o-i),l=oe(r-i),c=oe(i-o),u=oe(i-r);return i===o||i===r||s&&o===r||a>l&&c<u}function Ft(e,t,n){return Math.max(t,Math.min(n,e))}function Wy(e){return Ft(e,-32768,32767)}function $e(e,t,n,s=1e-6){return e>=Math.min(t,n)-s&&e<=Math.max(t,n)+s}function za(e,t,n){n=n||(r=>e[r]<t);let s=e.length-1,i=0,o;for(;s-i>1;)o=i+s>>1,n(o)?i=o:s=o;return{lo:i,hi:s}}const Ue=(e,t,n,s)=>za(e,n,s?i=>{const o=e[i][t];return o<n||o===n&&e[i+1][t]===n}:i=>e[i][t]<n),$y=(e,t,n)=>za(e,n,s=>e[s][t]>=n);function Uy(e,t,n){let s=0,i=e.length;for(;s<i&&e[s]<t;)s++;for(;i>s&&e[i-1]>n;)i--;return s>0||i<e.length?e.slice(s,i):e}const Zh=["push","pop","shift","splice","unshift"];function Ky(e,t){if(e._chartjs){e._chartjs.listeners.push(t);return}Object.defineProperty(e,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),Zh.forEach(n=>{const s="_onData"+Na(n),i=e[n];Object.defineProperty(e,n,{configurable:!0,enumerable:!1,value(...o){const r=i.apply(this,o);return e._chartjs.listeners.forEach(a=>{typeof a[s]=="function"&&a[s](...o)}),r}})})}function Mc(e,t){const n=e._chartjs;if(!n)return;const s=n.listeners,i=s.indexOf(t);i!==-1&&s.splice(i,1),!(s.length>0)&&(Zh.forEach(o=>{delete e[o]}),delete e._chartjs)}function td(e){const t=new Set(e);return t.size===e.length?e:Array.from(t)}const ed=function(){return typeof window>"u"?function(e){return e()}:window.requestAnimationFrame}();function nd(e,t){let n=[],s=!1;return function(...i){n=i,s||(s=!0,ed.call(window,()=>{s=!1,e.apply(t,n)}))}}function qy(e,t){let n;return function(...s){return t?(clearTimeout(n),n=setTimeout(e,t,s)):e.apply(this,s),t}}const Ha=e=>e==="start"?"left":e==="end"?"right":"center",Nt=(e,t,n)=>e==="start"?t:e==="end"?n:(t+n)/2,Yy=(e,t,n,s)=>e===(s?"left":"right")?n:e==="center"?(t+n)/2:t;function sd(e,t,n){const s=t.length;let i=0,o=s;if(e._sorted){const{iScale:r,vScale:a,_parsed:l}=e,c=e.dataset&&e.dataset.options?e.dataset.options.spanGaps:null,u=r.axis,{min:f,max:h,minDefined:d,maxDefined:p}=r.getUserBounds();if(d){if(i=Math.min(Ue(l,u,f).lo,n?s:Ue(t,u,r.getPixelForValue(f)).lo),c){const g=l.slice(0,i+1).reverse().findIndex(m=>!it(m[a.axis]));i-=Math.max(0,g)}i=Ft(i,0,s-1)}if(p){let g=Math.max(Ue(l,r.axis,h,!0).hi+1,n?0:Ue(t,u,r.getPixelForValue(h),!0).hi+1);if(c){const m=l.slice(g-1).findIndex(_=>!it(_[a.axis]));g+=Math.max(0,m)}o=Ft(g,i,s)-i}else o=s-i}return{start:i,count:o}}function id(e){const{xScale:t,yScale:n,_scaleRanges:s}=e,i={xmin:t.min,xmax:t.max,ymin:n.min,ymax:n.max};if(!s)return e._scaleRanges=i,!0;const o=s.xmin!==t.min||s.xmax!==t.max||s.ymin!==n.min||s.ymax!==n.max;return Object.assign(s,i),o}const Mi=e=>e===0||e===1,Cc=(e,t,n)=>-(Math.pow(2,10*(e-=1))*Math.sin((e-t)*xt/n)),Pc=(e,t,n)=>Math.pow(2,-10*e)*Math.sin((e-t)*xt/n)+1,Hs={linear:e=>e,easeInQuad:e=>e*e,easeOutQuad:e=>-e*(e-2),easeInOutQuad:e=>(e/=.5)<1?.5*e*e:-.5*(--e*(e-2)-1),easeInCubic:e=>e*e*e,easeOutCubic:e=>(e-=1)*e*e+1,easeInOutCubic:e=>(e/=.5)<1?.5*e*e*e:.5*((e-=2)*e*e+2),easeInQuart:e=>e*e*e*e,easeOutQuart:e=>-((e-=1)*e*e*e-1),easeInOutQuart:e=>(e/=.5)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2),easeInQuint:e=>e*e*e*e*e,easeOutQuint:e=>(e-=1)*e*e*e*e+1,easeInOutQuint:e=>(e/=.5)<1?.5*e*e*e*e*e:.5*((e-=2)*e*e*e*e+2),easeInSine:e=>-Math.cos(e*Et)+1,easeOutSine:e=>Math.sin(e*Et),easeInOutSine:e=>-.5*(Math.cos(vt*e)-1),easeInExpo:e=>e===0?0:Math.pow(2,10*(e-1)),easeOutExpo:e=>e===1?1:-Math.pow(2,-10*e)+1,easeInOutExpo:e=>Mi(e)?e:e<.5?.5*Math.pow(2,10*(e*2-1)):.5*(-Math.pow(2,-10*(e*2-1))+2),easeInCirc:e=>e>=1?e:-(Math.sqrt(1-e*e)-1),easeOutCirc:e=>Math.sqrt(1-(e-=1)*e),easeInOutCirc:e=>(e/=.5)<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1),easeInElastic:e=>Mi(e)?e:Cc(e,.075,.3),easeOutElastic:e=>Mi(e)?e:Pc(e,.075,.3),easeInOutElastic(e){return Mi(e)?e:e<.5?.5*Cc(e*2,.1125,.45):.5+.5*Pc(e*2-1,.1125,.45)},easeInBack(e){return e*e*((1.70158+1)*e-1.70158)},easeOutBack(e){return(e-=1)*e*((1.70158+1)*e********)+1},easeInOutBack(e){let t=1.70158;return(e/=.5)<1?.5*(e*e*(((t*=1.525)+1)*e-t)):.5*((e-=2)*e*(((t*=1.525)+1)*e+t)+2)},easeInBounce:e=>1-Hs.easeOutBounce(1-e),easeOutBounce(e){return e<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375},easeInOutBounce:e=>e<.5?Hs.easeInBounce(e*2)*.5:Hs.easeOutBounce(e*2-1)*.5+.5};function ja(e){if(e&&typeof e=="object"){const t=e.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function Ec(e){return ja(e)?e:new ti(e)}function fr(e){return ja(e)?e:new ti(e).saturate(.5).darken(.1).hexString()}const Xy=["x","y","borderWidth","radius","tension"],Gy=["color","borderColor","backgroundColor"];function Jy(e){e.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),e.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),e.set("animations",{colors:{type:"color",properties:Gy},numbers:{type:"number",properties:Xy}}),e.describe("animations",{_fallback:"animation"}),e.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function Qy(e){e.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const kc=new Map;function Zy(e,t){t=t||{};const n=e+JSON.stringify(t);let s=kc.get(n);return s||(s=new Intl.NumberFormat(e,t),kc.set(n,s)),s}function mi(e,t,n){return Zy(t,n).format(e)}const od={values(e){return wt(e)?e:""+e},numeric(e,t,n){if(e===0)return"0";const s=this.chart.options.locale;let i,o=e;if(n.length>1){const c=Math.max(Math.abs(n[0].value),Math.abs(n[n.length-1].value));(c<1e-4||c>1e15)&&(i="scientific"),o=tx(e,n)}const r=ln(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:i,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),mi(e,s,l)},logarithmic(e,t,n){if(e===0)return"0";const s=n[t].significand||e/Math.pow(10,Math.floor(ln(e)));return[1,2,3,5,10,15].includes(s)||t>.8*n.length?od.numeric.call(this,e,t,n):""}};function tx(e,t){let n=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(n)>=1&&e!==Math.floor(e)&&(n=e-Math.floor(e)),n}var jo={formatters:od};function ex(e){e.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,n)=>n.lineWidth,tickColor:(t,n)=>n.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:jo.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),e.route("scale.ticks","color","","color"),e.route("scale.grid","color","","borderColor"),e.route("scale.border","color","","borderColor"),e.route("scale.title","color","","color"),e.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),e.describe("scales",{_fallback:"scale"}),e.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const zn=Object.create(null),Xr=Object.create(null);function js(e,t){if(!t)return e;const n=t.split(".");for(let s=0,i=n.length;s<i;++s){const o=n[s];e=e[o]||(e[o]=Object.create(null))}return e}function hr(e,t,n){return typeof t=="string"?ei(js(e,t),n):ei(js(e,""),t)}class nx{constructor(t,n){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,i)=>fr(i.backgroundColor),this.hoverBorderColor=(s,i)=>fr(i.borderColor),this.hoverColor=(s,i)=>fr(i.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(n)}set(t,n){return hr(this,t,n)}get(t){return js(this,t)}describe(t,n){return hr(Xr,t,n)}override(t,n){return hr(zn,t,n)}route(t,n,s,i){const o=js(this,t),r=js(this,s),a="_"+n;Object.defineProperties(o,{[a]:{value:o[n],writable:!0},[n]:{enumerable:!0,get(){const l=this[a],c=r[i];return ot(l)?Object.assign({},c,l):st(l,c)},set(l){this[a]=l}}})}apply(t){t.forEach(n=>n(this))}}var St=new nx({_scriptable:e=>!e.startsWith("on"),_indexable:e=>e!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[Jy,Qy,ex]);function sx(e){return!e||it(e.size)||it(e.family)?null:(e.style?e.style+" ":"")+(e.weight?e.weight+" ":"")+e.size+"px "+e.family}function go(e,t,n,s,i){let o=t[i];return o||(o=t[i]=e.measureText(i).width,n.push(i)),o>s&&(s=o),s}function ix(e,t,n,s){s=s||{};let i=s.data=s.data||{},o=s.garbageCollect=s.garbageCollect||[];s.font!==t&&(i=s.data={},o=s.garbageCollect=[],s.font=t),e.save(),e.font=t;let r=0;const a=n.length;let l,c,u,f,h;for(l=0;l<a;l++)if(f=n[l],f!=null&&!wt(f))r=go(e,i,o,r,f);else if(wt(f))for(c=0,u=f.length;c<u;c++)h=f[c],h!=null&&!wt(h)&&(r=go(e,i,o,r,h));e.restore();const d=o.length/2;if(d>n.length){for(l=0;l<d;l++)delete i[o[l]];o.splice(0,d)}return r}function Sn(e,t,n){const s=e.currentDevicePixelRatio,i=n!==0?Math.max(n/2,.5):0;return Math.round((t-i)*s)/s+i}function Ac(e,t){!t&&!e||(t=t||e.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,e.width,e.height),t.restore())}function Gr(e,t,n,s){rd(e,t,n,s,null)}function rd(e,t,n,s,i){let o,r,a,l,c,u,f,h;const d=t.pointStyle,p=t.rotation,g=t.radius;let m=(p||0)*By;if(d&&typeof d=="object"&&(o=d.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){e.save(),e.translate(n,s),e.rotate(m),e.drawImage(d,-d.width/2,-d.height/2,d.width,d.height),e.restore();return}if(!(isNaN(g)||g<=0)){switch(e.beginPath(),d){default:i?e.ellipse(n,s,i/2,g,0,0,xt):e.arc(n,s,g,0,xt),e.closePath();break;case"triangle":u=i?i/2:g,e.moveTo(n+Math.sin(m)*u,s-Math.cos(m)*g),m+=vc,e.lineTo(n+Math.sin(m)*u,s-Math.cos(m)*g),m+=vc,e.lineTo(n+Math.sin(m)*u,s-Math.cos(m)*g),e.closePath();break;case"rectRounded":c=g*.516,l=g-c,r=Math.cos(m+wn)*l,f=Math.cos(m+wn)*(i?i/2-c:l),a=Math.sin(m+wn)*l,h=Math.sin(m+wn)*(i?i/2-c:l),e.arc(n-f,s-a,c,m-vt,m-Et),e.arc(n+h,s-r,c,m-Et,m),e.arc(n+f,s+a,c,m,m+Et),e.arc(n-h,s+r,c,m+Et,m+vt),e.closePath();break;case"rect":if(!p){l=Math.SQRT1_2*g,u=i?i/2:l,e.rect(n-u,s-l,2*u,2*l);break}m+=wn;case"rectRot":f=Math.cos(m)*(i?i/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,h=Math.sin(m)*(i?i/2:g),e.moveTo(n-f,s-a),e.lineTo(n+h,s-r),e.lineTo(n+f,s+a),e.lineTo(n-h,s+r),e.closePath();break;case"crossRot":m+=wn;case"cross":f=Math.cos(m)*(i?i/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,h=Math.sin(m)*(i?i/2:g),e.moveTo(n-f,s-a),e.lineTo(n+f,s+a),e.moveTo(n+h,s-r),e.lineTo(n-h,s+r);break;case"star":f=Math.cos(m)*(i?i/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,h=Math.sin(m)*(i?i/2:g),e.moveTo(n-f,s-a),e.lineTo(n+f,s+a),e.moveTo(n+h,s-r),e.lineTo(n-h,s+r),m+=wn,f=Math.cos(m)*(i?i/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,h=Math.sin(m)*(i?i/2:g),e.moveTo(n-f,s-a),e.lineTo(n+f,s+a),e.moveTo(n+h,s-r),e.lineTo(n-h,s+r);break;case"line":r=i?i/2:Math.cos(m)*g,a=Math.sin(m)*g,e.moveTo(n-r,s-a),e.lineTo(n+r,s+a);break;case"dash":e.moveTo(n,s),e.lineTo(n+Math.cos(m)*(i?i/2:g),s+Math.sin(m)*g);break;case!1:e.closePath();break}e.fill(),t.borderWidth>0&&e.stroke()}}function Ke(e,t,n){return n=n||.5,!t||e&&e.x>t.left-n&&e.x<t.right+n&&e.y>t.top-n&&e.y<t.bottom+n}function Vo(e,t){e.save(),e.beginPath(),e.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),e.clip()}function Wo(e){e.restore()}function ox(e,t,n,s,i){if(!t)return e.lineTo(n.x,n.y);if(i==="middle"){const o=(t.x+n.x)/2;e.lineTo(o,t.y),e.lineTo(o,n.y)}else i==="after"!=!!s?e.lineTo(t.x,n.y):e.lineTo(n.x,t.y);e.lineTo(n.x,n.y)}function rx(e,t,n,s){if(!t)return e.lineTo(n.x,n.y);e.bezierCurveTo(s?t.cp1x:t.cp2x,s?t.cp1y:t.cp2y,s?n.cp2x:n.cp1x,s?n.cp2y:n.cp1y,n.x,n.y)}function ax(e,t){t.translation&&e.translate(t.translation[0],t.translation[1]),it(t.rotation)||e.rotate(t.rotation),t.color&&(e.fillStyle=t.color),t.textAlign&&(e.textAlign=t.textAlign),t.textBaseline&&(e.textBaseline=t.textBaseline)}function lx(e,t,n,s,i){if(i.strikethrough||i.underline){const o=e.measureText(s),r=t-o.actualBoundingBoxLeft,a=t+o.actualBoundingBoxRight,l=n-o.actualBoundingBoxAscent,c=n+o.actualBoundingBoxDescent,u=i.strikethrough?(l+c)/2:c;e.strokeStyle=e.fillStyle,e.beginPath(),e.lineWidth=i.decorationWidth||2,e.moveTo(r,u),e.lineTo(a,u),e.stroke()}}function cx(e,t){const n=e.fillStyle;e.fillStyle=t.color,e.fillRect(t.left,t.top,t.width,t.height),e.fillStyle=n}function Hn(e,t,n,s,i,o={}){const r=wt(t)?t:[t],a=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(e.save(),e.font=i.string,ax(e,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&cx(e,o.backdrop),a&&(o.strokeColor&&(e.strokeStyle=o.strokeColor),it(o.strokeWidth)||(e.lineWidth=o.strokeWidth),e.strokeText(c,n,s,o.maxWidth)),e.fillText(c,n,s,o.maxWidth),lx(e,n,s,c,o),s+=Number(i.lineHeight);e.restore()}function ii(e,t){const{x:n,y:s,w:i,h:o,radius:r}=t;e.arc(n+r.topLeft,s+r.topLeft,r.topLeft,1.5*vt,vt,!0),e.lineTo(n,s+o-r.bottomLeft),e.arc(n+r.bottomLeft,s+o-r.bottomLeft,r.bottomLeft,vt,Et,!0),e.lineTo(n+i-r.bottomRight,s+o),e.arc(n+i-r.bottomRight,s+o-r.bottomRight,r.bottomRight,Et,0,!0),e.lineTo(n+i,s+r.topRight),e.arc(n+i-r.topRight,s+r.topRight,r.topRight,0,-Et,!0),e.lineTo(n+r.topLeft,s)}const ux=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,fx=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function hx(e,t){const n=(""+e).match(ux);if(!n||n[1]==="normal")return t*1.2;switch(e=+n[2],n[3]){case"px":return e;case"%":e/=100;break}return t*e}const dx=e=>+e||0;function Va(e,t){const n={},s=ot(t),i=s?Object.keys(t):t,o=ot(e)?s?r=>st(e[r],e[t[r]]):r=>e[r]:()=>e;for(const r of i)n[r]=dx(o(r));return n}function ad(e){return Va(e,{top:"y",right:"x",bottom:"y",left:"x"})}function Fn(e){return Va(e,["topLeft","topRight","bottomLeft","bottomRight"])}function Wt(e){const t=ad(e);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function Tt(e,t){e=e||{},t=t||St.font;let n=st(e.size,t.size);typeof n=="string"&&(n=parseInt(n,10));let s=st(e.style,t.style);s&&!(""+s).match(fx)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const i={family:st(e.family,t.family),lineHeight:hx(st(e.lineHeight,t.lineHeight),n),size:n,style:s,weight:st(e.weight,t.weight),string:""};return i.string=sx(i),i}function Ms(e,t,n,s){let i,o,r;for(i=0,o=e.length;i<o;++i)if(r=e[i],r!==void 0&&r!==void 0)return r}function px(e,t,n){const{min:s,max:i}=e,o=Xh(t,(i-s)/2),r=(a,l)=>n&&a===0?0:a+l;return{min:r(s,-Math.abs(o)),max:r(i,o)}}function _n(e,t){return Object.assign(Object.create(e),t)}function Wa(e,t=[""],n,s,i=()=>e[0]){const o=n||e;typeof s>"u"&&(s=fd("_fallback",e));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:e,_rootScopes:o,_fallback:s,_getTarget:i,override:a=>Wa([a,...e],t,o,s)};return new Proxy(r,{deleteProperty(a,l){return delete a[l],delete a._keys,delete e[0][l],!0},get(a,l){return cd(a,l,()=>wx(l,t,e,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(e[0])},has(a,l){return Rc(a).includes(l)},ownKeys(a){return Rc(a)},set(a,l,c){const u=a._storage||(a._storage=i());return a[l]=u[l]=c,delete a._keys,!0}})}function as(e,t,n,s){const i={_cacheable:!1,_proxy:e,_context:t,_subProxy:n,_stack:new Set,_descriptors:ld(e,s),setContext:o=>as(e,o,n,s),override:o=>as(e.override(o),t,n,s)};return new Proxy(i,{deleteProperty(o,r){return delete o[r],delete e[r],!0},get(o,r,a){return cd(o,r,()=>mx(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(e,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(e,r)},getPrototypeOf(){return Reflect.getPrototypeOf(e)},has(o,r){return Reflect.has(e,r)},ownKeys(){return Reflect.ownKeys(e)},set(o,r,a){return e[r]=a,delete o[r],!0}})}function ld(e,t={scriptable:!0,indexable:!0}){const{_scriptable:n=t.scriptable,_indexable:s=t.indexable,_allKeys:i=t.allKeys}=e;return{allKeys:i,scriptable:n,indexable:s,isScriptable:mn(n)?n:()=>n,isIndexable:mn(s)?s:()=>s}}const gx=(e,t)=>e?e+Na(t):t,$a=(e,t)=>ot(t)&&e!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function cd(e,t,n){if(Object.prototype.hasOwnProperty.call(e,t)||t==="constructor")return e[t];const s=n();return e[t]=s,s}function mx(e,t,n){const{_proxy:s,_context:i,_subProxy:o,_descriptors:r}=e;let a=s[t];return mn(a)&&r.isScriptable(t)&&(a=bx(t,a,e,n)),wt(a)&&a.length&&(a=_x(t,a,e,r.isIndexable)),$a(t,a)&&(a=as(a,i,o&&o[t],r)),a}function bx(e,t,n,s){const{_proxy:i,_context:o,_subProxy:r,_stack:a}=n;if(a.has(e))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+e);a.add(e);let l=t(o,r||s);return a.delete(e),$a(e,l)&&(l=Ua(i._scopes,i,e,l)),l}function _x(e,t,n,s){const{_proxy:i,_context:o,_subProxy:r,_descriptors:a}=n;if(typeof o.index<"u"&&s(e))return t[o.index%t.length];if(ot(t[0])){const l=t,c=i._scopes.filter(u=>u!==l);t=[];for(const u of l){const f=Ua(c,i,e,u);t.push(as(f,o,r&&r[e],a))}}return t}function ud(e,t,n){return mn(e)?e(t,n):e}const yx=(e,t)=>e===!0?t:typeof e=="string"?gn(t,e):void 0;function xx(e,t,n,s,i){for(const o of t){const r=yx(n,o);if(r){e.add(r);const a=ud(r._fallback,n,i);if(typeof a<"u"&&a!==n&&a!==s)return a}else if(r===!1&&typeof s<"u"&&n!==s)return null}return!1}function Ua(e,t,n,s){const i=t._rootScopes,o=ud(t._fallback,n,s),r=[...e,...i],a=new Set;a.add(s);let l=Oc(a,r,n,o||n,s);return l===null||typeof o<"u"&&o!==n&&(l=Oc(a,r,o,l,s),l===null)?!1:Wa(Array.from(a),[""],i,o,()=>vx(t,n,s))}function Oc(e,t,n,s,i){for(;n;)n=xx(e,t,n,s,i);return n}function vx(e,t,n){const s=e._getTarget();t in s||(s[t]={});const i=s[t];return wt(i)&&ot(n)?n:i||{}}function wx(e,t,n,s){let i;for(const o of t)if(i=fd(gx(o,e),n),typeof i<"u")return $a(e,i)?Ua(n,s,e,i):i}function fd(e,t){for(const n of t){if(!n)continue;const s=n[e];if(typeof s<"u")return s}}function Rc(e){let t=e._keys;return t||(t=e._keys=Sx(e._scopes)),t}function Sx(e){const t=new Set;for(const n of e)for(const s of Object.keys(n).filter(i=>!i.startsWith("_")))t.add(s);return Array.from(t)}function hd(e,t,n,s){const{iScale:i}=e,{key:o="r"}=this._parsing,r=new Array(s);let a,l,c,u;for(a=0,l=s;a<l;++a)c=a+n,u=t[c],r[a]={r:i.parse(gn(u,o),c)};return r}const Mx=Number.EPSILON||1e-14,ls=(e,t)=>t<e.length&&!e[t].skip&&e[t],dd=e=>e==="x"?"y":"x";function Cx(e,t,n,s){const i=e.skip?t:e,o=t,r=n.skip?t:n,a=Yr(o,i),l=Yr(r,o);let c=a/(a+l),u=l/(a+l);c=isNaN(c)?0:c,u=isNaN(u)?0:u;const f=s*c,h=s*u;return{previous:{x:o.x-f*(r.x-i.x),y:o.y-f*(r.y-i.y)},next:{x:o.x+h*(r.x-i.x),y:o.y+h*(r.y-i.y)}}}function Px(e,t,n){const s=e.length;let i,o,r,a,l,c=ls(e,0);for(let u=0;u<s-1;++u)if(l=c,c=ls(e,u+1),!(!l||!c)){if(zs(t[u],0,Mx)){n[u]=n[u+1]=0;continue}i=n[u]/t[u],o=n[u+1]/t[u],a=Math.pow(i,2)+Math.pow(o,2),!(a<=9)&&(r=3/Math.sqrt(a),n[u]=i*r*t[u],n[u+1]=o*r*t[u])}}function Ex(e,t,n="x"){const s=dd(n),i=e.length;let o,r,a,l=ls(e,0);for(let c=0;c<i;++c){if(r=a,a=l,l=ls(e,c+1),!a)continue;const u=a[n],f=a[s];r&&(o=(u-r[n])/3,a[`cp1${n}`]=u-o,a[`cp1${s}`]=f-o*t[c]),l&&(o=(l[n]-u)/3,a[`cp2${n}`]=u+o,a[`cp2${s}`]=f+o*t[c])}}function kx(e,t="x"){const n=dd(t),s=e.length,i=Array(s).fill(0),o=Array(s);let r,a,l,c=ls(e,0);for(r=0;r<s;++r)if(a=l,l=c,c=ls(e,r+1),!!l){if(c){const u=c[t]-l[t];i[r]=u!==0?(c[n]-l[n])/u:0}o[r]=a?c?De(i[r-1])!==De(i[r])?0:(i[r-1]+i[r])/2:i[r-1]:i[r]}Px(e,i,o),Ex(e,o,t)}function Ci(e,t,n){return Math.max(Math.min(e,n),t)}function Ax(e,t){let n,s,i,o,r,a=Ke(e[0],t);for(n=0,s=e.length;n<s;++n)r=o,o=a,a=n<s-1&&Ke(e[n+1],t),o&&(i=e[n],r&&(i.cp1x=Ci(i.cp1x,t.left,t.right),i.cp1y=Ci(i.cp1y,t.top,t.bottom)),a&&(i.cp2x=Ci(i.cp2x,t.left,t.right),i.cp2y=Ci(i.cp2y,t.top,t.bottom)))}function Ox(e,t,n,s,i){let o,r,a,l;if(t.spanGaps&&(e=e.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")kx(e,i);else{let c=s?e[e.length-1]:e[0];for(o=0,r=e.length;o<r;++o)a=e[o],l=Cx(c,a,e[Math.min(o+1,r-(s?0:1))%r],t.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,c=a}t.capBezierPoints&&Ax(e,n)}function Ka(){return typeof window<"u"&&typeof document<"u"}function qa(e){let t=e.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function mo(e,t,n){let s;return typeof e=="string"?(s=parseInt(e,10),e.indexOf("%")!==-1&&(s=s/100*t.parentNode[n])):s=e,s}const $o=e=>e.ownerDocument.defaultView.getComputedStyle(e,null);function Rx(e,t){return $o(e).getPropertyValue(t)}const Tx=["top","right","bottom","left"];function In(e,t,n){const s={};n=n?"-"+n:"";for(let i=0;i<4;i++){const o=Tx[i];s[o]=parseFloat(e[t+"-"+o+n])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const Dx=(e,t,n)=>(e>0||t>0)&&(!n||!n.shadowRoot);function Lx(e,t){const n=e.touches,s=n&&n.length?n[0]:e,{offsetX:i,offsetY:o}=s;let r=!1,a,l;if(Dx(i,o,e.target))a=i,l=o;else{const c=t.getBoundingClientRect();a=s.clientX-c.left,l=s.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function En(e,t){if("native"in e)return e;const{canvas:n,currentDevicePixelRatio:s}=t,i=$o(n),o=i.boxSizing==="border-box",r=In(i,"padding"),a=In(i,"border","width"),{x:l,y:c,box:u}=Lx(e,n),f=r.left+(u&&a.left),h=r.top+(u&&a.top);let{width:d,height:p}=t;return o&&(d-=r.width+a.width,p-=r.height+a.height),{x:Math.round((l-f)/d*n.width/s),y:Math.round((c-h)/p*n.height/s)}}function Fx(e,t,n){let s,i;if(t===void 0||n===void 0){const o=e&&qa(e);if(!o)t=e.clientWidth,n=e.clientHeight;else{const r=o.getBoundingClientRect(),a=$o(o),l=In(a,"border","width"),c=In(a,"padding");t=r.width-c.width-l.width,n=r.height-c.height-l.height,s=mo(a.maxWidth,o,"clientWidth"),i=mo(a.maxHeight,o,"clientHeight")}}return{width:t,height:n,maxWidth:s||po,maxHeight:i||po}}const Pi=e=>Math.round(e*10)/10;function Ix(e,t,n,s){const i=$o(e),o=In(i,"margin"),r=mo(i.maxWidth,e,"clientWidth")||po,a=mo(i.maxHeight,e,"clientHeight")||po,l=Fx(e,t,n);let{width:c,height:u}=l;if(i.boxSizing==="content-box"){const h=In(i,"border","width"),d=In(i,"padding");c-=d.width+h.width,u-=d.height+h.height}return c=Math.max(0,c-o.width),u=Math.max(0,s?c/s:u-o.height),c=Pi(Math.min(c,r,l.maxWidth)),u=Pi(Math.min(u,a,l.maxHeight)),c&&!u&&(u=Pi(c/2)),(t!==void 0||n!==void 0)&&s&&l.height&&u>l.height&&(u=l.height,c=Pi(Math.floor(u*s))),{width:c,height:u}}function Tc(e,t,n){const s=t||1,i=Math.floor(e.height*s),o=Math.floor(e.width*s);e.height=Math.floor(e.height),e.width=Math.floor(e.width);const r=e.canvas;return r.style&&(n||!r.style.height&&!r.style.width)&&(r.style.height=`${e.height}px`,r.style.width=`${e.width}px`),e.currentDevicePixelRatio!==s||r.height!==i||r.width!==o?(e.currentDevicePixelRatio=s,r.height=i,r.width=o,e.ctx.setTransform(s,0,0,s,0,0),!0):!1}const Nx=function(){let e=!1;try{const t={get passive(){return e=!0,!1}};Ka()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return e}();function Dc(e,t){const n=Rx(e,t),s=n&&n.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function kn(e,t,n,s){return{x:e.x+n*(t.x-e.x),y:e.y+n*(t.y-e.y)}}function Bx(e,t,n,s){return{x:e.x+n*(t.x-e.x),y:s==="middle"?n<.5?e.y:t.y:s==="after"?n<1?e.y:t.y:n>0?t.y:e.y}}function zx(e,t,n,s){const i={x:e.cp2x,y:e.cp2y},o={x:t.cp1x,y:t.cp1y},r=kn(e,i,n),a=kn(i,o,n),l=kn(o,t,n),c=kn(r,a,n),u=kn(a,l,n);return kn(c,u,n)}const Hx=function(e,t){return{x(n){return e+e+t-n},setWidth(n){t=n},textAlign(n){return n==="center"?n:n==="right"?"left":"right"},xPlus(n,s){return n-s},leftForLtr(n,s){return n-s}}},jx=function(){return{x(e){return e},setWidth(e){},textAlign(e){return e},xPlus(e,t){return e+t},leftForLtr(e,t){return e}}};function es(e,t,n){return e?Hx(t,n):jx()}function pd(e,t){let n,s;(t==="ltr"||t==="rtl")&&(n=e.canvas.style,s=[n.getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",t,"important"),e.prevTextDirection=s)}function gd(e,t){t!==void 0&&(delete e.prevTextDirection,e.canvas.style.setProperty("direction",t[0],t[1]))}function md(e){return e==="angle"?{between:si,compare:Vy,normalize:oe}:{between:$e,compare:(t,n)=>t-n,normalize:t=>t}}function Lc({start:e,end:t,count:n,loop:s,style:i}){return{start:e%n,end:t%n,loop:s&&(t-e+1)%n===0,style:i}}function Vx(e,t,n){const{property:s,start:i,end:o}=n,{between:r,normalize:a}=md(s),l=t.length;let{start:c,end:u,loop:f}=e,h,d;if(f){for(c+=l,u+=l,h=0,d=l;h<d&&r(a(t[c%l][s]),i,o);++h)c--,u--;c%=l,u%=l}return u<c&&(u+=l),{start:c,end:u,loop:f,style:e.style}}function bd(e,t,n){if(!n)return[e];const{property:s,start:i,end:o}=n,r=t.length,{compare:a,between:l,normalize:c}=md(s),{start:u,end:f,loop:h,style:d}=Vx(e,t,n),p=[];let g=!1,m=null,_,x,v;const w=()=>l(i,v,_)&&a(i,v)!==0,S=()=>a(o,_)===0||l(o,v,_),A=()=>g||w(),k=()=>!g||S();for(let E=u,P=u;E<=f;++E)x=t[E%r],!x.skip&&(_=c(x[s]),_!==v&&(g=l(_,i,o),m===null&&A()&&(m=a(_,i)===0?E:P),m!==null&&k()&&(p.push(Lc({start:m,end:E,loop:h,count:r,style:d})),m=null),P=E,v=_));return m!==null&&p.push(Lc({start:m,end:f,loop:h,count:r,style:d})),p}function _d(e,t){const n=[],s=e.segments;for(let i=0;i<s.length;i++){const o=bd(s[i],e.points,t);o.length&&n.push(...o)}return n}function Wx(e,t,n,s){let i=0,o=t-1;if(n&&!s)for(;i<t&&!e[i].skip;)i++;for(;i<t&&e[i].skip;)i++;for(i%=t,n&&(o+=i);o>i&&e[o%t].skip;)o--;return o%=t,{start:i,end:o}}function $x(e,t,n,s){const i=e.length,o=[];let r=t,a=e[t],l;for(l=t+1;l<=n;++l){const c=e[l%i];c.skip||c.stop?a.skip||(s=!1,o.push({start:t%i,end:(l-1)%i,loop:s}),t=r=c.stop?l:null):(r=l,a.skip&&(t=l)),a=c}return r!==null&&o.push({start:t%i,end:r%i,loop:s}),o}function Ux(e,t){const n=e.points,s=e.options.spanGaps,i=n.length;if(!i)return[];const o=!!e._loop,{start:r,end:a}=Wx(n,i,o,s);if(s===!0)return Fc(e,[{start:r,end:a,loop:o}],n,t);const l=a<r?a+i:a,c=!!e._fullLoop&&r===0&&a===i-1;return Fc(e,$x(n,r,l,c),n,t)}function Fc(e,t,n,s){return!s||!s.setContext||!n?t:Kx(e,t,n,s)}function Kx(e,t,n,s){const i=e._chart.getContext(),o=Ic(e.options),{_datasetIndex:r,options:{spanGaps:a}}=e,l=n.length,c=[];let u=o,f=t[0].start,h=f;function d(p,g,m,_){const x=a?-1:1;if(p!==g){for(p+=l;n[p%l].skip;)p-=x;for(;n[g%l].skip;)g+=x;p%l!==g%l&&(c.push({start:p%l,end:g%l,loop:m,style:_}),u=_,f=g%l)}}for(const p of t){f=a?f:p.start;let g=n[f%l],m;for(h=f+1;h<=p.end;h++){const _=n[h%l];m=Ic(s.setContext(_n(i,{type:"segment",p0:g,p1:_,p0DataIndex:(h-1)%l,p1DataIndex:h%l,datasetIndex:r}))),qx(m,u)&&d(f,h-1,p.loop,u),g=_,u=m}f<h-1&&d(f,h-1,p.loop,u)}return c}function Ic(e){return{backgroundColor:e.backgroundColor,borderCapStyle:e.borderCapStyle,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderJoinStyle:e.borderJoinStyle,borderWidth:e.borderWidth,borderColor:e.borderColor}}function qx(e,t){if(!t)return!1;const n=[],s=function(i,o){return ja(o)?(n.includes(o)||n.push(o),n.indexOf(o)):o};return JSON.stringify(e,s)!==JSON.stringify(t,s)}function Ei(e,t,n){return e.options.clip?e[n]:t[n]}function Yx(e,t){const{xScale:n,yScale:s}=e;return n&&s?{left:Ei(n,t,"left"),right:Ei(n,t,"right"),top:Ei(s,t,"top"),bottom:Ei(s,t,"bottom")}:t}function yd(e,t){const n=t._clip;if(n.disabled)return!1;const s=Yx(t,e.chartArea);return{left:n.left===!1?0:s.left-(n.left===!0?0:n.left),right:n.right===!1?e.width:s.right+(n.right===!0?0:n.right),top:n.top===!1?0:s.top-(n.top===!0?0:n.top),bottom:n.bottom===!1?e.height:s.bottom+(n.bottom===!0?0:n.bottom)}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class Xx{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,n,s,i){const o=n.listeners[i],r=n.duration;o.forEach(a=>a({chart:t,initial:n.initial,numSteps:r,currentStep:Math.min(s-n.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=ed.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let n=0;this._charts.forEach((s,i)=>{if(!s.running||!s.items.length)return;const o=s.items;let r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(t),a=!0):(o[r]=o[o.length-1],o.pop());a&&(i.draw(),this._notify(i,s,t,"progress")),o.length||(s.running=!1,this._notify(i,s,t,"complete"),s.initial=!1),n+=o.length}),this._lastDate=t,n===0&&(this._running=!1)}_getAnims(t){const n=this._charts;let s=n.get(t);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},n.set(t,s)),s}listen(t,n,s){this._getAnims(t).listeners[n].push(s)}add(t,n){!n||!n.length||this._getAnims(t).items.push(...n)}has(t){return this._getAnims(t).items.length>0}start(t){const n=this._charts.get(t);n&&(n.running=!0,n.start=Date.now(),n.duration=n.items.reduce((s,i)=>Math.max(s,i._duration),0),this._refresh())}running(t){if(!this._running)return!1;const n=this._charts.get(t);return!(!n||!n.running||!n.items.length)}stop(t){const n=this._charts.get(t);if(!n||!n.items.length)return;const s=n.items;let i=s.length-1;for(;i>=0;--i)s[i].cancel();n.items=[],this._notify(t,n,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var Be=new Xx;const Nc="transparent",Gx={boolean(e,t,n){return n>.5?t:e},color(e,t,n){const s=Ec(e||Nc),i=s.valid&&Ec(t||Nc);return i&&i.valid?i.mix(s,n).hexString():t},number(e,t,n){return e+(t-e)*n}};class Jx{constructor(t,n,s,i){const o=n[s];i=Ms([t.to,i,o,t.from]);const r=Ms([t.from,o,i]);this._active=!0,this._fn=t.fn||Gx[t.type||typeof r],this._easing=Hs[t.easing]||Hs.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=n,this._prop=s,this._from=r,this._to=i,this._promises=void 0}active(){return this._active}update(t,n,s){if(this._active){this._notify(!1);const i=this._target[this._prop],o=s-this._start,r=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=Ms([t.to,n,i,t.from]),this._from=Ms([t.from,i,n])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const n=t-this._start,s=this._duration,i=this._prop,o=this._from,r=this._loop,a=this._to;let l;if(this._active=o!==a&&(r||n<s),!this._active){this._target[i]=a,this._notify(!0);return}if(n<0){this._target[i]=o;return}l=n/s%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[i]=this._fn(o,a,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((n,s)=>{t.push({res:n,rej:s})})}_notify(t){const n=t?"res":"rej",s=this._promises||[];for(let i=0;i<s.length;i++)s[i][n]()}}class xd{constructor(t,n){this._chart=t,this._properties=new Map,this.configure(n)}configure(t){if(!ot(t))return;const n=Object.keys(St.animation),s=this._properties;Object.getOwnPropertyNames(t).forEach(i=>{const o=t[i];if(!ot(o))return;const r={};for(const a of n)r[a]=o[a];(wt(o.properties)&&o.properties||[i]).forEach(a=>{(a===i||!s.has(a))&&s.set(a,r)})})}_animateOptions(t,n){const s=n.options,i=Zx(t,s);if(!i)return[];const o=this._createAnimations(i,s);return s.$shared&&Qx(t.options.$animations,s).then(()=>{t.options=s},()=>{}),o}_createAnimations(t,n){const s=this._properties,i=[],o=t.$animations||(t.$animations={}),r=Object.keys(n),a=Date.now();let l;for(l=r.length-1;l>=0;--l){const c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){i.push(...this._animateOptions(t,n));continue}const u=n[c];let f=o[c];const h=s.get(c);if(f)if(h&&f.active()){f.update(h,u,a);continue}else f.cancel();if(!h||!h.duration){t[c]=u;continue}o[c]=f=new Jx(h,t,c,u),i.push(f)}return i}update(t,n){if(this._properties.size===0){Object.assign(t,n);return}const s=this._createAnimations(t,n);if(s.length)return Be.add(this._chart,s),!0}}function Qx(e,t){const n=[],s=Object.keys(t);for(let i=0;i<s.length;i++){const o=e[s[i]];o&&o.active()&&n.push(o.wait())}return Promise.all(n)}function Zx(e,t){if(!t)return;let n=e.options;if(!n){e.options=t;return}return n.$shared&&(e.options=n=Object.assign({},n,{$shared:!1,$animations:{}})),n}function Bc(e,t){const n=e&&e.options||{},s=n.reverse,i=n.min===void 0?t:0,o=n.max===void 0?t:0;return{start:s?o:i,end:s?i:o}}function t0(e,t,n){if(n===!1)return!1;const s=Bc(e,n),i=Bc(t,n);return{top:i.end,right:s.end,bottom:i.start,left:s.start}}function e0(e){let t,n,s,i;return ot(e)?(t=e.top,n=e.right,s=e.bottom,i=e.left):t=n=s=i=e,{top:t,right:n,bottom:s,left:i,disabled:e===!1}}function vd(e,t){const n=[],s=e._getSortedDatasetMetas(t);let i,o;for(i=0,o=s.length;i<o;++i)n.push(s[i].index);return n}function zc(e,t,n,s={}){const i=e.keys,o=s.mode==="single";let r,a,l,c;if(t===null)return;let u=!1;for(r=0,a=i.length;r<a;++r){if(l=+i[r],l===n){if(u=!0,s.all)continue;break}c=e.values[l],Mt(c)&&(o||t===0||De(t)===De(c))&&(t+=c)}return!u&&!s.all?0:t}function n0(e,t){const{iScale:n,vScale:s}=t,i=n.axis==="x"?"x":"y",o=s.axis==="x"?"x":"y",r=Object.keys(e),a=new Array(r.length);let l,c,u;for(l=0,c=r.length;l<c;++l)u=r[l],a[l]={[i]:u,[o]:e[u]};return a}function dr(e,t){const n=e&&e.options.stacked;return n||n===void 0&&t.stack!==void 0}function s0(e,t,n){return`${e.id}.${t.id}.${n.stack||n.type}`}function i0(e){const{min:t,max:n,minDefined:s,maxDefined:i}=e.getUserBounds();return{min:s?t:Number.NEGATIVE_INFINITY,max:i?n:Number.POSITIVE_INFINITY}}function o0(e,t,n){const s=e[t]||(e[t]={});return s[n]||(s[n]={})}function Hc(e,t,n,s){for(const i of t.getMatchingVisibleMetas(s).reverse()){const o=e[i.index];if(n&&o>0||!n&&o<0)return i.index}return null}function jc(e,t){const{chart:n,_cachedMeta:s}=e,i=n._stacks||(n._stacks={}),{iScale:o,vScale:r,index:a}=s,l=o.axis,c=r.axis,u=s0(o,r,s),f=t.length;let h;for(let d=0;d<f;++d){const p=t[d],{[l]:g,[c]:m}=p,_=p._stacks||(p._stacks={});h=_[c]=o0(i,u,g),h[a]=m,h._top=Hc(h,r,!0,s.type),h._bottom=Hc(h,r,!1,s.type);const x=h._visualValues||(h._visualValues={});x[a]=m}}function pr(e,t){const n=e.scales;return Object.keys(n).filter(s=>n[s].axis===t).shift()}function r0(e,t){return _n(e,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function a0(e,t,n){return _n(e,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:n,index:t,mode:"default",type:"data"})}function bs(e,t){const n=e.controller.index,s=e.vScale&&e.vScale.axis;if(s){t=t||e._parsed;for(const i of t){const o=i._stacks;if(!o||o[s]===void 0||o[s][n]===void 0)return;delete o[s][n],o[s]._visualValues!==void 0&&o[s]._visualValues[n]!==void 0&&delete o[s]._visualValues[n]}}}const gr=e=>e==="reset"||e==="none",Vc=(e,t)=>t?e:Object.assign({},e),l0=(e,t,n)=>e&&!t.hidden&&t._stacked&&{keys:vd(n,!0),values:null};class me{constructor(t,n){this.chart=t,this._ctx=t.ctx,this.index=n,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=dr(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&bs(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,n=this._cachedMeta,s=this.getDataset(),i=(f,h,d,p)=>f==="x"?h:f==="r"?p:d,o=n.xAxisID=st(s.xAxisID,pr(t,"x")),r=n.yAxisID=st(s.yAxisID,pr(t,"y")),a=n.rAxisID=st(s.rAxisID,pr(t,"r")),l=n.indexAxis,c=n.iAxisID=i(l,o,r,a),u=n.vAxisID=i(l,r,o,a);n.xScale=this.getScaleForId(o),n.yScale=this.getScaleForId(r),n.rScale=this.getScaleForId(a),n.iScale=this.getScaleForId(c),n.vScale=this.getScaleForId(u)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const n=this._cachedMeta;return t===n.iScale?n.vScale:n.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&Mc(this._data,this),t._stacked&&bs(t)}_dataCheck(){const t=this.getDataset(),n=t.data||(t.data=[]),s=this._data;if(ot(n)){const i=this._cachedMeta;this._data=n0(n,i)}else if(s!==n){if(s){Mc(s,this);const i=this._cachedMeta;bs(i),i._parsed=[]}n&&Object.isExtensible(n)&&Ky(n,this),this._syncList=[],this._data=n}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const n=this._cachedMeta,s=this.getDataset();let i=!1;this._dataCheck();const o=n._stacked;n._stacked=dr(n.vScale,n),n.stack!==s.stack&&(i=!0,bs(n),n.stack=s.stack),this._resyncElements(t),(i||o!==n._stacked)&&(jc(this,n._parsed),n._stacked=dr(n.vScale,n))}configure(){const t=this.chart.config,n=t.datasetScopeKeys(this._type),s=t.getOptionScopes(this.getDataset(),n,!0);this.options=t.createResolver(s,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,n){const{_cachedMeta:s,_data:i}=this,{iScale:o,_stacked:r}=s,a=o.axis;let l=t===0&&n===i.length?!0:s._sorted,c=t>0&&s._parsed[t-1],u,f,h;if(this._parsing===!1)s._parsed=i,s._sorted=!0,h=i;else{wt(i[t])?h=this.parseArrayData(s,i,t,n):ot(i[t])?h=this.parseObjectData(s,i,t,n):h=this.parsePrimitiveData(s,i,t,n);const d=()=>f[a]===null||c&&f[a]<c[a];for(u=0;u<n;++u)s._parsed[u+t]=f=h[u],l&&(d()&&(l=!1),c=f);s._sorted=l}r&&jc(this,h)}parsePrimitiveData(t,n,s,i){const{iScale:o,vScale:r}=t,a=o.axis,l=r.axis,c=o.getLabels(),u=o===r,f=new Array(i);let h,d,p;for(h=0,d=i;h<d;++h)p=h+s,f[h]={[a]:u||o.parse(c[p],p),[l]:r.parse(n[p],p)};return f}parseArrayData(t,n,s,i){const{xScale:o,yScale:r}=t,a=new Array(i);let l,c,u,f;for(l=0,c=i;l<c;++l)u=l+s,f=n[u],a[l]={x:o.parse(f[0],u),y:r.parse(f[1],u)};return a}parseObjectData(t,n,s,i){const{xScale:o,yScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(i);let u,f,h,d;for(u=0,f=i;u<f;++u)h=u+s,d=n[h],c[u]={x:o.parse(gn(d,a),h),y:r.parse(gn(d,l),h)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,n,s){const i=this.chart,o=this._cachedMeta,r=n[t.axis],a={keys:vd(i,!0),values:n._stacks[t.axis]._visualValues};return zc(a,r,o.index,{mode:s})}updateRangeFromParsed(t,n,s,i){const o=s[n.axis];let r=o===null?NaN:o;const a=i&&s._stacks[n.axis];i&&a&&(i.values=a,r=zc(i,o,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,n){const s=this._cachedMeta,i=s._parsed,o=s._sorted&&t===s.iScale,r=i.length,a=this._getOtherScale(t),l=l0(n,s,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:u,max:f}=i0(a);let h,d;function p(){d=i[h];const g=d[a.axis];return!Mt(d[t.axis])||u>g||f<g}for(h=0;h<r&&!(!p()&&(this.updateRangeFromParsed(c,t,d,l),o));++h);if(o){for(h=r-1;h>=0;--h)if(!p()){this.updateRangeFromParsed(c,t,d,l);break}}return c}getAllParsedValues(t){const n=this._cachedMeta._parsed,s=[];let i,o,r;for(i=0,o=n.length;i<o;++i)r=n[i][t.axis],Mt(r)&&s.push(r);return s}getMaxOverflow(){return!1}getLabelAndValue(t){const n=this._cachedMeta,s=n.iScale,i=n.vScale,o=this.getParsed(t);return{label:s?""+s.getLabelForValue(o[s.axis]):"",value:i?""+i.getLabelForValue(o[i.axis]):""}}_update(t){const n=this._cachedMeta;this.update(t||"default"),n._clip=e0(st(this.options.clip,t0(n.xScale,n.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,n=this.chart,s=this._cachedMeta,i=s.data||[],o=n.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||i.length-a,c=this.options.drawActiveElementsOnTop;let u;for(s.dataset&&s.dataset.draw(t,o,a,l),u=a;u<a+l;++u){const f=i[u];f.hidden||(f.active&&c?r.push(f):f.draw(t,o))}for(u=0;u<r.length;++u)r[u].draw(t,o)}getStyle(t,n){const s=n?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(t||0,s)}getContext(t,n,s){const i=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];o=r.$context||(r.$context=a0(this.getContext(),t,r)),o.parsed=this.getParsed(t),o.raw=i.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=r0(this.chart.getContext(),this.index)),o.dataset=i,o.index=o.datasetIndex=this.index;return o.active=!!n,o.mode=s,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,n){return this._resolveElementOptions(this.dataElementType.id,n,t)}_resolveElementOptions(t,n="default",s){const i=n==="active",o=this._cachedDataOpts,r=t+"-"+n,a=o[r],l=this.enableOptionSharing&&ni(s);if(a)return Vc(a,l);const c=this.chart.config,u=c.datasetElementScopeKeys(this._type,t),f=i?[`${t}Hover`,"hover",t,""]:[t,""],h=c.getOptionScopes(this.getDataset(),u),d=Object.keys(St.elements[t]),p=()=>this.getContext(s,i,n),g=c.resolveNamedOptions(h,d,p,f);return g.$shared&&(g.$shared=l,o[r]=Object.freeze(Vc(g,l))),g}_resolveAnimations(t,n,s){const i=this.chart,o=this._cachedDataOpts,r=`animation-${n}`,a=o[r];if(a)return a;let l;if(i.options.animation!==!1){const u=this.chart.config,f=u.datasetAnimationScopeKeys(this._type,n),h=u.getOptionScopes(this.getDataset(),f);l=u.createResolver(h,this.getContext(t,s,n))}const c=new xd(i,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,n){return!n||gr(t)||this.chart._animationsDisabled}_getSharedOptions(t,n){const s=this.resolveDataElementOptions(t,n),i=this._sharedOptions,o=this.getSharedOptions(s),r=this.includeOptions(n,o)||o!==i;return this.updateSharedOptions(o,n,s),{sharedOptions:o,includeOptions:r}}updateElement(t,n,s,i){gr(i)?Object.assign(t,s):this._resolveAnimations(n,i).update(t,s)}updateSharedOptions(t,n,s){t&&!gr(n)&&this._resolveAnimations(void 0,n).update(t,s)}_setStyle(t,n,s,i){t.active=i;const o=this.getStyle(n,i);this._resolveAnimations(n,s,i).update(t,{options:!i&&this.getSharedOptions(o)||o})}removeHoverStyle(t,n,s){this._setStyle(t,s,"active",!1)}setHoverStyle(t,n,s){this._setStyle(t,s,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const n=this._data,s=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const i=s.length,o=n.length,r=Math.min(o,i);r&&this.parse(0,r),o>i?this._insertElements(i,o-i,t):o<i&&this._removeElements(o,i-o)}_insertElements(t,n,s=!0){const i=this._cachedMeta,o=i.data,r=t+n;let a;const l=c=>{for(c.length+=n,a=c.length-1;a>=r;a--)c[a]=c[a-n]};for(l(o),a=t;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(i._parsed),this.parse(t,n),s&&this.updateElements(o,t,n,"reset")}updateElements(t,n,s,i){}_removeElements(t,n){const s=this._cachedMeta;if(this._parsing){const i=s._parsed.splice(t,n);s._stacked&&bs(s,i)}s.data.splice(t,n)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[n,s,i]=t;this[n](s,i)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,n){n&&this._sync(["_removeElements",t,n]);const s=arguments.length-2;s&&this._sync(["_insertElements",t,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}N(me,"defaults",{}),N(me,"datasetElementType",null),N(me,"dataElementType",null);function c0(e,t){if(!e._cache.$bar){const n=e.getMatchingVisibleMetas(t);let s=[];for(let i=0,o=n.length;i<o;i++)s=s.concat(n[i].controller.getAllParsedValues(e));e._cache.$bar=td(s.sort((i,o)=>i-o))}return e._cache.$bar}function u0(e){const t=e.iScale,n=c0(t,e.type);let s=t._length,i,o,r,a;const l=()=>{r===32767||r===-32768||(ni(a)&&(s=Math.min(s,Math.abs(r-a)||s)),a=r)};for(i=0,o=n.length;i<o;++i)r=t.getPixelForValue(n[i]),l();for(a=void 0,i=0,o=t.ticks.length;i<o;++i)r=t.getPixelForTick(i),l();return s}function f0(e,t,n,s){const i=n.barThickness;let o,r;return it(i)?(o=t.min*n.categoryPercentage,r=n.barPercentage):(o=i*s,r=1),{chunk:o/s,ratio:r,start:t.pixels[e]-o/2}}function h0(e,t,n,s){const i=t.pixels,o=i[e];let r=e>0?i[e-1]:null,a=e<i.length-1?i[e+1]:null;const l=n.categoryPercentage;r===null&&(r=o-(a===null?t.end-t.start:a-o)),a===null&&(a=o+o-r);const c=o-(o-Math.min(r,a))/2*l;return{chunk:Math.abs(a-r)/2*l/s,ratio:n.barPercentage,start:c}}function d0(e,t,n,s){const i=n.parse(e[0],s),o=n.parse(e[1],s),r=Math.min(i,o),a=Math.max(i,o);let l=r,c=a;Math.abs(r)>Math.abs(a)&&(l=a,c=r),t[n.axis]=c,t._custom={barStart:l,barEnd:c,start:i,end:o,min:r,max:a}}function wd(e,t,n,s){return wt(e)?d0(e,t,n,s):t[n.axis]=n.parse(e,s),t}function Wc(e,t,n,s){const i=e.iScale,o=e.vScale,r=i.getLabels(),a=i===o,l=[];let c,u,f,h;for(c=n,u=n+s;c<u;++c)h=t[c],f={},f[i.axis]=a||i.parse(r[c],c),l.push(wd(h,f,o,c));return l}function mr(e){return e&&e.barStart!==void 0&&e.barEnd!==void 0}function p0(e,t,n){return e!==0?De(e):(t.isHorizontal()?1:-1)*(t.min>=n?1:-1)}function g0(e){let t,n,s,i,o;return e.horizontal?(t=e.base>e.x,n="left",s="right"):(t=e.base<e.y,n="bottom",s="top"),t?(i="end",o="start"):(i="start",o="end"),{start:n,end:s,reverse:t,top:i,bottom:o}}function m0(e,t,n,s){let i=t.borderSkipped;const o={};if(!i){e.borderSkipped=o;return}if(i===!0){e.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:r,end:a,reverse:l,top:c,bottom:u}=g0(e);i==="middle"&&n&&(e.enableBorderRadius=!0,(n._top||0)===s?i=c:(n._bottom||0)===s?i=u:(o[$c(u,r,a,l)]=!0,i=c)),o[$c(i,r,a,l)]=!0,e.borderSkipped=o}function $c(e,t,n,s){return s?(e=b0(e,t,n),e=Uc(e,n,t)):e=Uc(e,t,n),e}function b0(e,t,n){return e===t?n:e===n?t:e}function Uc(e,t,n){return e==="start"?t:e==="end"?n:e}function _0(e,{inflateAmount:t},n){e.inflateAmount=t==="auto"?n===1?.33:0:t}class $i extends me{parsePrimitiveData(t,n,s,i){return Wc(t,n,s,i)}parseArrayData(t,n,s,i){return Wc(t,n,s,i)}parseObjectData(t,n,s,i){const{iScale:o,vScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=o.axis==="x"?a:l,u=r.axis==="x"?a:l,f=[];let h,d,p,g;for(h=s,d=s+i;h<d;++h)g=n[h],p={},p[o.axis]=o.parse(gn(g,c),h),f.push(wd(gn(g,u),p,r,h));return f}updateRangeFromParsed(t,n,s,i){super.updateRangeFromParsed(t,n,s,i);const o=s._custom;o&&n===this._cachedMeta.vScale&&(t.min=Math.min(t.min,o.min),t.max=Math.max(t.max,o.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const n=this._cachedMeta,{iScale:s,vScale:i}=n,o=this.getParsed(t),r=o._custom,a=mr(r)?"["+r.start+", "+r.end+"]":""+i.getLabelForValue(o[i.axis]);return{label:""+s.getLabelForValue(o[s.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();const t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){const n=this._cachedMeta;this.updateElements(n.data,0,n.data.length,t)}updateElements(t,n,s,i){const o=i==="reset",{index:r,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),c=a.isHorizontal(),u=this._getRuler(),{sharedOptions:f,includeOptions:h}=this._getSharedOptions(n,i);for(let d=n;d<n+s;d++){const p=this.getParsed(d),g=o||it(p[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(d),m=this._calculateBarIndexPixels(d,u),_=(p._stacks||{})[a.axis],x={horizontal:c,base:g.base,enableBorderRadius:!_||mr(p._custom)||r===_._top||r===_._bottom,x:c?g.head:m.center,y:c?m.center:g.head,height:c?m.size:Math.abs(g.size),width:c?Math.abs(g.size):m.size};h&&(x.options=f||this.resolveDataElementOptions(d,t[d].active?"active":i));const v=x.options||t[d].options;m0(x,v,_,r),_0(x,v,u.ratio),this.updateElement(t[d],d,x,i)}}_getStacks(t,n){const{iScale:s}=this._cachedMeta,i=s.getMatchingVisibleMetas(this._type).filter(u=>u.controller.options.grouped),o=s.options.stacked,r=[],a=this._cachedMeta.controller.getParsed(n),l=a&&a[s.axis],c=u=>{const f=u._parsed.find(d=>d[s.axis]===l),h=f&&f[u.vScale.axis];if(it(h)||isNaN(h))return!0};for(const u of i)if(!(n!==void 0&&c(u))&&((o===!1||r.indexOf(u.stack)===-1||o===void 0&&u.stack===void 0)&&r.push(u.stack),u.index===t))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,n,s){const i=this._getStacks(t,s),o=n!==void 0?i.indexOf(n):-1;return o===-1?i.length-1:o}_getRuler(){const t=this.options,n=this._cachedMeta,s=n.iScale,i=[];let o,r;for(o=0,r=n.data.length;o<r;++o)i.push(s.getPixelForValue(this.getParsed(o)[s.axis],o));const a=t.barThickness;return{min:a||u0(n),pixels:i,start:s._startPixel,end:s._endPixel,stackCount:this._getStackCount(),scale:s,grouped:t.grouped,ratio:a?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:n,_stacked:s,index:i},options:{base:o,minBarLength:r}}=this,a=o||0,l=this.getParsed(t),c=l._custom,u=mr(c);let f=l[n.axis],h=0,d=s?this.applyStack(n,l,s):f,p,g;d!==f&&(h=d-f,d=f),u&&(f=c.barStart,d=c.barEnd-c.barStart,f!==0&&De(f)!==De(c.barEnd)&&(h=0),h+=f);const m=!it(o)&&!u?o:h;let _=n.getPixelForValue(m);if(this.chart.getDataVisibility(t)?p=n.getPixelForValue(h+d):p=_,g=p-_,Math.abs(g)<r){g=p0(g,n,a)*r,f===a&&(_-=g/2);const x=n.getPixelForDecimal(0),v=n.getPixelForDecimal(1),w=Math.min(x,v),S=Math.max(x,v);_=Math.max(Math.min(_,S),w),p=_+g,s&&!u&&(l._stacks[n.axis]._visualValues[i]=n.getValueForPixel(p)-n.getValueForPixel(_))}if(_===n.getPixelForValue(a)){const x=De(g)*n.getLineWidthForValue(a)/2;_+=x,g-=x}return{size:g,base:_,head:p,center:p+g/2}}_calculateBarIndexPixels(t,n){const s=n.scale,i=this.options,o=i.skipNull,r=st(i.maxBarThickness,1/0);let a,l;if(n.grouped){const c=o?this._getStackCount(t):n.stackCount,u=i.barThickness==="flex"?h0(t,n,i,c):f0(t,n,i,c),f=this._getStackIndex(this.index,this._cachedMeta.stack,o?t:void 0);a=u.start+u.chunk*f+u.chunk/2,l=Math.min(r,u.chunk*u.ratio)}else a=s.getPixelForValue(this.getParsed(t)[s.axis],t),l=Math.min(r,n.min*n.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){const t=this._cachedMeta,n=t.vScale,s=t.data,i=s.length;let o=0;for(;o<i;++o)this.getParsed(o)[n.axis]!==null&&!s[o].hidden&&s[o].draw(this._ctx)}}N($i,"id","bar"),N($i,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),N($i,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});class Ui extends me{initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,n,s,i){const o=super.parsePrimitiveData(t,n,s,i);for(let r=0;r<o.length;r++)o[r]._custom=this.resolveDataElementOptions(r+s).radius;return o}parseArrayData(t,n,s,i){const o=super.parseArrayData(t,n,s,i);for(let r=0;r<o.length;r++){const a=n[s+r];o[r]._custom=st(a[2],this.resolveDataElementOptions(r+s).radius)}return o}parseObjectData(t,n,s,i){const o=super.parseObjectData(t,n,s,i);for(let r=0;r<o.length;r++){const a=n[s+r];o[r]._custom=st(a&&a.r&&+a.r,this.resolveDataElementOptions(r+s).radius)}return o}getMaxOverflow(){const t=this._cachedMeta.data;let n=0;for(let s=t.length-1;s>=0;--s)n=Math.max(n,t[s].size(this.resolveDataElementOptions(s))/2);return n>0&&n}getLabelAndValue(t){const n=this._cachedMeta,s=this.chart.data.labels||[],{xScale:i,yScale:o}=n,r=this.getParsed(t),a=i.getLabelForValue(r.x),l=o.getLabelForValue(r.y),c=r._custom;return{label:s[t]||"",value:"("+a+", "+l+(c?", "+c:"")+")"}}update(t){const n=this._cachedMeta.data;this.updateElements(n,0,n.length,t)}updateElements(t,n,s,i){const o=i==="reset",{iScale:r,vScale:a}=this._cachedMeta,{sharedOptions:l,includeOptions:c}=this._getSharedOptions(n,i),u=r.axis,f=a.axis;for(let h=n;h<n+s;h++){const d=t[h],p=!o&&this.getParsed(h),g={},m=g[u]=o?r.getPixelForDecimal(.5):r.getPixelForValue(p[u]),_=g[f]=o?a.getBasePixel():a.getPixelForValue(p[f]);g.skip=isNaN(m)||isNaN(_),c&&(g.options=l||this.resolveDataElementOptions(h,d.active?"active":i),o&&(g.options.radius=0)),this.updateElement(d,h,g,i)}}resolveDataElementOptions(t,n){const s=this.getParsed(t);let i=super.resolveDataElementOptions(t,n);i.$shared&&(i=Object.assign({},i,{$shared:!1}));const o=i.radius;return n!=="active"&&(i.radius=0),i.radius+=st(s&&s._custom,o),i}}N(Ui,"id","bubble"),N(Ui,"defaults",{datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}}),N(Ui,"overrides",{scales:{x:{type:"linear"},y:{type:"linear"}}});function y0(e,t,n){let s=1,i=1,o=0,r=0;if(t<xt){const a=e,l=a+t,c=Math.cos(a),u=Math.sin(a),f=Math.cos(l),h=Math.sin(l),d=(v,w,S)=>si(v,a,l,!0)?1:Math.max(w,w*n,S,S*n),p=(v,w,S)=>si(v,a,l,!0)?-1:Math.min(w,w*n,S,S*n),g=d(0,c,f),m=d(Et,u,h),_=p(vt,c,f),x=p(vt+Et,u,h);s=(g-_)/2,i=(m-x)/2,o=-(g+_)/2,r=-(m+x)/2}return{ratioX:s,ratioY:i,offsetX:o,offsetY:r}}class Rn extends me{constructor(t,n){super(t,n),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,n){const s=this.getDataset().data,i=this._cachedMeta;if(this._parsing===!1)i._parsed=s;else{let o=l=>+s[l];if(ot(s[t])){const{key:l="value"}=this._parsing;o=c=>+gn(s[c],l)}let r,a;for(r=t,a=t+n;r<a;++r)i._parsed[r]=o(r)}}_getRotation(){return pe(this.options.rotation-90)}_getCircumference(){return pe(this.options.circumference)}_getRotationExtents(){let t=xt,n=-xt;for(let s=0;s<this.chart.data.datasets.length;++s)if(this.chart.isDatasetVisible(s)&&this.chart.getDatasetMeta(s).type===this._type){const i=this.chart.getDatasetMeta(s).controller,o=i._getRotation(),r=i._getCircumference();t=Math.min(t,o),n=Math.max(n,o+r)}return{rotation:t,circumference:n-t}}update(t){const n=this.chart,{chartArea:s}=n,i=this._cachedMeta,o=i.data,r=this.getMaxBorderWidth()+this.getMaxOffset(o)+this.options.spacing,a=Math.max((Math.min(s.width,s.height)-r)/2,0),l=Math.min(Ry(this.options.cutout,a),1),c=this._getRingWeight(this.index),{circumference:u,rotation:f}=this._getRotationExtents(),{ratioX:h,ratioY:d,offsetX:p,offsetY:g}=y0(f,u,l),m=(s.width-r)/h,_=(s.height-r)/d,x=Math.max(Math.min(m,_)/2,0),v=Xh(this.options.radius,x),w=Math.max(v*l,0),S=(v-w)/this._getVisibleDatasetWeightTotal();this.offsetX=p*v,this.offsetY=g*v,i.total=this.calculateTotal(),this.outerRadius=v-S*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-S*c,0),this.updateElements(o,0,o.length,t)}_circumference(t,n){const s=this.options,i=this._cachedMeta,o=this._getCircumference();return n&&s.animation.animateRotate||!this.chart.getDataVisibility(t)||i._parsed[t]===null||i.data[t].hidden?0:this.calculateCircumference(i._parsed[t]*o/xt)}updateElements(t,n,s,i){const o=i==="reset",r=this.chart,a=r.chartArea,c=r.options.animation,u=(a.left+a.right)/2,f=(a.top+a.bottom)/2,h=o&&c.animateScale,d=h?0:this.innerRadius,p=h?0:this.outerRadius,{sharedOptions:g,includeOptions:m}=this._getSharedOptions(n,i);let _=this._getRotation(),x;for(x=0;x<n;++x)_+=this._circumference(x,o);for(x=n;x<n+s;++x){const v=this._circumference(x,o),w=t[x],S={x:u+this.offsetX,y:f+this.offsetY,startAngle:_,endAngle:_+v,circumference:v,outerRadius:p,innerRadius:d};m&&(S.options=g||this.resolveDataElementOptions(x,w.active?"active":i)),_+=v,this.updateElement(w,x,S,i)}}calculateTotal(){const t=this._cachedMeta,n=t.data;let s=0,i;for(i=0;i<n.length;i++){const o=t._parsed[i];o!==null&&!isNaN(o)&&this.chart.getDataVisibility(i)&&!n[i].hidden&&(s+=Math.abs(o))}return s}calculateCircumference(t){const n=this._cachedMeta.total;return n>0&&!isNaN(t)?xt*(Math.abs(t)/n):0}getLabelAndValue(t){const n=this._cachedMeta,s=this.chart,i=s.data.labels||[],o=mi(n._parsed[t],s.options.locale);return{label:i[t]||"",value:o}}getMaxBorderWidth(t){let n=0;const s=this.chart;let i,o,r,a,l;if(!t){for(i=0,o=s.data.datasets.length;i<o;++i)if(s.isDatasetVisible(i)){r=s.getDatasetMeta(i),t=r.data,a=r.controller;break}}if(!t)return 0;for(i=0,o=t.length;i<o;++i)l=a.resolveDataElementOptions(i),l.borderAlign!=="inner"&&(n=Math.max(n,l.borderWidth||0,l.hoverBorderWidth||0));return n}getMaxOffset(t){let n=0;for(let s=0,i=t.length;s<i;++s){const o=this.resolveDataElementOptions(s);n=Math.max(n,o.offset||0,o.hoverOffset||0)}return n}_getRingWeightOffset(t){let n=0;for(let s=0;s<t;++s)this.chart.isDatasetVisible(s)&&(n+=this._getRingWeight(s));return n}_getRingWeight(t){return Math.max(st(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}N(Rn,"id","doughnut"),N(Rn,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),N(Rn,"descriptors",{_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")}),N(Rn,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const n=t.data;if(n.labels.length&&n.datasets.length){const{labels:{pointStyle:s,color:i}}=t.legend.options;return n.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:i,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,n,s){s.chart.toggleDataVisibility(n.index),s.chart.update()}}}});class Ki extends me{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const n=this._cachedMeta,{dataset:s,data:i=[],_dataset:o}=n,r=this.chart._animationsDisabled;let{start:a,count:l}=sd(n,i,r);this._drawStart=a,this._drawCount=l,id(n)&&(a=0,l=i.length),s._chart=this.chart,s._datasetIndex=this.index,s._decimated=!!o._decimated,s.points=i;const c=this.resolveDatasetElementOptions(t);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(s,void 0,{animated:!r,options:c},t),this.updateElements(i,a,l,t)}updateElements(t,n,s,i){const o=i==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,{sharedOptions:u,includeOptions:f}=this._getSharedOptions(n,i),h=r.axis,d=a.axis,{spanGaps:p,segment:g}=this.options,m=rs(p)?p:Number.POSITIVE_INFINITY,_=this.chart._animationsDisabled||o||i==="none",x=n+s,v=t.length;let w=n>0&&this.getParsed(n-1);for(let S=0;S<v;++S){const A=t[S],k=_?A:{};if(S<n||S>=x){k.skip=!0;continue}const E=this.getParsed(S),P=it(E[d]),I=k[h]=r.getPixelForValue(E[h],S),H=k[d]=o||P?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,E,l):E[d],S);k.skip=isNaN(I)||isNaN(H)||P,k.stop=S>0&&Math.abs(E[h]-w[h])>m,g&&(k.parsed=E,k.raw=c.data[S]),f&&(k.options=u||this.resolveDataElementOptions(S,A.active?"active":i)),_||this.updateElement(A,S,k,i),w=E}}getMaxOverflow(){const t=this._cachedMeta,n=t.dataset,s=n.options&&n.options.borderWidth||0,i=t.data||[];if(!i.length)return s;const o=i[0].size(this.resolveDataElementOptions(0)),r=i[i.length-1].size(this.resolveDataElementOptions(i.length-1));return Math.max(s,o,r)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}N(Ki,"id","line"),N(Ki,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),N(Ki,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});class Vs extends me{constructor(t,n){super(t,n),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){const n=this._cachedMeta,s=this.chart,i=s.data.labels||[],o=mi(n._parsed[t].r,s.options.locale);return{label:i[t]||"",value:o}}parseObjectData(t,n,s,i){return hd.bind(this)(t,n,s,i)}update(t){const n=this._cachedMeta.data;this._updateRadius(),this.updateElements(n,0,n.length,t)}getMinMax(){const t=this._cachedMeta,n={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((s,i)=>{const o=this.getParsed(i).r;!isNaN(o)&&this.chart.getDataVisibility(i)&&(o<n.min&&(n.min=o),o>n.max&&(n.max=o))}),n}_updateRadius(){const t=this.chart,n=t.chartArea,s=t.options,i=Math.min(n.right-n.left,n.bottom-n.top),o=Math.max(i/2,0),r=Math.max(s.cutoutPercentage?o/100*s.cutoutPercentage:1,0),a=(o-r)/t.getVisibleDatasetCount();this.outerRadius=o-a*this.index,this.innerRadius=this.outerRadius-a}updateElements(t,n,s,i){const o=i==="reset",r=this.chart,l=r.options.animation,c=this._cachedMeta.rScale,u=c.xCenter,f=c.yCenter,h=c.getIndexAngle(0)-.5*vt;let d=h,p;const g=360/this.countVisibleElements();for(p=0;p<n;++p)d+=this._computeAngle(p,i,g);for(p=n;p<n+s;p++){const m=t[p];let _=d,x=d+this._computeAngle(p,i,g),v=r.getDataVisibility(p)?c.getDistanceFromCenterForValue(this.getParsed(p).r):0;d=x,o&&(l.animateScale&&(v=0),l.animateRotate&&(_=x=h));const w={x:u,y:f,innerRadius:0,outerRadius:v,startAngle:_,endAngle:x,options:this.resolveDataElementOptions(p,m.active?"active":i)};this.updateElement(m,p,w,i)}}countVisibleElements(){const t=this._cachedMeta;let n=0;return t.data.forEach((s,i)=>{!isNaN(this.getParsed(i).r)&&this.chart.getDataVisibility(i)&&n++}),n}_computeAngle(t,n,s){return this.chart.getDataVisibility(t)?pe(this.resolveDataElementOptions(t,n).angle||s):0}}N(Vs,"id","polarArea"),N(Vs,"defaults",{dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0}),N(Vs,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const n=t.data;if(n.labels.length&&n.datasets.length){const{labels:{pointStyle:s,color:i}}=t.legend.options;return n.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:i,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,n,s){s.chart.toggleDataVisibility(n.index),s.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}});class Jr extends Rn{}N(Jr,"id","pie"),N(Jr,"defaults",{cutout:0,rotation:0,circumference:360,radius:"100%"});class qi extends me{getLabelAndValue(t){const n=this._cachedMeta.vScale,s=this.getParsed(t);return{label:n.getLabels()[t],value:""+n.getLabelForValue(s[n.axis])}}parseObjectData(t,n,s,i){return hd.bind(this)(t,n,s,i)}update(t){const n=this._cachedMeta,s=n.dataset,i=n.data||[],o=n.iScale.getLabels();if(s.points=i,t!=="resize"){const r=this.resolveDatasetElementOptions(t);this.options.showLine||(r.borderWidth=0);const a={_loop:!0,_fullLoop:o.length===i.length,options:r};this.updateElement(s,void 0,a,t)}this.updateElements(i,0,i.length,t)}updateElements(t,n,s,i){const o=this._cachedMeta.rScale,r=i==="reset";for(let a=n;a<n+s;a++){const l=t[a],c=this.resolveDataElementOptions(a,l.active?"active":i),u=o.getPointPositionForValue(a,this.getParsed(a).r),f=r?o.xCenter:u.x,h=r?o.yCenter:u.y,d={x:f,y:h,angle:u.angle,skip:isNaN(f)||isNaN(h),options:c};this.updateElement(l,a,d,i)}}}N(qi,"id","radar"),N(qi,"defaults",{datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}}),N(qi,"overrides",{aspectRatio:1,scales:{r:{type:"radialLinear"}}});class Yi extends me{getLabelAndValue(t){const n=this._cachedMeta,s=this.chart.data.labels||[],{xScale:i,yScale:o}=n,r=this.getParsed(t),a=i.getLabelForValue(r.x),l=o.getLabelForValue(r.y);return{label:s[t]||"",value:"("+a+", "+l+")"}}update(t){const n=this._cachedMeta,{data:s=[]}=n,i=this.chart._animationsDisabled;let{start:o,count:r}=sd(n,s,i);if(this._drawStart=o,this._drawCount=r,id(n)&&(o=0,r=s.length),this.options.showLine){this.datasetElementType||this.addElements();const{dataset:a,_dataset:l}=n;a._chart=this.chart,a._datasetIndex=this.index,a._decimated=!!l._decimated,a.points=s;const c=this.resolveDatasetElementOptions(t);c.segment=this.options.segment,this.updateElement(a,void 0,{animated:!i,options:c},t)}else this.datasetElementType&&(delete n.dataset,this.datasetElementType=!1);this.updateElements(s,o,r,t)}addElements(){const{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,n,s,i){const o=i==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,u=this.resolveDataElementOptions(n,i),f=this.getSharedOptions(u),h=this.includeOptions(i,f),d=r.axis,p=a.axis,{spanGaps:g,segment:m}=this.options,_=rs(g)?g:Number.POSITIVE_INFINITY,x=this.chart._animationsDisabled||o||i==="none";let v=n>0&&this.getParsed(n-1);for(let w=n;w<n+s;++w){const S=t[w],A=this.getParsed(w),k=x?S:{},E=it(A[p]),P=k[d]=r.getPixelForValue(A[d],w),I=k[p]=o||E?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,A,l):A[p],w);k.skip=isNaN(P)||isNaN(I)||E,k.stop=w>0&&Math.abs(A[d]-v[d])>_,m&&(k.parsed=A,k.raw=c.data[w]),h&&(k.options=f||this.resolveDataElementOptions(w,S.active?"active":i)),x||this.updateElement(S,w,k,i),v=A}this.updateSharedOptions(f,i,u)}getMaxOverflow(){const t=this._cachedMeta,n=t.data||[];if(!this.options.showLine){let a=0;for(let l=n.length-1;l>=0;--l)a=Math.max(a,n[l].size(this.resolveDataElementOptions(l))/2);return a>0&&a}const s=t.dataset,i=s.options&&s.options.borderWidth||0;if(!n.length)return i;const o=n[0].size(this.resolveDataElementOptions(0)),r=n[n.length-1].size(this.resolveDataElementOptions(n.length-1));return Math.max(i,o,r)/2}}N(Yi,"id","scatter"),N(Yi,"defaults",{datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1}),N(Yi,"overrides",{interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}});var x0=Object.freeze({__proto__:null,BarController:$i,BubbleController:Ui,DoughnutController:Rn,LineController:Ki,PieController:Jr,PolarAreaController:Vs,RadarController:qi,ScatterController:Yi});function Mn(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class Ya{constructor(t){N(this,"options");this.options=t||{}}static override(t){Object.assign(Ya.prototype,t)}init(){}formats(){return Mn()}parse(){return Mn()}format(){return Mn()}add(){return Mn()}diff(){return Mn()}startOf(){return Mn()}endOf(){return Mn()}}var v0={_date:Ya};function w0(e,t,n,s){const{controller:i,data:o,_sorted:r}=e,a=i._cachedMeta.iScale,l=e.dataset&&e.dataset.options?e.dataset.options.spanGaps:null;if(a&&t===a.axis&&t!=="r"&&r&&o.length){const c=a._reversePixels?$y:Ue;if(s){if(i._sharedOptions){const u=o[0],f=typeof u.getRange=="function"&&u.getRange(t);if(f){const h=c(o,t,n-f),d=c(o,t,n+f);return{lo:h.lo,hi:d.hi}}}}else{const u=c(o,t,n);if(l){const{vScale:f}=i._cachedMeta,{_parsed:h}=e,d=h.slice(0,u.lo+1).reverse().findIndex(g=>!it(g[f.axis]));u.lo-=Math.max(0,d);const p=h.slice(u.hi).findIndex(g=>!it(g[f.axis]));u.hi+=Math.max(0,p)}return u}}return{lo:0,hi:o.length-1}}function Uo(e,t,n,s,i){const o=e.getSortedVisibleDatasetMetas(),r=n[t];for(let a=0,l=o.length;a<l;++a){const{index:c,data:u}=o[a],{lo:f,hi:h}=w0(o[a],t,r,i);for(let d=f;d<=h;++d){const p=u[d];p.skip||s(p,c,d)}}}function S0(e){const t=e.indexOf("x")!==-1,n=e.indexOf("y")!==-1;return function(s,i){const o=t?Math.abs(s.x-i.x):0,r=n?Math.abs(s.y-i.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function br(e,t,n,s,i){const o=[];return!i&&!e.isPointInArea(t)||Uo(e,n,t,function(a,l,c){!i&&!Ke(a,e.chartArea,0)||a.inRange(t.x,t.y,s)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function M0(e,t,n,s){let i=[];function o(r,a,l){const{startAngle:c,endAngle:u}=r.getProps(["startAngle","endAngle"],s),{angle:f}=Qh(r,{x:t.x,y:t.y});si(f,c,u)&&i.push({element:r,datasetIndex:a,index:l})}return Uo(e,n,t,o),i}function C0(e,t,n,s,i,o){let r=[];const a=S0(n);let l=Number.POSITIVE_INFINITY;function c(u,f,h){const d=u.inRange(t.x,t.y,i);if(s&&!d)return;const p=u.getCenterPoint(i);if(!(!!o||e.isPointInArea(p))&&!d)return;const m=a(t,p);m<l?(r=[{element:u,datasetIndex:f,index:h}],l=m):m===l&&r.push({element:u,datasetIndex:f,index:h})}return Uo(e,n,t,c),r}function _r(e,t,n,s,i,o){return!o&&!e.isPointInArea(t)?[]:n==="r"&&!s?M0(e,t,n,i):C0(e,t,n,s,i,o)}function Kc(e,t,n,s,i){const o=[],r=n==="x"?"inXRange":"inYRange";let a=!1;return Uo(e,n,t,(l,c,u)=>{l[r]&&l[r](t[n],i)&&(o.push({element:l,datasetIndex:c,index:u}),a=a||l.inRange(t.x,t.y,i))}),s&&!a?[]:o}var P0={modes:{index(e,t,n,s){const i=En(t,e),o=n.axis||"x",r=n.includeInvisible||!1,a=n.intersect?br(e,i,o,s,r):_r(e,i,o,!1,s,r),l=[];return a.length?(e.getSortedVisibleDatasetMetas().forEach(c=>{const u=a[0].index,f=c.data[u];f&&!f.skip&&l.push({element:f,datasetIndex:c.index,index:u})}),l):[]},dataset(e,t,n,s){const i=En(t,e),o=n.axis||"xy",r=n.includeInvisible||!1;let a=n.intersect?br(e,i,o,s,r):_r(e,i,o,!1,s,r);if(a.length>0){const l=a[0].datasetIndex,c=e.getDatasetMeta(l).data;a=[];for(let u=0;u<c.length;++u)a.push({element:c[u],datasetIndex:l,index:u})}return a},point(e,t,n,s){const i=En(t,e),o=n.axis||"xy",r=n.includeInvisible||!1;return br(e,i,o,s,r)},nearest(e,t,n,s){const i=En(t,e),o=n.axis||"xy",r=n.includeInvisible||!1;return _r(e,i,o,n.intersect,s,r)},x(e,t,n,s){const i=En(t,e);return Kc(e,i,"x",n.intersect,s)},y(e,t,n,s){const i=En(t,e);return Kc(e,i,"y",n.intersect,s)}}};const Sd=["left","top","right","bottom"];function _s(e,t){return e.filter(n=>n.pos===t)}function qc(e,t){return e.filter(n=>Sd.indexOf(n.pos)===-1&&n.box.axis===t)}function ys(e,t){return e.sort((n,s)=>{const i=t?s:n,o=t?n:s;return i.weight===o.weight?i.index-o.index:i.weight-o.weight})}function E0(e){const t=[];let n,s,i,o,r,a;for(n=0,s=(e||[]).length;n<s;++n)i=e[n],{position:o,options:{stack:r,stackWeight:a=1}}=i,t.push({index:n,box:i,pos:o,horizontal:i.isHorizontal(),weight:i.weight,stack:r&&o+r,stackWeight:a});return t}function k0(e){const t={};for(const n of e){const{stack:s,pos:i,stackWeight:o}=n;if(!s||!Sd.includes(i))continue;const r=t[s]||(t[s]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return t}function A0(e,t){const n=k0(e),{vBoxMaxWidth:s,hBoxMaxHeight:i}=t;let o,r,a;for(o=0,r=e.length;o<r;++o){a=e[o];const{fullSize:l}=a.box,c=n[a.stack],u=c&&a.stackWeight/c.weight;a.horizontal?(a.width=u?u*s:l&&t.availableWidth,a.height=i):(a.width=s,a.height=u?u*i:l&&t.availableHeight)}return n}function O0(e){const t=E0(e),n=ys(t.filter(c=>c.box.fullSize),!0),s=ys(_s(t,"left"),!0),i=ys(_s(t,"right")),o=ys(_s(t,"top"),!0),r=ys(_s(t,"bottom")),a=qc(t,"x"),l=qc(t,"y");return{fullSize:n,leftAndTop:s.concat(o),rightAndBottom:i.concat(l).concat(r).concat(a),chartArea:_s(t,"chartArea"),vertical:s.concat(i).concat(l),horizontal:o.concat(r).concat(a)}}function Yc(e,t,n,s){return Math.max(e[n],t[n])+Math.max(e[s],t[s])}function Md(e,t){e.top=Math.max(e.top,t.top),e.left=Math.max(e.left,t.left),e.bottom=Math.max(e.bottom,t.bottom),e.right=Math.max(e.right,t.right)}function R0(e,t,n,s){const{pos:i,box:o}=n,r=e.maxPadding;if(!ot(i)){n.size&&(e[i]-=n.size);const f=s[n.stack]||{size:0,count:1};f.size=Math.max(f.size,n.horizontal?o.height:o.width),n.size=f.size/f.count,e[i]+=n.size}o.getPadding&&Md(r,o.getPadding());const a=Math.max(0,t.outerWidth-Yc(r,e,"left","right")),l=Math.max(0,t.outerHeight-Yc(r,e,"top","bottom")),c=a!==e.w,u=l!==e.h;return e.w=a,e.h=l,n.horizontal?{same:c,other:u}:{same:u,other:c}}function T0(e){const t=e.maxPadding;function n(s){const i=Math.max(t[s]-e[s],0);return e[s]+=i,i}e.y+=n("top"),e.x+=n("left"),n("right"),n("bottom")}function D0(e,t){const n=t.maxPadding;function s(i){const o={left:0,top:0,right:0,bottom:0};return i.forEach(r=>{o[r]=Math.max(t[r],n[r])}),o}return s(e?["left","right"]:["top","bottom"])}function Cs(e,t,n,s){const i=[];let o,r,a,l,c,u;for(o=0,r=e.length,c=0;o<r;++o){a=e[o],l=a.box,l.update(a.width||t.w,a.height||t.h,D0(a.horizontal,t));const{same:f,other:h}=R0(t,n,a,s);c|=f&&i.length,u=u||h,l.fullSize||i.push(a)}return c&&Cs(i,t,n,s)||u}function ki(e,t,n,s,i){e.top=n,e.left=t,e.right=t+s,e.bottom=n+i,e.width=s,e.height=i}function Xc(e,t,n,s){const i=n.padding;let{x:o,y:r}=t;for(const a of e){const l=a.box,c=s[a.stack]||{placed:0,weight:1},u=a.stackWeight/c.weight||1;if(a.horizontal){const f=t.w*u,h=c.size||l.height;ni(c.start)&&(r=c.start),l.fullSize?ki(l,i.left,r,n.outerWidth-i.right-i.left,h):ki(l,t.left+c.placed,r,f,h),c.start=r,c.placed+=f,r=l.bottom}else{const f=t.h*u,h=c.size||l.width;ni(c.start)&&(o=c.start),l.fullSize?ki(l,o,i.top,h,n.outerHeight-i.bottom-i.top):ki(l,o,t.top+c.placed,h,f),c.start=o,c.placed+=f,o=l.right}}t.x=o,t.y=r}var Vt={addBox(e,t){e.boxes||(e.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(n){t.draw(n)}}]},e.boxes.push(t)},removeBox(e,t){const n=e.boxes?e.boxes.indexOf(t):-1;n!==-1&&e.boxes.splice(n,1)},configure(e,t,n){t.fullSize=n.fullSize,t.position=n.position,t.weight=n.weight},update(e,t,n,s){if(!e)return;const i=Wt(e.options.layout.padding),o=Math.max(t-i.width,0),r=Math.max(n-i.height,0),a=O0(e.boxes),l=a.vertical,c=a.horizontal;ht(e.boxes,g=>{typeof g.beforeLayout=="function"&&g.beforeLayout()});const u=l.reduce((g,m)=>m.box.options&&m.box.options.display===!1?g:g+1,0)||1,f=Object.freeze({outerWidth:t,outerHeight:n,padding:i,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/u,hBoxMaxHeight:r/2}),h=Object.assign({},i);Md(h,Wt(s));const d=Object.assign({maxPadding:h,w:o,h:r,x:i.left,y:i.top},i),p=A0(l.concat(c),f);Cs(a.fullSize,d,f,p),Cs(l,d,f,p),Cs(c,d,f,p)&&Cs(l,d,f,p),T0(d),Xc(a.leftAndTop,d,f,p),d.x+=d.w,d.y+=d.h,Xc(a.rightAndBottom,d,f,p),e.chartArea={left:d.left,top:d.top,right:d.left+d.w,bottom:d.top+d.h,height:d.h,width:d.w},ht(a.chartArea,g=>{const m=g.box;Object.assign(m,e.chartArea),m.update(d.w,d.h,{left:0,top:0,right:0,bottom:0})})}};class Cd{acquireContext(t,n){}releaseContext(t){return!1}addEventListener(t,n,s){}removeEventListener(t,n,s){}getDevicePixelRatio(){return 1}getMaximumSize(t,n,s,i){return n=Math.max(0,n||t.width),s=s||t.height,{width:n,height:Math.max(0,i?Math.floor(n/i):s)}}isAttached(t){return!0}updateConfig(t){}}class L0 extends Cd{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const Xi="$chartjs",F0={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},Gc=e=>e===null||e==="";function I0(e,t){const n=e.style,s=e.getAttribute("height"),i=e.getAttribute("width");if(e[Xi]={initial:{height:s,width:i,style:{display:n.display,height:n.height,width:n.width}}},n.display=n.display||"block",n.boxSizing=n.boxSizing||"border-box",Gc(i)){const o=Dc(e,"width");o!==void 0&&(e.width=o)}if(Gc(s))if(e.style.height==="")e.height=e.width/(t||2);else{const o=Dc(e,"height");o!==void 0&&(e.height=o)}return e}const Pd=Nx?{passive:!0}:!1;function N0(e,t,n){e&&e.addEventListener(t,n,Pd)}function B0(e,t,n){e&&e.canvas&&e.canvas.removeEventListener(t,n,Pd)}function z0(e,t){const n=F0[e.type]||e.type,{x:s,y:i}=En(e,t);return{type:n,chart:t,native:e,x:s!==void 0?s:null,y:i!==void 0?i:null}}function bo(e,t){for(const n of e)if(n===t||n.contains(t))return!0}function H0(e,t,n){const s=e.canvas,i=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||bo(a.addedNodes,s),r=r&&!bo(a.removedNodes,s);r&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}function j0(e,t,n){const s=e.canvas,i=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||bo(a.removedNodes,s),r=r&&!bo(a.addedNodes,s);r&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}const oi=new Map;let Jc=0;function Ed(){const e=window.devicePixelRatio;e!==Jc&&(Jc=e,oi.forEach((t,n)=>{n.currentDevicePixelRatio!==e&&t()}))}function V0(e,t){oi.size||window.addEventListener("resize",Ed),oi.set(e,t)}function W0(e){oi.delete(e),oi.size||window.removeEventListener("resize",Ed)}function $0(e,t,n){const s=e.canvas,i=s&&qa(s);if(!i)return;const o=nd((a,l)=>{const c=i.clientWidth;n(a,l),c<i.clientWidth&&n()},window),r=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,u=l.contentRect.height;c===0&&u===0||o(c,u)});return r.observe(i),V0(e,o),r}function yr(e,t,n){n&&n.disconnect(),t==="resize"&&W0(e)}function U0(e,t,n){const s=e.canvas,i=nd(o=>{e.ctx!==null&&n(z0(o,e))},e);return N0(s,t,i),i}class K0 extends Cd{acquireContext(t,n){const s=t&&t.getContext&&t.getContext("2d");return s&&s.canvas===t?(I0(t,n),s):null}releaseContext(t){const n=t.canvas;if(!n[Xi])return!1;const s=n[Xi].initial;["height","width"].forEach(o=>{const r=s[o];it(r)?n.removeAttribute(o):n.setAttribute(o,r)});const i=s.style||{};return Object.keys(i).forEach(o=>{n.style[o]=i[o]}),n.width=n.width,delete n[Xi],!0}addEventListener(t,n,s){this.removeEventListener(t,n);const i=t.$proxies||(t.$proxies={}),r={attach:H0,detach:j0,resize:$0}[n]||U0;i[n]=r(t,n,s)}removeEventListener(t,n){const s=t.$proxies||(t.$proxies={}),i=s[n];if(!i)return;({attach:yr,detach:yr,resize:yr}[n]||B0)(t,n,i),s[n]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,n,s,i){return Ix(t,n,s,i)}isAttached(t){const n=t&&qa(t);return!!(n&&n.isConnected)}}function q0(e){return!Ka()||typeof OffscreenCanvas<"u"&&e instanceof OffscreenCanvas?L0:K0}var Fi;let Je=(Fi=class{constructor(){N(this,"x");N(this,"y");N(this,"active",!1);N(this,"options");N(this,"$animations")}tooltipPosition(t){const{x:n,y:s}=this.getProps(["x","y"],t);return{x:n,y:s}}hasValue(){return rs(this.x)&&rs(this.y)}getProps(t,n){const s=this.$animations;if(!n||!s)return this;const i={};return t.forEach(o=>{i[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),i}},N(Fi,"defaults",{}),N(Fi,"defaultRoutes"),Fi);function Y0(e,t){const n=e.options.ticks,s=X0(e),i=Math.min(n.maxTicksLimit||s,s),o=n.major.enabled?J0(t):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>i)return Q0(t,c,o,r/i),c;const u=G0(o,t,i);if(r>0){let f,h;const d=r>1?Math.round((l-a)/(r-1)):null;for(Ai(t,c,u,it(d)?0:a-d,a),f=0,h=r-1;f<h;f++)Ai(t,c,u,o[f],o[f+1]);return Ai(t,c,u,l,it(d)?t.length:l+d),c}return Ai(t,c,u),c}function X0(e){const t=e.options.offset,n=e._tickSize(),s=e._length/n+(t?0:1),i=e._maxLength/n;return Math.floor(Math.min(s,i))}function G0(e,t,n){const s=Z0(e),i=t.length/n;if(!s)return Math.max(i,1);const o=zy(s);for(let r=0,a=o.length-1;r<a;r++){const l=o[r];if(l>i)return l}return Math.max(i,1)}function J0(e){const t=[];let n,s;for(n=0,s=e.length;n<s;n++)e[n].major&&t.push(n);return t}function Q0(e,t,n,s){let i=0,o=n[0],r;for(s=Math.ceil(s),r=0;r<e.length;r++)r===o&&(t.push(e[r]),i++,o=n[i*s])}function Ai(e,t,n,s,i){const o=st(s,0),r=Math.min(st(i,e.length),e.length);let a=0,l,c,u;for(n=Math.ceil(n),i&&(l=i-s,n=l/Math.floor(l/n)),u=o;u<0;)a++,u=Math.round(o+a*n);for(c=Math.max(o,0);c<r;c++)c===u&&(t.push(e[c]),a++,u=Math.round(o+a*n))}function Z0(e){const t=e.length;let n,s;if(t<2)return!1;for(s=e[0],n=1;n<t;++n)if(e[n]-e[n-1]!==s)return!1;return s}const tv=e=>e==="left"?"right":e==="right"?"left":e,Qc=(e,t,n)=>t==="top"||t==="left"?e[t]+n:e[t]-n,Zc=(e,t)=>Math.min(t||e,e);function tu(e,t){const n=[],s=e.length/t,i=e.length;let o=0;for(;o<i;o+=s)n.push(e[Math.floor(o)]);return n}function ev(e,t,n){const s=e.ticks.length,i=Math.min(t,s-1),o=e._startPixel,r=e._endPixel,a=1e-6;let l=e.getPixelForTick(i),c;if(!(n&&(s===1?c=Math.max(l-o,r-l):t===0?c=(e.getPixelForTick(1)-l)/2:c=(l-e.getPixelForTick(i-1))/2,l+=i<t?c:-c,l<o-a||l>r+a)))return l}function nv(e,t){ht(e,n=>{const s=n.gc,i=s.length/2;let o;if(i>t){for(o=0;o<i;++o)delete n.data[s[o]];s.splice(0,i)}})}function xs(e){return e.drawTicks?e.tickLength:0}function eu(e,t){if(!e.display)return 0;const n=Tt(e.font,t),s=Wt(e.padding);return(wt(e.text)?e.text.length:1)*n.lineHeight+s.height}function sv(e,t){return _n(e,{scale:t,type:"scale"})}function iv(e,t,n){return _n(e,{tick:n,index:t,type:"tick"})}function ov(e,t,n){let s=Ha(e);return(n&&t!=="right"||!n&&t==="right")&&(s=tv(s)),s}function rv(e,t,n,s){const{top:i,left:o,bottom:r,right:a,chart:l}=e,{chartArea:c,scales:u}=l;let f=0,h,d,p;const g=r-i,m=a-o;if(e.isHorizontal()){if(d=Nt(s,o,a),ot(n)){const _=Object.keys(n)[0],x=n[_];p=u[_].getPixelForValue(x)+g-t}else n==="center"?p=(c.bottom+c.top)/2+g-t:p=Qc(e,n,t);h=a-o}else{if(ot(n)){const _=Object.keys(n)[0],x=n[_];d=u[_].getPixelForValue(x)-m+t}else n==="center"?d=(c.left+c.right)/2-m+t:d=Qc(e,n,t);p=Nt(s,r,i),f=n==="left"?-Et:Et}return{titleX:d,titleY:p,maxWidth:h,rotation:f}}class jn extends Je{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,n){return t}getUserBounds(){let{_userMin:t,_userMax:n,_suggestedMin:s,_suggestedMax:i}=this;return t=ie(t,Number.POSITIVE_INFINITY),n=ie(n,Number.NEGATIVE_INFINITY),s=ie(s,Number.POSITIVE_INFINITY),i=ie(i,Number.NEGATIVE_INFINITY),{min:ie(t,s),max:ie(n,i),minDefined:Mt(t),maxDefined:Mt(n)}}getMinMax(t){let{min:n,max:s,minDefined:i,maxDefined:o}=this.getUserBounds(),r;if(i&&o)return{min:n,max:s};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,t),i||(n=Math.min(n,r.min)),o||(s=Math.max(s,r.max));return n=o&&n>s?s:n,s=i&&n>s?n:s,{min:ie(n,ie(s,n)),max:ie(s,ie(n,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){yt(this.options.beforeUpdate,[this])}update(t,n,s){const{beginAtZero:i,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=n,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=px(this,o,i),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?tu(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=Y0(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,n,s;this.isHorizontal()?(n=this.left,s=this.right):(n=this.top,s=this.bottom,t=!t),this._startPixel=n,this._endPixel=s,this._reversePixels=t,this._length=s-n,this._alignToPixels=this.options.alignToPixels}afterUpdate(){yt(this.options.afterUpdate,[this])}beforeSetDimensions(){yt(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){yt(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),yt(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){yt(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const n=this.options.ticks;let s,i,o;for(s=0,i=t.length;s<i;s++)o=t[s],o.label=yt(n.callback,[o.value,s,t],this)}afterTickToLabelConversion(){yt(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){yt(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,n=t.ticks,s=Zc(this.ticks.length,t.ticks.maxTicksLimit),i=n.minRotation||0,o=n.maxRotation;let r=i,a,l,c;if(!this._isVisible()||!n.display||i>=o||s<=1||!this.isHorizontal()){this.labelRotation=i;return}const u=this._getLabelSizes(),f=u.widest.width,h=u.highest.height,d=Ft(this.chart.width-f,0,this.maxWidth);a=t.offset?this.maxWidth/s:d/(s-1),f+6>a&&(a=d/(s-(t.offset?.5:1)),l=this.maxHeight-xs(t.grid)-n.padding-eu(t.title,this.chart.options.font),c=Math.sqrt(f*f+h*h),r=Ba(Math.min(Math.asin(Ft((u.highest.height+6)/a,-1,1)),Math.asin(Ft(l/c,-1,1))-Math.asin(Ft(h/c,-1,1)))),r=Math.max(i,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){yt(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){yt(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:n,options:{ticks:s,title:i,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){const l=eu(i,n.options.font);if(a?(t.width=this.maxWidth,t.height=xs(o)+l):(t.height=this.maxHeight,t.width=xs(o)+l),s.display&&this.ticks.length){const{first:c,last:u,widest:f,highest:h}=this._getLabelSizes(),d=s.padding*2,p=pe(this.labelRotation),g=Math.cos(p),m=Math.sin(p);if(a){const _=s.mirror?0:m*f.width+g*h.height;t.height=Math.min(this.maxHeight,t.height+_+d)}else{const _=s.mirror?0:g*f.width+m*h.height;t.width=Math.min(this.maxWidth,t.width+_+d)}this._calculatePadding(c,u,m,g)}}this._handleMargins(),a?(this.width=this._length=n.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=n.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,n,s,i){const{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const u=this.getPixelForTick(0)-this.left,f=this.right-this.getPixelForTick(this.ticks.length-1);let h=0,d=0;l?c?(h=i*t.width,d=s*n.height):(h=s*t.height,d=i*n.width):o==="start"?d=n.width:o==="end"?h=t.width:o!=="inner"&&(h=t.width/2,d=n.width/2),this.paddingLeft=Math.max((h-u+r)*this.width/(this.width-u),0),this.paddingRight=Math.max((d-f+r)*this.width/(this.width-f),0)}else{let u=n.height/2,f=t.height/2;o==="start"?(u=0,f=t.height):o==="end"&&(u=n.height,f=0),this.paddingTop=u+r,this.paddingBottom=f+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){yt(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:n}=this.options;return n==="top"||n==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let n,s;for(n=0,s=t.length;n<s;n++)it(t[n].label)&&(t.splice(n,1),s--,n--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const n=this.options.ticks.sampleSize;let s=this.ticks;n<s.length&&(s=tu(s,n)),this._labelSizes=t=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,n,s){const{ctx:i,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(n/Zc(n,s));let c=0,u=0,f,h,d,p,g,m,_,x,v,w,S;for(f=0;f<n;f+=l){if(p=t[f].label,g=this._resolveTickFontOptions(f),i.font=m=g.string,_=o[m]=o[m]||{data:{},gc:[]},x=g.lineHeight,v=w=0,!it(p)&&!wt(p))v=go(i,_.data,_.gc,v,p),w=x;else if(wt(p))for(h=0,d=p.length;h<d;++h)S=p[h],!it(S)&&!wt(S)&&(v=go(i,_.data,_.gc,v,S),w+=x);r.push(v),a.push(w),c=Math.max(v,c),u=Math.max(w,u)}nv(o,n);const A=r.indexOf(c),k=a.indexOf(u),E=P=>({width:r[P]||0,height:a[P]||0});return{first:E(0),last:E(n-1),widest:E(A),highest:E(k),widths:r,heights:a}}getLabelForValue(t){return t}getPixelForValue(t,n){return NaN}getValueForPixel(t){}getPixelForTick(t){const n=this.ticks;return t<0||t>n.length-1?null:this.getPixelForValue(n[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const n=this._startPixel+t*this._length;return Wy(this._alignToPixels?Sn(this.chart,n,0):n)}getDecimalForPixel(t){const n=(t-this._startPixel)/this._length;return this._reversePixels?1-n:n}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:n}=this;return t<0&&n<0?n:t>0&&n>0?t:0}getContext(t){const n=this.ticks||[];if(t>=0&&t<n.length){const s=n[t];return s.$context||(s.$context=iv(this.getContext(),t,s))}return this.$context||(this.$context=sv(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,n=pe(this.labelRotation),s=Math.abs(Math.cos(n)),i=Math.abs(Math.sin(n)),o=this._getLabelSizes(),r=t.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*s>a*i?a/s:l/i:l*i<a*s?l/s:a/i}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const n=this.axis,s=this.chart,i=this.options,{grid:o,position:r,border:a}=i,l=o.offset,c=this.isHorizontal(),f=this.ticks.length+(l?1:0),h=xs(o),d=[],p=a.setContext(this.getContext()),g=p.display?p.width:0,m=g/2,_=function(G){return Sn(s,G,g)};let x,v,w,S,A,k,E,P,I,H,L,X;if(r==="top")x=_(this.bottom),k=this.bottom-h,P=x-m,H=_(t.top)+m,X=t.bottom;else if(r==="bottom")x=_(this.top),H=t.top,X=_(t.bottom)-m,k=x+m,P=this.top+h;else if(r==="left")x=_(this.right),A=this.right-h,E=x-m,I=_(t.left)+m,L=t.right;else if(r==="right")x=_(this.left),I=t.left,L=_(t.right)-m,A=x+m,E=this.left+h;else if(n==="x"){if(r==="center")x=_((t.top+t.bottom)/2+.5);else if(ot(r)){const G=Object.keys(r)[0],U=r[G];x=_(this.chart.scales[G].getPixelForValue(U))}H=t.top,X=t.bottom,k=x+m,P=k+h}else if(n==="y"){if(r==="center")x=_((t.left+t.right)/2);else if(ot(r)){const G=Object.keys(r)[0],U=r[G];x=_(this.chart.scales[G].getPixelForValue(U))}A=x-m,E=A-h,I=t.left,L=t.right}const rt=st(i.ticks.maxTicksLimit,f),Z=Math.max(1,Math.ceil(f/rt));for(v=0;v<f;v+=Z){const G=this.getContext(v),U=o.setContext(G),et=a.setContext(G),mt=U.lineWidth,$t=U.color,Ut=et.dash||[],Pt=et.dashOffset,ae=U.tickWidth,Yt=U.tickColor,ve=U.tickBorderDash||[],At=U.tickBorderDashOffset;w=ev(this,v,l),w!==void 0&&(S=Sn(s,w,mt),c?A=E=I=L=S:k=P=H=X=S,d.push({tx1:A,ty1:k,tx2:E,ty2:P,x1:I,y1:H,x2:L,y2:X,width:mt,color:$t,borderDash:Ut,borderDashOffset:Pt,tickWidth:ae,tickColor:Yt,tickBorderDash:ve,tickBorderDashOffset:At}))}return this._ticksLength=f,this._borderValue=x,d}_computeLabelItems(t){const n=this.axis,s=this.options,{position:i,ticks:o}=s,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:u,mirror:f}=o,h=xs(s.grid),d=h+u,p=f?-u:d,g=-pe(this.labelRotation),m=[];let _,x,v,w,S,A,k,E,P,I,H,L,X="middle";if(i==="top")A=this.bottom-p,k=this._getXAxisLabelAlignment();else if(i==="bottom")A=this.top+p,k=this._getXAxisLabelAlignment();else if(i==="left"){const Z=this._getYAxisLabelAlignment(h);k=Z.textAlign,S=Z.x}else if(i==="right"){const Z=this._getYAxisLabelAlignment(h);k=Z.textAlign,S=Z.x}else if(n==="x"){if(i==="center")A=(t.top+t.bottom)/2+d;else if(ot(i)){const Z=Object.keys(i)[0],G=i[Z];A=this.chart.scales[Z].getPixelForValue(G)+d}k=this._getXAxisLabelAlignment()}else if(n==="y"){if(i==="center")S=(t.left+t.right)/2-d;else if(ot(i)){const Z=Object.keys(i)[0],G=i[Z];S=this.chart.scales[Z].getPixelForValue(G)}k=this._getYAxisLabelAlignment(h).textAlign}n==="y"&&(l==="start"?X="top":l==="end"&&(X="bottom"));const rt=this._getLabelSizes();for(_=0,x=a.length;_<x;++_){v=a[_],w=v.label;const Z=o.setContext(this.getContext(_));E=this.getPixelForTick(_)+o.labelOffset,P=this._resolveTickFontOptions(_),I=P.lineHeight,H=wt(w)?w.length:1;const G=H/2,U=Z.color,et=Z.textStrokeColor,mt=Z.textStrokeWidth;let $t=k;r?(S=E,k==="inner"&&(_===x-1?$t=this.options.reverse?"left":"right":_===0?$t=this.options.reverse?"right":"left":$t="center"),i==="top"?c==="near"||g!==0?L=-H*I+I/2:c==="center"?L=-rt.highest.height/2-G*I+I:L=-rt.highest.height+I/2:c==="near"||g!==0?L=I/2:c==="center"?L=rt.highest.height/2-G*I:L=rt.highest.height-H*I,f&&(L*=-1),g!==0&&!Z.showLabelBackdrop&&(S+=I/2*Math.sin(g))):(A=E,L=(1-H)*I/2);let Ut;if(Z.showLabelBackdrop){const Pt=Wt(Z.backdropPadding),ae=rt.heights[_],Yt=rt.widths[_];let ve=L-Pt.top,At=0-Pt.left;switch(X){case"middle":ve-=ae/2;break;case"bottom":ve-=ae;break}switch(k){case"center":At-=Yt/2;break;case"right":At-=Yt;break;case"inner":_===x-1?At-=Yt:_>0&&(At-=Yt/2);break}Ut={left:At,top:ve,width:Yt+Pt.width,height:ae+Pt.height,color:Z.backdropColor}}m.push({label:w,font:P,textOffset:L,options:{rotation:g,color:U,strokeColor:et,strokeWidth:mt,textAlign:$t,textBaseline:X,translation:[S,A],backdrop:Ut}})}return m}_getXAxisLabelAlignment(){const{position:t,ticks:n}=this.options;if(-pe(this.labelRotation))return t==="top"?"left":"right";let i="center";return n.align==="start"?i="left":n.align==="end"?i="right":n.align==="inner"&&(i="inner"),i}_getYAxisLabelAlignment(t){const{position:n,ticks:{crossAlign:s,mirror:i,padding:o}}=this.options,r=this._getLabelSizes(),a=t+o,l=r.widest.width;let c,u;return n==="left"?i?(u=this.right+o,s==="near"?c="left":s==="center"?(c="center",u+=l/2):(c="right",u+=l)):(u=this.right-a,s==="near"?c="right":s==="center"?(c="center",u-=l/2):(c="left",u=this.left)):n==="right"?i?(u=this.left+o,s==="near"?c="right":s==="center"?(c="center",u-=l/2):(c="left",u-=l)):(u=this.left+a,s==="near"?c="left":s==="center"?(c="center",u+=l/2):(c="right",u=this.right)):c="right",{textAlign:c,x:u}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,n=this.options.position;if(n==="left"||n==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(n==="top"||n==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:n},left:s,top:i,width:o,height:r}=this;n&&(t.save(),t.fillStyle=n,t.fillRect(s,i,o,r),t.restore())}getLineWidthForValue(t){const n=this.options.grid;if(!this._isVisible()||!n.display)return 0;const i=this.ticks.findIndex(o=>o.value===t);return i>=0?n.setContext(this.getContext(i)).lineWidth:0}drawGrid(t){const n=this.options.grid,s=this.ctx,i=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,r;const a=(l,c,u)=>{!u.width||!u.color||(s.save(),s.lineWidth=u.width,s.strokeStyle=u.color,s.setLineDash(u.borderDash||[]),s.lineDashOffset=u.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(n.display)for(o=0,r=i.length;o<r;++o){const l=i[o];n.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),n.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:n,options:{border:s,grid:i}}=this,o=s.setContext(this.getContext()),r=s.display?o.width:0;if(!r)return;const a=i.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,u,f,h;this.isHorizontal()?(c=Sn(t,this.left,r)-r/2,u=Sn(t,this.right,a)+a/2,f=h=l):(f=Sn(t,this.top,r)-r/2,h=Sn(t,this.bottom,a)+a/2,c=u=l),n.save(),n.lineWidth=o.width,n.strokeStyle=o.color,n.beginPath(),n.moveTo(c,f),n.lineTo(u,h),n.stroke(),n.restore()}drawLabels(t){if(!this.options.ticks.display)return;const s=this.ctx,i=this._computeLabelArea();i&&Vo(s,i);const o=this.getLabelItems(t);for(const r of o){const a=r.options,l=r.font,c=r.label,u=r.textOffset;Hn(s,c,0,u,l,a)}i&&Wo(s)}drawTitle(){const{ctx:t,options:{position:n,title:s,reverse:i}}=this;if(!s.display)return;const o=Tt(s.font),r=Wt(s.padding),a=s.align;let l=o.lineHeight/2;n==="bottom"||n==="center"||ot(n)?(l+=r.bottom,wt(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=r.top;const{titleX:c,titleY:u,maxWidth:f,rotation:h}=rv(this,l,n,a);Hn(t,s.text,0,0,o,{color:s.color,maxWidth:f,rotation:h,textAlign:ov(a,n,i),textBaseline:"middle",translation:[c,u]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,n=t.ticks&&t.ticks.z||0,s=st(t.grid&&t.grid.z,-1),i=st(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==jn.prototype.draw?[{z:n,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:i,draw:()=>{this.drawBorder()}},{z:n,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const n=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",i=[];let o,r;for(o=0,r=n.length;o<r;++o){const a=n[o];a[s]===this.id&&(!t||a.type===t)&&i.push(a)}return i}_resolveTickFontOptions(t){const n=this.options.ticks.setContext(this.getContext(t));return Tt(n.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class Oi{constructor(t,n,s){this.type=t,this.scope=n,this.override=s,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const n=Object.getPrototypeOf(t);let s;cv(n)&&(s=this.register(n));const i=this.items,o=t.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in i||(i[o]=t,av(t,r,s),this.override&&St.override(t.id,t.overrides)),r}get(t){return this.items[t]}unregister(t){const n=this.items,s=t.id,i=this.scope;s in n&&delete n[s],i&&s in St[i]&&(delete St[i][s],this.override&&delete zn[s])}}function av(e,t,n){const s=ei(Object.create(null),[n?St.get(n):{},St.get(t),e.defaults]);St.set(t,s),e.defaultRoutes&&lv(t,e.defaultRoutes),e.descriptors&&St.describe(t,e.descriptors)}function lv(e,t){Object.keys(t).forEach(n=>{const s=n.split("."),i=s.pop(),o=[e].concat(s).join("."),r=t[n].split("."),a=r.pop(),l=r.join(".");St.route(o,i,l,a)})}function cv(e){return"id"in e&&"defaults"in e}class uv{constructor(){this.controllers=new Oi(me,"datasets",!0),this.elements=new Oi(Je,"elements"),this.plugins=new Oi(Object,"plugins"),this.scales=new Oi(jn,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,n,s){[...n].forEach(i=>{const o=s||this._getRegistryForType(i);s||o.isForType(i)||o===this.plugins&&i.id?this._exec(t,o,i):ht(i,r=>{const a=s||this._getRegistryForType(r);this._exec(t,a,r)})})}_exec(t,n,s){const i=Na(t);yt(s["before"+i],[],s),n[t](s),yt(s["after"+i],[],s)}_getRegistryForType(t){for(let n=0;n<this._typedRegistries.length;n++){const s=this._typedRegistries[n];if(s.isForType(t))return s}return this.plugins}_get(t,n,s){const i=n.get(t);if(i===void 0)throw new Error('"'+t+'" is not a registered '+s+".");return i}}var Oe=new uv;class fv{constructor(){this._init=[]}notify(t,n,s,i){n==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=i?this._descriptors(t).filter(i):this._descriptors(t),r=this._notify(o,t,n,s);return n==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,n,s,i){i=i||{};for(const o of t){const r=o.plugin,a=r[s],l=[n,i,o.options];if(yt(a,l,r)===!1&&i.cancelable)return!1}return!0}invalidate(){it(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const n=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),n}_createDescriptors(t,n){const s=t&&t.config,i=st(s.options&&s.options.plugins,{}),o=hv(s);return i===!1&&!n?[]:pv(t,o,i,n)}_notifyStateChanges(t){const n=this._oldCache||[],s=this._cache,i=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(i(n,s),t,"stop"),this._notify(i(s,n),t,"start")}}function hv(e){const t={},n=[],s=Object.keys(Oe.plugins.items);for(let o=0;o<s.length;o++)n.push(Oe.getPlugin(s[o]));const i=e.plugins||[];for(let o=0;o<i.length;o++){const r=i[o];n.indexOf(r)===-1&&(n.push(r),t[r.id]=!0)}return{plugins:n,localIds:t}}function dv(e,t){return!t&&e===!1?null:e===!0?{}:e}function pv(e,{plugins:t,localIds:n},s,i){const o=[],r=e.getContext();for(const a of t){const l=a.id,c=dv(s[l],i);c!==null&&o.push({plugin:a,options:gv(e.config,{plugin:a,local:n[l]},c,r)})}return o}function gv(e,{plugin:t,local:n},s,i){const o=e.pluginScopeKeys(t),r=e.getOptionScopes(s,o);return n&&t.defaults&&r.push(t.defaults),e.createResolver(r,i,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Qr(e,t){const n=St.datasets[e]||{};return((t.datasets||{})[e]||{}).indexAxis||t.indexAxis||n.indexAxis||"x"}function mv(e,t){let n=e;return e==="_index_"?n=t:e==="_value_"&&(n=t==="x"?"y":"x"),n}function bv(e,t){return e===t?"_index_":"_value_"}function nu(e){if(e==="x"||e==="y"||e==="r")return e}function _v(e){if(e==="top"||e==="bottom")return"x";if(e==="left"||e==="right")return"y"}function Zr(e,...t){if(nu(e))return e;for(const n of t){const s=n.axis||_v(n.position)||e.length>1&&nu(e[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${e}' axis. Please provide 'axis' or 'position' option.`)}function su(e,t,n){if(n[t+"AxisID"]===e)return{axis:t}}function yv(e,t){if(t.data&&t.data.datasets){const n=t.data.datasets.filter(s=>s.xAxisID===e||s.yAxisID===e);if(n.length)return su(e,"x",n[0])||su(e,"y",n[0])}return{}}function xv(e,t){const n=zn[e.type]||{scales:{}},s=t.scales||{},i=Qr(e.type,t),o=Object.create(null);return Object.keys(s).forEach(r=>{const a=s[r];if(!ot(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);const l=Zr(r,a,yv(r,e),St.scales[a.type]),c=bv(l,i),u=n.scales||{};o[r]=Bs(Object.create(null),[{axis:l},a,u[l],u[c]])}),e.data.datasets.forEach(r=>{const a=r.type||e.type,l=r.indexAxis||Qr(a,t),u=(zn[a]||{}).scales||{};Object.keys(u).forEach(f=>{const h=mv(f,l),d=r[h+"AxisID"]||h;o[d]=o[d]||Object.create(null),Bs(o[d],[{axis:h},s[d],u[f]])})}),Object.keys(o).forEach(r=>{const a=o[r];Bs(a,[St.scales[a.type],St.scale])}),o}function kd(e){const t=e.options||(e.options={});t.plugins=st(t.plugins,{}),t.scales=xv(e,t)}function Ad(e){return e=e||{},e.datasets=e.datasets||[],e.labels=e.labels||[],e}function vv(e){return e=e||{},e.data=Ad(e.data),kd(e),e}const iu=new Map,Od=new Set;function Ri(e,t){let n=iu.get(e);return n||(n=t(),iu.set(e,n),Od.add(n)),n}const vs=(e,t,n)=>{const s=gn(t,n);s!==void 0&&e.add(s)};class wv{constructor(t){this._config=vv(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=Ad(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),kd(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return Ri(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,n){return Ri(`${t}.transition.${n}`,()=>[[`datasets.${t}.transitions.${n}`,`transitions.${n}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,n){return Ri(`${t}-${n}`,()=>[[`datasets.${t}.elements.${n}`,`datasets.${t}`,`elements.${n}`,""]])}pluginScopeKeys(t){const n=t.id,s=this.type;return Ri(`${s}-plugin-${n}`,()=>[[`plugins.${n}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,n){const s=this._scopeCache;let i=s.get(t);return(!i||n)&&(i=new Map,s.set(t,i)),i}getOptionScopes(t,n,s){const{options:i,type:o}=this,r=this._cachedScopes(t,s),a=r.get(n);if(a)return a;const l=new Set;n.forEach(u=>{t&&(l.add(t),u.forEach(f=>vs(l,t,f))),u.forEach(f=>vs(l,i,f)),u.forEach(f=>vs(l,zn[o]||{},f)),u.forEach(f=>vs(l,St,f)),u.forEach(f=>vs(l,Xr,f))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),Od.has(n)&&r.set(n,c),c}chartOptionScopes(){const{options:t,type:n}=this;return[t,zn[n]||{},St.datasets[n]||{},{type:n},St,Xr]}resolveNamedOptions(t,n,s,i=[""]){const o={$shared:!0},{resolver:r,subPrefixes:a}=ou(this._resolverCache,t,i);let l=r;if(Mv(r,n)){o.$shared=!1,s=mn(s)?s():s;const c=this.createResolver(t,s,a);l=as(r,s,c)}for(const c of n)o[c]=l[c];return o}createResolver(t,n,s=[""],i){const{resolver:o}=ou(this._resolverCache,t,s);return ot(n)?as(o,n,void 0,i):o}}function ou(e,t,n){let s=e.get(t);s||(s=new Map,e.set(t,s));const i=n.join();let o=s.get(i);return o||(o={resolver:Wa(t,n),subPrefixes:n.filter(a=>!a.toLowerCase().includes("hover"))},s.set(i,o)),o}const Sv=e=>ot(e)&&Object.getOwnPropertyNames(e).some(t=>mn(e[t]));function Mv(e,t){const{isScriptable:n,isIndexable:s}=ld(e);for(const i of t){const o=n(i),r=s(i),a=(r||o)&&e[i];if(o&&(mn(a)||Sv(a))||r&&wt(a))return!0}return!1}var Cv="4.4.9";const Pv=["top","bottom","left","right","chartArea"];function ru(e,t){return e==="top"||e==="bottom"||Pv.indexOf(e)===-1&&t==="x"}function au(e,t){return function(n,s){return n[e]===s[e]?n[t]-s[t]:n[e]-s[e]}}function lu(e){const t=e.chart,n=t.options.animation;t.notifyPlugins("afterRender"),yt(n&&n.onComplete,[e],t)}function Ev(e){const t=e.chart,n=t.options.animation;yt(n&&n.onProgress,[e],t)}function Rd(e){return Ka()&&typeof e=="string"?e=document.getElementById(e):e&&e.length&&(e=e[0]),e&&e.canvas&&(e=e.canvas),e}const Gi={},cu=e=>{const t=Rd(e);return Object.values(Gi).filter(n=>n.canvas===t).pop()};function kv(e,t,n){const s=Object.keys(e);for(const i of s){const o=+i;if(o>=t){const r=e[i];delete e[i],(n>0||o>t)&&(e[o+n]=r)}}}function Av(e,t,n,s){return!n||e.type==="mouseout"?null:s?t:e}class An{static register(...t){Oe.add(...t),uu()}static unregister(...t){Oe.remove(...t),uu()}constructor(t,n){const s=this.config=new wv(n),i=Rd(t),o=cu(i);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const r=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||q0(i)),this.platform.updateConfig(s);const a=this.platform.acquireContext(i,r.aspectRatio),l=a&&a.canvas,c=l&&l.height,u=l&&l.width;if(this.id=Oy(),this.ctx=a,this.canvas=l,this.width=u,this.height=c,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new fv,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=qy(f=>this.update(f),r.resizeDelay||0),this._dataChanges=[],Gi[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}Be.listen(this,"complete",lu),Be.listen(this,"progress",Ev),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:n},width:s,height:i,_aspectRatio:o}=this;return it(t)?n&&o?o:i?s/i:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return Oe}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Tc(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Ac(this.canvas,this.ctx),this}stop(){return Be.stop(this),this}resize(t,n){Be.running(this)?this._resizeBeforeDraw={width:t,height:n}:this._resize(t,n)}_resize(t,n){const s=this.options,i=this.canvas,o=s.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(i,t,n,o),a=s.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,Tc(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),yt(s.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const n=this.options.scales||{};ht(n,(s,i)=>{s.id=i})}buildOrUpdateScales(){const t=this.options,n=t.scales,s=this.scales,i=Object.keys(s).reduce((r,a)=>(r[a]=!1,r),{});let o=[];n&&(o=o.concat(Object.keys(n).map(r=>{const a=n[r],l=Zr(r,a),c=l==="r",u=l==="x";return{options:a,dposition:c?"chartArea":u?"bottom":"left",dtype:c?"radialLinear":u?"category":"linear"}}))),ht(o,r=>{const a=r.options,l=a.id,c=Zr(l,a),u=st(a.type,r.dtype);(a.position===void 0||ru(a.position,c)!==ru(r.dposition))&&(a.position=r.dposition),i[l]=!0;let f=null;if(l in s&&s[l].type===u)f=s[l];else{const h=Oe.getScale(u);f=new h({id:l,type:u,ctx:this.ctx,chart:this}),s[f.id]=f}f.init(a,t)}),ht(i,(r,a)=>{r||delete s[a]}),ht(s,r=>{Vt.configure(this,r,r.options),Vt.addBox(this,r)})}_updateMetasets(){const t=this._metasets,n=this.data.datasets.length,s=t.length;if(t.sort((i,o)=>i.index-o.index),s>n){for(let i=n;i<s;++i)this._destroyDatasetMeta(i);t.splice(n,s-n)}this._sortedMetasets=t.slice(0).sort(au("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:n}}=this;t.length>n.length&&delete this._stacks,t.forEach((s,i)=>{n.filter(o=>o===s._dataset).length===0&&this._destroyDatasetMeta(i)})}buildOrUpdateControllers(){const t=[],n=this.data.datasets;let s,i;for(this._removeUnreferencedMetasets(),s=0,i=n.length;s<i;s++){const o=n[s];let r=this.getDatasetMeta(s);const a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(s),r=this.getDatasetMeta(s)),r.type=a,r.indexAxis=o.indexAxis||Qr(a,this.options),r.order=o.order||0,r.index=s,r.label=""+o.label,r.visible=this.isDatasetVisible(s),r.controller)r.controller.updateIndex(s),r.controller.linkScales();else{const l=Oe.getController(a),{datasetElementType:c,dataElementType:u}=St.datasets[a];Object.assign(l,{dataElementType:Oe.getElement(u),datasetElementType:c&&Oe.getElement(c)}),r.controller=new l(this,s),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){ht(this.data.datasets,(t,n)=>{this.getDatasetMeta(n).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const n=this.config;n.update();const s=this._options=n.createResolver(n.chartOptionScopes(),this.getContext()),i=this._animationsDisabled=!s.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let c=0,u=this.data.datasets.length;c<u;c++){const{controller:f}=this.getDatasetMeta(c),h=!i&&o.indexOf(f)===-1;f.buildOrUpdateElements(h),r=Math.max(+f.getMaxOverflow(),r)}r=this._minPadding=s.layout.autoPadding?r:0,this._updateLayout(r),i||ht(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(au("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){ht(this.scales,t=>{Vt.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,n=new Set(Object.keys(this._listeners)),s=new Set(t.events);(!xc(n,s)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,n=this._getUniformDataChanges()||[];for(const{method:s,start:i,count:o}of n){const r=s==="_removeElements"?-o:o;kv(t,i,r)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const n=this.data.datasets.length,s=o=>new Set(t.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),i=s(0);for(let o=1;o<n;o++)if(!xc(i,s(o)))return;return Array.from(i).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;Vt.update(this,this.width,this.height,t);const n=this.chartArea,s=n.width<=0||n.height<=0;this._layers=[],ht(this.boxes,i=>{s&&i.position==="chartArea"||(i.configure&&i.configure(),this._layers.push(...i._layers()))},this),this._layers.forEach((i,o)=>{i._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let n=0,s=this.data.datasets.length;n<s;++n)this.getDatasetMeta(n).controller.configure();for(let n=0,s=this.data.datasets.length;n<s;++n)this._updateDataset(n,mn(t)?t({datasetIndex:n}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,n){const s=this.getDatasetMeta(t),i={meta:s,index:t,mode:n,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",i)!==!1&&(s.controller._update(n),i.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",i))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(Be.has(this)?this.attached&&!Be.running(this)&&Be.start(this):(this.draw(),lu({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:s,height:i}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(s,i)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const n=this._layers;for(t=0;t<n.length&&n[t].z<=0;++t)n[t].draw(this.chartArea);for(this._drawDatasets();t<n.length;++t)n[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const n=this._sortedMetasets,s=[];let i,o;for(i=0,o=n.length;i<o;++i){const r=n[i];(!t||r.visible)&&s.push(r)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let n=t.length-1;n>=0;--n)this._drawDataset(t[n]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const n=this.ctx,s={meta:t,index:t.index,cancelable:!0},i=yd(this,t);this.notifyPlugins("beforeDatasetDraw",s)!==!1&&(i&&Vo(n,i),t.controller.draw(),i&&Wo(n),s.cancelable=!1,this.notifyPlugins("afterDatasetDraw",s))}isPointInArea(t){return Ke(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,n,s,i){const o=P0.modes[n];return typeof o=="function"?o(this,t,s,i):[]}getDatasetMeta(t){const n=this.data.datasets[t],s=this._metasets;let i=s.filter(o=>o&&o._dataset===n).pop();return i||(i={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:n&&n.order||0,index:t,_dataset:n,_parsed:[],_sorted:!1},s.push(i)),i}getContext(){return this.$context||(this.$context=_n(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const n=this.data.datasets[t];if(!n)return!1;const s=this.getDatasetMeta(t);return typeof s.hidden=="boolean"?!s.hidden:!n.hidden}setDatasetVisibility(t,n){const s=this.getDatasetMeta(t);s.hidden=!n}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,n,s){const i=s?"show":"hide",o=this.getDatasetMeta(t),r=o.controller._resolveAnimations(void 0,i);ni(n)?(o.data[n].hidden=!s,this.update()):(this.setDatasetVisibility(t,s),r.update(o,{visible:s}),this.update(a=>a.datasetIndex===t?i:void 0))}hide(t,n){this._updateVisibility(t,n,!1)}show(t,n){this._updateVisibility(t,n,!0)}_destroyDatasetMeta(t){const n=this._metasets[t];n&&n.controller&&n.controller._destroy(),delete this._metasets[t]}_stop(){let t,n;for(this.stop(),Be.remove(this),t=0,n=this.data.datasets.length;t<n;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:n}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),Ac(t,n),this.platform.releaseContext(n),this.canvas=null,this.ctx=null),delete Gi[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,n=this.platform,s=(o,r)=>{n.addEventListener(this,o,r),t[o]=r},i=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};ht(this.options.events,o=>s(o,i))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,n=this.platform,s=(l,c)=>{n.addEventListener(this,l,c),t[l]=c},i=(l,c)=>{t[l]&&(n.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let r;const a=()=>{i("attach",a),this.attached=!0,this.resize(),s("resize",o),s("detach",r)};r=()=>{this.attached=!1,i("resize",o),this._stop(),this._resize(0,0),s("attach",a)},n.isAttached(this.canvas)?a():r()}unbindEvents(){ht(this._listeners,(t,n)=>{this.platform.removeEventListener(this,n,t)}),this._listeners={},ht(this._responsiveListeners,(t,n)=>{this.platform.removeEventListener(this,n,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,n,s){const i=s?"set":"remove";let o,r,a,l;for(n==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+i+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){r=t[a];const c=r&&this.getDatasetMeta(r.datasetIndex).controller;c&&c[i+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const n=this._active||[],s=t.map(({datasetIndex:o,index:r})=>{const a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!fo(s,n)&&(this._active=s,this._lastEvent=null,this._updateHoverStyles(s,n))}notifyPlugins(t,n,s){return this._plugins.notify(this,t,n,s)}isPluginEnabled(t){return this._plugins._cache.filter(n=>n.plugin.id===t).length===1}_updateHoverStyles(t,n,s){const i=this.options.hover,o=(l,c)=>l.filter(u=>!c.some(f=>u.datasetIndex===f.datasetIndex&&u.index===f.index)),r=o(n,t),a=s?t:o(t,n);r.length&&this.updateHoverStyle(r,i.mode,!1),a.length&&i.mode&&this.updateHoverStyle(a,i.mode,!0)}_eventHandler(t,n){const s={event:t,replay:n,cancelable:!0,inChartArea:this.isPointInArea(t)},i=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",s,i)===!1)return;const o=this._handleEvent(t,n,s.inChartArea);return s.cancelable=!1,this.notifyPlugins("afterEvent",s,i),(o||s.changed)&&this.render(),this}_handleEvent(t,n,s){const{_active:i=[],options:o}=this,r=n,a=this._getActiveElements(t,i,s,r),l=Iy(t),c=Av(t,this._lastEvent,s,l);s&&(this._lastEvent=null,yt(o.onHover,[t,a,this],this),l&&yt(o.onClick,[t,a,this],this));const u=!fo(a,i);return(u||n)&&(this._active=a,this._updateHoverStyles(a,i,n)),this._lastEvent=c,u}_getActiveElements(t,n,s,i){if(t.type==="mouseout")return[];if(!s)return n;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,i)}}N(An,"defaults",St),N(An,"instances",Gi),N(An,"overrides",zn),N(An,"registry",Oe),N(An,"version",Cv),N(An,"getChart",cu);function uu(){return ht(An.instances,e=>e._plugins.invalidate())}function Ov(e,t,n){const{startAngle:s,pixelMargin:i,x:o,y:r,outerRadius:a,innerRadius:l}=t;let c=i/a;e.beginPath(),e.arc(o,r,a,s-c,n+c),l>i?(c=i/l,e.arc(o,r,l,n+c,s-c,!0)):e.arc(o,r,i,n+Et,s-Et),e.closePath(),e.clip()}function Rv(e){return Va(e,["outerStart","outerEnd","innerStart","innerEnd"])}function Tv(e,t,n,s){const i=Rv(e.options.borderRadius),o=(n-t)/2,r=Math.min(o,s*t/2),a=l=>{const c=(n-Math.min(o,l))*s/2;return Ft(l,0,Math.min(o,c))};return{outerStart:a(i.outerStart),outerEnd:a(i.outerEnd),innerStart:Ft(i.innerStart,0,r),innerEnd:Ft(i.innerEnd,0,r)}}function Kn(e,t,n,s){return{x:n+e*Math.cos(t),y:s+e*Math.sin(t)}}function _o(e,t,n,s,i,o){const{x:r,y:a,startAngle:l,pixelMargin:c,innerRadius:u}=t,f=Math.max(t.outerRadius+s+n-c,0),h=u>0?u+s+n+c:0;let d=0;const p=i-l;if(s){const Z=u>0?u-s:0,G=f>0?f-s:0,U=(Z+G)/2,et=U!==0?p*U/(U+s):p;d=(p-et)/2}const g=Math.max(.001,p*f-n/vt)/f,m=(p-g)/2,_=l+m+d,x=i-m-d,{outerStart:v,outerEnd:w,innerStart:S,innerEnd:A}=Tv(t,h,f,x-_),k=f-v,E=f-w,P=_+v/k,I=x-w/E,H=h+S,L=h+A,X=_+S/H,rt=x-A/L;if(e.beginPath(),o){const Z=(P+I)/2;if(e.arc(r,a,f,P,Z),e.arc(r,a,f,Z,I),w>0){const mt=Kn(E,I,r,a);e.arc(mt.x,mt.y,w,I,x+Et)}const G=Kn(L,x,r,a);if(e.lineTo(G.x,G.y),A>0){const mt=Kn(L,rt,r,a);e.arc(mt.x,mt.y,A,x+Et,rt+Math.PI)}const U=(x-A/h+(_+S/h))/2;if(e.arc(r,a,h,x-A/h,U,!0),e.arc(r,a,h,U,_+S/h,!0),S>0){const mt=Kn(H,X,r,a);e.arc(mt.x,mt.y,S,X+Math.PI,_-Et)}const et=Kn(k,_,r,a);if(e.lineTo(et.x,et.y),v>0){const mt=Kn(k,P,r,a);e.arc(mt.x,mt.y,v,_-Et,P)}}else{e.moveTo(r,a);const Z=Math.cos(P)*f+r,G=Math.sin(P)*f+a;e.lineTo(Z,G);const U=Math.cos(I)*f+r,et=Math.sin(I)*f+a;e.lineTo(U,et)}e.closePath()}function Dv(e,t,n,s,i){const{fullCircles:o,startAngle:r,circumference:a}=t;let l=t.endAngle;if(o){_o(e,t,n,s,l,i);for(let c=0;c<o;++c)e.fill();isNaN(a)||(l=r+(a%xt||xt))}return _o(e,t,n,s,l,i),e.fill(),l}function Lv(e,t,n,s,i){const{fullCircles:o,startAngle:r,circumference:a,options:l}=t,{borderWidth:c,borderJoinStyle:u,borderDash:f,borderDashOffset:h}=l,d=l.borderAlign==="inner";if(!c)return;e.setLineDash(f||[]),e.lineDashOffset=h,d?(e.lineWidth=c*2,e.lineJoin=u||"round"):(e.lineWidth=c,e.lineJoin=u||"bevel");let p=t.endAngle;if(o){_o(e,t,n,s,p,i);for(let g=0;g<o;++g)e.stroke();isNaN(a)||(p=r+(a%xt||xt))}d&&Ov(e,t,p),o||(_o(e,t,n,s,p,i),e.stroke())}class Ps extends Je{constructor(n){super();N(this,"circumference");N(this,"endAngle");N(this,"fullCircles");N(this,"innerRadius");N(this,"outerRadius");N(this,"pixelMargin");N(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,n&&Object.assign(this,n)}inRange(n,s,i){const o=this.getProps(["x","y"],i),{angle:r,distance:a}=Qh(o,{x:n,y:s}),{startAngle:l,endAngle:c,innerRadius:u,outerRadius:f,circumference:h}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),d=(this.options.spacing+this.options.borderWidth)/2,p=st(h,c-l),g=si(r,l,c)&&l!==c,m=p>=xt||g,_=$e(a,u+d,f+d);return m&&_}getCenterPoint(n){const{x:s,y:i,startAngle:o,endAngle:r,innerRadius:a,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],n),{offset:c,spacing:u}=this.options,f=(o+r)/2,h=(a+l+u+c)/2;return{x:s+Math.cos(f)*h,y:i+Math.sin(f)*h}}tooltipPosition(n){return this.getCenterPoint(n)}draw(n){const{options:s,circumference:i}=this,o=(s.offset||0)/4,r=(s.spacing||0)/2,a=s.circular;if(this.pixelMargin=s.borderAlign==="inner"?.33:0,this.fullCircles=i>xt?Math.floor(i/xt):0,i===0||this.innerRadius<0||this.outerRadius<0)return;n.save();const l=(this.startAngle+this.endAngle)/2;n.translate(Math.cos(l)*o,Math.sin(l)*o);const c=1-Math.sin(Math.min(vt,i||0)),u=o*c;n.fillStyle=s.backgroundColor,n.strokeStyle=s.borderColor,Dv(n,this,u,r,a),Lv(n,this,u,r,a),n.restore()}}N(Ps,"id","arc"),N(Ps,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0}),N(Ps,"defaultRoutes",{backgroundColor:"backgroundColor"}),N(Ps,"descriptors",{_scriptable:!0,_indexable:n=>n!=="borderDash"});function Td(e,t,n=t){e.lineCap=st(n.borderCapStyle,t.borderCapStyle),e.setLineDash(st(n.borderDash,t.borderDash)),e.lineDashOffset=st(n.borderDashOffset,t.borderDashOffset),e.lineJoin=st(n.borderJoinStyle,t.borderJoinStyle),e.lineWidth=st(n.borderWidth,t.borderWidth),e.strokeStyle=st(n.borderColor,t.borderColor)}function Fv(e,t,n){e.lineTo(n.x,n.y)}function Iv(e){return e.stepped?ox:e.tension||e.cubicInterpolationMode==="monotone"?rx:Fv}function Dd(e,t,n={}){const s=e.length,{start:i=0,end:o=s-1}=n,{start:r,end:a}=t,l=Math.max(i,r),c=Math.min(o,a),u=i<r&&o<r||i>a&&o>a;return{count:s,start:l,loop:t.loop,ilen:c<l&&!u?s+c-l:c-l}}function Nv(e,t,n,s){const{points:i,options:o}=t,{count:r,start:a,loop:l,ilen:c}=Dd(i,n,s),u=Iv(o);let{move:f=!0,reverse:h}=s||{},d,p,g;for(d=0;d<=c;++d)p=i[(a+(h?c-d:d))%r],!p.skip&&(f?(e.moveTo(p.x,p.y),f=!1):u(e,g,p,h,o.stepped),g=p);return l&&(p=i[(a+(h?c:0))%r],u(e,g,p,h,o.stepped)),!!l}function Bv(e,t,n,s){const i=t.points,{count:o,start:r,ilen:a}=Dd(i,n,s),{move:l=!0,reverse:c}=s||{};let u=0,f=0,h,d,p,g,m,_;const x=w=>(r+(c?a-w:w))%o,v=()=>{g!==m&&(e.lineTo(u,m),e.lineTo(u,g),e.lineTo(u,_))};for(l&&(d=i[x(0)],e.moveTo(d.x,d.y)),h=0;h<=a;++h){if(d=i[x(h)],d.skip)continue;const w=d.x,S=d.y,A=w|0;A===p?(S<g?g=S:S>m&&(m=S),u=(f*u+w)/++f):(v(),e.lineTo(w,S),p=A,f=0,g=m=S),_=S}v()}function ta(e){const t=e.options,n=t.borderDash&&t.borderDash.length;return!e._decimated&&!e._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!n?Bv:Nv}function zv(e){return e.stepped?Bx:e.tension||e.cubicInterpolationMode==="monotone"?zx:kn}function Hv(e,t,n,s){let i=t._path;i||(i=t._path=new Path2D,t.path(i,n,s)&&i.closePath()),Td(e,t.options),e.stroke(i)}function jv(e,t,n,s){const{segments:i,options:o}=t,r=ta(t);for(const a of i)Td(e,o,a.style),e.beginPath(),r(e,t,a,{start:n,end:n+s-1})&&e.closePath(),e.stroke()}const Vv=typeof Path2D=="function";function Wv(e,t,n,s){Vv&&!t.options.segment?Hv(e,t,n,s):jv(e,t,n,s)}class cn extends Je{constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,n){const s=this.options;if((s.tension||s.cubicInterpolationMode==="monotone")&&!s.stepped&&!this._pointsUpdated){const i=s.spanGaps?this._loop:this._fullLoop;Ox(this._points,s,t,i,n),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=Ux(this,this.options.segment))}first(){const t=this.segments,n=this.points;return t.length&&n[t[0].start]}last(){const t=this.segments,n=this.points,s=t.length;return s&&n[t[s-1].end]}interpolate(t,n){const s=this.options,i=t[n],o=this.points,r=_d(this,{property:n,start:i,end:i});if(!r.length)return;const a=[],l=zv(s);let c,u;for(c=0,u=r.length;c<u;++c){const{start:f,end:h}=r[c],d=o[f],p=o[h];if(d===p){a.push(d);continue}const g=Math.abs((i-d[n])/(p[n]-d[n])),m=l(d,p,g,s.stepped);m[n]=t[n],a.push(m)}return a.length===1?a[0]:a}pathSegment(t,n,s){return ta(this)(t,this,n,s)}path(t,n,s){const i=this.segments,o=ta(this);let r=this._loop;n=n||0,s=s||this.points.length-n;for(const a of i)r&=o(t,this,a,{start:n,end:n+s-1});return!!r}draw(t,n,s,i){const o=this.options||{};(this.points||[]).length&&o.borderWidth&&(t.save(),Wv(t,this,s,i),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}N(cn,"id","line"),N(cn,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),N(cn,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),N(cn,"descriptors",{_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"});function fu(e,t,n,s){const i=e.options,{[n]:o}=e.getProps([n],s);return Math.abs(t-o)<i.radius+i.hitRadius}class Ji extends Je{constructor(n){super();N(this,"parsed");N(this,"skip");N(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,n&&Object.assign(this,n)}inRange(n,s,i){const o=this.options,{x:r,y:a}=this.getProps(["x","y"],i);return Math.pow(n-r,2)+Math.pow(s-a,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(n,s){return fu(this,n,"x",s)}inYRange(n,s){return fu(this,n,"y",s)}getCenterPoint(n){const{x:s,y:i}=this.getProps(["x","y"],n);return{x:s,y:i}}size(n){n=n||this.options||{};let s=n.radius||0;s=Math.max(s,s&&n.hoverRadius||0);const i=s&&n.borderWidth||0;return(s+i)*2}draw(n,s){const i=this.options;this.skip||i.radius<.1||!Ke(this,s,this.size(i)/2)||(n.strokeStyle=i.borderColor,n.lineWidth=i.borderWidth,n.fillStyle=i.backgroundColor,Gr(n,i,this.x,this.y))}getRange(){const n=this.options||{};return n.radius+n.hitRadius}}N(Ji,"id","point"),N(Ji,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),N(Ji,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function Ld(e,t){const{x:n,y:s,base:i,width:o,height:r}=e.getProps(["x","y","base","width","height"],t);let a,l,c,u,f;return e.horizontal?(f=r/2,a=Math.min(n,i),l=Math.max(n,i),c=s-f,u=s+f):(f=o/2,a=n-f,l=n+f,c=Math.min(s,i),u=Math.max(s,i)),{left:a,top:c,right:l,bottom:u}}function un(e,t,n,s){return e?0:Ft(t,n,s)}function $v(e,t,n){const s=e.options.borderWidth,i=e.borderSkipped,o=ad(s);return{t:un(i.top,o.top,0,n),r:un(i.right,o.right,0,t),b:un(i.bottom,o.bottom,0,n),l:un(i.left,o.left,0,t)}}function Uv(e,t,n){const{enableBorderRadius:s}=e.getProps(["enableBorderRadius"]),i=e.options.borderRadius,o=Fn(i),r=Math.min(t,n),a=e.borderSkipped,l=s||ot(i);return{topLeft:un(!l||a.top||a.left,o.topLeft,0,r),topRight:un(!l||a.top||a.right,o.topRight,0,r),bottomLeft:un(!l||a.bottom||a.left,o.bottomLeft,0,r),bottomRight:un(!l||a.bottom||a.right,o.bottomRight,0,r)}}function Kv(e){const t=Ld(e),n=t.right-t.left,s=t.bottom-t.top,i=$v(e,n/2,s/2),o=Uv(e,n/2,s/2);return{outer:{x:t.left,y:t.top,w:n,h:s,radius:o},inner:{x:t.left+i.l,y:t.top+i.t,w:n-i.l-i.r,h:s-i.t-i.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(i.t,i.l)),topRight:Math.max(0,o.topRight-Math.max(i.t,i.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(i.b,i.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(i.b,i.r))}}}}function xr(e,t,n,s){const i=t===null,o=n===null,a=e&&!(i&&o)&&Ld(e,s);return a&&(i||$e(t,a.left,a.right))&&(o||$e(n,a.top,a.bottom))}function qv(e){return e.topLeft||e.topRight||e.bottomLeft||e.bottomRight}function Yv(e,t){e.rect(t.x,t.y,t.w,t.h)}function vr(e,t,n={}){const s=e.x!==n.x?-t:0,i=e.y!==n.y?-t:0,o=(e.x+e.w!==n.x+n.w?t:0)-s,r=(e.y+e.h!==n.y+n.h?t:0)-i;return{x:e.x+s,y:e.y+i,w:e.w+o,h:e.h+r,radius:e.radius}}class Qi extends Je{constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:n,options:{borderColor:s,backgroundColor:i}}=this,{inner:o,outer:r}=Kv(this),a=qv(r.radius)?ii:Yv;t.save(),(r.w!==o.w||r.h!==o.h)&&(t.beginPath(),a(t,vr(r,n,o)),t.clip(),a(t,vr(o,-n,r)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),a(t,vr(o,n)),t.fillStyle=i,t.fill(),t.restore()}inRange(t,n,s){return xr(this,t,n,s)}inXRange(t,n){return xr(this,t,null,n)}inYRange(t,n){return xr(this,null,t,n)}getCenterPoint(t){const{x:n,y:s,base:i,horizontal:o}=this.getProps(["x","y","base","horizontal"],t);return{x:o?(n+i)/2:n,y:o?s:(s+i)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}}N(Qi,"id","bar"),N(Qi,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),N(Qi,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});var Xv=Object.freeze({__proto__:null,ArcElement:Ps,BarElement:Qi,LineElement:cn,PointElement:Ji});const ea=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],hu=ea.map(e=>e.replace("rgb(","rgba(").replace(")",", 0.5)"));function Fd(e){return ea[e%ea.length]}function Id(e){return hu[e%hu.length]}function Gv(e,t){return e.borderColor=Fd(t),e.backgroundColor=Id(t),++t}function Jv(e,t){return e.backgroundColor=e.data.map(()=>Fd(t++)),t}function Qv(e,t){return e.backgroundColor=e.data.map(()=>Id(t++)),t}function Zv(e){let t=0;return(n,s)=>{const i=e.getDatasetMeta(s).controller;i instanceof Rn?t=Jv(n,t):i instanceof Vs?t=Qv(n,t):i&&(t=Gv(n,t))}}function du(e){let t;for(t in e)if(e[t].borderColor||e[t].backgroundColor)return!0;return!1}function tw(e){return e&&(e.borderColor||e.backgroundColor)}function ew(){return St.borderColor!=="rgba(0,0,0,0.1)"||St.backgroundColor!=="rgba(0,0,0,0.1)"}var nw={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(e,t,n){if(!n.enabled)return;const{data:{datasets:s},options:i}=e.config,{elements:o}=i,r=du(s)||tw(i)||o&&du(o)||ew();if(!n.forceOverride&&r)return;const a=Zv(e);s.forEach(a)}};function sw(e,t,n,s,i){const o=i.samples||s;if(o>=n)return e.slice(t,t+n);const r=[],a=(n-2)/(o-2);let l=0;const c=t+n-1;let u=t,f,h,d,p,g;for(r[l++]=e[u],f=0;f<o-2;f++){let m=0,_=0,x;const v=Math.floor((f+1)*a)+1+t,w=Math.min(Math.floor((f+2)*a)+1,n)+t,S=w-v;for(x=v;x<w;x++)m+=e[x].x,_+=e[x].y;m/=S,_/=S;const A=Math.floor(f*a)+1+t,k=Math.min(Math.floor((f+1)*a)+1,n)+t,{x:E,y:P}=e[u];for(d=p=-1,x=A;x<k;x++)p=.5*Math.abs((E-m)*(e[x].y-P)-(E-e[x].x)*(_-P)),p>d&&(d=p,h=e[x],g=x);r[l++]=h,u=g}return r[l++]=e[c],r}function iw(e,t,n,s){let i=0,o=0,r,a,l,c,u,f,h,d,p,g;const m=[],_=t+n-1,x=e[t].x,w=e[_].x-x;for(r=t;r<t+n;++r){a=e[r],l=(a.x-x)/w*s,c=a.y;const S=l|0;if(S===u)c<p?(p=c,f=r):c>g&&(g=c,h=r),i=(o*i+a.x)/++o;else{const A=r-1;if(!it(f)&&!it(h)){const k=Math.min(f,h),E=Math.max(f,h);k!==d&&k!==A&&m.push({...e[k],x:i}),E!==d&&E!==A&&m.push({...e[E],x:i})}r>0&&A!==d&&m.push(e[A]),m.push(a),u=S,o=0,p=g=c,f=h=d=r}}return m}function Nd(e){if(e._decimated){const t=e._data;delete e._decimated,delete e._data,Object.defineProperty(e,"data",{configurable:!0,enumerable:!0,writable:!0,value:t})}}function pu(e){e.data.datasets.forEach(t=>{Nd(t)})}function ow(e,t){const n=t.length;let s=0,i;const{iScale:o}=e,{min:r,max:a,minDefined:l,maxDefined:c}=o.getUserBounds();return l&&(s=Ft(Ue(t,o.axis,r).lo,0,n-1)),c?i=Ft(Ue(t,o.axis,a).hi+1,s,n)-s:i=n-s,{start:s,count:i}}var rw={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(e,t,n)=>{if(!n.enabled){pu(e);return}const s=e.width;e.data.datasets.forEach((i,o)=>{const{_data:r,indexAxis:a}=i,l=e.getDatasetMeta(o),c=r||i.data;if(Ms([a,e.options.indexAxis])==="y"||!l.controller.supportsDecimation)return;const u=e.scales[l.xAxisID];if(u.type!=="linear"&&u.type!=="time"||e.options.parsing)return;let{start:f,count:h}=ow(l,c);const d=n.threshold||4*s;if(h<=d){Nd(i);return}it(r)&&(i._data=c,delete i.data,Object.defineProperty(i,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(g){this._data=g}}));let p;switch(n.algorithm){case"lttb":p=sw(c,f,h,s,n);break;case"min-max":p=iw(c,f,h,s);break;default:throw new Error(`Unsupported decimation algorithm '${n.algorithm}'`)}i._decimated=p})},destroy(e){pu(e)}};function aw(e,t,n){const s=e.segments,i=e.points,o=t.points,r=[];for(const a of s){let{start:l,end:c}=a;c=Xa(l,c,i);const u=na(n,i[l],i[c],a.loop);if(!t.segments){r.push({source:a,target:u,start:i[l],end:i[c]});continue}const f=_d(t,u);for(const h of f){const d=na(n,o[h.start],o[h.end],h.loop),p=bd(a,i,d);for(const g of p)r.push({source:g,target:h,start:{[n]:gu(u,d,"start",Math.max)},end:{[n]:gu(u,d,"end",Math.min)}})}}return r}function na(e,t,n,s){if(s)return;let i=t[e],o=n[e];return e==="angle"&&(i=oe(i),o=oe(o)),{property:e,start:i,end:o}}function lw(e,t){const{x:n=null,y:s=null}=e||{},i=t.points,o=[];return t.segments.forEach(({start:r,end:a})=>{a=Xa(r,a,i);const l=i[r],c=i[a];s!==null?(o.push({x:l.x,y:s}),o.push({x:c.x,y:s})):n!==null&&(o.push({x:n,y:l.y}),o.push({x:n,y:c.y}))}),o}function Xa(e,t,n){for(;t>e;t--){const s=n[t];if(!isNaN(s.x)&&!isNaN(s.y))break}return t}function gu(e,t,n,s){return e&&t?s(e[n],t[n]):e?e[n]:t?t[n]:0}function Bd(e,t){let n=[],s=!1;return wt(e)?(s=!0,n=e):n=lw(e,t),n.length?new cn({points:n,options:{tension:0},_loop:s,_fullLoop:s}):null}function mu(e){return e&&e.fill!==!1}function cw(e,t,n){let i=e[t].fill;const o=[t];let r;if(!n)return i;for(;i!==!1&&o.indexOf(i)===-1;){if(!Mt(i))return i;if(r=e[i],!r)return!1;if(r.visible)return i;o.push(i),i=r.fill}return!1}function uw(e,t,n){const s=pw(e);if(ot(s))return isNaN(s.value)?!1:s;let i=parseFloat(s);return Mt(i)&&Math.floor(i)===i?fw(s[0],t,i,n):["origin","start","end","stack","shape"].indexOf(s)>=0&&s}function fw(e,t,n,s){return(e==="-"||e==="+")&&(n=t+n),n===t||n<0||n>=s?!1:n}function hw(e,t){let n=null;return e==="start"?n=t.bottom:e==="end"?n=t.top:ot(e)?n=t.getPixelForValue(e.value):t.getBasePixel&&(n=t.getBasePixel()),n}function dw(e,t,n){let s;return e==="start"?s=n:e==="end"?s=t.options.reverse?t.min:t.max:ot(e)?s=e.value:s=t.getBaseValue(),s}function pw(e){const t=e.options,n=t.fill;let s=st(n&&n.target,n);return s===void 0&&(s=!!t.backgroundColor),s===!1||s===null?!1:s===!0?"origin":s}function gw(e){const{scale:t,index:n,line:s}=e,i=[],o=s.segments,r=s.points,a=mw(t,n);a.push(Bd({x:null,y:t.bottom},s));for(let l=0;l<o.length;l++){const c=o[l];for(let u=c.start;u<=c.end;u++)bw(i,r[u],a)}return new cn({points:i,options:{}})}function mw(e,t){const n=[],s=e.getMatchingVisibleMetas("line");for(let i=0;i<s.length;i++){const o=s[i];if(o.index===t)break;o.hidden||n.unshift(o.dataset)}return n}function bw(e,t,n){const s=[];for(let i=0;i<n.length;i++){const o=n[i],{first:r,last:a,point:l}=_w(o,t,"x");if(!(!l||r&&a)){if(r)s.unshift(l);else if(e.push(l),!a)break}}e.push(...s)}function _w(e,t,n){const s=e.interpolate(t,n);if(!s)return{};const i=s[n],o=e.segments,r=e.points;let a=!1,l=!1;for(let c=0;c<o.length;c++){const u=o[c],f=r[u.start][n],h=r[u.end][n];if($e(i,f,h)){a=i===f,l=i===h;break}}return{first:a,last:l,point:s}}class zd{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,n,s){const{x:i,y:o,radius:r}=this;return n=n||{start:0,end:xt},t.arc(i,o,r,n.end,n.start,!0),!s.bounds}interpolate(t){const{x:n,y:s,radius:i}=this,o=t.angle;return{x:n+Math.cos(o)*i,y:s+Math.sin(o)*i,angle:o}}}function yw(e){const{chart:t,fill:n,line:s}=e;if(Mt(n))return xw(t,n);if(n==="stack")return gw(e);if(n==="shape")return!0;const i=vw(e);return i instanceof zd?i:Bd(i,s)}function xw(e,t){const n=e.getDatasetMeta(t);return n&&e.isDatasetVisible(t)?n.dataset:null}function vw(e){return(e.scale||{}).getPointPositionForValue?Sw(e):ww(e)}function ww(e){const{scale:t={},fill:n}=e,s=hw(n,t);if(Mt(s)){const i=t.isHorizontal();return{x:i?s:null,y:i?null:s}}return null}function Sw(e){const{scale:t,fill:n}=e,s=t.options,i=t.getLabels().length,o=s.reverse?t.max:t.min,r=dw(n,t,o),a=[];if(s.grid.circular){const l=t.getPointPositionForValue(0,o);return new zd({x:l.x,y:l.y,radius:t.getDistanceFromCenterForValue(r)})}for(let l=0;l<i;++l)a.push(t.getPointPositionForValue(l,r));return a}function wr(e,t,n){const s=yw(t),{chart:i,index:o,line:r,scale:a,axis:l}=t,c=r.options,u=c.fill,f=c.backgroundColor,{above:h=f,below:d=f}=u||{},p=i.getDatasetMeta(o),g=yd(i,p);s&&r.points.length&&(Vo(e,n),Mw(e,{line:r,target:s,above:h,below:d,area:n,scale:a,axis:l,clip:g}),Wo(e))}function Mw(e,t){const{line:n,target:s,above:i,below:o,area:r,scale:a,clip:l}=t,c=n._loop?"angle":t.axis;e.save(),c==="x"&&o!==i&&(bu(e,s,r.top),_u(e,{line:n,target:s,color:i,scale:a,property:c,clip:l}),e.restore(),e.save(),bu(e,s,r.bottom)),_u(e,{line:n,target:s,color:o,scale:a,property:c,clip:l}),e.restore()}function bu(e,t,n){const{segments:s,points:i}=t;let o=!0,r=!1;e.beginPath();for(const a of s){const{start:l,end:c}=a,u=i[l],f=i[Xa(l,c,i)];o?(e.moveTo(u.x,u.y),o=!1):(e.lineTo(u.x,n),e.lineTo(u.x,u.y)),r=!!t.pathSegment(e,a,{move:r}),r?e.closePath():e.lineTo(f.x,n)}e.lineTo(t.first().x,n),e.closePath(),e.clip()}function _u(e,t){const{line:n,target:s,property:i,color:o,scale:r,clip:a}=t,l=aw(n,s,i);for(const{source:c,target:u,start:f,end:h}of l){const{style:{backgroundColor:d=o}={}}=c,p=s!==!0;e.save(),e.fillStyle=d,Cw(e,r,a,p&&na(i,f,h)),e.beginPath();const g=!!n.pathSegment(e,c);let m;if(p){g?e.closePath():yu(e,s,h,i);const _=!!s.pathSegment(e,u,{move:g,reverse:!0});m=g&&_,m||yu(e,s,f,i)}e.closePath(),e.fill(m?"evenodd":"nonzero"),e.restore()}}function Cw(e,t,n,s){const i=t.chart.chartArea,{property:o,start:r,end:a}=s||{};if(o==="x"||o==="y"){let l,c,u,f;o==="x"?(l=r,c=i.top,u=a,f=i.bottom):(l=i.left,c=r,u=i.right,f=a),e.beginPath(),n&&(l=Math.max(l,n.left),u=Math.min(u,n.right),c=Math.max(c,n.top),f=Math.min(f,n.bottom)),e.rect(l,c,u-l,f-c),e.clip()}}function yu(e,t,n,s){const i=t.interpolate(n,s);i&&e.lineTo(i.x,i.y)}var Pw={id:"filler",afterDatasetsUpdate(e,t,n){const s=(e.data.datasets||[]).length,i=[];let o,r,a,l;for(r=0;r<s;++r)o=e.getDatasetMeta(r),a=o.dataset,l=null,a&&a.options&&a instanceof cn&&(l={visible:e.isDatasetVisible(r),index:r,fill:uw(a,r,s),chart:e,axis:o.controller.options.indexAxis,scale:o.vScale,line:a}),o.$filler=l,i.push(l);for(r=0;r<s;++r)l=i[r],!(!l||l.fill===!1)&&(l.fill=cw(i,r,n.propagate))},beforeDraw(e,t,n){const s=n.drawTime==="beforeDraw",i=e.getSortedVisibleDatasetMetas(),o=e.chartArea;for(let r=i.length-1;r>=0;--r){const a=i[r].$filler;a&&(a.line.updateControlPoints(o,a.axis),s&&a.fill&&wr(e.ctx,a,o))}},beforeDatasetsDraw(e,t,n){if(n.drawTime!=="beforeDatasetsDraw")return;const s=e.getSortedVisibleDatasetMetas();for(let i=s.length-1;i>=0;--i){const o=s[i].$filler;mu(o)&&wr(e.ctx,o,e.chartArea)}},beforeDatasetDraw(e,t,n){const s=t.meta.$filler;!mu(s)||n.drawTime!=="beforeDatasetDraw"||wr(e.ctx,s,e.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const xu=(e,t)=>{let{boxHeight:n=t,boxWidth:s=t}=e;return e.usePointStyle&&(n=Math.min(n,t),s=e.pointStyleWidth||Math.min(s,t)),{boxWidth:s,boxHeight:n,itemHeight:Math.max(t,n)}},Ew=(e,t)=>e!==null&&t!==null&&e.datasetIndex===t.datasetIndex&&e.index===t.index;class vu extends Je{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,n,s){this.maxWidth=t,this.maxHeight=n,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let n=yt(t.generateLabels,[this.chart],this)||[];t.filter&&(n=n.filter(s=>t.filter(s,this.chart.data))),t.sort&&(n=n.sort((s,i)=>t.sort(s,i,this.chart.data))),this.options.reverse&&n.reverse(),this.legendItems=n}fit(){const{options:t,ctx:n}=this;if(!t.display){this.width=this.height=0;return}const s=t.labels,i=Tt(s.font),o=i.size,r=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=xu(s,o);let c,u;n.font=i.string,this.isHorizontal()?(c=this.maxWidth,u=this._fitRows(r,o,a,l)+10):(u=this.maxHeight,c=this._fitCols(r,i,a,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(u,t.maxHeight||this.maxHeight)}_fitRows(t,n,s,i){const{ctx:o,maxWidth:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],u=i+a;let f=t;o.textAlign="left",o.textBaseline="middle";let h=-1,d=-u;return this.legendItems.forEach((p,g)=>{const m=s+n/2+o.measureText(p.text).width;(g===0||c[c.length-1]+m+2*a>r)&&(f+=u,c[c.length-(g>0?0:1)]=0,d+=u,h++),l[g]={left:0,top:d,row:h,width:m,height:i},c[c.length-1]+=m+a}),f}_fitCols(t,n,s,i){const{ctx:o,maxHeight:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],u=r-t;let f=a,h=0,d=0,p=0,g=0;return this.legendItems.forEach((m,_)=>{const{itemWidth:x,itemHeight:v}=kw(s,n,o,m,i);_>0&&d+v+2*a>u&&(f+=h+a,c.push({width:h,height:d}),p+=h+a,g++,h=d=0),l[_]={left:p,top:d,col:g,width:x,height:v},h=Math.max(h,x),d+=v+a}),f+=h,c.push({width:h,height:d}),f}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:n,options:{align:s,labels:{padding:i},rtl:o}}=this,r=es(o,this.left,this.width);if(this.isHorizontal()){let a=0,l=Nt(s,this.left+i,this.right-this.lineWidths[a]);for(const c of n)a!==c.row&&(a=c.row,l=Nt(s,this.left+i,this.right-this.lineWidths[a])),c.top+=this.top+t+i,c.left=r.leftForLtr(r.x(l),c.width),l+=c.width+i}else{let a=0,l=Nt(s,this.top+t+i,this.bottom-this.columnSizes[a].height);for(const c of n)c.col!==a&&(a=c.col,l=Nt(s,this.top+t+i,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+i,c.left=r.leftForLtr(r.x(c.left),c.width),l+=c.height+i}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;Vo(t,this),this._draw(),Wo(t)}}_draw(){const{options:t,columnSizes:n,lineWidths:s,ctx:i}=this,{align:o,labels:r}=t,a=St.color,l=es(t.rtl,this.left,this.width),c=Tt(r.font),{padding:u}=r,f=c.size,h=f/2;let d;this.drawTitle(),i.textAlign=l.textAlign("left"),i.textBaseline="middle",i.lineWidth=.5,i.font=c.string;const{boxWidth:p,boxHeight:g,itemHeight:m}=xu(r,f),_=function(A,k,E){if(isNaN(p)||p<=0||isNaN(g)||g<0)return;i.save();const P=st(E.lineWidth,1);if(i.fillStyle=st(E.fillStyle,a),i.lineCap=st(E.lineCap,"butt"),i.lineDashOffset=st(E.lineDashOffset,0),i.lineJoin=st(E.lineJoin,"miter"),i.lineWidth=P,i.strokeStyle=st(E.strokeStyle,a),i.setLineDash(st(E.lineDash,[])),r.usePointStyle){const I={radius:g*Math.SQRT2/2,pointStyle:E.pointStyle,rotation:E.rotation,borderWidth:P},H=l.xPlus(A,p/2),L=k+h;rd(i,I,H,L,r.pointStyleWidth&&p)}else{const I=k+Math.max((f-g)/2,0),H=l.leftForLtr(A,p),L=Fn(E.borderRadius);i.beginPath(),Object.values(L).some(X=>X!==0)?ii(i,{x:H,y:I,w:p,h:g,radius:L}):i.rect(H,I,p,g),i.fill(),P!==0&&i.stroke()}i.restore()},x=function(A,k,E){Hn(i,E.text,A,k+m/2,c,{strikethrough:E.hidden,textAlign:l.textAlign(E.textAlign)})},v=this.isHorizontal(),w=this._computeTitleHeight();v?d={x:Nt(o,this.left+u,this.right-s[0]),y:this.top+u+w,line:0}:d={x:this.left+u,y:Nt(o,this.top+w+u,this.bottom-n[0].height),line:0},pd(this.ctx,t.textDirection);const S=m+u;this.legendItems.forEach((A,k)=>{i.strokeStyle=A.fontColor,i.fillStyle=A.fontColor;const E=i.measureText(A.text).width,P=l.textAlign(A.textAlign||(A.textAlign=r.textAlign)),I=p+h+E;let H=d.x,L=d.y;l.setWidth(this.width),v?k>0&&H+I+u>this.right&&(L=d.y+=S,d.line++,H=d.x=Nt(o,this.left+u,this.right-s[d.line])):k>0&&L+S>this.bottom&&(H=d.x=H+n[d.line].width+u,d.line++,L=d.y=Nt(o,this.top+w+u,this.bottom-n[d.line].height));const X=l.x(H);if(_(X,L,A),H=Yy(P,H+p+h,v?H+I:this.right,t.rtl),x(l.x(H),L,A),v)d.x+=I+u;else if(typeof A.text!="string"){const rt=c.lineHeight;d.y+=Hd(A,rt)+u}else d.y+=S}),gd(this.ctx,t.textDirection)}drawTitle(){const t=this.options,n=t.title,s=Tt(n.font),i=Wt(n.padding);if(!n.display)return;const o=es(t.rtl,this.left,this.width),r=this.ctx,a=n.position,l=s.size/2,c=i.top+l;let u,f=this.left,h=this.width;if(this.isHorizontal())h=Math.max(...this.lineWidths),u=this.top+c,f=Nt(t.align,f,this.right-h);else{const p=this.columnSizes.reduce((g,m)=>Math.max(g,m.height),0);u=c+Nt(t.align,this.top,this.bottom-p-t.labels.padding-this._computeTitleHeight())}const d=Nt(a,f,f+h);r.textAlign=o.textAlign(Ha(a)),r.textBaseline="middle",r.strokeStyle=n.color,r.fillStyle=n.color,r.font=s.string,Hn(r,n.text,d,u,s)}_computeTitleHeight(){const t=this.options.title,n=Tt(t.font),s=Wt(t.padding);return t.display?n.lineHeight+s.height:0}_getLegendItemAt(t,n){let s,i,o;if($e(t,this.left,this.right)&&$e(n,this.top,this.bottom)){for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(i=o[s],$e(t,i.left,i.left+i.width)&&$e(n,i.top,i.top+i.height))return this.legendItems[s]}return null}handleEvent(t){const n=this.options;if(!Rw(t.type,n))return;const s=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const i=this._hoveredItem,o=Ew(i,s);i&&!o&&yt(n.onLeave,[t,i,this],this),this._hoveredItem=s,s&&!o&&yt(n.onHover,[t,s,this],this)}else s&&yt(n.onClick,[t,s,this],this)}}function kw(e,t,n,s,i){const o=Aw(s,e,t,n),r=Ow(i,s,t.lineHeight);return{itemWidth:o,itemHeight:r}}function Aw(e,t,n,s){let i=e.text;return i&&typeof i!="string"&&(i=i.reduce((o,r)=>o.length>r.length?o:r)),t+n.size/2+s.measureText(i).width}function Ow(e,t,n){let s=e;return typeof t.text!="string"&&(s=Hd(t,n)),s}function Hd(e,t){const n=e.text?e.text.length:0;return t*n}function Rw(e,t){return!!((e==="mousemove"||e==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(e==="click"||e==="mouseup"))}var Tw={id:"legend",_element:vu,start(e,t,n){const s=e.legend=new vu({ctx:e.ctx,options:n,chart:e});Vt.configure(e,s,n),Vt.addBox(e,s)},stop(e){Vt.removeBox(e,e.legend),delete e.legend},beforeUpdate(e,t,n){const s=e.legend;Vt.configure(e,s,n),s.options=n},afterUpdate(e){const t=e.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(e,t){t.replay||e.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(e,t,n){const s=t.datasetIndex,i=n.chart;i.isDatasetVisible(s)?(i.hide(s),t.hidden=!0):(i.show(s),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:e=>e.chart.options.color,boxWidth:40,padding:10,generateLabels(e){const t=e.data.datasets,{labels:{usePointStyle:n,pointStyle:s,textAlign:i,color:o,useBorderRadius:r,borderRadius:a}}=e.legend.options;return e._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(n?0:void 0),u=Wt(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(u.width+u.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:i||c.textAlign,borderRadius:r&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:e=>e.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:e=>!e.startsWith("on"),labels:{_scriptable:e=>!["generateLabels","filter","sort"].includes(e)}}};class Ga extends Je{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,n){const s=this.options;if(this.left=0,this.top=0,!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=n;const i=wt(s.text)?s.text.length:1;this._padding=Wt(s.padding);const o=i*Tt(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:n,left:s,bottom:i,right:o,options:r}=this,a=r.align;let l=0,c,u,f;return this.isHorizontal()?(u=Nt(a,s,o),f=n+t,c=o-s):(r.position==="left"?(u=s+t,f=Nt(a,i,n),l=vt*-.5):(u=o-t,f=Nt(a,n,i),l=vt*.5),c=i-n),{titleX:u,titleY:f,maxWidth:c,rotation:l}}draw(){const t=this.ctx,n=this.options;if(!n.display)return;const s=Tt(n.font),o=s.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(o);Hn(t,n.text,0,0,s,{color:n.color,maxWidth:l,rotation:c,textAlign:Ha(n.align),textBaseline:"middle",translation:[r,a]})}}function Dw(e,t){const n=new Ga({ctx:e.ctx,options:t,chart:e});Vt.configure(e,n,t),Vt.addBox(e,n),e.titleBlock=n}var Lw={id:"title",_element:Ga,start(e,t,n){Dw(e,n)},stop(e){const t=e.titleBlock;Vt.removeBox(e,t),delete e.titleBlock},beforeUpdate(e,t,n){const s=e.titleBlock;Vt.configure(e,s,n),s.options=n},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Ti=new WeakMap;var Fw={id:"subtitle",start(e,t,n){const s=new Ga({ctx:e.ctx,options:n,chart:e});Vt.configure(e,s,n),Vt.addBox(e,s),Ti.set(e,s)},stop(e){Vt.removeBox(e,Ti.get(e)),Ti.delete(e)},beforeUpdate(e,t,n){const s=Ti.get(e);Vt.configure(e,s,n),s.options=n},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Es={average(e){if(!e.length)return!1;let t,n,s=new Set,i=0,o=0;for(t=0,n=e.length;t<n;++t){const a=e[t].element;if(a&&a.hasValue()){const l=a.tooltipPosition();s.add(l.x),i+=l.y,++o}}return o===0||s.size===0?!1:{x:[...s].reduce((a,l)=>a+l)/s.size,y:i/o}},nearest(e,t){if(!e.length)return!1;let n=t.x,s=t.y,i=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=e.length;o<r;++o){const l=e[o].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),u=Yr(t,c);u<i&&(i=u,a=l)}}if(a){const l=a.tooltipPosition();n=l.x,s=l.y}return{x:n,y:s}}};function ke(e,t){return t&&(wt(t)?Array.prototype.push.apply(e,t):e.push(t)),e}function ze(e){return(typeof e=="string"||e instanceof String)&&e.indexOf(`
`)>-1?e.split(`
`):e}function Iw(e,t){const{element:n,datasetIndex:s,index:i}=t,o=e.getDatasetMeta(s).controller,{label:r,value:a}=o.getLabelAndValue(i);return{chart:e,label:r,parsed:o.getParsed(i),raw:e.data.datasets[s].data[i],formattedValue:a,dataset:o.getDataset(),dataIndex:i,datasetIndex:s,element:n}}function wu(e,t){const n=e.chart.ctx,{body:s,footer:i,title:o}=e,{boxWidth:r,boxHeight:a}=t,l=Tt(t.bodyFont),c=Tt(t.titleFont),u=Tt(t.footerFont),f=o.length,h=i.length,d=s.length,p=Wt(t.padding);let g=p.height,m=0,_=s.reduce((w,S)=>w+S.before.length+S.lines.length+S.after.length,0);if(_+=e.beforeBody.length+e.afterBody.length,f&&(g+=f*c.lineHeight+(f-1)*t.titleSpacing+t.titleMarginBottom),_){const w=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;g+=d*w+(_-d)*l.lineHeight+(_-1)*t.bodySpacing}h&&(g+=t.footerMarginTop+h*u.lineHeight+(h-1)*t.footerSpacing);let x=0;const v=function(w){m=Math.max(m,n.measureText(w).width+x)};return n.save(),n.font=c.string,ht(e.title,v),n.font=l.string,ht(e.beforeBody.concat(e.afterBody),v),x=t.displayColors?r+2+t.boxPadding:0,ht(s,w=>{ht(w.before,v),ht(w.lines,v),ht(w.after,v)}),x=0,n.font=u.string,ht(e.footer,v),n.restore(),m+=p.width,{width:m,height:g}}function Nw(e,t){const{y:n,height:s}=t;return n<s/2?"top":n>e.height-s/2?"bottom":"center"}function Bw(e,t,n,s){const{x:i,width:o}=s,r=n.caretSize+n.caretPadding;if(e==="left"&&i+o+r>t.width||e==="right"&&i-o-r<0)return!0}function zw(e,t,n,s){const{x:i,width:o}=n,{width:r,chartArea:{left:a,right:l}}=e;let c="center";return s==="center"?c=i<=(a+l)/2?"left":"right":i<=o/2?c="left":i>=r-o/2&&(c="right"),Bw(c,e,t,n)&&(c="center"),c}function Su(e,t,n){const s=n.yAlign||t.yAlign||Nw(e,n);return{xAlign:n.xAlign||t.xAlign||zw(e,t,n,s),yAlign:s}}function Hw(e,t){let{x:n,width:s}=e;return t==="right"?n-=s:t==="center"&&(n-=s/2),n}function jw(e,t,n){let{y:s,height:i}=e;return t==="top"?s+=n:t==="bottom"?s-=i+n:s-=i/2,s}function Mu(e,t,n,s){const{caretSize:i,caretPadding:o,cornerRadius:r}=e,{xAlign:a,yAlign:l}=n,c=i+o,{topLeft:u,topRight:f,bottomLeft:h,bottomRight:d}=Fn(r);let p=Hw(t,a);const g=jw(t,l,c);return l==="center"?a==="left"?p+=c:a==="right"&&(p-=c):a==="left"?p-=Math.max(u,h)+i:a==="right"&&(p+=Math.max(f,d)+i),{x:Ft(p,0,s.width-t.width),y:Ft(g,0,s.height-t.height)}}function Di(e,t,n){const s=Wt(n.padding);return t==="center"?e.x+e.width/2:t==="right"?e.x+e.width-s.right:e.x+s.left}function Cu(e){return ke([],ze(e))}function Vw(e,t,n){return _n(e,{tooltip:t,tooltipItems:n,type:"tooltip"})}function Pu(e,t){const n=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return n?e.override(n):e}const jd={beforeTitle:Ne,title(e){if(e.length>0){const t=e[0],n=t.chart.data.labels,s=n?n.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(s>0&&t.dataIndex<s)return n[t.dataIndex]}return""},afterTitle:Ne,beforeBody:Ne,beforeLabel:Ne,label(e){if(this&&this.options&&this.options.mode==="dataset")return e.label+": "+e.formattedValue||e.formattedValue;let t=e.dataset.label||"";t&&(t+=": ");const n=e.formattedValue;return it(n)||(t+=n),t},labelColor(e){const n=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{borderColor:n.borderColor,backgroundColor:n.backgroundColor,borderWidth:n.borderWidth,borderDash:n.borderDash,borderDashOffset:n.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(e){const n=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{pointStyle:n.pointStyle,rotation:n.rotation}},afterLabel:Ne,afterBody:Ne,beforeFooter:Ne,footer:Ne,afterFooter:Ne};function Gt(e,t,n,s){const i=e[t].call(n,s);return typeof i>"u"?jd[t].call(n,s):i}class sa extends Je{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const n=this.chart,s=this.options.setContext(this.getContext()),i=s.enabled&&n.options.animation&&s.animations,o=new xd(this.chart,i);return i._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=Vw(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,n){const{callbacks:s}=n,i=Gt(s,"beforeTitle",this,t),o=Gt(s,"title",this,t),r=Gt(s,"afterTitle",this,t);let a=[];return a=ke(a,ze(i)),a=ke(a,ze(o)),a=ke(a,ze(r)),a}getBeforeBody(t,n){return Cu(Gt(n.callbacks,"beforeBody",this,t))}getBody(t,n){const{callbacks:s}=n,i=[];return ht(t,o=>{const r={before:[],lines:[],after:[]},a=Pu(s,o);ke(r.before,ze(Gt(a,"beforeLabel",this,o))),ke(r.lines,Gt(a,"label",this,o)),ke(r.after,ze(Gt(a,"afterLabel",this,o))),i.push(r)}),i}getAfterBody(t,n){return Cu(Gt(n.callbacks,"afterBody",this,t))}getFooter(t,n){const{callbacks:s}=n,i=Gt(s,"beforeFooter",this,t),o=Gt(s,"footer",this,t),r=Gt(s,"afterFooter",this,t);let a=[];return a=ke(a,ze(i)),a=ke(a,ze(o)),a=ke(a,ze(r)),a}_createItems(t){const n=this._active,s=this.chart.data,i=[],o=[],r=[];let a=[],l,c;for(l=0,c=n.length;l<c;++l)a.push(Iw(this.chart,n[l]));return t.filter&&(a=a.filter((u,f,h)=>t.filter(u,f,h,s))),t.itemSort&&(a=a.sort((u,f)=>t.itemSort(u,f,s))),ht(a,u=>{const f=Pu(t.callbacks,u);i.push(Gt(f,"labelColor",this,u)),o.push(Gt(f,"labelPointStyle",this,u)),r.push(Gt(f,"labelTextColor",this,u))}),this.labelColors=i,this.labelPointStyles=o,this.labelTextColors=r,this.dataPoints=a,a}update(t,n){const s=this.options.setContext(this.getContext()),i=this._active;let o,r=[];if(!i.length)this.opacity!==0&&(o={opacity:0});else{const a=Es[s.position].call(this,i,this._eventPosition);r=this._createItems(s),this.title=this.getTitle(r,s),this.beforeBody=this.getBeforeBody(r,s),this.body=this.getBody(r,s),this.afterBody=this.getAfterBody(r,s),this.footer=this.getFooter(r,s);const l=this._size=wu(this,s),c=Object.assign({},a,l),u=Su(this.chart,s,c),f=Mu(s,c,u,this.chart);this.xAlign=u.xAlign,this.yAlign=u.yAlign,o={opacity:1,x:f.x,y:f.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=r,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:n})}drawCaret(t,n,s,i){const o=this.getCaretPosition(t,s,i);n.lineTo(o.x1,o.y1),n.lineTo(o.x2,o.y2),n.lineTo(o.x3,o.y3)}getCaretPosition(t,n,s){const{xAlign:i,yAlign:o}=this,{caretSize:r,cornerRadius:a}=s,{topLeft:l,topRight:c,bottomLeft:u,bottomRight:f}=Fn(a),{x:h,y:d}=t,{width:p,height:g}=n;let m,_,x,v,w,S;return o==="center"?(w=d+g/2,i==="left"?(m=h,_=m-r,v=w+r,S=w-r):(m=h+p,_=m+r,v=w-r,S=w+r),x=m):(i==="left"?_=h+Math.max(l,u)+r:i==="right"?_=h+p-Math.max(c,f)-r:_=this.caretX,o==="top"?(v=d,w=v-r,m=_-r,x=_+r):(v=d+g,w=v+r,m=_+r,x=_-r),S=v),{x1:m,x2:_,x3:x,y1:v,y2:w,y3:S}}drawTitle(t,n,s){const i=this.title,o=i.length;let r,a,l;if(o){const c=es(s.rtl,this.x,this.width);for(t.x=Di(this,s.titleAlign,s),n.textAlign=c.textAlign(s.titleAlign),n.textBaseline="middle",r=Tt(s.titleFont),a=s.titleSpacing,n.fillStyle=s.titleColor,n.font=r.string,l=0;l<o;++l)n.fillText(i[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+a,l+1===o&&(t.y+=s.titleMarginBottom-a)}}_drawColorBox(t,n,s,i,o){const r=this.labelColors[s],a=this.labelPointStyles[s],{boxHeight:l,boxWidth:c}=o,u=Tt(o.bodyFont),f=Di(this,"left",o),h=i.x(f),d=l<u.lineHeight?(u.lineHeight-l)/2:0,p=n.y+d;if(o.usePointStyle){const g={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},m=i.leftForLtr(h,c)+c/2,_=p+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,Gr(t,g,m,_),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,Gr(t,g,m,_)}else{t.lineWidth=ot(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;const g=i.leftForLtr(h,c),m=i.leftForLtr(i.xPlus(h,1),c-2),_=Fn(r.borderRadius);Object.values(_).some(x=>x!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,ii(t,{x:g,y:p,w:c,h:l,radius:_}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),ii(t,{x:m,y:p+1,w:c-2,h:l-2,radius:_}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(g,p,c,l),t.strokeRect(g,p,c,l),t.fillStyle=r.backgroundColor,t.fillRect(m,p+1,c-2,l-2))}t.fillStyle=this.labelTextColors[s]}drawBody(t,n,s){const{body:i}=this,{bodySpacing:o,bodyAlign:r,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:u}=s,f=Tt(s.bodyFont);let h=f.lineHeight,d=0;const p=es(s.rtl,this.x,this.width),g=function(E){n.fillText(E,p.x(t.x+d),t.y+h/2),t.y+=h+o},m=p.textAlign(r);let _,x,v,w,S,A,k;for(n.textAlign=r,n.textBaseline="middle",n.font=f.string,t.x=Di(this,m,s),n.fillStyle=s.bodyColor,ht(this.beforeBody,g),d=a&&m!=="right"?r==="center"?c/2+u:c+2+u:0,w=0,A=i.length;w<A;++w){for(_=i[w],x=this.labelTextColors[w],n.fillStyle=x,ht(_.before,g),v=_.lines,a&&v.length&&(this._drawColorBox(n,t,w,p,s),h=Math.max(f.lineHeight,l)),S=0,k=v.length;S<k;++S)g(v[S]),h=f.lineHeight;ht(_.after,g)}d=0,h=f.lineHeight,ht(this.afterBody,g),t.y-=o}drawFooter(t,n,s){const i=this.footer,o=i.length;let r,a;if(o){const l=es(s.rtl,this.x,this.width);for(t.x=Di(this,s.footerAlign,s),t.y+=s.footerMarginTop,n.textAlign=l.textAlign(s.footerAlign),n.textBaseline="middle",r=Tt(s.footerFont),n.fillStyle=s.footerColor,n.font=r.string,a=0;a<o;++a)n.fillText(i[a],l.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+s.footerSpacing}}drawBackground(t,n,s,i){const{xAlign:o,yAlign:r}=this,{x:a,y:l}=t,{width:c,height:u}=s,{topLeft:f,topRight:h,bottomLeft:d,bottomRight:p}=Fn(i.cornerRadius);n.fillStyle=i.backgroundColor,n.strokeStyle=i.borderColor,n.lineWidth=i.borderWidth,n.beginPath(),n.moveTo(a+f,l),r==="top"&&this.drawCaret(t,n,s,i),n.lineTo(a+c-h,l),n.quadraticCurveTo(a+c,l,a+c,l+h),r==="center"&&o==="right"&&this.drawCaret(t,n,s,i),n.lineTo(a+c,l+u-p),n.quadraticCurveTo(a+c,l+u,a+c-p,l+u),r==="bottom"&&this.drawCaret(t,n,s,i),n.lineTo(a+d,l+u),n.quadraticCurveTo(a,l+u,a,l+u-d),r==="center"&&o==="left"&&this.drawCaret(t,n,s,i),n.lineTo(a,l+f),n.quadraticCurveTo(a,l,a+f,l),n.closePath(),n.fill(),i.borderWidth>0&&n.stroke()}_updateAnimationTarget(t){const n=this.chart,s=this.$animations,i=s&&s.x,o=s&&s.y;if(i||o){const r=Es[t.position].call(this,this._active,this._eventPosition);if(!r)return;const a=this._size=wu(this,t),l=Object.assign({},r,this._size),c=Su(n,t,l),u=Mu(t,l,c,n);(i._to!==u.x||o._to!==u.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,u))}}_willRender(){return!!this.opacity}draw(t){const n=this.options.setContext(this.getContext());let s=this.opacity;if(!s)return;this._updateAnimationTarget(n);const i={width:this.width,height:this.height},o={x:this.x,y:this.y};s=Math.abs(s)<.001?0:s;const r=Wt(n.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;n.enabled&&a&&(t.save(),t.globalAlpha=s,this.drawBackground(o,t,i,n),pd(t,n.textDirection),o.y+=r.top,this.drawTitle(o,t,n),this.drawBody(o,t,n),this.drawFooter(o,t,n),gd(t,n.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,n){const s=this._active,i=t.map(({datasetIndex:a,index:l})=>{const c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),o=!fo(s,i),r=this._positionChanged(i,n);(o||r)&&(this._active=i,this._eventPosition=n,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,n,s=!0){if(n&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const i=this.options,o=this._active||[],r=this._getActiveElements(t,o,n,s),a=this._positionChanged(r,t),l=n||!fo(r,o)||a;return l&&(this._active=r,(i.enabled||i.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,n))),l}_getActiveElements(t,n,s,i){const o=this.options;if(t.type==="mouseout")return[];if(!i)return n.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const r=this.chart.getElementsAtEventForMode(t,o.mode,o,s);return o.reverse&&r.reverse(),r}_positionChanged(t,n){const{caretX:s,caretY:i,options:o}=this,r=Es[o.position].call(this,t,n);return r!==!1&&(s!==r.x||i!==r.y)}}N(sa,"positioners",Es);var Ww={id:"tooltip",_element:sa,positioners:Es,afterInit(e,t,n){n&&(e.tooltip=new sa({chart:e,options:n}))},beforeUpdate(e,t,n){e.tooltip&&e.tooltip.initialize(n)},reset(e,t,n){e.tooltip&&e.tooltip.initialize(n)},afterDraw(e){const t=e.tooltip;if(t&&t._willRender()){const n={tooltip:t};if(e.notifyPlugins("beforeTooltipDraw",{...n,cancelable:!0})===!1)return;t.draw(e.ctx),e.notifyPlugins("afterTooltipDraw",n)}},afterEvent(e,t){if(e.tooltip){const n=t.replay;e.tooltip.handleEvent(t.event,n,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(e,t)=>t.bodyFont.size,boxWidth:(e,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:jd},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:e=>e!=="filter"&&e!=="itemSort"&&e!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},$w=Object.freeze({__proto__:null,Colors:nw,Decimation:rw,Filler:Pw,Legend:Tw,SubTitle:Fw,Title:Lw,Tooltip:Ww});const Uw=(e,t,n,s)=>(typeof t=="string"?(n=e.push(t)-1,s.unshift({index:n,label:t})):isNaN(t)&&(n=null),n);function Kw(e,t,n,s){const i=e.indexOf(t);if(i===-1)return Uw(e,t,n,s);const o=e.lastIndexOf(t);return i!==o?n:i}const qw=(e,t)=>e===null?null:Ft(Math.round(e),0,t);function Eu(e){const t=this.getLabels();return e>=0&&e<t.length?t[e]:e}class ia extends jn{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const n=this._addedLabels;if(n.length){const s=this.getLabels();for(const{index:i,label:o}of n)s[i]===o&&s.splice(i,1);this._addedLabels=[]}super.init(t)}parse(t,n){if(it(t))return null;const s=this.getLabels();return n=isFinite(n)&&s[n]===t?n:Kw(s,t,st(n,t),this._addedLabels),qw(n,s.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:n}=this.getUserBounds();let{min:s,max:i}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(s=0),n||(i=this.getLabels().length-1)),this.min=s,this.max=i}buildTicks(){const t=this.min,n=this.max,s=this.options.offset,i=[];let o=this.getLabels();o=t===0&&n===o.length-1?o:o.slice(t,n+1),this._valueRange=Math.max(o.length-(s?0:1),1),this._startValue=this.min-(s?.5:0);for(let r=t;r<=n;r++)i.push({value:r});return i}getLabelForValue(t){return Eu.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const n=this.ticks;return t<0||t>n.length-1?null:this.getPixelForValue(n[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}N(ia,"id","category"),N(ia,"defaults",{ticks:{callback:Eu}});function Yw(e,t){const n=[],{bounds:i,step:o,min:r,max:a,precision:l,count:c,maxTicks:u,maxDigits:f,includeBounds:h}=e,d=o||1,p=u-1,{min:g,max:m}=t,_=!it(r),x=!it(a),v=!it(c),w=(m-g)/(f+1);let S=wc((m-g)/p/d)*d,A,k,E,P;if(S<1e-14&&!_&&!x)return[{value:g},{value:m}];P=Math.ceil(m/S)-Math.floor(g/S),P>p&&(S=wc(P*S/p/d)*d),it(l)||(A=Math.pow(10,l),S=Math.ceil(S*A)/A),i==="ticks"?(k=Math.floor(g/S)*S,E=Math.ceil(m/S)*S):(k=g,E=m),_&&x&&o&&jy((a-r)/o,S/1e3)?(P=Math.round(Math.min((a-r)/S,u)),S=(a-r)/P,k=r,E=a):v?(k=_?r:k,E=x?a:E,P=c-1,S=(E-k)/P):(P=(E-k)/S,zs(P,Math.round(P),S/1e3)?P=Math.round(P):P=Math.ceil(P));const I=Math.max(Sc(S),Sc(k));A=Math.pow(10,it(l)?I:l),k=Math.round(k*A)/A,E=Math.round(E*A)/A;let H=0;for(_&&(h&&k!==r?(n.push({value:r}),k<r&&H++,zs(Math.round((k+H*S)*A)/A,r,ku(r,w,e))&&H++):k<r&&H++);H<P;++H){const L=Math.round((k+H*S)*A)/A;if(x&&L>a)break;n.push({value:L})}return x&&h&&E!==a?n.length&&zs(n[n.length-1].value,a,ku(a,w,e))?n[n.length-1].value=a:n.push({value:a}):(!x||E===a)&&n.push({value:E}),n}function ku(e,t,{horizontal:n,minRotation:s}){const i=pe(s),o=(n?Math.sin(i):Math.cos(i))||.001,r=.75*t*(""+e).length;return Math.min(t/o,r)}class yo extends jn{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,n){return it(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:n,maxDefined:s}=this.getUserBounds();let{min:i,max:o}=this;const r=l=>i=n?i:l,a=l=>o=s?o:l;if(t){const l=De(i),c=De(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(i===o){let l=o===0?1:Math.abs(o*.05);a(o+l),t||r(i-l)}this.min=i,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:n,stepSize:s}=t,i;return s?(i=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,i>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${i} ticks. Limiting to 1000.`),i=1e3)):(i=this.computeTickLimit(),n=n||11),n&&(i=Math.min(n,i)),i}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,n=t.ticks;let s=this.getTickLimit();s=Math.max(2,s);const i={maxTicks:s,bounds:t.bounds,min:t.min,max:t.max,precision:n.precision,step:n.stepSize,count:n.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:n.minRotation||0,includeBounds:n.includeBounds!==!1},o=this._range||this,r=Yw(i,o);return t.bounds==="ticks"&&Jh(r,this,"value"),t.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const t=this.ticks;let n=this.min,s=this.max;if(super.configure(),this.options.offset&&t.length){const i=(s-n)/Math.max(t.length-1,1)/2;n-=i,s+=i}this._startValue=n,this._endValue=s,this._valueRange=s-n}getLabelForValue(t){return mi(t,this.chart.options.locale,this.options.ticks.format)}}class oa extends yo{determineDataLimits(){const{min:t,max:n}=this.getMinMax(!0);this.min=Mt(t)?t:0,this.max=Mt(n)?n:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),n=t?this.width:this.height,s=pe(this.options.ticks.minRotation),i=(t?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(n/Math.min(40,o.lineHeight/i))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}N(oa,"id","linear"),N(oa,"defaults",{ticks:{callback:jo.formatters.numeric}});const ri=e=>Math.floor(ln(e)),Cn=(e,t)=>Math.pow(10,ri(e)+t);function Au(e){return e/Math.pow(10,ri(e))===1}function Ou(e,t,n){const s=Math.pow(10,n),i=Math.floor(e/s);return Math.ceil(t/s)-i}function Xw(e,t){const n=t-e;let s=ri(n);for(;Ou(e,t,s)>10;)s++;for(;Ou(e,t,s)<10;)s--;return Math.min(s,ri(e))}function Gw(e,{min:t,max:n}){t=ie(e.min,t);const s=[],i=ri(t);let o=Xw(t,n),r=o<0?Math.pow(10,Math.abs(o)):1;const a=Math.pow(10,o),l=i>o?Math.pow(10,i):0,c=Math.round((t-l)*r)/r,u=Math.floor((t-l)/a/10)*a*10;let f=Math.floor((c-u)/Math.pow(10,o)),h=ie(e.min,Math.round((l+u+f*Math.pow(10,o))*r)/r);for(;h<n;)s.push({value:h,major:Au(h),significand:f}),f>=10?f=f<15?15:20:f++,f>=20&&(o++,f=2,r=o>=0?1:r),h=Math.round((l+u+f*Math.pow(10,o))*r)/r;const d=ie(e.max,h);return s.push({value:d,major:Au(d),significand:f}),s}class ra extends jn{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,n){const s=yo.prototype.parse.apply(this,[t,n]);if(s===0){this._zero=!0;return}return Mt(s)&&s>0?s:null}determineDataLimits(){const{min:t,max:n}=this.getMinMax(!0);this.min=Mt(t)?Math.max(0,t):null,this.max=Mt(n)?Math.max(0,n):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!Mt(this._userMin)&&(this.min=t===Cn(this.min,0)?Cn(this.min,-1):Cn(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:n}=this.getUserBounds();let s=this.min,i=this.max;const o=a=>s=t?s:a,r=a=>i=n?i:a;s===i&&(s<=0?(o(1),r(10)):(o(Cn(s,-1)),r(Cn(i,1)))),s<=0&&o(Cn(i,-1)),i<=0&&r(Cn(s,1)),this.min=s,this.max=i}buildTicks(){const t=this.options,n={min:this._userMin,max:this._userMax},s=Gw(n,this);return t.bounds==="ticks"&&Jh(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}getLabelForValue(t){return t===void 0?"0":mi(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure(),this._startValue=ln(t),this._valueRange=ln(this.max)-ln(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(ln(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const n=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+n*this._valueRange)}}N(ra,"id","logarithmic"),N(ra,"defaults",{ticks:{callback:jo.formatters.logarithmic,major:{enabled:!0}}});function aa(e){const t=e.ticks;if(t.display&&e.display){const n=Wt(t.backdropPadding);return st(t.font&&t.font.size,St.font.size)+n.height}return 0}function Jw(e,t,n){return n=wt(n)?n:[n],{w:ix(e,t.string,n),h:n.length*t.lineHeight}}function Ru(e,t,n,s,i){return e===s||e===i?{start:t-n/2,end:t+n/2}:e<s||e>i?{start:t-n,end:t}:{start:t,end:t+n}}function Qw(e){const t={l:e.left+e._padding.left,r:e.right-e._padding.right,t:e.top+e._padding.top,b:e.bottom-e._padding.bottom},n=Object.assign({},t),s=[],i=[],o=e._pointLabels.length,r=e.options.pointLabels,a=r.centerPointLabels?vt/o:0;for(let l=0;l<o;l++){const c=r.setContext(e.getPointLabelContext(l));i[l]=c.padding;const u=e.getPointPosition(l,e.drawingArea+i[l],a),f=Tt(c.font),h=Jw(e.ctx,f,e._pointLabels[l]);s[l]=h;const d=oe(e.getIndexAngle(l)+a),p=Math.round(Ba(d)),g=Ru(p,u.x,h.w,0,180),m=Ru(p,u.y,h.h,90,270);Zw(n,t,d,g,m)}e.setCenterPoint(t.l-n.l,n.r-t.r,t.t-n.t,n.b-t.b),e._pointLabelItems=nS(e,s,i)}function Zw(e,t,n,s,i){const o=Math.abs(Math.sin(n)),r=Math.abs(Math.cos(n));let a=0,l=0;s.start<t.l?(a=(t.l-s.start)/o,e.l=Math.min(e.l,t.l-a)):s.end>t.r&&(a=(s.end-t.r)/o,e.r=Math.max(e.r,t.r+a)),i.start<t.t?(l=(t.t-i.start)/r,e.t=Math.min(e.t,t.t-l)):i.end>t.b&&(l=(i.end-t.b)/r,e.b=Math.max(e.b,t.b+l))}function tS(e,t,n){const s=e.drawingArea,{extra:i,additionalAngle:o,padding:r,size:a}=n,l=e.getPointPosition(t,s+i+r,o),c=Math.round(Ba(oe(l.angle+Et))),u=oS(l.y,a.h,c),f=sS(c),h=iS(l.x,a.w,f);return{visible:!0,x:l.x,y:u,textAlign:f,left:h,top:u,right:h+a.w,bottom:u+a.h}}function eS(e,t){if(!t)return!0;const{left:n,top:s,right:i,bottom:o}=e;return!(Ke({x:n,y:s},t)||Ke({x:n,y:o},t)||Ke({x:i,y:s},t)||Ke({x:i,y:o},t))}function nS(e,t,n){const s=[],i=e._pointLabels.length,o=e.options,{centerPointLabels:r,display:a}=o.pointLabels,l={extra:aa(o)/2,additionalAngle:r?vt/i:0};let c;for(let u=0;u<i;u++){l.padding=n[u],l.size=t[u];const f=tS(e,u,l);s.push(f),a==="auto"&&(f.visible=eS(f,c),f.visible&&(c=f))}return s}function sS(e){return e===0||e===180?"center":e<180?"left":"right"}function iS(e,t,n){return n==="right"?e-=t:n==="center"&&(e-=t/2),e}function oS(e,t,n){return n===90||n===270?e-=t/2:(n>270||n<90)&&(e-=t),e}function rS(e,t,n){const{left:s,top:i,right:o,bottom:r}=n,{backdropColor:a}=t;if(!it(a)){const l=Fn(t.borderRadius),c=Wt(t.backdropPadding);e.fillStyle=a;const u=s-c.left,f=i-c.top,h=o-s+c.width,d=r-i+c.height;Object.values(l).some(p=>p!==0)?(e.beginPath(),ii(e,{x:u,y:f,w:h,h:d,radius:l}),e.fill()):e.fillRect(u,f,h,d)}}function aS(e,t){const{ctx:n,options:{pointLabels:s}}=e;for(let i=t-1;i>=0;i--){const o=e._pointLabelItems[i];if(!o.visible)continue;const r=s.setContext(e.getPointLabelContext(i));rS(n,r,o);const a=Tt(r.font),{x:l,y:c,textAlign:u}=o;Hn(n,e._pointLabels[i],l,c+a.lineHeight/2,a,{color:r.color,textAlign:u,textBaseline:"middle"})}}function Vd(e,t,n,s){const{ctx:i}=e;if(n)i.arc(e.xCenter,e.yCenter,t,0,xt);else{let o=e.getPointPosition(0,t);i.moveTo(o.x,o.y);for(let r=1;r<s;r++)o=e.getPointPosition(r,t),i.lineTo(o.x,o.y)}}function lS(e,t,n,s,i){const o=e.ctx,r=t.circular,{color:a,lineWidth:l}=t;!r&&!s||!a||!l||n<0||(o.save(),o.strokeStyle=a,o.lineWidth=l,o.setLineDash(i.dash||[]),o.lineDashOffset=i.dashOffset,o.beginPath(),Vd(e,n,r,s),o.closePath(),o.stroke(),o.restore())}function cS(e,t,n){return _n(e,{label:n,index:t,type:"pointLabel"})}class ks extends yo{constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=Wt(aa(this.options)/2),n=this.width=this.maxWidth-t.width,s=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+n/2+t.left),this.yCenter=Math.floor(this.top+s/2+t.top),this.drawingArea=Math.floor(Math.min(n,s)/2)}determineDataLimits(){const{min:t,max:n}=this.getMinMax(!1);this.min=Mt(t)&&!isNaN(t)?t:0,this.max=Mt(n)&&!isNaN(n)?n:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/aa(this.options))}generateTickLabels(t){yo.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((n,s)=>{const i=yt(this.options.pointLabels.callback,[n,s],this);return i||i===0?i:""}).filter((n,s)=>this.chart.getDataVisibility(s))}fit(){const t=this.options;t.display&&t.pointLabels.display?Qw(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,n,s,i){this.xCenter+=Math.floor((t-n)/2),this.yCenter+=Math.floor((s-i)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,n,s,i))}getIndexAngle(t){const n=xt/(this._pointLabels.length||1),s=this.options.startAngle||0;return oe(t*n+pe(s))}getDistanceFromCenterForValue(t){if(it(t))return NaN;const n=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*n:(t-this.min)*n}getValueForDistanceFromCenter(t){if(it(t))return NaN;const n=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-n:this.min+n}getPointLabelContext(t){const n=this._pointLabels||[];if(t>=0&&t<n.length){const s=n[t];return cS(this.getContext(),t,s)}}getPointPosition(t,n,s=0){const i=this.getIndexAngle(t)-Et+s;return{x:Math.cos(i)*n+this.xCenter,y:Math.sin(i)*n+this.yCenter,angle:i}}getPointPositionForValue(t,n){return this.getPointPosition(t,this.getDistanceFromCenterForValue(n))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:n,top:s,right:i,bottom:o}=this._pointLabelItems[t];return{left:n,top:s,right:i,bottom:o}}drawBackground(){const{backgroundColor:t,grid:{circular:n}}=this.options;if(t){const s=this.ctx;s.save(),s.beginPath(),Vd(this,this.getDistanceFromCenterForValue(this._endValue),n,this._pointLabels.length),s.closePath(),s.fillStyle=t,s.fill(),s.restore()}}drawGrid(){const t=this.ctx,n=this.options,{angleLines:s,grid:i,border:o}=n,r=this._pointLabels.length;let a,l,c;if(n.pointLabels.display&&aS(this,r),i.display&&this.ticks.forEach((u,f)=>{if(f!==0||f===0&&this.min<0){l=this.getDistanceFromCenterForValue(u.value);const h=this.getContext(f),d=i.setContext(h),p=o.setContext(h);lS(this,d,l,r,p)}}),s.display){for(t.save(),a=r-1;a>=0;a--){const u=s.setContext(this.getPointLabelContext(a)),{color:f,lineWidth:h}=u;!h||!f||(t.lineWidth=h,t.strokeStyle=f,t.setLineDash(u.borderDash),t.lineDashOffset=u.borderDashOffset,l=this.getDistanceFromCenterForValue(n.reverse?this.min:this.max),c=this.getPointPosition(a,l),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(c.x,c.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,n=this.options,s=n.ticks;if(!s.display)return;const i=this.getIndexAngle(0);let o,r;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(i),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((a,l)=>{if(l===0&&this.min>=0&&!n.reverse)return;const c=s.setContext(this.getContext(l)),u=Tt(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){t.font=u.string,r=t.measureText(a.label).width,t.fillStyle=c.backdropColor;const f=Wt(c.backdropPadding);t.fillRect(-r/2-f.left,-o-u.size/2-f.top,r+f.width,u.size+f.height)}Hn(t,a.label,0,-o,u,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),t.restore()}drawTitle(){}}N(ks,"id","radialLinear"),N(ks,"defaults",{display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:jo.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(t){return t},padding:5,centerPointLabels:!1}}),N(ks,"defaultRoutes",{"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"}),N(ks,"descriptors",{angleLines:{_fallback:"grid"}});const Ko={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},Qt=Object.keys(Ko);function Tu(e,t){return e-t}function Du(e,t){if(it(t))return null;const n=e._adapter,{parser:s,round:i,isoWeekday:o}=e._parseOpts;let r=t;return typeof s=="function"&&(r=s(r)),Mt(r)||(r=typeof s=="string"?n.parse(r,s):n.parse(r)),r===null?null:(i&&(r=i==="week"&&(rs(o)||o===!0)?n.startOf(r,"isoWeek",o):n.startOf(r,i)),+r)}function Lu(e,t,n,s){const i=Qt.length;for(let o=Qt.indexOf(e);o<i-1;++o){const r=Ko[Qt[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((n-t)/(a*r.size))<=s)return Qt[o]}return Qt[i-1]}function uS(e,t,n,s,i){for(let o=Qt.length-1;o>=Qt.indexOf(n);o--){const r=Qt[o];if(Ko[r].common&&e._adapter.diff(i,s,r)>=t-1)return r}return Qt[n?Qt.indexOf(n):0]}function fS(e){for(let t=Qt.indexOf(e)+1,n=Qt.length;t<n;++t)if(Ko[Qt[t]].common)return Qt[t]}function Fu(e,t,n){if(!n)e[t]=!0;else if(n.length){const{lo:s,hi:i}=za(n,t),o=n[s]>=t?n[s]:n[i];e[o]=!0}}function hS(e,t,n,s){const i=e._adapter,o=+i.startOf(t[0].value,s),r=t[t.length-1].value;let a,l;for(a=o;a<=r;a=+i.add(a,1,s))l=n[a],l>=0&&(t[l].major=!0);return t}function Iu(e,t,n){const s=[],i={},o=t.length;let r,a;for(r=0;r<o;++r)a=t[r],i[a]=r,s.push({value:a,major:!1});return o===0||!n?s:hS(e,s,i,n)}class ai extends jn{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,n={}){const s=t.time||(t.time={}),i=this._adapter=new v0._date(t.adapters.date);i.init(n),Bs(s.displayFormats,i.formats()),this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday},super.init(t),this._normalized=n.normalized}parse(t,n){return t===void 0?null:Du(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,n=this._adapter,s=t.time.unit||"day";let{min:i,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(c){!r&&!isNaN(c.min)&&(i=Math.min(i,c.min)),!a&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!r||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),i=Mt(i)&&!isNaN(i)?i:+n.startOf(Date.now(),s),o=Mt(o)&&!isNaN(o)?o:+n.endOf(Date.now(),s)+1,this.min=Math.min(i,o-1),this.max=Math.max(i+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let n=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;return t.length&&(n=t[0],s=t[t.length-1]),{min:n,max:s}}buildTicks(){const t=this.options,n=t.time,s=t.ticks,i=s.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&i.length&&(this.min=this._userMin||i[0],this.max=this._userMax||i[i.length-1]);const o=this.min,r=this.max,a=Uy(i,o,r);return this._unit=n.unit||(s.autoSkip?Lu(n.minUnit,this.min,this.max,this._getLabelCapacity(o)):uS(this,a.length,n.minUnit,this.min,this.max)),this._majorUnit=!s.major.enabled||this._unit==="year"?void 0:fS(this._unit),this.initOffsets(i),t.reverse&&a.reverse(),Iu(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let n=0,s=0,i,o;this.options.offset&&t.length&&(i=this.getDecimalForValue(t[0]),t.length===1?n=1-i:n=(this.getDecimalForValue(t[1])-i)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?s=o:s=(o-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;n=Ft(n,0,r),s=Ft(s,0,r),this._offsets={start:n,end:s,factor:1/(n+1+s)}}_generate(){const t=this._adapter,n=this.min,s=this.max,i=this.options,o=i.time,r=o.unit||Lu(o.minUnit,n,s,this._getLabelCapacity(n)),a=st(i.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,c=rs(l)||l===!0,u={};let f=n,h,d;if(c&&(f=+t.startOf(f,"isoWeek",l)),f=+t.startOf(f,c?"day":r),t.diff(s,n,r)>1e5*a)throw new Error(n+" and "+s+" are too far apart with stepSize of "+a+" "+r);const p=i.ticks.source==="data"&&this.getDataTimestamps();for(h=f,d=0;h<s;h=+t.add(h,a,r),d++)Fu(u,h,p);return(h===s||i.bounds==="ticks"||d===1)&&Fu(u,h,p),Object.keys(u).sort(Tu).map(g=>+g)}getLabelForValue(t){const n=this._adapter,s=this.options.time;return s.tooltipFormat?n.format(t,s.tooltipFormat):n.format(t,s.displayFormats.datetime)}format(t,n){const i=this.options.time.displayFormats,o=this._unit,r=n||i[o];return this._adapter.format(t,r)}_tickFormatFunction(t,n,s,i){const o=this.options,r=o.ticks.callback;if(r)return yt(r,[t,n,s],this);const a=o.time.displayFormats,l=this._unit,c=this._majorUnit,u=l&&a[l],f=c&&a[c],h=s[n],d=c&&f&&h&&h.major;return this._adapter.format(t,i||(d?f:u))}generateTickLabels(t){let n,s,i;for(n=0,s=t.length;n<s;++n)i=t[n],i.label=this._tickFormatFunction(i.value,n,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const n=this._offsets,s=this.getDecimalForValue(t);return this.getPixelForDecimal((n.start+s)*n.factor)}getValueForPixel(t){const n=this._offsets,s=this.getDecimalForPixel(t)/n.factor-n.end;return this.min+s*(this.max-this.min)}_getLabelSize(t){const n=this.options.ticks,s=this.ctx.measureText(t).width,i=pe(this.isHorizontal()?n.maxRotation:n.minRotation),o=Math.cos(i),r=Math.sin(i),a=this._resolveTickFontOptions(0).size;return{w:s*o+a*r,h:s*r+a*o}}_getLabelCapacity(t){const n=this.options.time,s=n.displayFormats,i=s[n.unit]||s.millisecond,o=this._tickFormatFunction(t,0,Iu(this,[t],this._majorUnit),i),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],n,s;if(t.length)return t;const i=this.getMatchingVisibleMetas();if(this._normalized&&i.length)return this._cache.data=i[0].controller.getAllParsedValues(this);for(n=0,s=i.length;n<s;++n)t=t.concat(i[n].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let n,s;if(t.length)return t;const i=this.getLabels();for(n=0,s=i.length;n<s;++n)t.push(Du(this,i[n]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return td(t.sort(Tu))}}N(ai,"id","time"),N(ai,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function Li(e,t,n){let s=0,i=e.length-1,o,r,a,l;n?(t>=e[s].pos&&t<=e[i].pos&&({lo:s,hi:i}=Ue(e,"pos",t)),{pos:o,time:a}=e[s],{pos:r,time:l}=e[i]):(t>=e[s].time&&t<=e[i].time&&({lo:s,hi:i}=Ue(e,"time",t)),{time:o,pos:a}=e[s],{time:r,pos:l}=e[i]);const c=r-o;return c?a+(l-a)*(t-o)/c:a}class la extends ai{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),n=this._table=this.buildLookupTable(t);this._minPos=Li(n,this.min),this._tableRange=Li(n,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:n,max:s}=this,i=[],o=[];let r,a,l,c,u;for(r=0,a=t.length;r<a;++r)c=t[r],c>=n&&c<=s&&i.push(c);if(i.length<2)return[{time:n,pos:0},{time:s,pos:1}];for(r=0,a=i.length;r<a;++r)u=i[r+1],l=i[r-1],c=i[r],Math.round((u+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_generate(){const t=this.min,n=this.max;let s=super.getDataTimestamps();return(!s.includes(t)||!s.length)&&s.splice(0,0,t),(!s.includes(n)||s.length===1)&&s.push(n),s.sort((i,o)=>i-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const n=this.getDataTimestamps(),s=this.getLabelTimestamps();return n.length&&s.length?t=this.normalize(n.concat(s)):t=n.length?n:s,t=this._cache.all=t,t}getDecimalForValue(t){return(Li(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const n=this._offsets,s=this.getDecimalForPixel(t)/n.factor-n.end;return Li(this._table,s*this._tableRange+this._minPos,!0)}}N(la,"id","timeseries"),N(la,"defaults",ai.defaults);var dS=Object.freeze({__proto__:null,CategoryScale:ia,LinearScale:oa,LogarithmicScale:ra,RadialLinearScale:ks,TimeScale:ai,TimeSeriesScale:la});const eM=[x0,Xv,$w,dS];export{wf as A,yS as B,CS as C,bS as D,An as E,Jt as F,eM as G,PS as H,vS as I,DS as J,TS as K,AS as L,OS as M,SS as T,jt as a,mS as b,xS as c,Ot as d,RS as e,ue as f,wS as g,Tr as h,Bp as i,Xf as j,_S as k,Rg as l,LS as m,pa as n,Rr as o,Qn as p,Po as q,Ma as r,ES as s,ip as t,FS as u,gS as v,Ds as w,MS as x,kS as y,da as z};
