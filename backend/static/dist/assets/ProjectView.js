import{d as R,e as j,f as k,c as d,o as i,g as _,k as P,n as H,t as f,i as e,a as I,F as N,j as A,h as L,D as O,y as q,M,m as T,z as K,u as U,l as J}from"./vendor.js";import{a as $,_ as B,u as Q}from"./app.js";const W=R("projects",()=>{const t=j([]),a=j(null),o=j(!1),r=j(null),n=j({page:1,perPage:20,total:0,totalPages:0}),c=j({search:"",status:"",client:"",type:""}),m=k(()=>{let l=t.value;if(c.value.search){const u=c.value.search.toLowerCase();l=l.filter(v=>{var p,h,w;return v.name.toLowerCase().includes(u)||((p=v.description)==null?void 0:p.toLowerCase().includes(u))||((w=(h=v.client)==null?void 0:h.name)==null?void 0:w.toLowerCase().includes(u))})}return c.value.status&&(l=l.filter(u=>u.status===c.value.status)),c.value.client&&(l=l.filter(u=>u.client_id===c.value.client)),c.value.type&&(l=l.filter(u=>u.project_type===c.value.type)),l}),s=k(()=>{const l={};return t.value.forEach(u=>{l[u.status]||(l[u.status]=[]),l[u.status].push(u)}),l});return{projects:t,currentProject:a,loading:o,error:r,pagination:n,filters:c,filteredProjects:m,projectsByStatus:s,fetchProjects:async(l={})=>{var u,v;o.value=!0,r.value=null;try{const p=new URLSearchParams({page:l.page||n.value.page,per_page:l.perPage||n.value.perPage,search:l.search||c.value.search,status:l.status||c.value.status,client:l.client||c.value.client,type:l.type||c.value.type}),h=await $.get(`/api/projects?${p}`);h.data.success&&(t.value=h.data.data.projects,n.value=h.data.data.pagination)}catch(p){r.value=((v=(u=p.response)==null?void 0:u.data)==null?void 0:v.message)||"Errore nel caricamento progetti",console.error("Error fetching projects:",p)}finally{o.value=!1}},fetchProject:async l=>{var u,v;o.value=!0,r.value=null;try{const p=await $.get(`/api/projects/${l}`);p.data.success&&(a.value=p.data.data.project)}catch(p){throw r.value=((v=(u=p.response)==null?void 0:u.data)==null?void 0:v.message)||"Errore nel caricamento progetto",console.error("Error fetching project:",p),p}finally{o.value=!1}},createProject:async l=>{var u,v;o.value=!0,r.value=null;try{const p=await $.post("/api/projects",l);if(p.data.success){const h=p.data.data.project;return t.value.unshift(h),h}}catch(p){throw r.value=((v=(u=p.response)==null?void 0:u.data)==null?void 0:v.message)||"Errore nella creazione progetto",console.error("Error creating project:",p),p}finally{o.value=!1}},updateProject:async(l,u)=>{var v,p,h;o.value=!0,r.value=null;try{const w=await $.put(`/api/projects/${l}`,u);if(w.data.success){const z=w.data.data.project,S=t.value.findIndex(G=>G.id===l);return S!==-1&&(t.value[S]=z),((v=a.value)==null?void 0:v.id)===l&&(a.value=z),z}}catch(w){throw r.value=((h=(p=w.response)==null?void 0:p.data)==null?void 0:h.message)||"Errore nell'aggiornamento progetto",console.error("Error updating project:",w),w}finally{o.value=!1}},deleteProject:async l=>{var u,v,p;o.value=!0,r.value=null;try{(await $.delete(`/api/projects/${l}`)).data.success&&(t.value=t.value.filter(w=>w.id!==l),((u=a.value)==null?void 0:u.id)===l&&(a.value=null))}catch(h){throw r.value=((p=(v=h.response)==null?void 0:v.data)==null?void 0:p.message)||"Errore nell'eliminazione progetto",console.error("Error deleting project:",h),h}finally{o.value=!1}},setFilters:l=>{c.value={...c.value,...l}},clearFilters:()=>{c.value={search:"",status:"",client:"",type:""}},setCurrentProject:l=>{a.value=l},clearCurrentProject:()=>{a.value=null},$reset:()=>{t.value=[],a.value=null,o.value=!1,r.value=null,n.value={page:1,perPage:20,total:0,totalPages:0},c.value={search:"",status:"",client:"",type:""}}}}),X={__name:"StatusBadge",props:{status:{type:String,required:!0},type:{type:String,default:"project",validator:t=>["project","task","user","generic"].includes(t)},showDot:{type:Boolean,default:!1}},setup(t){const a=t,o={project:{planning:{label:"Pianificazione",classes:"bg-yellow-100 text-yellow-800",dot:"bg-yellow-400"},active:{label:"Attivo",classes:"bg-green-100 text-green-800",dot:"bg-green-400"},on_hold:{label:"In Pausa",classes:"bg-orange-100 text-orange-800",dot:"bg-orange-400"},completed:{label:"Completato",classes:"bg-blue-100 text-blue-800",dot:"bg-blue-400"},cancelled:{label:"Annullato",classes:"bg-red-100 text-red-800",dot:"bg-red-400"}},task:{todo:{label:"Da Fare",classes:"bg-gray-100 text-gray-800",dot:"bg-gray-400"},in_progress:{label:"In Corso",classes:"bg-blue-100 text-blue-800",dot:"bg-blue-400"},review:{label:"In Revisione",classes:"bg-purple-100 text-purple-800",dot:"bg-purple-400"},done:{label:"Completato",classes:"bg-green-100 text-green-800",dot:"bg-green-400"},blocked:{label:"Bloccato",classes:"bg-red-100 text-red-800",dot:"bg-red-400"}},user:{active:{label:"Attivo",classes:"bg-green-100 text-green-800",dot:"bg-green-400"},inactive:{label:"Inattivo",classes:"bg-gray-100 text-gray-800",dot:"bg-gray-400"},pending:{label:"In Attesa",classes:"bg-yellow-100 text-yellow-800",dot:"bg-yellow-400"}},generic:{success:{label:"Successo",classes:"bg-green-100 text-green-800",dot:"bg-green-400"},warning:{label:"Attenzione",classes:"bg-yellow-100 text-yellow-800",dot:"bg-yellow-400"},error:{label:"Errore",classes:"bg-red-100 text-red-800",dot:"bg-red-400"},info:{label:"Info",classes:"bg-blue-100 text-blue-800",dot:"bg-blue-400"}}},r=k(()=>(o[a.type]||o.generic)[a.status]||{label:a.status,classes:"bg-gray-100 text-gray-800",dot:"bg-gray-400"}),n=k(()=>r.value.label),c=k(()=>r.value.classes),m=k(()=>r.value.dot);return(s,x)=>(i(),d("span",{class:H(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",c.value])},[t.showDot?(i(),d("span",{key:0,class:H(["w-1.5 h-1.5 rounded-full mr-1.5",m.value])},null,2)):_("",!0),P(" "+f(n.value),1)],2))}},Y=B(X,[["__scopeId","data-v-30f7d29d"]]),Z={class:"project-header bg-white shadow-sm rounded-lg p-6 mb-6"},ee={key:0,class:"animate-pulse"},te={key:1,class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},se={class:"flex-1"},oe={class:"flex items-center space-x-3 mb-2"},re={class:"text-2xl font-bold text-gray-900"},ae={class:"flex flex-wrap items-center gap-4 text-sm text-gray-500"},ne={key:0},le={key:1},ie={key:2},ce={key:3},de={class:"mt-4 sm:mt-0 flex space-x-3"},ue={key:2,class:"text-center py-8"},pe={__name:"ProjectHeader",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["edit","delete"],setup(t){const a=r=>r?new Date(r).toLocaleDateString("it-IT"):"",o=r=>r?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(r):"";return(r,n)=>(i(),d("div",Z,[t.loading?(i(),d("div",ee,n[2]||(n[2]=[e("div",{class:"h-8 bg-gray-200 rounded w-1/3 mb-2"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1)]))):t.project?(i(),d("div",te,[e("div",se,[e("div",oe,[e("h1",re,f(t.project.name),1),I(Y,{status:t.project.status,type:"project"},null,8,["status"])]),e("div",ae,[t.project.client?(i(),d("span",ne,[n[3]||(n[3]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),P(" Cliente: "+f(t.project.client.name),1)])):_("",!0),t.project.start_date?(i(),d("span",le,[n[4]||(n[4]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),P(" Inizio: "+f(a(t.project.start_date)),1)])):_("",!0),t.project.end_date?(i(),d("span",ie,[n[5]||(n[5]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),P(" Fine: "+f(a(t.project.end_date)),1)])):_("",!0),t.project.budget?(i(),d("span",ce,[n[6]||(n[6]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})],-1)),P(" Budget: "+f(o(t.project.budget)),1)])):_("",!0)])]),e("div",de,[e("button",{onClick:n[0]||(n[0]=c=>r.$emit("edit")),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},n[7]||(n[7]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),P(" Modifica ")])),e("button",{onClick:n[1]||(n[1]=c=>r.$emit("delete")),class:"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},n[8]||(n[8]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),P(" Elimina ")]))])])):(i(),d("div",ue,n[9]||(n[9]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},me=B(pe,[["__scopeId","data-v-9c9dc7a4"]]),ge={class:"tab-navigation"},ve={class:"border-b border-gray-200"},he={class:"-mb-px flex space-x-8","aria-label":"Tabs"},fe=["onClick","aria-current"],xe={key:1,class:"ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"},ye={__name:"TabNavigation",props:{modelValue:{type:String,required:!0},tabs:{type:Array,required:!0,validator:t=>t.every(a=>typeof a=="object"&&a.id&&a.label)}},emits:["update:modelValue"],setup(t,{emit:a}){const o=t,r=a,n=s=>o.modelValue===s,c=s=>{r("update:modelValue",s)},m=s=>{const x={"chart-bar":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>`},"clipboard-list":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>`},users:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>`},folder:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
      </svg>`},"trending-up":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      </svg>`},calendar:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`},clock:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}};return x[s]||x["chart-bar"]};return(s,x)=>(i(),d("div",ge,[e("div",ve,[e("nav",he,[(i(!0),d(N,null,A(t.tabs,y=>(i(),d("button",{key:y.id,onClick:C=>c(y.id),class:H(["whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2",n(y.id)?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"]),"aria-current":n(y.id)?"page":void 0},[y.icon?(i(),L(O(m(y.icon)),{key:0,class:"w-4 h-4"})):_("",!0),e("span",null,f(y.label),1),y.count!==void 0?(i(),d("span",xe,f(y.count),1)):_("",!0)],10,fe))),128))])])]))}},be=B(ye,[["__scopeId","data-v-c205976e"]]),we={class:"project-overview"},ke={key:0,class:"animate-pulse space-y-4"},je={key:1,class:"space-y-6"},_e={class:"bg-white shadow rounded-lg p-6"},Pe={key:0,class:"text-gray-600"},Me={key:1,class:"text-gray-400 italic"},Ce={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},$e={class:"bg-white shadow rounded-lg p-6"},Be={class:"flex items-center"},Ve={class:"ml-5 w-0 flex-1"},ze={class:"text-lg font-medium text-gray-900"},Te={class:"bg-white shadow rounded-lg p-6"},He={class:"flex items-center"},Ie={class:"ml-5 w-0 flex-1"},Ee={class:"text-lg font-medium text-gray-900"},De={class:"bg-white shadow rounded-lg p-6"},Se={class:"flex items-center"},Fe={class:"ml-5 w-0 flex-1"},Ne={class:"text-lg font-medium text-gray-900"},Ae={class:"bg-white shadow rounded-lg p-6"},Le={class:"flex items-center"},Oe={class:"ml-5 w-0 flex-1"},Ge={class:"text-lg font-medium text-gray-900"},Re={class:"bg-white shadow rounded-lg p-6"},qe={class:"w-full bg-gray-200 rounded-full h-2.5"},Ke={class:"text-sm text-gray-500 mt-2"},Ue={class:"bg-white shadow rounded-lg p-6"},Je={class:"space-y-3"},Qe={class:"flex-1"},We={class:"text-sm text-gray-900"},Xe={class:"text-xs text-gray-500"},Ye={key:0,class:"text-center py-4"},Ze={key:2,class:"text-center py-8"},et={__name:"ProjectOverview",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(t){const a=t,o=k(()=>{if(!a.project||!a.project.task_count)return 0;const m=a.project.completed_tasks||0,s=a.project.task_count||1;return Math.round(m/s*100)}),r=k(()=>{var m;return((m=a.project)==null?void 0:m.recent_activities)||[]}),n=m=>m?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(m):"Non specificato",c=m=>m?new Date(m).toLocaleDateString("it-IT",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"}):"";return(m,s)=>(i(),d("div",we,[t.loading?(i(),d("div",ke,s[0]||(s[0]=[e("div",{class:"h-4 bg-gray-200 rounded w-3/4"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1),e("div",{class:"h-32 bg-gray-200 rounded"},null,-1)]))):t.project?(i(),d("div",je,[e("div",_e,[s[1]||(s[1]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Descrizione Progetto",-1)),t.project.description?(i(),d("p",Pe,f(t.project.description),1)):(i(),d("p",Me,"Nessuna descrizione disponibile"))]),e("div",Ce,[e("div",$e,[e("div",Be,[s[3]||(s[3]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})])],-1)),e("div",Ve,[e("dl",null,[s[2]||(s[2]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Totali",-1)),e("dd",ze,f(t.project.task_count||0),1)])])])]),e("div",Te,[e("div",He,[s[5]||(s[5]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",Ie,[e("dl",null,[s[4]||(s[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Completati",-1)),e("dd",Ee,f(t.project.completed_tasks||0),1)])])])]),e("div",De,[e("div",Se,[s[7]||(s[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",Fe,[e("dl",null,[s[6]||(s[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Membri Team",-1)),e("dd",Ne,f(t.project.team_count||0),1)])])])]),e("div",Ae,[e("div",Le,[s[9]||(s[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",Oe,[e("dl",null,[s[8]||(s[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Budget",-1)),e("dd",Ge,f(n(t.project.budget)),1)])])])])]),e("div",Re,[s[10]||(s[10]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Progresso Progetto",-1)),e("div",qe,[e("div",{class:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:q({width:`${o.value}%`})},null,4)]),e("p",Ke,f(o.value)+"% completato",1)]),e("div",Ue,[s[13]||(s[13]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Attività Recenti",-1)),e("div",Je,[(i(!0),d(N,null,A(r.value,x=>(i(),d("div",{key:x.id,class:"flex items-start space-x-3"},[s[11]||(s[11]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-2 h-2 bg-blue-600 rounded-full mt-2"})],-1)),e("div",Qe,[e("p",We,f(x.description),1),e("p",Xe,f(c(x.created_at)),1)])]))),128)),r.value.length===0?(i(),d("div",Ye,s[12]||(s[12]=[e("p",{class:"text-gray-500"},"Nessuna attività recente",-1)]))):_("",!0)])])])):(i(),d("div",Ze,s[14]||(s[14]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},F=B(et,[["__scopeId","data-v-1b51fe50"]]),tt={class:"project-tasks"},st={__name:"ProjectTasks",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(t){return(a,o)=>(i(),d("div",tt,o[0]||(o[0]=[M('<div class="bg-white shadow rounded-lg p-6"><div class="flex justify-between items-center mb-6"><h3 class="text-lg font-medium text-gray-900">Task del Progetto</h3><button class="btn-primary"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg> Nuovo Task </button></div><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Componente Task in sviluppo</h3><p class="mt-1 text-sm text-gray-500"> Migrazione da template legacy Alpine.js in corso... </p></div></div>',1)])))}},ot={class:"project-team"},rt={__name:"ProjectTeam",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(t){return(a,o)=>(i(),d("div",ot,o[0]||(o[0]=[M('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">Team del Progetto</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Componente Team in sviluppo</h3><p class="mt-1 text-sm text-gray-500"> Gestione membri del team in arrivo... </p></div></div>',1)])))}},at={class:"project-files"},nt={__name:"ProjectFiles",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(t){return(a,o)=>(i(),d("div",at,o[0]||(o[0]=[M('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">File del Progetto</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Gestione File in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Upload e gestione documenti in arrivo...</p></div></div>',1)])))}},lt={class:"project-kpi"},it={__name:"ProjectKPI",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(t){return(a,o)=>(i(),d("div",lt,o[0]||(o[0]=[M('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">KPI del Progetto</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">KPI e Metriche in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Dashboard KPI progetto in arrivo...</p></div></div>',1)])))}},ct={class:"project-gantt"},dt={__name:"ProjectGantt",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(t){return(a,o)=>(i(),d("div",ct,o[0]||(o[0]=[M('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">Diagramma di Gantt</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Gantt Chart in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Integrazione Frappe Gantt in arrivo...</p></div></div>',1)])))}},ut={class:"project-timesheet"},pt={__name:"ProjectTimesheet",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(t){return(a,o)=>(i(),d("div",ut,o[0]||(o[0]=[M('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">Timesheet del Progetto</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Timesheet in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Gestione ore lavorate in arrivo...</p></div></div>',1)])))}},mt={class:"project-view"},gt={class:"tab-content"},vt={__name:"ProjectView",setup(t){const a=W(),o=Q(),r=U(),n=J(),c=j(!0),m=j("overview"),s=k(()=>a.currentProject),x=k(()=>{const g=[{id:"overview",label:"Panoramica",icon:"chart-bar"},{id:"tasks",label:"Task",icon:"clipboard-list"},{id:"team",label:"Team",icon:"users"},{id:"files",label:"File",icon:"folder"}];return o.hasPermission("view_reports")&&g.push({id:"kpi",label:"KPI",icon:"trending-up"}),o.hasPermission("view_gantt")&&g.push({id:"gantt",label:"Gantt",icon:"calendar"}),o.hasPermission("view_timesheets")&&g.push({id:"timesheet",label:"Timesheet",icon:"clock"}),g}),y=k(()=>({overview:F,tasks:st,team:rt,files:nt,kpi:it,gantt:dt,timesheet:pt})[m.value]||F),C=async()=>{c.value=!0;try{const g=r.params.id;await a.fetchProject(g)}catch(g){console.error("Error loading project:",g)}finally{c.value=!1}},E=()=>{n.push(`/projects/${r.params.id}/edit`)},D=async()=>{if(confirm("Sei sicuro di voler eliminare questo progetto?"))try{await a.deleteProject(r.params.id),n.push("/projects")}catch(g){console.error("Error deleting project:",g)}};return T(()=>r.params.id,g=>{g&&C()}),T(()=>r.hash,g=>{if(g){const b=g.replace("#","");x.value.find(V=>V.id===b)&&(m.value=b)}}),T(m,g=>{const b=`#${g}`;r.hash!==b&&n.replace({...r,hash:b})}),K(()=>{if(r.hash){const g=r.hash.replace("#","");x.value.find(b=>b.id===g)&&(m.value=g)}C()}),(g,b)=>(i(),d("div",mt,[I(me,{project:s.value,loading:c.value,onEdit:E,onDelete:D},null,8,["project","loading"]),I(be,{modelValue:m.value,"onUpdate:modelValue":b[0]||(b[0]=V=>m.value=V),tabs:x.value,class:"mb-6"},null,8,["modelValue","tabs"]),e("div",gt,[(i(),L(O(y.value),{project:s.value,loading:c.value,onRefresh:C},null,40,["project","loading"]))])]))}},bt=B(vt,[["__scopeId","data-v-fe41580c"]]);export{bt as default};
