import{c as l,o as n,j as t,t as s,n as $,g as k,l as C,F as j,k as M,h as I,D as L,f as _,z as T,I as O,r as P,w as V,A as R,u as K,a as N,N as q,m as G}from"./vendor.js";import{_ as z,u as J,a as X}from"./app.js";const Q={class:"project-header bg-white shadow-sm rounded-lg p-6 mb-6"},W={key:0,class:"animate-pulse"},Y={key:1,class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},Z={class:"flex-1"},tt={class:"flex items-center space-x-3 mb-2"},et={class:"text-2xl font-bold text-gray-900"},st={class:"flex flex-wrap items-center gap-4 text-sm text-gray-500"},ot={key:0},rt={key:1},nt={key:2},lt={key:3},at={class:"mt-4 sm:mt-0 flex space-x-3"},it={key:2,class:"text-center py-8"},dt={__name:"ProjectHeader",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["edit","delete"],setup(g){const x=i=>({planning:"bg-yellow-100 text-yellow-800",active:"bg-green-100 text-green-800",on_hold:"bg-orange-100 text-orange-800",completed:"bg-blue-100 text-blue-800",cancelled:"bg-red-100 text-red-800"})[i]||"bg-gray-100 text-gray-800",h=i=>({planning:"Pianificazione",active:"Attivo",on_hold:"In Pausa",completed:"Completato",cancelled:"Annullato"})[i]||i,f=i=>i?new Date(i).toLocaleDateString("it-IT"):"",y=i=>i?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(i):"";return(i,e)=>(n(),l("div",Q,[g.loading?(n(),l("div",W,e[2]||(e[2]=[t("div",{class:"h-8 bg-gray-200 rounded w-1/3 mb-2"},null,-1),t("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1)]))):g.project?(n(),l("div",Y,[t("div",Z,[t("div",tt,[t("h1",et,s(g.project.name),1),t("span",{class:$(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",x(g.project.status)])},s(h(g.project.status)),3)]),t("div",st,[g.project.client?(n(),l("span",ot,[e[3]||(e[3]=t("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),C(" Cliente: "+s(g.project.client.name),1)])):k("",!0),g.project.start_date?(n(),l("span",rt,[e[4]||(e[4]=t("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),C(" Inizio: "+s(f(g.project.start_date)),1)])):k("",!0),g.project.end_date?(n(),l("span",nt,[e[5]||(e[5]=t("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),C(" Fine: "+s(f(g.project.end_date)),1)])):k("",!0),g.project.budget?(n(),l("span",lt,[e[6]||(e[6]=t("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})],-1)),C(" Budget: "+s(y(g.project.budget)),1)])):k("",!0)])]),t("div",at,[t("button",{onClick:e[0]||(e[0]=a=>i.$emit("edit")),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},e[7]||(e[7]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),C(" Modifica ")])),t("button",{onClick:e[1]||(e[1]=a=>i.$emit("delete")),class:"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},e[8]||(e[8]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),C(" Elimina ")]))])])):(n(),l("div",it,e[9]||(e[9]=[t("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},ct=z(dt,[["__scopeId","data-v-24fa98b0"]]),ut={class:"tab-navigation"},gt={class:"border-b border-gray-200"},mt={class:"-mb-px flex space-x-8","aria-label":"Tabs"},vt=["onClick","aria-current"],pt={key:1,class:"ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"},xt={__name:"TabNavigation",props:{modelValue:{type:String,required:!0},tabs:{type:Array,required:!0,validator:g=>g.every(x=>typeof x=="object"&&x.id&&x.label)}},emits:["update:modelValue"],setup(g,{emit:x}){const h=g,f=x,y=a=>h.modelValue===a,i=a=>{f("update:modelValue",a)},e=a=>{const m={"chart-bar":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>`},"clipboard-list":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>`},users:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>`},folder:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
      </svg>`},"trending-up":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      </svg>`},calendar:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`},clock:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}};return m[a]||m["chart-bar"]};return(a,m)=>(n(),l("div",ut,[t("div",gt,[t("nav",mt,[(n(!0),l(j,null,M(g.tabs,o=>(n(),l("button",{key:o.id,onClick:c=>i(o.id),class:$(["whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2",y(o.id)?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"]),"aria-current":y(o.id)?"page":void 0},[o.icon?(n(),I(L(e(o.icon)),{key:0,class:"w-4 h-4"})):k("",!0),t("span",null,s(o.label),1),o.count!==void 0?(n(),l("span",pt,s(o.count),1)):k("",!0)],10,vt))),128))])])]))}},ht=z(xt,[["__scopeId","data-v-c205976e"]]),ft={class:"project-overview"},_t={key:0,class:"animate-pulse space-y-4"},yt={key:1,class:"space-y-6"},bt={class:"bg-white shadow rounded-lg p-6"},wt={key:0,class:"text-gray-600"},kt={key:1,class:"text-gray-400 italic"},$t={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},jt={class:"bg-white shadow rounded-lg p-6"},Mt={class:"flex items-center"},Ct={class:"ml-5 w-0 flex-1"},Pt={class:"text-lg font-medium text-gray-900"},Bt={class:"bg-white shadow rounded-lg p-6"},Tt={class:"flex items-center"},Vt={class:"ml-5 w-0 flex-1"},zt={class:"text-lg font-medium text-gray-900"},Ht={class:"bg-white shadow rounded-lg p-6"},Dt={class:"flex items-center"},It={class:"ml-5 w-0 flex-1"},At={class:"text-lg font-medium text-gray-900"},St={class:"bg-white shadow rounded-lg p-6"},Nt={class:"flex items-center"},Ft={class:"ml-5 w-0 flex-1"},Lt={class:"text-lg font-medium text-gray-900"},Ot={class:"bg-white shadow rounded-lg p-6"},Rt={class:"w-full bg-gray-200 rounded-full h-2.5"},Et={class:"text-sm text-gray-500 mt-2"},Ut={class:"bg-white shadow rounded-lg p-6"},Kt={class:"space-y-4"},qt={class:"flex justify-between items-center"},Gt={class:"text-sm font-medium"},Jt={class:"flex justify-between items-center"},Xt={class:"text-sm font-medium"},Qt={class:"w-full bg-gray-200 rounded-full h-3"},Wt={class:"flex justify-between items-center text-sm"},Yt={class:"bg-white shadow rounded-lg p-6"},Zt={class:"space-y-3"},te={class:"flex-shrink-0"},ee=["src","alt"],se={key:1,class:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"},oe={class:"text-xs font-medium text-gray-600"},re={class:"flex-1"},ne={class:"text-sm font-medium text-gray-900"},le={class:"text-xs text-gray-500"},ae={class:"text-right"},ie={class:"text-xs text-gray-500"},de={key:0,class:"text-center py-4"},ce={class:"bg-white shadow rounded-lg p-6"},ue={class:"space-y-3"},ge={class:"flex-shrink-0"},me={class:"flex-1"},ve={class:"text-sm text-gray-900"},pe={class:"flex items-center space-x-2 mt-1"},xe={class:"text-xs text-gray-500"},he={class:"text-xs text-gray-500"},fe={key:0,class:"text-center py-4"},_e={key:2,class:"text-center py-8"},ye={__name:"ProjectOverview",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(g){const x=g,h=_(()=>{if(!x.project||!x.project.task_count)return 0;const u=x.project.completed_tasks||0,r=x.project.task_count||1;return Math.round(u/r*100)}),f=_(()=>{var u;return((u=x.project)==null?void 0:u.recent_activities)||[]}),y=_(()=>{var u;return((u=x.project)==null?void 0:u.team_members)||[]}),i=_(()=>{var b;return(((b=x.project)==null?void 0:b.total_hours)||0)*50}),e=_(()=>{var r;return(((r=x.project)==null?void 0:r.budget)||0)-i.value}),a=_(()=>{var r;const u=((r=x.project)==null?void 0:r.budget)||1;return Math.min(Math.round(i.value/u*100),100)}),m=_(()=>{const u=a.value;return u>=90?"bg-red-600":u>=75?"bg-yellow-600":"bg-green-600"}),o=_(()=>{var r;const u=e.value;return u<0?"text-red-600":u<(((r=x.project)==null?void 0:r.budget)||0)*.1?"text-yellow-600":"text-green-600"}),c=u=>u?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(u):"Non specificato",p=u=>u?new Date(u).toLocaleDateString("it-IT",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"}):"",B=u=>u?u.split(" ").map(r=>r.charAt(0).toUpperCase()).slice(0,2).join(""):"??",w=u=>{const r={task_created:"bg-blue-600",task_completed:"bg-green-600",task_updated:"bg-yellow-600",comment_added:"bg-purple-600",file_uploaded:"bg-indigo-600",member_added:"bg-pink-600",default:"bg-gray-600"};return r[u]||r.default};return(u,r)=>(n(),l("div",ft,[g.loading?(n(),l("div",_t,r[0]||(r[0]=[t("div",{class:"h-4 bg-gray-200 rounded w-3/4"},null,-1),t("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1),t("div",{class:"h-32 bg-gray-200 rounded"},null,-1)]))):g.project?(n(),l("div",yt,[t("div",bt,[r[1]||(r[1]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Descrizione Progetto",-1)),g.project.description?(n(),l("p",wt,s(g.project.description),1)):(n(),l("p",kt,"Nessuna descrizione disponibile"))]),t("div",$t,[t("div",jt,[t("div",Mt,[r[3]||(r[3]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})])],-1)),t("div",Ct,[t("dl",null,[r[2]||(r[2]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Totali",-1)),t("dd",Pt,s(g.project.task_count||0),1)])])])]),t("div",Bt,[t("div",Tt,[r[5]||(r[5]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),t("div",Vt,[t("dl",null,[r[4]||(r[4]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Completati",-1)),t("dd",zt,s(g.project.completed_tasks||0),1)])])])]),t("div",Ht,[t("div",Dt,[r[7]||(r[7]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),t("div",It,[t("dl",null,[r[6]||(r[6]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Membri Team",-1)),t("dd",At,s(g.project.team_count||0),1)])])])]),t("div",St,[t("div",Nt,[r[9]||(r[9]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),t("div",Ft,[t("dl",null,[r[8]||(r[8]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Budget",-1)),t("dd",Lt,s(c(g.project.budget)),1)])])])])]),t("div",Ot,[r[10]||(r[10]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Progresso Progetto",-1)),t("div",Rt,[t("div",{class:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:T({width:`${h.value}%`})},null,4)]),t("p",Et,s(h.value)+"% completato",1)]),t("div",Ut,[r[15]||(r[15]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Budget vs Spese",-1)),t("div",Kt,[t("div",qt,[r[11]||(r[11]=t("span",{class:"text-sm text-gray-600"},"Budget Totale",-1)),t("span",Gt,s(c(g.project.budget)),1)]),r[14]||(r[14]=t("div",{class:"w-full bg-gray-200 rounded-full h-3"},[t("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:{width:"100%"}})],-1)),t("div",Jt,[r[12]||(r[12]=t("span",{class:"text-sm text-gray-600"},"Spese Sostenute",-1)),t("span",Xt,s(c(i.value)),1)]),t("div",Qt,[t("div",{class:$(["h-3 rounded-full transition-all duration-300",m.value]),style:T({width:a.value+"%"})},null,6)]),t("div",Wt,[r[13]||(r[13]=t("span",{class:"text-gray-600"},"Rimanente",-1)),t("span",{class:$(["font-medium",o.value])},s(c(e.value)),3)])])]),t("div",Yt,[r[17]||(r[17]=t("div",{class:"flex items-center justify-between mb-4"},[t("h3",{class:"text-lg font-medium text-gray-900"},"Team Members"),t("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutti")],-1)),t("div",Zt,[(n(!0),l(j,null,M(y.value,b=>(n(),l("div",{key:b.id,class:"flex items-center space-x-3"},[t("div",te,[b.profile_image?(n(),l("img",{key:0,src:b.profile_image,alt:b.full_name,class:"w-8 h-8 rounded-full"},null,8,ee)):(n(),l("div",se,[t("span",oe,s(B(b.full_name)),1)]))]),t("div",re,[t("p",ne,s(b.full_name),1),t("p",le,s(b.role||"Team Member"),1)]),t("div",ae,[t("p",ie,s(b.hours_worked||0)+"h",1)])]))),128)),y.value.length===0?(n(),l("div",de,r[16]||(r[16]=[t("p",{class:"text-gray-500"},"Nessun membro del team assegnato",-1)]))):k("",!0)])]),t("div",ce,[r[20]||(r[20]=t("div",{class:"flex items-center justify-between mb-4"},[t("h3",{class:"text-lg font-medium text-gray-900"},"Attività Recenti"),t("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutte")],-1)),t("div",ue,[(n(!0),l(j,null,M(f.value,b=>(n(),l("div",{key:b.id,class:"flex items-start space-x-3"},[t("div",ge,[t("div",{class:$(["w-2 h-2 rounded-full mt-2",w(b.type)])},null,2)]),t("div",me,[t("p",ve,s(b.description),1),t("div",pe,[t("p",xe,s(p(b.created_at)),1),r[18]||(r[18]=t("span",{class:"text-xs text-gray-400"},"•",-1)),t("p",he,s(b.user_name),1)])])]))),128)),f.value.length===0?(n(),l("div",fe,r[19]||(r[19]=[t("p",{class:"text-gray-500"},"Nessuna attività recente",-1),t("button",{class:"text-sm text-blue-600 hover:text-blue-800 mt-2"},"Aggiungi prima attività",-1)]))):k("",!0)])])])):(n(),l("div",_e,r[21]||(r[21]=[t("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},F=z(ye,[["__scopeId","data-v-47f9c860"]]),be={class:"project-tasks"},we={class:"bg-white shadow rounded-lg p-6"},ke={class:"space-y-4"},$e={class:"flex items-start justify-between"},je={class:"flex-1"},Me={class:"flex items-center space-x-3"},Ce={class:"flex-shrink-0"},Pe={class:"text-lg font-medium text-gray-900"},Be={class:"mt-2 text-sm text-gray-600"},Te={class:"mt-3 flex items-center space-x-4 text-sm text-gray-500"},Ve={key:0},ze={key:1},He={key:2},De={key:0,class:"text-center py-8"},Ie={__name:"ProjectTasks",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(g){const x=g,h=_(()=>{var o;return((o=x.project)==null?void 0:o.tasks)||[]}),f=_(()=>{var o;return((o=x.project)==null?void 0:o.team_members)||[]}),y=o=>({todo:"bg-gray-400","in-progress":"bg-blue-500",completed:"bg-green-500","on-hold":"bg-yellow-500"})[o]||"bg-gray-400",i=o=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-red-100 text-red-800"})[o]||"bg-gray-100 text-gray-800",e=o=>({low:"Bassa",medium:"Media",high:"Alta"})[o]||"Non specificata",a=o=>{const c=f.value.find(p=>p.id===o);return c?c.full_name:"Non assegnato"},m=o=>o?new Date(o).toLocaleDateString("it-IT"):"";return(o,c)=>(n(),l("div",be,[t("div",we,[c[2]||(c[2]=O('<div class="flex justify-between items-center mb-6"><h3 class="text-lg font-medium text-gray-900">Task del Progetto</h3><button class="btn-primary"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg> Nuovo Task </button></div>',1)),t("div",ke,[(n(!0),l(j,null,M(h.value,p=>(n(),l("div",{key:p.id,class:"border border-gray-200 rounded-lg p-4 hover:bg-gray-50"},[t("div",$e,[t("div",je,[t("div",Me,[t("div",Ce,[t("div",{class:$(["w-3 h-3 rounded-full",y(p.status)])},null,2)]),t("h5",Pe,s(p.name),1),t("span",{class:$(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",i(p.priority)])},s(e(p.priority)),3)]),t("p",Be,s(p.description),1),t("div",Te,[p.assignee_id?(n(),l("span",Ve,"Assegnato a: "+s(a(p.assignee_id)),1)):k("",!0),p.due_date?(n(),l("span",ze,"Scadenza: "+s(m(p.due_date)),1)):k("",!0),p.estimated_hours?(n(),l("span",He,"Stimate: "+s(p.estimated_hours)+"h",1)):k("",!0)])]),c[0]||(c[0]=t("div",{class:"flex items-center space-x-2"},[t("button",{class:"text-gray-400 hover:text-gray-600"},[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})])])],-1))])]))),128)),h.value.length===0?(n(),l("div",De,c[1]||(c[1]=[t("p",{class:"text-gray-500"},"Nessun task trovato per questo progetto",-1)]))):k("",!0)])])]))}},Ae={class:"project-team"},Se={class:"bg-white shadow rounded-lg p-6"},Ne={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},Fe={class:"flex items-center space-x-4"},Le={class:"flex-shrink-0"},Oe=["src","alt"],Re={key:1,class:"w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center"},Ee={class:"text-sm font-medium text-gray-600"},Ue={class:"flex-1"},Ke={class:"text-lg font-medium text-gray-900"},qe={class:"text-sm text-gray-600"},Ge={class:"text-xs text-gray-500"},Je={class:"mt-4 grid grid-cols-2 gap-4 text-sm"},Xe={class:"font-medium ml-1"},Qe={class:"font-medium ml-1"},We={key:0,class:"col-span-full text-center py-8"},Ye={__name:"ProjectTeam",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(g){const x=g,h=_(()=>{var i;return((i=x.project)==null?void 0:i.team_members)||[]}),f=i=>i?i.split(" ").map(e=>e.charAt(0).toUpperCase()).slice(0,2).join(""):"??",y=i=>{var a;return(((a=x.project)==null?void 0:a.tasks)||[]).filter(m=>m.assignee_id===i).length};return(i,e)=>(n(),l("div",Ae,[t("div",Se,[e[3]||(e[3]=t("h3",{class:"text-lg font-medium text-gray-900 mb-6"},"Team del Progetto",-1)),t("div",Ne,[(n(!0),l(j,null,M(h.value,a=>(n(),l("div",{key:a.id,class:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"},[t("div",Fe,[t("div",Le,[a.profile_image?(n(),l("img",{key:0,src:a.profile_image,alt:a.full_name,class:"w-12 h-12 rounded-full"},null,8,Oe)):(n(),l("div",Re,[t("span",Ee,s(f(a.full_name)),1)]))]),t("div",Ue,[t("h4",Ke,s(a.full_name),1),t("p",qe,s(a.role||"Team Member"),1),t("p",Ge,s(a.email),1)])]),t("div",Je,[t("div",null,[e[0]||(e[0]=t("span",{class:"text-gray-600"},"Ore Lavorate:",-1)),t("span",Xe,s(a.hours_worked||0)+"h",1)]),t("div",null,[e[1]||(e[1]=t("span",{class:"text-gray-600"},"Task Assegnati:",-1)),t("span",Qe,s(y(a.id)),1)])])]))),128)),h.value.length===0?(n(),l("div",We,e[2]||(e[2]=[t("p",{class:"text-gray-500"},"Nessun membro del team assegnato",-1)]))):k("",!0)])])]))}},Ze={class:"project-files"},ts={class:"bg-white shadow rounded-lg p-6"},es={class:"space-y-3"},ss={class:"flex items-center space-x-4"},os={class:"flex-1"},rs={class:"text-sm font-medium text-gray-900"},ns={class:"flex items-center space-x-4 text-xs text-gray-500"},ls={key:0,class:"text-center py-8"},as={__name:"ProjectFiles",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(g){const x=g,h=_(()=>{var i;return((i=x.project)==null?void 0:i.files)||[]}),f=i=>{if(!i)return"0 B";const e=1024,a=["B","KB","MB","GB"],m=Math.floor(Math.log(i)/Math.log(e));return parseFloat((i/Math.pow(e,m)).toFixed(2))+" "+a[m]},y=i=>i?new Date(i).toLocaleDateString("it-IT"):"";return(i,e)=>(n(),l("div",Ze,[t("div",ts,[e[3]||(e[3]=O('<h3 class="text-lg font-medium text-gray-900 mb-6">File del Progetto</h3><div class="border-2 border-dashed border-gray-300 rounded-lg p-6 mb-6 hover:border-gray-400 transition-colors"><div class="text-center"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path></svg><div class="mt-4"><label class="cursor-pointer"><span class="mt-2 block text-sm font-medium text-gray-900"> Trascina file qui o clicca per selezionare </span><input type="file" class="sr-only" multiple></label><p class="mt-2 text-xs text-gray-500"> PNG, JPG, PDF, DOC, XLS fino a 10MB </p></div></div></div>',2)),t("div",es,[(n(!0),l(j,null,M(h.value,a=>(n(),l("div",{key:a.id,class:"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"},[t("div",ss,[e[0]||(e[0]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1)),t("div",os,[t("h4",rs,s(a.name),1),t("div",ns,[t("span",null,s(f(a.size)),1),t("span",null,s(y(a.uploaded_at)),1)])])]),e[1]||(e[1]=t("div",{class:"flex items-center space-x-2"},[t("button",{class:"text-gray-400 hover:text-gray-600",title:"Scarica"},[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])])],-1))]))),128)),h.value.length===0?(n(),l("div",ls,e[2]||(e[2]=[t("p",{class:"text-gray-500"},"Nessun file caricato per questo progetto",-1)]))):k("",!0)])])]))}},is={class:"project-kpi"},ds={key:0,class:"animate-pulse space-y-4"},cs={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},us={key:1,class:"space-y-6"},gs={class:"bg-white shadow rounded-lg p-6"},ms={class:"flex items-center justify-between"},vs=["disabled"],ps={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},xs={class:"bg-white shadow rounded-lg p-6"},hs={class:"flex items-center"},fs={class:"ml-5 w-0 flex-1"},_s={class:"text-lg font-medium text-gray-900"},ys={class:"text-xs text-gray-500"},bs={class:"bg-white shadow rounded-lg p-6"},ws={class:"flex items-center"},ks={class:"ml-5 w-0 flex-1"},$s={class:"text-lg font-medium text-gray-900"},js={class:"bg-white shadow rounded-lg p-6"},Ms={class:"flex items-center"},Cs={class:"ml-5 w-0 flex-1"},Ps={class:"text-lg font-medium text-gray-900"},Bs={class:"text-xs text-gray-500"},Ts={class:"bg-white shadow rounded-lg p-6"},Vs={class:"flex items-center"},zs={class:"ml-5 w-0 flex-1"},Hs={class:"text-lg font-medium text-gray-900"},Ds={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Is={class:"bg-white shadow rounded-lg p-6"},As={class:"space-y-4"},Ss={class:"flex justify-between text-sm"},Ns={class:"font-medium"},Fs={class:"w-full bg-gray-200 rounded-full h-3"},Ls={class:"flex justify-between text-sm"},Os={class:"text-gray-600"},Rs={class:"font-medium"},Es={class:"bg-white shadow rounded-lg p-6"},Us={class:"space-y-4"},Ks={class:"flex justify-between text-sm"},qs={class:"font-medium"},Gs={class:"w-full bg-gray-200 rounded-full h-3"},Js={class:"flex justify-between text-sm"},Xs={class:"text-gray-600"},Qs={class:"font-medium"},Ws={class:"bg-white shadow rounded-lg p-6"},Ys={class:"flex items-center justify-between mb-4"},Zs={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},to={class:"text-center p-4 border rounded-lg"},eo={class:"text-xs text-gray-500"},so={class:"text-center p-4 border rounded-lg"},oo={class:"text-xs text-gray-500"},ro={class:"text-center p-4 border rounded-lg"},no={class:"text-xs text-gray-500"},lo={key:2,class:"text-center py-8"},ao={__name:"ProjectKPI",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(g,{emit:x}){const h=g,f=x,y=P(!1),i=P(!1),e=P({totalHours:0,workDays:0,totalCosts:0,costVariance:0,potentialRevenue:0,actualRevenue:0,marginPercentage:0}),a=P({budget:80,time:85,margin:15}),m=_(()=>{var d;return!((d=h.project)!=null&&d.budget)||e.value.totalCosts===0?0:Math.round(e.value.totalCosts/h.project.budget*100)}),o=_(()=>{var d;return!((d=h.project)!=null&&d.estimated_hours)||e.value.totalHours===0?0:Math.round(e.value.totalHours/h.project.estimated_hours*100)}),c=_(()=>{const d=e.value.costVariance;return d>0?"text-red-600":d<0?"text-green-600":"text-gray-600"}),p=_(()=>{const d=e.value.marginPercentage;return d>=a.value.margin?"text-green-600":d>=a.value.margin*.7?"text-yellow-600":"text-red-600"}),B=_(()=>{const d=e.value.marginPercentage;return d>=a.value.margin?"Ottimo":d>=a.value.margin*.7?"Accettabile":"Critico"}),w=_(()=>{const d=m.value;return d>=a.value.budget?"text-red-600":d>=a.value.budget*.8?"text-yellow-600":"text-green-600"}),u=_(()=>{const d=o.value;return d>=a.value.time?"text-red-600":d>=a.value.time*.8?"text-yellow-600":"text-green-600"}),r=_(()=>{const d=e.value.marginPercentage;return d>=a.value.margin?"text-green-600":d>=a.value.margin*.7?"text-yellow-600":"text-red-600"}),b=d=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(d||0),H=d=>`${d||0}h`,A=d=>`${(d||0).toFixed(1)}%`,D=async()=>{var d;(d=h.project)!=null&&d.id&&E()},E=()=>{const d=h.project;d&&(e.value={totalHours:d.total_hours||0,workDays:Math.ceil((d.total_hours||0)/8),totalCosts:(d.total_hours||0)*50,costVariance:(d.total_hours||0)*50-(d.budget||0),potentialRevenue:d.budget||0,actualRevenue:d.invoiced_amount||0,marginPercentage:d.budget?(d.budget-(d.total_hours||0)*50)/d.budget*100:0})},U=async()=>{y.value=!0;try{await D(),f("refresh")}catch(d){console.error("Error refreshing KPIs:",d)}finally{y.value=!1}};return V(()=>h.project,d=>{d&&D()},{immediate:!0}),R(()=>{h.project&&D()}),(d,v)=>(n(),l("div",is,[g.loading?(n(),l("div",ds,[t("div",cs,[(n(),l(j,null,M(4,S=>t("div",{key:S,class:"bg-gray-200 rounded-lg h-24"})),64))]),v[1]||(v[1]=t("div",{class:"bg-gray-200 rounded-lg h-64"},null,-1))])):g.project?(n(),l("div",us,[t("div",gs,[t("div",ms,[v[4]||(v[4]=t("div",null,[t("h3",{class:"text-lg font-medium text-gray-900"},"KPI Progetto"),t("p",{class:"text-sm text-gray-600"},"Dashboard metriche e performance del progetto")],-1)),t("button",{onClick:U,disabled:y.value,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(n(),l("svg",{class:$(["w-4 h-4 mr-2",{"animate-spin":y.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},v[2]||(v[2]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]),2)),v[3]||(v[3]=C(" Aggiorna "))],8,vs)])]),t("div",ps,[t("div",xs,[t("div",hs,[v[6]||(v[6]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),t("div",fs,[t("dl",null,[v[5]||(v[5]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ore Totali",-1)),t("dd",_s,s(H(e.value.totalHours)),1),t("dd",ys,s(e.value.workDays)+" giorni lavorati",1)])])])]),t("div",bs,[t("div",ws,[v[8]||(v[8]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),t("div",ks,[t("dl",null,[v[7]||(v[7]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Costi Totali",-1)),t("dd",$s,s(b(e.value.totalCosts)),1),t("dd",{class:$(["text-xs",c.value])},s(b(e.value.costVariance))+" vs budget",3)])])])]),t("div",js,[t("div",Ms,[v[10]||(v[10]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),t("div",Cs,[t("dl",null,[v[9]||(v[9]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ricavi Potenziali",-1)),t("dd",Ps,s(b(e.value.potentialRevenue)),1),t("dd",Bs,s(b(e.value.actualRevenue))+" fatturati",1)])])])]),t("div",Ts,[t("div",Vs,[v[12]||(v[12]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),t("div",zs,[t("dl",null,[v[11]||(v[11]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Margine",-1)),t("dd",Hs,s(A(e.value.marginPercentage)),1),t("dd",{class:$(["text-xs",p.value])},s(B.value),3)])])])])]),t("div",Ds,[t("div",Is,[v[14]||(v[14]=t("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Budget",-1)),t("div",As,[t("div",Ss,[v[13]||(v[13]=t("span",{class:"text-gray-600"},"Budget Totale",-1)),t("span",Ns,s(b(g.project.budget||0)),1)]),t("div",Fs,[t("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:T({width:m.value+"%"})},null,4)]),t("div",Ls,[t("span",Os,"Utilizzato: "+s(b(e.value.totalCosts)),1),t("span",Rs,s(m.value)+"%",1)])])]),t("div",Es,[v[16]||(v[16]=t("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Tempo",-1)),t("div",Us,[t("div",Ks,[v[15]||(v[15]=t("span",{class:"text-gray-600"},"Ore Stimate",-1)),t("span",qs,s(H(g.project.estimated_hours||0)),1)]),t("div",Gs,[t("div",{class:"bg-green-600 h-3 rounded-full transition-all duration-300",style:T({width:o.value+"%"})},null,4)]),t("div",Js,[t("span",Xs,"Lavorate: "+s(H(e.value.totalHours)),1),t("span",Qs,s(o.value)+"%",1)])])])]),t("div",Ws,[t("div",Ys,[v[17]||(v[17]=t("h4",{class:"text-lg font-medium text-gray-900"},"Soglie KPI",-1)),t("button",{onClick:v[0]||(v[0]=S=>i.value=!0),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Configura ")]),t("div",Zs,[t("div",to,[t("div",{class:$(["text-2xl font-bold",w.value])},s(m.value)+"% ",3),v[18]||(v[18]=t("div",{class:"text-sm text-gray-600"},"Budget Usage",-1)),t("div",eo,"Soglia: "+s(a.value.budget)+"%",1)]),t("div",so,[t("div",{class:$(["text-2xl font-bold",u.value])},s(o.value)+"% ",3),v[19]||(v[19]=t("div",{class:"text-sm text-gray-600"},"Time Usage",-1)),t("div",oo,"Soglia: "+s(a.value.time)+"%",1)]),t("div",ro,[t("div",{class:$(["text-2xl font-bold",r.value])},s(A(e.value.marginPercentage)),3),v[20]||(v[20]=t("div",{class:"text-sm text-gray-600"},"Margine",-1)),t("div",no,"Soglia: "+s(a.value.margin)+"%",1)])])])])):(n(),l("div",lo,v[21]||(v[21]=[t("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},io={class:"project-gantt"},co={class:"bg-white shadow rounded-lg p-6"},uo={class:"space-y-3"},go={class:"flex items-center justify-between"},mo={class:"flex-1"},vo={class:"flex items-center space-x-3"},po={class:"flex-shrink-0"},xo={class:"text-sm font-medium text-gray-900"},ho={class:"mt-2 flex items-center space-x-4 text-sm text-gray-500"},fo={key:0},_o={key:1},yo={key:2},bo={class:"mt-3"},wo={class:"flex items-center space-x-2"},ko={class:"text-xs text-gray-500 w-16"},$o={class:"flex-1 bg-gray-200 rounded-full h-2"},jo={class:"text-xs text-gray-500 w-16"},Mo={class:"flex items-center space-x-2 ml-4"},Co={class:"text-right"},Po={class:"text-sm font-medium text-gray-900"},Bo={key:0,class:"text-center py-8"},To={__name:"ProjectGantt",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(g){const x=g,h=_(()=>{var o;return((o=x.project)==null?void 0:o.tasks)||[]}),f=o=>({todo:"bg-gray-400","in-progress":"bg-blue-500",completed:"bg-green-500","on-hold":"bg-yellow-500"})[o]||"bg-gray-400",y=o=>({todo:"bg-gray-400","in-progress":"bg-blue-500",completed:"bg-green-500","on-hold":"bg-yellow-500"})[o]||"bg-gray-400",i=o=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-red-100 text-red-800"})[o]||"bg-gray-100 text-gray-800",e=o=>({low:"Bassa",medium:"Media",high:"Alta"})[o]||"Non specificata",a=o=>({todo:0,"in-progress":50,completed:100,"on-hold":25})[o.status]||0,m=o=>o?new Date(o).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit"}):"";return(o,c)=>(n(),l("div",io,[t("div",co,[c[2]||(c[2]=t("h3",{class:"text-lg font-medium text-gray-900 mb-6"},"Diagramma di Gantt",-1)),t("div",uo,[(n(!0),l(j,null,M(h.value,p=>(n(),l("div",{key:p.id,class:"bg-gray-50 border border-gray-200 rounded-lg p-4"},[t("div",go,[t("div",mo,[t("div",vo,[t("div",po,[t("div",{class:$(["w-3 h-3 rounded-full",f(p.status)])},null,2)]),t("h4",xo,s(p.name),1),t("span",{class:$(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",i(p.priority)])},s(e(p.priority)),3)]),t("div",ho,[p.start_date?(n(),l("span",fo,"Inizio: "+s(m(p.start_date)),1)):k("",!0),p.due_date?(n(),l("span",_o,"Fine: "+s(m(p.due_date)),1)):k("",!0),p.estimated_hours?(n(),l("span",yo,"Durata: "+s(p.estimated_hours)+"h",1)):k("",!0)]),t("div",bo,[t("div",wo,[t("span",ko,s(m(p.start_date)),1),t("div",$o,[t("div",{class:$(["h-2 rounded-full transition-all duration-300",y(p.status)]),style:T({width:a(p)+"%"})},null,6)]),t("span",jo,s(m(p.due_date)),1)])])]),t("div",Mo,[t("div",Co,[c[0]||(c[0]=t("div",{class:"text-xs text-gray-500"},"Progresso",-1)),t("div",Po,s(a(p))+"%",1)])])])]))),128)),h.value.length===0?(n(),l("div",Bo,c[1]||(c[1]=[t("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),t("p",{class:"text-gray-500 mt-2"},"Nessun task pianificato per questo progetto",-1)]))):k("",!0)])])]))}},Vo={class:"project-timesheet"},zo={class:"bg-white shadow rounded-lg p-6"},Ho={class:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6"},Do={class:"bg-blue-50 p-4 rounded-lg"},Io={class:"text-2xl font-bold text-blue-900"},Ao={class:"bg-green-50 p-4 rounded-lg"},So={class:"text-2xl font-bold text-green-900"},No={class:"bg-purple-50 p-4 rounded-lg"},Fo={class:"text-2xl font-bold text-purple-900"},Lo={class:"space-y-4"},Oo={class:"flex items-center justify-between"},Ro={class:"flex items-center space-x-4"},Eo={class:"flex-shrink-0"},Uo=["src","alt"],Ko={key:1,class:"h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center"},qo={class:"text-xs font-medium text-gray-600"},Go={class:"text-sm font-medium text-gray-900"},Jo={class:"text-xs text-gray-500"},Xo={class:"flex items-center space-x-4"},Qo={class:"text-right"},Wo={class:"text-sm font-medium text-gray-900"},Yo={class:"text-xs text-gray-500"},Zo={key:0,class:"mt-2 text-sm text-gray-600"},tr={key:0,class:"text-center py-8"},er={__name:"ProjectTimesheet",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(g){const x=g,h=_(()=>{var m;return((m=x.project)==null?void 0:m.team_members)||[]}),f=_(()=>{var m;return((m=x.project)==null?void 0:m.timesheet_entries)||[]}),y=_(()=>f.value.reduce((m,o)=>m+(o.hours||0),0)),i=_(()=>{const m=f.value;if(m.length===0)return 0;const o=new Set(m.map(c=>c.date));return(y.value/o.size).toFixed(1)}),e=m=>m?m.split(" ").map(o=>o.charAt(0).toUpperCase()).slice(0,2).join(""):"??",a=m=>m?new Date(m).toLocaleDateString("it-IT"):"";return(m,o)=>(n(),l("div",Vo,[t("div",zo,[o[4]||(o[4]=t("h3",{class:"text-lg font-medium text-gray-900 mb-6"},"Timesheet del Progetto",-1)),t("div",Ho,[t("div",Do,[o[0]||(o[0]=t("div",{class:"text-sm text-blue-600"},"Ore Totali Mese",-1)),t("div",Io,s(y.value)+"h",1)]),t("div",Ao,[o[1]||(o[1]=t("div",{class:"text-sm text-green-600"},"Membri Attivi",-1)),t("div",So,s(h.value.length),1)]),t("div",No,[o[2]||(o[2]=t("div",{class:"text-sm text-purple-600"},"Media Giornaliera",-1)),t("div",Fo,s(i.value)+"h",1)])]),t("div",Lo,[(n(!0),l(j,null,M(f.value,c=>{var p;return n(),l("div",{key:c.id,class:"border border-gray-200 rounded-lg p-4"},[t("div",Oo,[t("div",Ro,[t("div",Eo,[c.user.profile_image?(n(),l("img",{key:0,src:c.user.profile_image,alt:c.user.full_name,class:"h-8 w-8 rounded-full"},null,8,Uo)):(n(),l("div",Ko,[t("span",qo,s(e(c.user.full_name)),1)]))]),t("div",null,[t("h4",Go,s(c.user.full_name),1),t("p",Jo,s(a(c.date)),1)])]),t("div",Xo,[t("div",Qo,[t("div",Wo,s(c.hours)+"h",1),t("div",Yo,s(((p=c.task)==null?void 0:p.name)||"Generale"),1)])])]),c.description?(n(),l("div",Zo,s(c.description),1)):k("",!0)])}),128)),f.value.length===0?(n(),l("div",tr,o[3]||(o[3]=[t("p",{class:"text-gray-500"},"Nessuna registrazione timesheet per questo progetto",-1)]))):k("",!0)])])]))}},sr={class:"project-view"},or={class:"tab-content"},rr={__name:"ProjectView",setup(g){const x=J(),h=X(),f=K(),y=G(),i=P(!0),e=P("overview"),a=_(()=>x.currentProject),m=_(()=>[{id:"overview",label:"Panoramica",icon:"chart-bar"},{id:"tasks",label:"Task",icon:"clipboard-list"},{id:"team",label:"Team",icon:"users"},{id:"gantt",label:"Gantt",icon:"calendar"},{id:"timesheet",label:"Timesheet",icon:"clock"},{id:"files",label:"File",icon:"folder"},{id:"kpi",label:"KPI",icon:"trending-up"}].filter(u=>!!(["overview","tasks","team","files"].includes(u.id)||u.id==="kpi"&&h.hasPermission("view_reports")||u.id==="gantt"&&h.hasPermission("view_all_projects")||u.id==="timesheet"&&h.hasPermission("view_own_timesheets")))),o=_(()=>({overview:F,tasks:Ie,team:Ye,files:as,kpi:ao,gantt:To,timesheet:er})[e.value]||F),c=async()=>{i.value=!0;try{const w=f.params.id;await x.fetchProject(w)}catch(w){console.error("Error loading project:",w)}finally{i.value=!1}},p=()=>{y.push(`/projects/${f.params.id}/edit`)},B=async()=>{if(confirm("Sei sicuro di voler eliminare questo progetto?"))try{await x.deleteProject(f.params.id),y.push("/projects")}catch(w){console.error("Error deleting project:",w)}};return V(()=>f.params.id,w=>{w&&c()}),V(()=>f.hash,w=>{if(w){const u=w.replace("#","");m.value.find(r=>r.id===u)&&(e.value=u)}}),V(e,w=>{const u=`#${w}`;f.hash!==u&&y.replace({...f,hash:u})}),R(()=>{if(f.hash){const w=f.hash.replace("#","");m.value.find(u=>u.id===w)&&(e.value=w)}c()}),(w,u)=>(n(),l("div",sr,[N(ct,{project:a.value,loading:i.value,onEdit:p,onDelete:B},null,8,["project","loading"]),N(ht,{modelValue:e.value,"onUpdate:modelValue":u[0]||(u[0]=r=>e.value=r),tabs:m.value,class:"mb-6"},null,8,["modelValue","tabs"]),t("div",or,[(n(),I(q,null,[(n(),I(L(o.value),{project:a.value,loading:i.value},null,8,["project","loading"]))],1024))])]))}},ar=z(rr,[["__scopeId","data-v-7c1c2936"]]);export{ar as default};
