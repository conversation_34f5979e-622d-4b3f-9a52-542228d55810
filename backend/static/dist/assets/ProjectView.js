import{c as a,o as r,j as t,t as o,n as k,g as j,l as $,F as M,k as P,h as S,D as F,f as y,z as V,I as z,r as C,w as T,A as U,u as K,a as R,N as G,m as q}from"./vendor.js";import{_ as H,u as J,a as Q}from"./app.js";const W={class:"project-header bg-white shadow-sm rounded-lg p-6 mb-6"},X={key:0,class:"animate-pulse"},Y={key:1,class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},Z={class:"flex-1"},tt={class:"flex items-center space-x-3 mb-2"},et={class:"text-2xl font-bold text-gray-900"},st={class:"flex flex-wrap items-center gap-4 text-sm text-gray-500"},ot={key:0},rt={key:1},nt={key:2},at={key:3},lt={class:"mt-4 sm:mt-0 flex space-x-3"},it={key:2,class:"text-center py-8"},dt={__name:"ProjectHeader",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["edit","delete"],setup(c){const v=u=>({planning:"bg-yellow-100 text-yellow-800",active:"bg-green-100 text-green-800",on_hold:"bg-orange-100 text-orange-800",completed:"bg-blue-100 text-blue-800",cancelled:"bg-red-100 text-red-800"})[u]||"bg-gray-100 text-gray-800",m=u=>({planning:"Pianificazione",active:"Attivo",on_hold:"In Pausa",completed:"Completato",cancelled:"Annullato"})[u]||u,h=u=>u?new Date(u).toLocaleDateString("it-IT"):"",_=u=>u?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(u):"";return(u,e)=>(r(),a("div",W,[c.loading?(r(),a("div",X,e[2]||(e[2]=[t("div",{class:"h-8 bg-gray-200 rounded w-1/3 mb-2"},null,-1),t("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1)]))):c.project?(r(),a("div",Y,[t("div",Z,[t("div",tt,[t("h1",et,o(c.project.name),1),t("span",{class:k(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",v(c.project.status)])},o(m(c.project.status)),3)]),t("div",st,[c.project.client?(r(),a("span",ot,[e[3]||(e[3]=t("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),$(" Cliente: "+o(c.project.client.name),1)])):j("",!0),c.project.start_date?(r(),a("span",rt,[e[4]||(e[4]=t("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),$(" Inizio: "+o(h(c.project.start_date)),1)])):j("",!0),c.project.end_date?(r(),a("span",nt,[e[5]||(e[5]=t("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),$(" Fine: "+o(h(c.project.end_date)),1)])):j("",!0),c.project.budget?(r(),a("span",at,[e[6]||(e[6]=t("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})],-1)),$(" Budget: "+o(_(c.project.budget)),1)])):j("",!0)])]),t("div",lt,[t("button",{onClick:e[0]||(e[0]=i=>u.$emit("edit")),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},e[7]||(e[7]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),$(" Modifica ")])),t("button",{onClick:e[1]||(e[1]=i=>u.$emit("delete")),class:"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},e[8]||(e[8]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),$(" Elimina ")]))])])):(r(),a("div",it,e[9]||(e[9]=[t("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},ct=H(dt,[["__scopeId","data-v-24fa98b0"]]),ut={class:"tab-navigation"},gt={class:"border-b border-gray-200"},mt={class:"-mb-px flex space-x-8","aria-label":"Tabs"},vt=["onClick","aria-current"],pt={key:1,class:"ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"},ht={__name:"TabNavigation",props:{modelValue:{type:String,required:!0},tabs:{type:Array,required:!0,validator:c=>c.every(v=>typeof v=="object"&&v.id&&v.label)}},emits:["update:modelValue"],setup(c,{emit:v}){const m=c,h=v,_=i=>m.modelValue===i,u=i=>{h("update:modelValue",i)},e=i=>{const w={"chart-bar":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>`},"clipboard-list":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>`},users:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>`},folder:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
      </svg>`},"trending-up":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      </svg>`},calendar:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`},clock:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}};return w[i]||w["chart-bar"]};return(i,w)=>(r(),a("div",ut,[t("div",gt,[t("nav",mt,[(r(!0),a(M,null,P(c.tabs,g=>(r(),a("button",{key:g.id,onClick:x=>u(g.id),class:k(["whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2",_(g.id)?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"]),"aria-current":_(g.id)?"page":void 0},[g.icon?(r(),S(F(e(g.icon)),{key:0,class:"w-4 h-4"})):j("",!0),t("span",null,o(g.label),1),g.count!==void 0?(r(),a("span",pt,o(g.count),1)):j("",!0)],10,vt))),128))])])]))}},xt=H(ht,[["__scopeId","data-v-c205976e"]]),ft={class:"project-overview"},yt={key:0,class:"animate-pulse space-y-4"},_t={key:1,class:"space-y-6"},bt={class:"bg-white shadow rounded-lg p-6"},wt={key:0,class:"text-gray-600"},kt={key:1,class:"text-gray-400 italic"},jt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},$t={class:"bg-white shadow rounded-lg p-6"},Ct={class:"flex items-center"},Mt={class:"ml-5 w-0 flex-1"},Pt={class:"text-lg font-medium text-gray-900"},Bt={class:"bg-white shadow rounded-lg p-6"},Tt={class:"flex items-center"},Vt={class:"ml-5 w-0 flex-1"},zt={class:"text-lg font-medium text-gray-900"},Ht={class:"bg-white shadow rounded-lg p-6"},It={class:"flex items-center"},At={class:"ml-5 w-0 flex-1"},St={class:"text-lg font-medium text-gray-900"},Dt={class:"bg-white shadow rounded-lg p-6"},Nt={class:"flex items-center"},Rt={class:"ml-5 w-0 flex-1"},Ot={class:"text-lg font-medium text-gray-900"},Ft={class:"bg-white shadow rounded-lg p-6"},Ut={class:"w-full bg-gray-200 rounded-full h-2.5"},Et={class:"text-sm text-gray-500 mt-2"},Lt={class:"bg-white shadow rounded-lg p-6"},Kt={class:"space-y-4"},Gt={class:"flex justify-between items-center"},qt={class:"text-sm font-medium"},Jt={class:"flex justify-between items-center"},Qt={class:"text-sm font-medium"},Wt={class:"w-full bg-gray-200 rounded-full h-3"},Xt={class:"flex justify-between items-center text-sm"},Yt={class:"bg-white shadow rounded-lg p-6"},Zt={class:"space-y-3"},te={class:"flex-shrink-0"},ee=["src","alt"],se={key:1,class:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"},oe={class:"text-xs font-medium text-gray-600"},re={class:"flex-1"},ne={class:"text-sm font-medium text-gray-900"},ae={class:"text-xs text-gray-500"},le={class:"text-right"},ie={class:"text-xs text-gray-500"},de={key:0,class:"text-center py-4"},ce={class:"bg-white shadow rounded-lg p-6"},ue={class:"space-y-3"},ge={class:"flex-shrink-0"},me={class:"flex-1"},ve={class:"text-sm text-gray-900"},pe={class:"flex items-center space-x-2 mt-1"},he={class:"text-xs text-gray-500"},xe={class:"text-xs text-gray-500"},fe={key:0,class:"text-center py-4"},ye={key:2,class:"text-center py-8"},_e={__name:"ProjectOverview",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(c){const v=c,m=y(()=>{if(!v.project||!v.project.task_count)return 0;const l=v.project.completed_tasks||0,s=v.project.task_count||1;return Math.round(l/s*100)}),h=y(()=>{var l;return((l=v.project)==null?void 0:l.recent_activities)||[]}),_=y(()=>{var l;return((l=v.project)==null?void 0:l.team_members)||[]}),u=y(()=>{var p;return(((p=v.project)==null?void 0:p.total_hours)||0)*50}),e=y(()=>{var s;return(((s=v.project)==null?void 0:s.budget)||0)-u.value}),i=y(()=>{var s;const l=((s=v.project)==null?void 0:s.budget)||1;return Math.min(Math.round(u.value/l*100),100)}),w=y(()=>{const l=i.value;return l>=90?"bg-red-600":l>=75?"bg-yellow-600":"bg-green-600"}),g=y(()=>{var s;const l=e.value;return l<0?"text-red-600":l<(((s=v.project)==null?void 0:s.budget)||0)*.1?"text-yellow-600":"text-green-600"}),x=l=>l?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(l):"Non specificato",b=l=>l?new Date(l).toLocaleDateString("it-IT",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"}):"",B=l=>l?l.split(" ").map(s=>s.charAt(0).toUpperCase()).slice(0,2).join(""):"??",f=l=>{const s={task_created:"bg-blue-600",task_completed:"bg-green-600",task_updated:"bg-yellow-600",comment_added:"bg-purple-600",file_uploaded:"bg-indigo-600",member_added:"bg-pink-600",default:"bg-gray-600"};return s[l]||s.default};return(l,s)=>(r(),a("div",ft,[c.loading?(r(),a("div",yt,s[0]||(s[0]=[t("div",{class:"h-4 bg-gray-200 rounded w-3/4"},null,-1),t("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1),t("div",{class:"h-32 bg-gray-200 rounded"},null,-1)]))):c.project?(r(),a("div",_t,[t("div",bt,[s[1]||(s[1]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Descrizione Progetto",-1)),c.project.description?(r(),a("p",wt,o(c.project.description),1)):(r(),a("p",kt,"Nessuna descrizione disponibile"))]),t("div",jt,[t("div",$t,[t("div",Ct,[s[3]||(s[3]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})])],-1)),t("div",Mt,[t("dl",null,[s[2]||(s[2]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Totali",-1)),t("dd",Pt,o(c.project.task_count||0),1)])])])]),t("div",Bt,[t("div",Tt,[s[5]||(s[5]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),t("div",Vt,[t("dl",null,[s[4]||(s[4]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Completati",-1)),t("dd",zt,o(c.project.completed_tasks||0),1)])])])]),t("div",Ht,[t("div",It,[s[7]||(s[7]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),t("div",At,[t("dl",null,[s[6]||(s[6]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Membri Team",-1)),t("dd",St,o(c.project.team_count||0),1)])])])]),t("div",Dt,[t("div",Nt,[s[9]||(s[9]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),t("div",Rt,[t("dl",null,[s[8]||(s[8]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Budget",-1)),t("dd",Ot,o(x(c.project.budget)),1)])])])])]),t("div",Ft,[s[10]||(s[10]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Progresso Progetto",-1)),t("div",Ut,[t("div",{class:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:V({width:`${m.value}%`})},null,4)]),t("p",Et,o(m.value)+"% completato",1)]),t("div",Lt,[s[15]||(s[15]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Budget vs Spese",-1)),t("div",Kt,[t("div",Gt,[s[11]||(s[11]=t("span",{class:"text-sm text-gray-600"},"Budget Totale",-1)),t("span",qt,o(x(c.project.budget)),1)]),s[14]||(s[14]=t("div",{class:"w-full bg-gray-200 rounded-full h-3"},[t("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:{width:"100%"}})],-1)),t("div",Jt,[s[12]||(s[12]=t("span",{class:"text-sm text-gray-600"},"Spese Sostenute",-1)),t("span",Qt,o(x(u.value)),1)]),t("div",Wt,[t("div",{class:k(["h-3 rounded-full transition-all duration-300",w.value]),style:V({width:i.value+"%"})},null,6)]),t("div",Xt,[s[13]||(s[13]=t("span",{class:"text-gray-600"},"Rimanente",-1)),t("span",{class:k(["font-medium",g.value])},o(x(e.value)),3)])])]),t("div",Yt,[s[17]||(s[17]=t("div",{class:"flex items-center justify-between mb-4"},[t("h3",{class:"text-lg font-medium text-gray-900"},"Team Members"),t("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutti")],-1)),t("div",Zt,[(r(!0),a(M,null,P(_.value,p=>(r(),a("div",{key:p.id,class:"flex items-center space-x-3"},[t("div",te,[p.profile_image?(r(),a("img",{key:0,src:p.profile_image,alt:p.full_name,class:"w-8 h-8 rounded-full"},null,8,ee)):(r(),a("div",se,[t("span",oe,o(B(p.full_name)),1)]))]),t("div",re,[t("p",ne,o(p.full_name),1),t("p",ae,o(p.role||"Team Member"),1)]),t("div",le,[t("p",ie,o(p.hours_worked||0)+"h",1)])]))),128)),_.value.length===0?(r(),a("div",de,s[16]||(s[16]=[t("p",{class:"text-gray-500"},"Nessun membro del team assegnato",-1)]))):j("",!0)])]),t("div",ce,[s[20]||(s[20]=t("div",{class:"flex items-center justify-between mb-4"},[t("h3",{class:"text-lg font-medium text-gray-900"},"Attività Recenti"),t("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutte")],-1)),t("div",ue,[(r(!0),a(M,null,P(h.value,p=>(r(),a("div",{key:p.id,class:"flex items-start space-x-3"},[t("div",ge,[t("div",{class:k(["w-2 h-2 rounded-full mt-2",f(p.type)])},null,2)]),t("div",me,[t("p",ve,o(p.description),1),t("div",pe,[t("p",he,o(b(p.created_at)),1),s[18]||(s[18]=t("span",{class:"text-xs text-gray-400"},"•",-1)),t("p",xe,o(p.user_name),1)])])]))),128)),h.value.length===0?(r(),a("div",fe,s[19]||(s[19]=[t("p",{class:"text-gray-500"},"Nessuna attività recente",-1),t("button",{class:"text-sm text-blue-600 hover:text-blue-800 mt-2"},"Aggiungi prima attività",-1)]))):j("",!0)])])])):(r(),a("div",ye,s[21]||(s[21]=[t("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},O=H(_e,[["__scopeId","data-v-47f9c860"]]),be={class:"project-tasks"},we={class:"bg-white shadow rounded-lg p-6"},ke={class:"space-y-4"},je={class:"flex items-start justify-between"},$e={class:"flex-1"},Ce={class:"flex items-center space-x-3"},Me={class:"flex-shrink-0"},Pe={class:"text-lg font-medium text-gray-900"},Be={class:"mt-2 text-sm text-gray-600"},Te={class:"mt-3 flex items-center space-x-4 text-sm text-gray-500"},Ve={key:0},ze={key:1},He={key:2},Ie={key:0,class:"text-center py-8"},Ae={__name:"ProjectTasks",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(c){const v=c,m=y(()=>{var g;return((g=v.project)==null?void 0:g.tasks)||[]}),h=y(()=>{var g;return((g=v.project)==null?void 0:g.team_members)||[]}),_=g=>({todo:"bg-gray-400","in-progress":"bg-blue-500",completed:"bg-green-500","on-hold":"bg-yellow-500"})[g]||"bg-gray-400",u=g=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-red-100 text-red-800"})[g]||"bg-gray-100 text-gray-800",e=g=>({low:"Bassa",medium:"Media",high:"Alta"})[g]||"Non specificata",i=g=>{const x=h.value.find(b=>b.id===g);return x?x.full_name:"Non assegnato"},w=g=>g?new Date(g).toLocaleDateString("it-IT"):"";return(g,x)=>(r(),a("div",be,[t("div",we,[x[2]||(x[2]=z('<div class="flex justify-between items-center mb-6"><h3 class="text-lg font-medium text-gray-900">Task del Progetto</h3><button class="btn-primary"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg> Nuovo Task </button></div>',1)),t("div",ke,[(r(!0),a(M,null,P(m.value,b=>(r(),a("div",{key:b.id,class:"border border-gray-200 rounded-lg p-4 hover:bg-gray-50"},[t("div",je,[t("div",$e,[t("div",Ce,[t("div",Me,[t("div",{class:k(["w-3 h-3 rounded-full",_(b.status)])},null,2)]),t("h5",Pe,o(b.name),1),t("span",{class:k(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",u(b.priority)])},o(e(b.priority)),3)]),t("p",Be,o(b.description),1),t("div",Te,[b.assignee_id?(r(),a("span",Ve,"Assegnato a: "+o(i(b.assignee_id)),1)):j("",!0),b.due_date?(r(),a("span",ze,"Scadenza: "+o(w(b.due_date)),1)):j("",!0),b.estimated_hours?(r(),a("span",He,"Stimate: "+o(b.estimated_hours)+"h",1)):j("",!0)])]),x[0]||(x[0]=t("div",{class:"flex items-center space-x-2"},[t("button",{class:"text-gray-400 hover:text-gray-600"},[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})])])],-1))])]))),128)),m.value.length===0?(r(),a("div",Ie,x[1]||(x[1]=[t("p",{class:"text-gray-500"},"Nessun task trovato per questo progetto",-1)]))):j("",!0)])])]))}},Se={class:"project-team"},De={class:"bg-white shadow rounded-lg p-6"},Ne={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},Re={class:"flex items-center space-x-4"},Oe={class:"flex-shrink-0"},Fe=["src","alt"],Ue={key:1,class:"w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center"},Ee={class:"text-sm font-medium text-gray-600"},Le={class:"flex-1"},Ke={class:"text-lg font-medium text-gray-900"},Ge={class:"text-sm text-gray-600"},qe={class:"text-xs text-gray-500"},Je={class:"mt-4 grid grid-cols-2 gap-4 text-sm"},Qe={class:"font-medium ml-1"},We={class:"font-medium ml-1"},Xe={key:0,class:"col-span-full text-center py-8"},Ye={__name:"ProjectTeam",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(c){const v=c,m=y(()=>{var u;return((u=v.project)==null?void 0:u.team_members)||[]}),h=u=>u?u.split(" ").map(e=>e.charAt(0).toUpperCase()).slice(0,2).join(""):"??",_=u=>{var i;return(((i=v.project)==null?void 0:i.tasks)||[]).filter(w=>w.assignee_id===u).length};return(u,e)=>(r(),a("div",Se,[t("div",De,[e[3]||(e[3]=t("h3",{class:"text-lg font-medium text-gray-900 mb-6"},"Team del Progetto",-1)),t("div",Ne,[(r(!0),a(M,null,P(m.value,i=>(r(),a("div",{key:i.id,class:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"},[t("div",Re,[t("div",Oe,[i.profile_image?(r(),a("img",{key:0,src:i.profile_image,alt:i.full_name,class:"w-12 h-12 rounded-full"},null,8,Fe)):(r(),a("div",Ue,[t("span",Ee,o(h(i.full_name)),1)]))]),t("div",Le,[t("h4",Ke,o(i.full_name),1),t("p",Ge,o(i.role||"Team Member"),1),t("p",qe,o(i.email),1)])]),t("div",Je,[t("div",null,[e[0]||(e[0]=t("span",{class:"text-gray-600"},"Ore Lavorate:",-1)),t("span",Qe,o(i.hours_worked||0)+"h",1)]),t("div",null,[e[1]||(e[1]=t("span",{class:"text-gray-600"},"Task Assegnati:",-1)),t("span",We,o(_(i.id)),1)])])]))),128)),m.value.length===0?(r(),a("div",Xe,e[2]||(e[2]=[t("p",{class:"text-gray-500"},"Nessun membro del team assegnato",-1)]))):j("",!0)])])]))}},Ze={class:"project-files"},ts={__name:"ProjectFiles",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(c){return(v,m)=>(r(),a("div",Ze,m[0]||(m[0]=[z('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">File del Progetto</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Gestione File in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Upload e gestione documenti in arrivo...</p></div></div>',1)])))}},es={class:"project-kpi"},ss={key:0,class:"animate-pulse space-y-4"},os={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},rs={key:1,class:"space-y-6"},ns={class:"bg-white shadow rounded-lg p-6"},as={class:"flex items-center justify-between"},ls=["disabled"],is={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},ds={class:"bg-white shadow rounded-lg p-6"},cs={class:"flex items-center"},us={class:"ml-5 w-0 flex-1"},gs={class:"text-lg font-medium text-gray-900"},ms={class:"text-xs text-gray-500"},vs={class:"bg-white shadow rounded-lg p-6"},ps={class:"flex items-center"},hs={class:"ml-5 w-0 flex-1"},xs={class:"text-lg font-medium text-gray-900"},fs={class:"bg-white shadow rounded-lg p-6"},ys={class:"flex items-center"},_s={class:"ml-5 w-0 flex-1"},bs={class:"text-lg font-medium text-gray-900"},ws={class:"text-xs text-gray-500"},ks={class:"bg-white shadow rounded-lg p-6"},js={class:"flex items-center"},$s={class:"ml-5 w-0 flex-1"},Cs={class:"text-lg font-medium text-gray-900"},Ms={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Ps={class:"bg-white shadow rounded-lg p-6"},Bs={class:"space-y-4"},Ts={class:"flex justify-between text-sm"},Vs={class:"font-medium"},zs={class:"w-full bg-gray-200 rounded-full h-3"},Hs={class:"flex justify-between text-sm"},Is={class:"text-gray-600"},As={class:"font-medium"},Ss={class:"bg-white shadow rounded-lg p-6"},Ds={class:"space-y-4"},Ns={class:"flex justify-between text-sm"},Rs={class:"font-medium"},Os={class:"w-full bg-gray-200 rounded-full h-3"},Fs={class:"flex justify-between text-sm"},Us={class:"text-gray-600"},Es={class:"font-medium"},Ls={class:"bg-white shadow rounded-lg p-6"},Ks={class:"flex items-center justify-between mb-4"},Gs={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},qs={class:"text-center p-4 border rounded-lg"},Js={class:"text-xs text-gray-500"},Qs={class:"text-center p-4 border rounded-lg"},Ws={class:"text-xs text-gray-500"},Xs={class:"text-center p-4 border rounded-lg"},Ys={class:"text-xs text-gray-500"},Zs={key:2,class:"text-center py-8"},to={__name:"ProjectKPI",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(c,{emit:v}){const m=c,h=v,_=C(!1),u=C(!1),e=C({totalHours:0,workDays:0,totalCosts:0,costVariance:0,potentialRevenue:0,actualRevenue:0,marginPercentage:0}),i=C({budget:80,time:85,margin:15}),w=y(()=>{var n;return!((n=m.project)!=null&&n.budget)||e.value.totalCosts===0?0:Math.round(e.value.totalCosts/m.project.budget*100)}),g=y(()=>{var n;return!((n=m.project)!=null&&n.estimated_hours)||e.value.totalHours===0?0:Math.round(e.value.totalHours/m.project.estimated_hours*100)}),x=y(()=>{const n=e.value.costVariance;return n>0?"text-red-600":n<0?"text-green-600":"text-gray-600"}),b=y(()=>{const n=e.value.marginPercentage;return n>=i.value.margin?"text-green-600":n>=i.value.margin*.7?"text-yellow-600":"text-red-600"}),B=y(()=>{const n=e.value.marginPercentage;return n>=i.value.margin?"Ottimo":n>=i.value.margin*.7?"Accettabile":"Critico"}),f=y(()=>{const n=w.value;return n>=i.value.budget?"text-red-600":n>=i.value.budget*.8?"text-yellow-600":"text-green-600"}),l=y(()=>{const n=g.value;return n>=i.value.time?"text-red-600":n>=i.value.time*.8?"text-yellow-600":"text-green-600"}),s=y(()=>{const n=e.value.marginPercentage;return n>=i.value.margin?"text-green-600":n>=i.value.margin*.7?"text-yellow-600":"text-red-600"}),p=n=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(n||0),I=n=>`${n||0}h`,D=n=>`${(n||0).toFixed(1)}%`,A=async()=>{var n;(n=m.project)!=null&&n.id&&E()},E=()=>{const n=m.project;n&&(e.value={totalHours:n.total_hours||0,workDays:Math.ceil((n.total_hours||0)/8),totalCosts:(n.total_hours||0)*50,costVariance:(n.total_hours||0)*50-(n.budget||0),potentialRevenue:n.budget||0,actualRevenue:n.invoiced_amount||0,marginPercentage:n.budget?(n.budget-(n.total_hours||0)*50)/n.budget*100:0})},L=async()=>{_.value=!0;try{await A(),h("refresh")}catch(n){console.error("Error refreshing KPIs:",n)}finally{_.value=!1}};return T(()=>m.project,n=>{n&&A()},{immediate:!0}),U(()=>{m.project&&A()}),(n,d)=>(r(),a("div",es,[c.loading?(r(),a("div",ss,[t("div",os,[(r(),a(M,null,P(4,N=>t("div",{key:N,class:"bg-gray-200 rounded-lg h-24"})),64))]),d[1]||(d[1]=t("div",{class:"bg-gray-200 rounded-lg h-64"},null,-1))])):c.project?(r(),a("div",rs,[t("div",ns,[t("div",as,[d[4]||(d[4]=t("div",null,[t("h3",{class:"text-lg font-medium text-gray-900"},"KPI Progetto"),t("p",{class:"text-sm text-gray-600"},"Dashboard metriche e performance del progetto")],-1)),t("button",{onClick:L,disabled:_.value,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(r(),a("svg",{class:k(["w-4 h-4 mr-2",{"animate-spin":_.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},d[2]||(d[2]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]),2)),d[3]||(d[3]=$(" Aggiorna "))],8,ls)])]),t("div",is,[t("div",ds,[t("div",cs,[d[6]||(d[6]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),t("div",us,[t("dl",null,[d[5]||(d[5]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ore Totali",-1)),t("dd",gs,o(I(e.value.totalHours)),1),t("dd",ms,o(e.value.workDays)+" giorni lavorati",1)])])])]),t("div",vs,[t("div",ps,[d[8]||(d[8]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),t("div",hs,[t("dl",null,[d[7]||(d[7]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Costi Totali",-1)),t("dd",xs,o(p(e.value.totalCosts)),1),t("dd",{class:k(["text-xs",x.value])},o(p(e.value.costVariance))+" vs budget",3)])])])]),t("div",fs,[t("div",ys,[d[10]||(d[10]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),t("div",_s,[t("dl",null,[d[9]||(d[9]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ricavi Potenziali",-1)),t("dd",bs,o(p(e.value.potentialRevenue)),1),t("dd",ws,o(p(e.value.actualRevenue))+" fatturati",1)])])])]),t("div",ks,[t("div",js,[d[12]||(d[12]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),t("div",$s,[t("dl",null,[d[11]||(d[11]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Margine",-1)),t("dd",Cs,o(D(e.value.marginPercentage)),1),t("dd",{class:k(["text-xs",b.value])},o(B.value),3)])])])])]),t("div",Ms,[t("div",Ps,[d[14]||(d[14]=t("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Budget",-1)),t("div",Bs,[t("div",Ts,[d[13]||(d[13]=t("span",{class:"text-gray-600"},"Budget Totale",-1)),t("span",Vs,o(p(c.project.budget||0)),1)]),t("div",zs,[t("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:V({width:w.value+"%"})},null,4)]),t("div",Hs,[t("span",Is,"Utilizzato: "+o(p(e.value.totalCosts)),1),t("span",As,o(w.value)+"%",1)])])]),t("div",Ss,[d[16]||(d[16]=t("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Tempo",-1)),t("div",Ds,[t("div",Ns,[d[15]||(d[15]=t("span",{class:"text-gray-600"},"Ore Stimate",-1)),t("span",Rs,o(I(c.project.estimated_hours||0)),1)]),t("div",Os,[t("div",{class:"bg-green-600 h-3 rounded-full transition-all duration-300",style:V({width:g.value+"%"})},null,4)]),t("div",Fs,[t("span",Us,"Lavorate: "+o(I(e.value.totalHours)),1),t("span",Es,o(g.value)+"%",1)])])])]),t("div",Ls,[t("div",Ks,[d[17]||(d[17]=t("h4",{class:"text-lg font-medium text-gray-900"},"Soglie KPI",-1)),t("button",{onClick:d[0]||(d[0]=N=>u.value=!0),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Configura ")]),t("div",Gs,[t("div",qs,[t("div",{class:k(["text-2xl font-bold",f.value])},o(w.value)+"% ",3),d[18]||(d[18]=t("div",{class:"text-sm text-gray-600"},"Budget Usage",-1)),t("div",Js,"Soglia: "+o(i.value.budget)+"%",1)]),t("div",Qs,[t("div",{class:k(["text-2xl font-bold",l.value])},o(g.value)+"% ",3),d[19]||(d[19]=t("div",{class:"text-sm text-gray-600"},"Time Usage",-1)),t("div",Ws,"Soglia: "+o(i.value.time)+"%",1)]),t("div",Xs,[t("div",{class:k(["text-2xl font-bold",s.value])},o(D(e.value.marginPercentage)),3),d[20]||(d[20]=t("div",{class:"text-sm text-gray-600"},"Margine",-1)),t("div",Ys,"Soglia: "+o(i.value.margin)+"%",1)])])])])):(r(),a("div",Zs,d[21]||(d[21]=[t("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},eo={class:"project-gantt"},so={__name:"ProjectGantt",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(c){return(v,m)=>(r(),a("div",eo,m[0]||(m[0]=[z('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">Diagramma di Gantt</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Gantt Chart in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Integrazione Frappe Gantt in arrivo...</p></div></div>',1)])))}},oo={class:"project-timesheet"},ro={__name:"ProjectTimesheet",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(c){return(v,m)=>(r(),a("div",oo,m[0]||(m[0]=[z('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">Timesheet del Progetto</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Timesheet in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Gestione ore lavorate in arrivo...</p></div></div>',1)])))}},no={class:"project-view"},ao={class:"tab-content"},lo={__name:"ProjectView",setup(c){const v=J(),m=Q(),h=K(),_=q(),u=C(!0),e=C("overview"),i=y(()=>v.currentProject),w=y(()=>[{id:"overview",label:"Panoramica",icon:"chart-bar"},{id:"tasks",label:"Task",icon:"clipboard-list"},{id:"team",label:"Team",icon:"users"},{id:"gantt",label:"Gantt",icon:"calendar"},{id:"timesheet",label:"Timesheet",icon:"clock"},{id:"files",label:"File",icon:"folder"},{id:"kpi",label:"KPI",icon:"trending-up"}].filter(l=>!!(["overview","tasks","team","files"].includes(l.id)||l.id==="kpi"&&m.hasPermission("view_reports")||l.id==="gantt"&&m.hasPermission("view_all_projects")||l.id==="timesheet"&&m.hasPermission("view_own_timesheets")))),g=y(()=>({overview:O,tasks:Ae,team:Ye,files:ts,kpi:to,gantt:so,timesheet:ro})[e.value]||O),x=async()=>{u.value=!0;try{const f=h.params.id;await v.fetchProject(f)}catch(f){console.error("Error loading project:",f)}finally{u.value=!1}},b=()=>{_.push(`/projects/${h.params.id}/edit`)},B=async()=>{if(confirm("Sei sicuro di voler eliminare questo progetto?"))try{await v.deleteProject(h.params.id),_.push("/projects")}catch(f){console.error("Error deleting project:",f)}};return T(()=>h.params.id,f=>{f&&x()}),T(()=>h.hash,f=>{if(f){const l=f.replace("#","");w.value.find(s=>s.id===l)&&(e.value=l)}}),T(e,f=>{const l=`#${f}`;h.hash!==l&&_.replace({...h,hash:l})}),U(()=>{if(h.hash){const f=h.hash.replace("#","");w.value.find(l=>l.id===f)&&(e.value=f)}x()}),(f,l)=>(r(),a("div",no,[R(ct,{project:i.value,loading:u.value,onEdit:b,onDelete:B},null,8,["project","loading"]),R(xt,{modelValue:e.value,"onUpdate:modelValue":l[0]||(l[0]=s=>e.value=s),tabs:w.value,class:"mb-6"},null,8,["modelValue","tabs"]),t("div",ao,[(r(),S(G,null,[(r(),S(F(g.value),{project:i.value,loading:u.value},null,8,["project","loading"]))],1024))])]))}},uo=H(lo,[["__scopeId","data-v-7c1c2936"]]);export{uo as default};
