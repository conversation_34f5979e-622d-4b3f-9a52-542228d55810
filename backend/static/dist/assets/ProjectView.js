import{e as R,r as j,f as _,c as u,o as c,j as e,t as f,n as S,g as b,l as P,F as N,k as L,h as A,D as O,z as K,M,w as T,A as U,u as q,a as D,m as J}from"./vendor.js";import{a as $,_ as V,u as Q}from"./app.js";const W=R("projects",()=>{const t=j([]),l=j(null),r=j(!1),i=j(null),g=j({page:1,perPage:20,total:0,totalPages:0}),a=j({search:"",status:"",client:"",type:""}),o=_(()=>{let n=t.value;if(a.value.search){const d=a.value.search.toLowerCase();n=n.filter(v=>{var p,h,k;return v.name.toLowerCase().includes(d)||((p=v.description)==null?void 0:p.toLowerCase().includes(d))||((k=(h=v.client)==null?void 0:h.name)==null?void 0:k.toLowerCase().includes(d))})}return a.value.status&&(n=n.filter(d=>d.status===a.value.status)),a.value.client&&(n=n.filter(d=>d.client_id===a.value.client)),a.value.type&&(n=n.filter(d=>d.project_type===a.value.type)),n}),s=_(()=>{const n={};return t.value.forEach(d=>{n[d.status]||(n[d.status]=[]),n[d.status].push(d)}),n});return{projects:t,currentProject:l,loading:r,error:i,pagination:g,filters:a,filteredProjects:o,projectsByStatus:s,fetchProjects:async(n={})=>{var d,v;r.value=!0,i.value=null;try{const p=new URLSearchParams({page:n.page||g.value.page,per_page:n.perPage||g.value.perPage,search:n.search||a.value.search,status:n.status||a.value.status,client:n.client||a.value.client,type:n.type||a.value.type}),h=await $.get(`/api/projects?${p}`);h.data.success&&(t.value=h.data.data.projects,g.value=h.data.data.pagination)}catch(p){i.value=((v=(d=p.response)==null?void 0:d.data)==null?void 0:v.message)||"Errore nel caricamento progetti",console.error("Error fetching projects:",p)}finally{r.value=!1}},fetchProject:async n=>{var d,v;r.value=!0,i.value=null;try{const p=await $.get(`/api/projects/${n}`);p.data.success&&(l.value=p.data.data.project)}catch(p){throw i.value=((v=(d=p.response)==null?void 0:d.data)==null?void 0:v.message)||"Errore nel caricamento progetto",console.error("Error fetching project:",p),p}finally{r.value=!1}},createProject:async n=>{var d,v;r.value=!0,i.value=null;try{const p=await $.post("/api/projects",n);if(p.data.success){const h=p.data.data.project;return t.value.unshift(h),h}}catch(p){throw i.value=((v=(d=p.response)==null?void 0:d.data)==null?void 0:v.message)||"Errore nella creazione progetto",console.error("Error creating project:",p),p}finally{r.value=!1}},updateProject:async(n,d)=>{var v,p,h;r.value=!0,i.value=null;try{const k=await $.put(`/api/projects/${n}`,d);if(k.data.success){const z=k.data.data.project,I=t.value.findIndex(G=>G.id===n);return I!==-1&&(t.value[I]=z),((v=l.value)==null?void 0:v.id)===n&&(l.value=z),z}}catch(k){throw i.value=((h=(p=k.response)==null?void 0:p.data)==null?void 0:h.message)||"Errore nell'aggiornamento progetto",console.error("Error updating project:",k),k}finally{r.value=!1}},deleteProject:async n=>{var d,v,p;r.value=!0,i.value=null;try{(await $.delete(`/api/projects/${n}`)).data.success&&(t.value=t.value.filter(k=>k.id!==n),((d=l.value)==null?void 0:d.id)===n&&(l.value=null))}catch(h){throw i.value=((p=(v=h.response)==null?void 0:v.data)==null?void 0:p.message)||"Errore nell'eliminazione progetto",console.error("Error deleting project:",h),h}finally{r.value=!1}},setFilters:n=>{a.value={...a.value,...n}},clearFilters:()=>{a.value={search:"",status:"",client:"",type:""}},setCurrentProject:n=>{l.value=n},clearCurrentProject:()=>{l.value=null},$reset:()=>{t.value=[],l.value=null,r.value=!1,i.value=null,g.value={page:1,perPage:20,total:0,totalPages:0},a.value={search:"",status:"",client:"",type:""}}}}),X={class:"project-header bg-white shadow-sm rounded-lg p-6 mb-6"},Y={key:0,class:"animate-pulse"},Z={key:1,class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},ee={class:"flex-1"},te={class:"flex items-center space-x-3 mb-2"},se={class:"text-2xl font-bold text-gray-900"},oe={class:"flex flex-wrap items-center gap-4 text-sm text-gray-500"},re={key:0},ae={key:1},ne={key:2},le={key:3},ie={class:"mt-4 sm:mt-0 flex space-x-3"},ce={key:2,class:"text-center py-8"},de={__name:"ProjectHeader",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["edit","delete"],setup(t){const l=a=>({planning:"bg-yellow-100 text-yellow-800",active:"bg-green-100 text-green-800",on_hold:"bg-orange-100 text-orange-800",completed:"bg-blue-100 text-blue-800",cancelled:"bg-red-100 text-red-800"})[a]||"bg-gray-100 text-gray-800",r=a=>({planning:"Pianificazione",active:"Attivo",on_hold:"In Pausa",completed:"Completato",cancelled:"Annullato"})[a]||a,i=a=>a?new Date(a).toLocaleDateString("it-IT"):"",g=a=>a?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(a):"";return(a,o)=>(c(),u("div",X,[t.loading?(c(),u("div",Y,o[2]||(o[2]=[e("div",{class:"h-8 bg-gray-200 rounded w-1/3 mb-2"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1)]))):t.project?(c(),u("div",Z,[e("div",ee,[e("div",te,[e("h1",se,f(t.project.name),1),e("span",{class:S(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",l(t.project.status)])},f(r(t.project.status)),3)]),e("div",oe,[t.project.client?(c(),u("span",re,[o[3]||(o[3]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),P(" Cliente: "+f(t.project.client.name),1)])):b("",!0),t.project.start_date?(c(),u("span",ae,[o[4]||(o[4]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),P(" Inizio: "+f(i(t.project.start_date)),1)])):b("",!0),t.project.end_date?(c(),u("span",ne,[o[5]||(o[5]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),P(" Fine: "+f(i(t.project.end_date)),1)])):b("",!0),t.project.budget?(c(),u("span",le,[o[6]||(o[6]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})],-1)),P(" Budget: "+f(g(t.project.budget)),1)])):b("",!0)])]),e("div",ie,[e("button",{onClick:o[0]||(o[0]=s=>a.$emit("edit")),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},o[7]||(o[7]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),P(" Modifica ")])),e("button",{onClick:o[1]||(o[1]=s=>a.$emit("delete")),class:"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},o[8]||(o[8]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),P(" Elimina ")]))])])):(c(),u("div",ce,o[9]||(o[9]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},ue=V(de,[["__scopeId","data-v-24fa98b0"]]),pe={class:"tab-navigation"},me={class:"border-b border-gray-200"},ve={class:"-mb-px flex space-x-8","aria-label":"Tabs"},he=["onClick","aria-current"],ge={key:1,class:"ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"},fe={__name:"TabNavigation",props:{modelValue:{type:String,required:!0},tabs:{type:Array,required:!0,validator:t=>t.every(l=>typeof l=="object"&&l.id&&l.label)}},emits:["update:modelValue"],setup(t,{emit:l}){const r=t,i=l,g=s=>r.modelValue===s,a=s=>{i("update:modelValue",s)},o=s=>{const x={"chart-bar":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>`},"clipboard-list":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>`},users:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>`},folder:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
      </svg>`},"trending-up":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      </svg>`},calendar:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`},clock:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}};return x[s]||x["chart-bar"]};return(s,x)=>(c(),u("div",pe,[e("div",me,[e("nav",ve,[(c(!0),u(N,null,L(t.tabs,y=>(c(),u("button",{key:y.id,onClick:C=>a(y.id),class:S(["whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2",g(y.id)?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"]),"aria-current":g(y.id)?"page":void 0},[y.icon?(c(),A(O(o(y.icon)),{key:0,class:"w-4 h-4"})):b("",!0),e("span",null,f(y.label),1),y.count!==void 0?(c(),u("span",ge,f(y.count),1)):b("",!0)],10,he))),128))])])]))}},xe=V(fe,[["__scopeId","data-v-c205976e"]]),ye={class:"project-overview"},we={key:0,class:"animate-pulse space-y-4"},ke={key:1,class:"space-y-6"},je={class:"bg-white shadow rounded-lg p-6"},be={key:0,class:"text-gray-600"},_e={key:1,class:"text-gray-400 italic"},Pe={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Me={class:"bg-white shadow rounded-lg p-6"},Ce={class:"flex items-center"},$e={class:"ml-5 w-0 flex-1"},Be={class:"text-lg font-medium text-gray-900"},Ve={class:"bg-white shadow rounded-lg p-6"},ze={class:"flex items-center"},Te={class:"ml-5 w-0 flex-1"},He={class:"text-lg font-medium text-gray-900"},Ee={class:"bg-white shadow rounded-lg p-6"},Ie={class:"flex items-center"},De={class:"ml-5 w-0 flex-1"},Fe={class:"text-lg font-medium text-gray-900"},Se={class:"bg-white shadow rounded-lg p-6"},Ne={class:"flex items-center"},Le={class:"ml-5 w-0 flex-1"},Ae={class:"text-lg font-medium text-gray-900"},Oe={class:"bg-white shadow rounded-lg p-6"},Ge={class:"w-full bg-gray-200 rounded-full h-2.5"},Re={class:"text-sm text-gray-500 mt-2"},Ke={class:"bg-white shadow rounded-lg p-6"},Ue={class:"space-y-3"},qe={class:"flex-1"},Je={class:"text-sm text-gray-900"},Qe={class:"text-xs text-gray-500"},We={key:0,class:"text-center py-4"},Xe={key:2,class:"text-center py-8"},Ye={__name:"ProjectOverview",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(t){const l=t,r=_(()=>{if(!l.project||!l.project.task_count)return 0;const o=l.project.completed_tasks||0,s=l.project.task_count||1;return Math.round(o/s*100)}),i=_(()=>{var o;return((o=l.project)==null?void 0:o.recent_activities)||[]}),g=o=>o?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(o):"Non specificato",a=o=>o?new Date(o).toLocaleDateString("it-IT",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"}):"";return(o,s)=>(c(),u("div",ye,[t.loading?(c(),u("div",we,s[0]||(s[0]=[e("div",{class:"h-4 bg-gray-200 rounded w-3/4"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1),e("div",{class:"h-32 bg-gray-200 rounded"},null,-1)]))):t.project?(c(),u("div",ke,[e("div",je,[s[1]||(s[1]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Descrizione Progetto",-1)),t.project.description?(c(),u("p",be,f(t.project.description),1)):(c(),u("p",_e,"Nessuna descrizione disponibile"))]),e("div",Pe,[e("div",Me,[e("div",Ce,[s[3]||(s[3]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})])],-1)),e("div",$e,[e("dl",null,[s[2]||(s[2]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Totali",-1)),e("dd",Be,f(t.project.task_count||0),1)])])])]),e("div",Ve,[e("div",ze,[s[5]||(s[5]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",Te,[e("dl",null,[s[4]||(s[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Completati",-1)),e("dd",He,f(t.project.completed_tasks||0),1)])])])]),e("div",Ee,[e("div",Ie,[s[7]||(s[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",De,[e("dl",null,[s[6]||(s[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Membri Team",-1)),e("dd",Fe,f(t.project.team_count||0),1)])])])]),e("div",Se,[e("div",Ne,[s[9]||(s[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",Le,[e("dl",null,[s[8]||(s[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Budget",-1)),e("dd",Ae,f(g(t.project.budget)),1)])])])])]),e("div",Oe,[s[10]||(s[10]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Progresso Progetto",-1)),e("div",Ge,[e("div",{class:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:K({width:`${r.value}%`})},null,4)]),e("p",Re,f(r.value)+"% completato",1)]),e("div",Ke,[s[13]||(s[13]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Attività Recenti",-1)),e("div",Ue,[(c(!0),u(N,null,L(i.value,x=>(c(),u("div",{key:x.id,class:"flex items-start space-x-3"},[s[11]||(s[11]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-2 h-2 bg-blue-600 rounded-full mt-2"})],-1)),e("div",qe,[e("p",Je,f(x.description),1),e("p",Qe,f(a(x.created_at)),1)])]))),128)),i.value.length===0?(c(),u("div",We,s[12]||(s[12]=[e("p",{class:"text-gray-500"},"Nessuna attività recente",-1)]))):b("",!0)])])])):(c(),u("div",Xe,s[14]||(s[14]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},F=V(Ye,[["__scopeId","data-v-9cac1cbd"]]),Ze={class:"project-tasks"},et={__name:"ProjectTasks",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(t){return(l,r)=>(c(),u("div",Ze,r[0]||(r[0]=[M('<div class="bg-white shadow rounded-lg p-6"><div class="flex justify-between items-center mb-6"><h3 class="text-lg font-medium text-gray-900">Task del Progetto</h3><button class="btn-primary"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg> Nuovo Task </button></div><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Componente Task in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Migrazione da template legacy Alpine.js in corso...</p></div></div>',1)])))}},tt={class:"project-team"},st={__name:"ProjectTeam",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(t){return(l,r)=>(c(),u("div",tt,r[0]||(r[0]=[M('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">Team del Progetto</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Componente Team in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Gestione membri del team in arrivo...</p></div></div>',1)])))}},ot={class:"project-files"},rt={__name:"ProjectFiles",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(t){return(l,r)=>(c(),u("div",ot,r[0]||(r[0]=[M('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">File del Progetto</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Gestione File in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Upload e gestione documenti in arrivo...</p></div></div>',1)])))}},at={class:"project-kpi"},nt={__name:"ProjectKPI",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(t){return(l,r)=>(c(),u("div",at,r[0]||(r[0]=[M('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">KPI del Progetto</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">KPI e Metriche in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Dashboard KPI progetto in arrivo...</p></div></div>',1)])))}},lt={class:"project-gantt"},it={__name:"ProjectGantt",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(t){return(l,r)=>(c(),u("div",lt,r[0]||(r[0]=[M('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">Diagramma di Gantt</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Gantt Chart in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Integrazione Frappe Gantt in arrivo...</p></div></div>',1)])))}},ct={class:"project-timesheet"},dt={__name:"ProjectTimesheet",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(t){return(l,r)=>(c(),u("div",ct,r[0]||(r[0]=[M('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">Timesheet del Progetto</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Timesheet in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Gestione ore lavorate in arrivo...</p></div></div>',1)])))}},ut={class:"project-view"},pt={class:"tab-content"},mt={__name:"ProjectView",setup(t){const l=W(),r=Q(),i=q(),g=J(),a=j(!0),o=j("overview"),s=_(()=>l.currentProject),x=_(()=>{const m=[{id:"overview",label:"Panoramica",icon:"chart-bar"},{id:"tasks",label:"Task",icon:"clipboard-list"},{id:"team",label:"Team",icon:"users"},{id:"files",label:"File",icon:"folder"}];return r.hasPermission("view_reports")&&m.push({id:"kpi",label:"KPI",icon:"trending-up"}),r.hasPermission("view_gantt")&&m.push({id:"gantt",label:"Gantt",icon:"calendar"}),r.hasPermission("view_timesheets")&&m.push({id:"timesheet",label:"Timesheet",icon:"clock"}),m}),y=_(()=>({overview:F,tasks:et,team:st,files:rt,kpi:nt,gantt:it,timesheet:dt})[o.value]||F),C=async()=>{a.value=!0;try{const m=i.params.id;await l.fetchProject(m)}catch(m){console.error("Error loading project:",m)}finally{a.value=!1}},H=()=>{g.push(`/projects/${i.params.id}/edit`)},E=async()=>{if(confirm("Sei sicuro di voler eliminare questo progetto?"))try{await l.deleteProject(i.params.id),g.push("/projects")}catch(m){console.error("Error deleting project:",m)}};return T(()=>i.params.id,m=>{m&&C()}),T(()=>i.hash,m=>{if(m){const w=m.replace("#","");x.value.find(B=>B.id===w)&&(o.value=w)}}),T(o,m=>{const w=`#${m}`;i.hash!==w&&g.replace({...i,hash:w})}),U(()=>{if(i.hash){const m=i.hash.replace("#","");x.value.find(w=>w.id===m)&&(o.value=m)}C()}),(m,w)=>(c(),u("div",ut,[D(ue,{project:s.value,loading:a.value,onEdit:H,onDelete:E},null,8,["project","loading"]),D(xe,{modelValue:o.value,"onUpdate:modelValue":w[0]||(w[0]=B=>o.value=B),tabs:x.value,class:"mb-6"},null,8,["modelValue","tabs"]),e("div",pt,[(c(),A(O(y.value),{project:s.value,loading:a.value,onRefresh:C},null,40,["project","loading"]))])]))}},xt=V(mt,[["__scopeId","data-v-c144d9c5"]]);export{xt as default};
