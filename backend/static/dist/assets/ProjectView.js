import{c as i,o as l,j as e,t as n,n as b,g as $,l as P,F as H,k as I,h as K,D as G,f as y,z as R,I as T,r as B,w as z,A as O,u as L,a as j,v as M,N as C,m as q}from"./vendor.js";import{_ as S,a as J,u as Q,b as W}from"./app.js";const X={class:"project-header bg-white shadow-sm rounded-lg p-6 mb-6"},Y={key:0,class:"animate-pulse"},Z={key:1,class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},ee={class:"flex-1"},te={class:"flex items-center space-x-3 mb-2"},se={class:"text-2xl font-bold text-gray-900"},oe={class:"flex flex-wrap items-center gap-4 text-sm text-gray-500"},re={key:0},ne={key:1},ae={key:2},le={key:3},ie={class:"mt-4 sm:mt-0 flex space-x-3"},de={key:2,class:"text-center py-8"},ce={__name:"ProjectHeader",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["edit","delete"],setup(a){const g=m=>({planning:"bg-yellow-100 text-yellow-800",active:"bg-green-100 text-green-800",on_hold:"bg-orange-100 text-orange-800",completed:"bg-blue-100 text-blue-800",cancelled:"bg-red-100 text-red-800"})[m]||"bg-gray-100 text-gray-800",d=m=>({planning:"Pianificazione",active:"Attivo",on_hold:"In Pausa",completed:"Completato",cancelled:"Annullato"})[m]||m,x=m=>m?new Date(m).toLocaleDateString("it-IT"):"",_=m=>m?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(m):"";return(m,s)=>(l(),i("div",X,[a.loading?(l(),i("div",Y,s[2]||(s[2]=[e("div",{class:"h-8 bg-gray-200 rounded w-1/3 mb-2"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1)]))):a.project?(l(),i("div",Z,[e("div",ee,[e("div",te,[e("h1",se,n(a.project.name),1),e("span",{class:b(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",g(a.project.status)])},n(d(a.project.status)),3)]),e("div",oe,[a.project.client?(l(),i("span",re,[s[3]||(s[3]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),P(" Cliente: "+n(a.project.client.name),1)])):$("",!0),a.project.start_date?(l(),i("span",ne,[s[4]||(s[4]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),P(" Inizio: "+n(x(a.project.start_date)),1)])):$("",!0),a.project.end_date?(l(),i("span",ae,[s[5]||(s[5]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),P(" Fine: "+n(x(a.project.end_date)),1)])):$("",!0),a.project.budget?(l(),i("span",le,[s[6]||(s[6]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})],-1)),P(" Budget: "+n(_(a.project.budget)),1)])):$("",!0)])]),e("div",ie,[e("button",{onClick:s[0]||(s[0]=u=>m.$emit("edit")),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},s[7]||(s[7]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),P(" Modifica ")])),e("button",{onClick:s[1]||(s[1]=u=>m.$emit("delete")),class:"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},s[8]||(s[8]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),P(" Elimina ")]))])])):(l(),i("div",de,s[9]||(s[9]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},ue=S(ce,[["__scopeId","data-v-24fa98b0"]]),me={class:"tab-navigation"},ge={class:"border-b border-gray-200"},ve={class:"-mb-px flex space-x-8","aria-label":"Tabs"},pe=["onClick","aria-current"],he={key:1,class:"ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"},xe={__name:"TabNavigation",props:{modelValue:{type:String,required:!0},tabs:{type:Array,required:!0,validator:a=>a.every(g=>typeof g=="object"&&g.id&&g.label)}},emits:["update:modelValue"],setup(a,{emit:g}){const d=a,x=g,_=u=>d.modelValue===u,m=u=>{x("update:modelValue",u)},s=u=>{const w={"chart-bar":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>`},"clipboard-list":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>`},users:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>`},folder:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
      </svg>`},"trending-up":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      </svg>`},calendar:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`},clock:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}};return w[u]||w["chart-bar"]};return(u,w)=>(l(),i("div",me,[e("div",ge,[e("nav",ve,[(l(!0),i(H,null,I(a.tabs,v=>(l(),i("button",{key:v.id,onClick:k=>m(v.id),class:b(["whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2",_(v.id)?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"]),"aria-current":_(v.id)?"page":void 0},[v.icon?(l(),K(G(s(v.icon)),{key:0,class:"w-4 h-4"})):$("",!0),e("span",null,n(v.label),1),v.count!==void 0?(l(),i("span",he,n(v.count),1)):$("",!0)],10,pe))),128))])])]))}},fe=S(xe,[["__scopeId","data-v-c205976e"]]),ye={class:"project-overview"},_e={key:0,class:"animate-pulse space-y-4"},we={key:1,class:"space-y-6"},be={class:"bg-white shadow rounded-lg p-6"},ke={key:0,class:"text-gray-600"},je={key:1,class:"text-gray-400 italic"},$e={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Me={class:"bg-white shadow rounded-lg p-6"},Ce={class:"flex items-center"},Pe={class:"ml-5 w-0 flex-1"},Be={class:"text-lg font-medium text-gray-900"},Ve={class:"bg-white shadow rounded-lg p-6"},Te={class:"flex items-center"},ze={class:"ml-5 w-0 flex-1"},He={class:"text-lg font-medium text-gray-900"},Ie={class:"bg-white shadow rounded-lg p-6"},Re={class:"flex items-center"},Se={class:"ml-5 w-0 flex-1"},De={class:"text-lg font-medium text-gray-900"},Ae={class:"bg-white shadow rounded-lg p-6"},Ne={class:"flex items-center"},Fe={class:"ml-5 w-0 flex-1"},Oe={class:"text-lg font-medium text-gray-900"},Ee={class:"bg-white shadow rounded-lg p-6"},Ue={class:"w-full bg-gray-200 rounded-full h-2.5"},Ke={class:"text-sm text-gray-500 mt-2"},Ge={class:"bg-white shadow rounded-lg p-6"},Le={class:"space-y-4"},qe={class:"flex justify-between items-center"},Je={class:"text-sm font-medium"},Qe={class:"flex justify-between items-center"},We={class:"text-sm font-medium"},Xe={class:"w-full bg-gray-200 rounded-full h-3"},Ye={class:"flex justify-between items-center text-sm"},Ze={class:"bg-white shadow rounded-lg p-6"},et={class:"space-y-3"},tt={class:"flex-shrink-0"},st=["src","alt"],ot={key:1,class:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"},rt={class:"text-xs font-medium text-gray-600"},nt={class:"flex-1"},at={class:"text-sm font-medium text-gray-900"},lt={class:"text-xs text-gray-500"},it={class:"text-right"},dt={class:"text-xs text-gray-500"},ct={key:0,class:"text-center py-4"},ut={class:"bg-white shadow rounded-lg p-6"},mt={class:"space-y-3"},gt={class:"flex-shrink-0"},vt={class:"flex-1"},pt={class:"text-sm text-gray-900"},ht={class:"flex items-center space-x-2 mt-1"},xt={class:"text-xs text-gray-500"},ft={class:"text-xs text-gray-500"},yt={key:0,class:"text-center py-4"},_t={key:2,class:"text-center py-8"},wt={__name:"ProjectOverview",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(a){const g=a,d=y(()=>{if(!g.project||!g.project.task_count)return 0;const c=g.project.completed_tasks||0,t=g.project.task_count||1;return Math.round(c/t*100)}),x=y(()=>{var c;return((c=g.project)==null?void 0:c.recent_activities)||[]}),_=y(()=>{var c;return((c=g.project)==null?void 0:c.team_members)||[]}),m=y(()=>{var p;return(((p=g.project)==null?void 0:p.total_hours)||0)*50}),s=y(()=>{var t;return(((t=g.project)==null?void 0:t.budget)||0)-m.value}),u=y(()=>{var t;const c=((t=g.project)==null?void 0:t.budget)||1;return Math.min(Math.round(m.value/c*100),100)}),w=y(()=>{const c=u.value;return c>=90?"bg-red-600":c>=75?"bg-yellow-600":"bg-green-600"}),v=y(()=>{var t;const c=s.value;return c<0?"text-red-600":c<(((t=g.project)==null?void 0:t.budget)||0)*.1?"text-yellow-600":"text-green-600"}),k=c=>c?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(c):"Non specificato",V=c=>c?new Date(c).toLocaleDateString("it-IT",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"}):"",h=c=>c?c.split(" ").map(t=>t.charAt(0).toUpperCase()).slice(0,2).join(""):"??",f=c=>{const t={task_created:"bg-blue-600",task_completed:"bg-green-600",task_updated:"bg-yellow-600",comment_added:"bg-purple-600",file_uploaded:"bg-indigo-600",member_added:"bg-pink-600",default:"bg-gray-600"};return t[c]||t.default};return(c,t)=>(l(),i("div",ye,[a.loading?(l(),i("div",_e,t[0]||(t[0]=[e("div",{class:"h-4 bg-gray-200 rounded w-3/4"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1),e("div",{class:"h-32 bg-gray-200 rounded"},null,-1)]))):a.project?(l(),i("div",we,[e("div",be,[t[1]||(t[1]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Descrizione Progetto",-1)),a.project.description?(l(),i("p",ke,n(a.project.description),1)):(l(),i("p",je,"Nessuna descrizione disponibile"))]),e("div",$e,[e("div",Me,[e("div",Ce,[t[3]||(t[3]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})])],-1)),e("div",Pe,[e("dl",null,[t[2]||(t[2]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Totali",-1)),e("dd",Be,n(a.project.task_count||0),1)])])])]),e("div",Ve,[e("div",Te,[t[5]||(t[5]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",ze,[e("dl",null,[t[4]||(t[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Completati",-1)),e("dd",He,n(a.project.completed_tasks||0),1)])])])]),e("div",Ie,[e("div",Re,[t[7]||(t[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",Se,[e("dl",null,[t[6]||(t[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Membri Team",-1)),e("dd",De,n(a.project.team_count||0),1)])])])]),e("div",Ae,[e("div",Ne,[t[9]||(t[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",Fe,[e("dl",null,[t[8]||(t[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Budget",-1)),e("dd",Oe,n(k(a.project.budget)),1)])])])])]),e("div",Ee,[t[10]||(t[10]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Progresso Progetto",-1)),e("div",Ue,[e("div",{class:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:R({width:`${d.value}%`})},null,4)]),e("p",Ke,n(d.value)+"% completato",1)]),e("div",Ge,[t[15]||(t[15]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Budget vs Spese",-1)),e("div",Le,[e("div",qe,[t[11]||(t[11]=e("span",{class:"text-sm text-gray-600"},"Budget Totale",-1)),e("span",Je,n(k(a.project.budget)),1)]),t[14]||(t[14]=e("div",{class:"w-full bg-gray-200 rounded-full h-3"},[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:{width:"100%"}})],-1)),e("div",Qe,[t[12]||(t[12]=e("span",{class:"text-sm text-gray-600"},"Spese Sostenute",-1)),e("span",We,n(k(m.value)),1)]),e("div",Xe,[e("div",{class:b(["h-3 rounded-full transition-all duration-300",w.value]),style:R({width:u.value+"%"})},null,6)]),e("div",Ye,[t[13]||(t[13]=e("span",{class:"text-gray-600"},"Rimanente",-1)),e("span",{class:b(["font-medium",v.value])},n(k(s.value)),3)])])]),e("div",Ze,[t[17]||(t[17]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Team Members"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutti")],-1)),e("div",et,[(l(!0),i(H,null,I(_.value,p=>(l(),i("div",{key:p.id,class:"flex items-center space-x-3"},[e("div",tt,[p.profile_image?(l(),i("img",{key:0,src:p.profile_image,alt:p.full_name,class:"w-8 h-8 rounded-full"},null,8,st)):(l(),i("div",ot,[e("span",rt,n(h(p.full_name)),1)]))]),e("div",nt,[e("p",at,n(p.full_name),1),e("p",lt,n(p.role||"Team Member"),1)]),e("div",it,[e("p",dt,n(p.hours_worked||0)+"h",1)])]))),128)),_.value.length===0?(l(),i("div",ct,t[16]||(t[16]=[e("p",{class:"text-gray-500"},"Nessun membro del team assegnato",-1)]))):$("",!0)])]),e("div",ut,[t[20]||(t[20]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Attività Recenti"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutte")],-1)),e("div",mt,[(l(!0),i(H,null,I(x.value,p=>(l(),i("div",{key:p.id,class:"flex items-start space-x-3"},[e("div",gt,[e("div",{class:b(["w-2 h-2 rounded-full mt-2",f(p.type)])},null,2)]),e("div",vt,[e("p",pt,n(p.description),1),e("div",ht,[e("p",xt,n(V(p.created_at)),1),t[18]||(t[18]=e("span",{class:"text-xs text-gray-400"},"•",-1)),e("p",ft,n(p.user_name),1)])])]))),128)),x.value.length===0?(l(),i("div",yt,t[19]||(t[19]=[e("p",{class:"text-gray-500"},"Nessuna attività recente",-1),e("button",{class:"text-sm text-blue-600 hover:text-blue-800 mt-2"},"Aggiungi prima attività",-1)]))):$("",!0)])])])):(l(),i("div",_t,t[21]||(t[21]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},bt=S(wt,[["__scopeId","data-v-47f9c860"]]),kt={class:"project-tasks"},jt={__name:"ProjectTasks",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(a){return(g,d)=>(l(),i("div",kt,d[0]||(d[0]=[T('<div class="bg-white shadow rounded-lg p-6"><div class="flex justify-between items-center mb-6"><h3 class="text-lg font-medium text-gray-900">Task del Progetto</h3><button class="btn-primary"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg> Nuovo Task </button></div><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Componente Task in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Migrazione da template legacy Alpine.js in corso...</p></div></div>',1)])))}},$t={class:"project-team"},Mt={__name:"ProjectTeam",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(a){return(g,d)=>(l(),i("div",$t,d[0]||(d[0]=[T('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">Team del Progetto</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Componente Team in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Gestione membri del team in arrivo...</p></div></div>',1)])))}},Ct={class:"project-files"},Pt={__name:"ProjectFiles",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(a){return(g,d)=>(l(),i("div",Ct,d[0]||(d[0]=[T('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">File del Progetto</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Gestione File in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Upload e gestione documenti in arrivo...</p></div></div>',1)])))}},Bt={class:"project-kpi"},Vt={key:0,class:"animate-pulse space-y-4"},Tt={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},zt={key:1,class:"space-y-6"},Ht={class:"bg-white shadow rounded-lg p-6"},It={class:"flex items-center justify-between"},Rt=["disabled"],St={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Dt={class:"bg-white shadow rounded-lg p-6"},At={class:"flex items-center"},Nt={class:"ml-5 w-0 flex-1"},Ft={class:"text-lg font-medium text-gray-900"},Ot={class:"text-xs text-gray-500"},Et={class:"bg-white shadow rounded-lg p-6"},Ut={class:"flex items-center"},Kt={class:"ml-5 w-0 flex-1"},Gt={class:"text-lg font-medium text-gray-900"},Lt={class:"bg-white shadow rounded-lg p-6"},qt={class:"flex items-center"},Jt={class:"ml-5 w-0 flex-1"},Qt={class:"text-lg font-medium text-gray-900"},Wt={class:"text-xs text-gray-500"},Xt={class:"bg-white shadow rounded-lg p-6"},Yt={class:"flex items-center"},Zt={class:"ml-5 w-0 flex-1"},es={class:"text-lg font-medium text-gray-900"},ts={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},ss={class:"bg-white shadow rounded-lg p-6"},os={class:"space-y-4"},rs={class:"flex justify-between text-sm"},ns={class:"font-medium"},as={class:"w-full bg-gray-200 rounded-full h-3"},ls={class:"flex justify-between text-sm"},is={class:"text-gray-600"},ds={class:"font-medium"},cs={class:"bg-white shadow rounded-lg p-6"},us={class:"space-y-4"},ms={class:"flex justify-between text-sm"},gs={class:"font-medium"},vs={class:"w-full bg-gray-200 rounded-full h-3"},ps={class:"flex justify-between text-sm"},hs={class:"text-gray-600"},xs={class:"font-medium"},fs={class:"bg-white shadow rounded-lg p-6"},ys={class:"flex items-center justify-between mb-4"},_s={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},ws={class:"text-center p-4 border rounded-lg"},bs={class:"text-xs text-gray-500"},ks={class:"text-center p-4 border rounded-lg"},js={class:"text-xs text-gray-500"},$s={class:"text-center p-4 border rounded-lg"},Ms={class:"text-xs text-gray-500"},Cs={key:2,class:"text-center py-8"},Ps={__name:"ProjectKPI",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(a,{emit:g}){const d=a,x=g,_=B(!1),m=B(!1),s=B({totalHours:0,workDays:0,totalCosts:0,costVariance:0,potentialRevenue:0,actualRevenue:0,marginPercentage:0}),u=B({budget:80,time:85,margin:15}),w=y(()=>{var o;return!((o=d.project)!=null&&o.budget)||s.value.totalCosts===0?0:Math.round(s.value.totalCosts/d.project.budget*100)}),v=y(()=>{var o;return!((o=d.project)!=null&&o.estimated_hours)||s.value.totalHours===0?0:Math.round(s.value.totalHours/d.project.estimated_hours*100)}),k=y(()=>{const o=s.value.costVariance;return o>0?"text-red-600":o<0?"text-green-600":"text-gray-600"}),V=y(()=>{const o=s.value.marginPercentage;return o>=u.value.margin?"text-green-600":o>=u.value.margin*.7?"text-yellow-600":"text-red-600"}),h=y(()=>{const o=s.value.marginPercentage;return o>=u.value.margin?"Ottimo":o>=u.value.margin*.7?"Accettabile":"Critico"}),f=y(()=>{const o=w.value;return o>=u.value.budget?"text-red-600":o>=u.value.budget*.8?"text-yellow-600":"text-green-600"}),c=y(()=>{const o=v.value;return o>=u.value.time?"text-red-600":o>=u.value.time*.8?"text-yellow-600":"text-green-600"}),t=y(()=>{const o=s.value.marginPercentage;return o>=u.value.margin?"text-green-600":o>=u.value.margin*.7?"text-yellow-600":"text-red-600"}),p=o=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(o||0),D=o=>`${o||0}h`,N=o=>`${(o||0).toFixed(1)}%`,A=async()=>{var o;if((o=d.project)!=null&&o.id)try{const r=await J.get(`/api/projects/${d.project.id}/kpi`);s.value=r.data}catch(r){console.error("Error loading KPI data:",r),E()}},E=()=>{const o=d.project;o&&(s.value={totalHours:o.total_hours||0,workDays:Math.ceil((o.total_hours||0)/8),totalCosts:(o.total_hours||0)*50,costVariance:(o.total_hours||0)*50-(o.budget||0),potentialRevenue:o.budget||0,actualRevenue:o.invoiced_amount||0,marginPercentage:o.budget?(o.budget-(o.total_hours||0)*50)/o.budget*100:0})},U=async()=>{_.value=!0;try{await A(),x("refresh")}catch(o){console.error("Error refreshing KPIs:",o)}finally{_.value=!1}};return z(()=>d.project,o=>{o&&A()},{immediate:!0}),O(()=>{d.project&&A()}),(o,r)=>(l(),i("div",Bt,[a.loading?(l(),i("div",Vt,[e("div",Tt,[(l(),i(H,null,I(4,F=>e("div",{key:F,class:"bg-gray-200 rounded-lg h-24"})),64))]),r[1]||(r[1]=e("div",{class:"bg-gray-200 rounded-lg h-64"},null,-1))])):a.project?(l(),i("div",zt,[e("div",Ht,[e("div",It,[r[4]||(r[4]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900"},"KPI Progetto"),e("p",{class:"text-sm text-gray-600"},"Dashboard metriche e performance del progetto")],-1)),e("button",{onClick:U,disabled:_.value,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(l(),i("svg",{class:b(["w-4 h-4 mr-2",{"animate-spin":_.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},r[2]||(r[2]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]),2)),r[3]||(r[3]=P(" Aggiorna "))],8,Rt)])]),e("div",St,[e("div",Dt,[e("div",At,[r[6]||(r[6]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",Nt,[e("dl",null,[r[5]||(r[5]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ore Totali",-1)),e("dd",Ft,n(D(s.value.totalHours)),1),e("dd",Ot,n(s.value.workDays)+" giorni lavorati",1)])])])]),e("div",Et,[e("div",Ut,[r[8]||(r[8]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",Kt,[e("dl",null,[r[7]||(r[7]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Costi Totali",-1)),e("dd",Gt,n(p(s.value.totalCosts)),1),e("dd",{class:b(["text-xs",k.value])},n(p(s.value.costVariance))+" vs budget",3)])])])]),e("div",Lt,[e("div",qt,[r[10]||(r[10]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),e("div",Jt,[e("dl",null,[r[9]||(r[9]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ricavi Potenziali",-1)),e("dd",Qt,n(p(s.value.potentialRevenue)),1),e("dd",Wt,n(p(s.value.actualRevenue))+" fatturati",1)])])])]),e("div",Xt,[e("div",Yt,[r[12]||(r[12]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",Zt,[e("dl",null,[r[11]||(r[11]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Margine",-1)),e("dd",es,n(N(s.value.marginPercentage)),1),e("dd",{class:b(["text-xs",V.value])},n(h.value),3)])])])])]),e("div",ts,[e("div",ss,[r[14]||(r[14]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Budget",-1)),e("div",os,[e("div",rs,[r[13]||(r[13]=e("span",{class:"text-gray-600"},"Budget Totale",-1)),e("span",ns,n(p(a.project.budget||0)),1)]),e("div",as,[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:R({width:w.value+"%"})},null,4)]),e("div",ls,[e("span",is,"Utilizzato: "+n(p(s.value.totalCosts)),1),e("span",ds,n(w.value)+"%",1)])])]),e("div",cs,[r[16]||(r[16]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Tempo",-1)),e("div",us,[e("div",ms,[r[15]||(r[15]=e("span",{class:"text-gray-600"},"Ore Stimate",-1)),e("span",gs,n(D(a.project.estimated_hours||0)),1)]),e("div",vs,[e("div",{class:"bg-green-600 h-3 rounded-full transition-all duration-300",style:R({width:v.value+"%"})},null,4)]),e("div",ps,[e("span",hs,"Lavorate: "+n(D(s.value.totalHours)),1),e("span",xs,n(v.value)+"%",1)])])])]),e("div",fs,[e("div",ys,[r[17]||(r[17]=e("h4",{class:"text-lg font-medium text-gray-900"},"Soglie KPI",-1)),e("button",{onClick:r[0]||(r[0]=F=>m.value=!0),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Configura ")]),e("div",_s,[e("div",ws,[e("div",{class:b(["text-2xl font-bold",f.value])},n(w.value)+"% ",3),r[18]||(r[18]=e("div",{class:"text-sm text-gray-600"},"Budget Usage",-1)),e("div",bs,"Soglia: "+n(u.value.budget)+"%",1)]),e("div",ks,[e("div",{class:b(["text-2xl font-bold",c.value])},n(v.value)+"% ",3),r[19]||(r[19]=e("div",{class:"text-sm text-gray-600"},"Time Usage",-1)),e("div",js,"Soglia: "+n(u.value.time)+"%",1)]),e("div",$s,[e("div",{class:b(["text-2xl font-bold",t.value])},n(N(s.value.marginPercentage)),3),r[20]||(r[20]=e("div",{class:"text-sm text-gray-600"},"Margine",-1)),e("div",Ms,"Soglia: "+n(u.value.margin)+"%",1)])])])])):(l(),i("div",Cs,r[21]||(r[21]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},Bs={class:"project-gantt"},Vs={__name:"ProjectGantt",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(a){return(g,d)=>(l(),i("div",Bs,d[0]||(d[0]=[T('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">Diagramma di Gantt</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Gantt Chart in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Integrazione Frappe Gantt in arrivo...</p></div></div>',1)])))}},Ts={class:"project-timesheet"},zs={__name:"ProjectTimesheet",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(a){return(g,d)=>(l(),i("div",Ts,d[0]||(d[0]=[T('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">Timesheet del Progetto</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Timesheet in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Gestione ore lavorate in arrivo...</p></div></div>',1)])))}},Hs={class:"project-view"},Is={class:"tab-content"},Rs={__name:"ProjectView",setup(a){const g=Q(),d=W(),x=L(),_=q(),m=B(!0),s=B("overview"),u=y(()=>g.currentProject),w=y(()=>[{id:"overview",label:"Panoramica",icon:"chart-bar"},{id:"tasks",label:"Task",icon:"clipboard-list"},{id:"team",label:"Team",icon:"users"},{id:"gantt",label:"Gantt",icon:"calendar"},{id:"timesheet",label:"Timesheet",icon:"clock"},{id:"files",label:"File",icon:"folder"},{id:"kpi",label:"KPI",icon:"trending-up"}].filter(f=>!!(["overview","tasks","team","files"].includes(f.id)||f.id==="kpi"&&d.hasPermission("view_reports")||f.id==="gantt"&&d.hasPermission("view_all_projects")||f.id==="timesheet"&&d.hasPermission("view_own_timesheets")))),v=async()=>{m.value=!0;try{const h=x.params.id;await g.fetchProject(h)}catch(h){console.error("Error loading project:",h)}finally{m.value=!1}},k=()=>{_.push(`/projects/${x.params.id}/edit`)},V=async()=>{if(confirm("Sei sicuro di voler eliminare questo progetto?"))try{await g.deleteProject(x.params.id),_.push("/projects")}catch(h){console.error("Error deleting project:",h)}};return z(()=>x.params.id,h=>{h&&v()}),z(()=>x.hash,h=>{if(h){const f=h.replace("#","");w.value.find(c=>c.id===f)&&(s.value=f)}}),z(s,h=>{const f=`#${h}`;x.hash!==f&&_.replace({...x,hash:f})}),O(()=>{if(x.hash){const h=x.hash.replace("#","");w.value.find(f=>f.id===h)&&(s.value=h)}v()}),(h,f)=>(l(),i("div",Hs,[j(ue,{project:u.value,loading:m.value,onEdit:k,onDelete:V},null,8,["project","loading"]),j(fe,{modelValue:s.value,"onUpdate:modelValue":f[0]||(f[0]=c=>s.value=c),tabs:w.value,class:"mb-6"},null,8,["modelValue","tabs"]),e("div",Is,[M(j(bt,{project:u.value,loading:m.value,onRefresh:v},null,8,["project","loading"]),[[C,s.value==="overview"]]),M(j(jt,{project:u.value,loading:m.value,onRefresh:v},null,8,["project","loading"]),[[C,s.value==="tasks"]]),M(j(Mt,{project:u.value,loading:m.value,onRefresh:v},null,8,["project","loading"]),[[C,s.value==="team"]]),M(j(Vs,{project:u.value,loading:m.value,onRefresh:v},null,8,["project","loading"]),[[C,s.value==="gantt"]]),M(j(zs,{project:u.value,loading:m.value,onRefresh:v},null,8,["project","loading"]),[[C,s.value==="timesheet"]]),M(j(Pt,{project:u.value,loading:m.value,onRefresh:v},null,8,["project","loading"]),[[C,s.value==="files"]]),M(j(Ps,{project:u.value,loading:m.value,onRefresh:v},null,8,["project","loading"]),[[C,s.value==="kpi"]])])]))}},As=S(Rs,[["__scopeId","data-v-d49c1b63"]]);export{As as default};
