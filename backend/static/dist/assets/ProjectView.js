import{c as i,o as l,j as t,t as n,n as b,g as $,l as P,F as H,k as I,h as G,D as K,f as y,z as S,I as T,r as B,w as z,A as O,u as L,a as j,v as M,N as C,m as q}from"./vendor.js";import{_ as D,u as J,a as Q}from"./app.js";const W={class:"project-header bg-white shadow-sm rounded-lg p-6 mb-6"},X={key:0,class:"animate-pulse"},Y={key:1,class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},Z={class:"flex-1"},tt={class:"flex items-center space-x-3 mb-2"},et={class:"text-2xl font-bold text-gray-900"},st={class:"flex flex-wrap items-center gap-4 text-sm text-gray-500"},ot={key:0},rt={key:1},nt={key:2},at={key:3},lt={class:"mt-4 sm:mt-0 flex space-x-3"},it={key:2,class:"text-center py-8"},dt={__name:"ProjectHeader",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["edit","delete"],setup(a){const g=m=>({planning:"bg-yellow-100 text-yellow-800",active:"bg-green-100 text-green-800",on_hold:"bg-orange-100 text-orange-800",completed:"bg-blue-100 text-blue-800",cancelled:"bg-red-100 text-red-800"})[m]||"bg-gray-100 text-gray-800",c=m=>({planning:"Pianificazione",active:"Attivo",on_hold:"In Pausa",completed:"Completato",cancelled:"Annullato"})[m]||m,h=m=>m?new Date(m).toLocaleDateString("it-IT"):"",_=m=>m?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(m):"";return(m,s)=>(l(),i("div",W,[a.loading?(l(),i("div",X,s[2]||(s[2]=[t("div",{class:"h-8 bg-gray-200 rounded w-1/3 mb-2"},null,-1),t("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1)]))):a.project?(l(),i("div",Y,[t("div",Z,[t("div",tt,[t("h1",et,n(a.project.name),1),t("span",{class:b(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",g(a.project.status)])},n(c(a.project.status)),3)]),t("div",st,[a.project.client?(l(),i("span",ot,[s[3]||(s[3]=t("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),P(" Cliente: "+n(a.project.client.name),1)])):$("",!0),a.project.start_date?(l(),i("span",rt,[s[4]||(s[4]=t("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),P(" Inizio: "+n(h(a.project.start_date)),1)])):$("",!0),a.project.end_date?(l(),i("span",nt,[s[5]||(s[5]=t("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),P(" Fine: "+n(h(a.project.end_date)),1)])):$("",!0),a.project.budget?(l(),i("span",at,[s[6]||(s[6]=t("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})],-1)),P(" Budget: "+n(_(a.project.budget)),1)])):$("",!0)])]),t("div",lt,[t("button",{onClick:s[0]||(s[0]=u=>m.$emit("edit")),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},s[7]||(s[7]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),P(" Modifica ")])),t("button",{onClick:s[1]||(s[1]=u=>m.$emit("delete")),class:"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},s[8]||(s[8]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),P(" Elimina ")]))])])):(l(),i("div",it,s[9]||(s[9]=[t("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},ct=D(dt,[["__scopeId","data-v-24fa98b0"]]),ut={class:"tab-navigation"},mt={class:"border-b border-gray-200"},gt={class:"-mb-px flex space-x-8","aria-label":"Tabs"},vt=["onClick","aria-current"],pt={key:1,class:"ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"},ht={__name:"TabNavigation",props:{modelValue:{type:String,required:!0},tabs:{type:Array,required:!0,validator:a=>a.every(g=>typeof g=="object"&&g.id&&g.label)}},emits:["update:modelValue"],setup(a,{emit:g}){const c=a,h=g,_=u=>c.modelValue===u,m=u=>{h("update:modelValue",u)},s=u=>{const w={"chart-bar":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>`},"clipboard-list":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>`},users:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>`},folder:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
      </svg>`},"trending-up":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      </svg>`},calendar:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`},clock:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}};return w[u]||w["chart-bar"]};return(u,w)=>(l(),i("div",ut,[t("div",mt,[t("nav",gt,[(l(!0),i(H,null,I(a.tabs,x=>(l(),i("button",{key:x.id,onClick:k=>m(x.id),class:b(["whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2",_(x.id)?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"]),"aria-current":_(x.id)?"page":void 0},[x.icon?(l(),G(K(s(x.icon)),{key:0,class:"w-4 h-4"})):$("",!0),t("span",null,n(x.label),1),x.count!==void 0?(l(),i("span",pt,n(x.count),1)):$("",!0)],10,vt))),128))])])]))}},xt=D(ht,[["__scopeId","data-v-c205976e"]]),ft={class:"project-overview"},yt={key:0,class:"animate-pulse space-y-4"},_t={key:1,class:"space-y-6"},wt={class:"bg-white shadow rounded-lg p-6"},bt={key:0,class:"text-gray-600"},kt={key:1,class:"text-gray-400 italic"},jt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},$t={class:"bg-white shadow rounded-lg p-6"},Mt={class:"flex items-center"},Ct={class:"ml-5 w-0 flex-1"},Pt={class:"text-lg font-medium text-gray-900"},Bt={class:"bg-white shadow rounded-lg p-6"},Vt={class:"flex items-center"},Tt={class:"ml-5 w-0 flex-1"},zt={class:"text-lg font-medium text-gray-900"},Ht={class:"bg-white shadow rounded-lg p-6"},It={class:"flex items-center"},St={class:"ml-5 w-0 flex-1"},Dt={class:"text-lg font-medium text-gray-900"},At={class:"bg-white shadow rounded-lg p-6"},Rt={class:"flex items-center"},Nt={class:"ml-5 w-0 flex-1"},Ft={class:"text-lg font-medium text-gray-900"},Ot={class:"bg-white shadow rounded-lg p-6"},Et={class:"w-full bg-gray-200 rounded-full h-2.5"},Ut={class:"text-sm text-gray-500 mt-2"},Gt={class:"bg-white shadow rounded-lg p-6"},Kt={class:"space-y-4"},Lt={class:"flex justify-between items-center"},qt={class:"text-sm font-medium"},Jt={class:"flex justify-between items-center"},Qt={class:"text-sm font-medium"},Wt={class:"w-full bg-gray-200 rounded-full h-3"},Xt={class:"flex justify-between items-center text-sm"},Yt={class:"bg-white shadow rounded-lg p-6"},Zt={class:"space-y-3"},te={class:"flex-shrink-0"},ee=["src","alt"],se={key:1,class:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"},oe={class:"text-xs font-medium text-gray-600"},re={class:"flex-1"},ne={class:"text-sm font-medium text-gray-900"},ae={class:"text-xs text-gray-500"},le={class:"text-right"},ie={class:"text-xs text-gray-500"},de={key:0,class:"text-center py-4"},ce={class:"bg-white shadow rounded-lg p-6"},ue={class:"space-y-3"},me={class:"flex-shrink-0"},ge={class:"flex-1"},ve={class:"text-sm text-gray-900"},pe={class:"flex items-center space-x-2 mt-1"},he={class:"text-xs text-gray-500"},xe={class:"text-xs text-gray-500"},fe={key:0,class:"text-center py-4"},ye={key:2,class:"text-center py-8"},_e={__name:"ProjectOverview",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(a){const g=a,c=y(()=>{if(!g.project||!g.project.task_count)return 0;const d=g.project.completed_tasks||0,e=g.project.task_count||1;return Math.round(d/e*100)}),h=y(()=>{var d;return((d=g.project)==null?void 0:d.recent_activities)||[]}),_=y(()=>{var d;return((d=g.project)==null?void 0:d.team_members)||[]}),m=y(()=>{var v;return(((v=g.project)==null?void 0:v.total_hours)||0)*50}),s=y(()=>{var e;return(((e=g.project)==null?void 0:e.budget)||0)-m.value}),u=y(()=>{var e;const d=((e=g.project)==null?void 0:e.budget)||1;return Math.min(Math.round(m.value/d*100),100)}),w=y(()=>{const d=u.value;return d>=90?"bg-red-600":d>=75?"bg-yellow-600":"bg-green-600"}),x=y(()=>{var e;const d=s.value;return d<0?"text-red-600":d<(((e=g.project)==null?void 0:e.budget)||0)*.1?"text-yellow-600":"text-green-600"}),k=d=>d?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(d):"Non specificato",V=d=>d?new Date(d).toLocaleDateString("it-IT",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"}):"",p=d=>d?d.split(" ").map(e=>e.charAt(0).toUpperCase()).slice(0,2).join(""):"??",f=d=>{const e={task_created:"bg-blue-600",task_completed:"bg-green-600",task_updated:"bg-yellow-600",comment_added:"bg-purple-600",file_uploaded:"bg-indigo-600",member_added:"bg-pink-600",default:"bg-gray-600"};return e[d]||e.default};return(d,e)=>(l(),i("div",ft,[a.loading?(l(),i("div",yt,e[0]||(e[0]=[t("div",{class:"h-4 bg-gray-200 rounded w-3/4"},null,-1),t("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1),t("div",{class:"h-32 bg-gray-200 rounded"},null,-1)]))):a.project?(l(),i("div",_t,[t("div",wt,[e[1]||(e[1]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Descrizione Progetto",-1)),a.project.description?(l(),i("p",bt,n(a.project.description),1)):(l(),i("p",kt,"Nessuna descrizione disponibile"))]),t("div",jt,[t("div",$t,[t("div",Mt,[e[3]||(e[3]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})])],-1)),t("div",Ct,[t("dl",null,[e[2]||(e[2]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Totali",-1)),t("dd",Pt,n(a.project.task_count||0),1)])])])]),t("div",Bt,[t("div",Vt,[e[5]||(e[5]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),t("div",Tt,[t("dl",null,[e[4]||(e[4]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Completati",-1)),t("dd",zt,n(a.project.completed_tasks||0),1)])])])]),t("div",Ht,[t("div",It,[e[7]||(e[7]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),t("div",St,[t("dl",null,[e[6]||(e[6]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Membri Team",-1)),t("dd",Dt,n(a.project.team_count||0),1)])])])]),t("div",At,[t("div",Rt,[e[9]||(e[9]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),t("div",Nt,[t("dl",null,[e[8]||(e[8]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Budget",-1)),t("dd",Ft,n(k(a.project.budget)),1)])])])])]),t("div",Ot,[e[10]||(e[10]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Progresso Progetto",-1)),t("div",Et,[t("div",{class:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:S({width:`${c.value}%`})},null,4)]),t("p",Ut,n(c.value)+"% completato",1)]),t("div",Gt,[e[15]||(e[15]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Budget vs Spese",-1)),t("div",Kt,[t("div",Lt,[e[11]||(e[11]=t("span",{class:"text-sm text-gray-600"},"Budget Totale",-1)),t("span",qt,n(k(a.project.budget)),1)]),e[14]||(e[14]=t("div",{class:"w-full bg-gray-200 rounded-full h-3"},[t("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:{width:"100%"}})],-1)),t("div",Jt,[e[12]||(e[12]=t("span",{class:"text-sm text-gray-600"},"Spese Sostenute",-1)),t("span",Qt,n(k(m.value)),1)]),t("div",Wt,[t("div",{class:b(["h-3 rounded-full transition-all duration-300",w.value]),style:S({width:u.value+"%"})},null,6)]),t("div",Xt,[e[13]||(e[13]=t("span",{class:"text-gray-600"},"Rimanente",-1)),t("span",{class:b(["font-medium",x.value])},n(k(s.value)),3)])])]),t("div",Yt,[e[17]||(e[17]=t("div",{class:"flex items-center justify-between mb-4"},[t("h3",{class:"text-lg font-medium text-gray-900"},"Team Members"),t("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutti")],-1)),t("div",Zt,[(l(!0),i(H,null,I(_.value,v=>(l(),i("div",{key:v.id,class:"flex items-center space-x-3"},[t("div",te,[v.profile_image?(l(),i("img",{key:0,src:v.profile_image,alt:v.full_name,class:"w-8 h-8 rounded-full"},null,8,ee)):(l(),i("div",se,[t("span",oe,n(p(v.full_name)),1)]))]),t("div",re,[t("p",ne,n(v.full_name),1),t("p",ae,n(v.role||"Team Member"),1)]),t("div",le,[t("p",ie,n(v.hours_worked||0)+"h",1)])]))),128)),_.value.length===0?(l(),i("div",de,e[16]||(e[16]=[t("p",{class:"text-gray-500"},"Nessun membro del team assegnato",-1)]))):$("",!0)])]),t("div",ce,[e[20]||(e[20]=t("div",{class:"flex items-center justify-between mb-4"},[t("h3",{class:"text-lg font-medium text-gray-900"},"Attività Recenti"),t("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutte")],-1)),t("div",ue,[(l(!0),i(H,null,I(h.value,v=>(l(),i("div",{key:v.id,class:"flex items-start space-x-3"},[t("div",me,[t("div",{class:b(["w-2 h-2 rounded-full mt-2",f(v.type)])},null,2)]),t("div",ge,[t("p",ve,n(v.description),1),t("div",pe,[t("p",he,n(V(v.created_at)),1),e[18]||(e[18]=t("span",{class:"text-xs text-gray-400"},"•",-1)),t("p",xe,n(v.user_name),1)])])]))),128)),h.value.length===0?(l(),i("div",fe,e[19]||(e[19]=[t("p",{class:"text-gray-500"},"Nessuna attività recente",-1),t("button",{class:"text-sm text-blue-600 hover:text-blue-800 mt-2"},"Aggiungi prima attività",-1)]))):$("",!0)])])])):(l(),i("div",ye,e[21]||(e[21]=[t("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},we=D(_e,[["__scopeId","data-v-47f9c860"]]),be={class:"project-tasks"},ke={__name:"ProjectTasks",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(a){return(g,c)=>(l(),i("div",be,c[0]||(c[0]=[T('<div class="bg-white shadow rounded-lg p-6"><div class="flex justify-between items-center mb-6"><h3 class="text-lg font-medium text-gray-900">Task del Progetto</h3><button class="btn-primary"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg> Nuovo Task </button></div><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Componente Task in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Migrazione da template legacy Alpine.js in corso...</p></div></div>',1)])))}},je={class:"project-team"},$e={__name:"ProjectTeam",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(a){return(g,c)=>(l(),i("div",je,c[0]||(c[0]=[T('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">Team del Progetto</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Componente Team in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Gestione membri del team in arrivo...</p></div></div>',1)])))}},Me={class:"project-files"},Ce={__name:"ProjectFiles",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(a){return(g,c)=>(l(),i("div",Me,c[0]||(c[0]=[T('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">File del Progetto</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Gestione File in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Upload e gestione documenti in arrivo...</p></div></div>',1)])))}},Pe={class:"project-kpi"},Be={key:0,class:"animate-pulse space-y-4"},Ve={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Te={key:1,class:"space-y-6"},ze={class:"bg-white shadow rounded-lg p-6"},He={class:"flex items-center justify-between"},Ie=["disabled"],Se={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},De={class:"bg-white shadow rounded-lg p-6"},Ae={class:"flex items-center"},Re={class:"ml-5 w-0 flex-1"},Ne={class:"text-lg font-medium text-gray-900"},Fe={class:"text-xs text-gray-500"},Oe={class:"bg-white shadow rounded-lg p-6"},Ee={class:"flex items-center"},Ue={class:"ml-5 w-0 flex-1"},Ge={class:"text-lg font-medium text-gray-900"},Ke={class:"bg-white shadow rounded-lg p-6"},Le={class:"flex items-center"},qe={class:"ml-5 w-0 flex-1"},Je={class:"text-lg font-medium text-gray-900"},Qe={class:"text-xs text-gray-500"},We={class:"bg-white shadow rounded-lg p-6"},Xe={class:"flex items-center"},Ye={class:"ml-5 w-0 flex-1"},Ze={class:"text-lg font-medium text-gray-900"},ts={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},es={class:"bg-white shadow rounded-lg p-6"},ss={class:"space-y-4"},os={class:"flex justify-between text-sm"},rs={class:"font-medium"},ns={class:"w-full bg-gray-200 rounded-full h-3"},as={class:"flex justify-between text-sm"},ls={class:"text-gray-600"},is={class:"font-medium"},ds={class:"bg-white shadow rounded-lg p-6"},cs={class:"space-y-4"},us={class:"flex justify-between text-sm"},ms={class:"font-medium"},gs={class:"w-full bg-gray-200 rounded-full h-3"},vs={class:"flex justify-between text-sm"},ps={class:"text-gray-600"},hs={class:"font-medium"},xs={class:"bg-white shadow rounded-lg p-6"},fs={class:"flex items-center justify-between mb-4"},ys={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},_s={class:"text-center p-4 border rounded-lg"},ws={class:"text-xs text-gray-500"},bs={class:"text-center p-4 border rounded-lg"},ks={class:"text-xs text-gray-500"},js={class:"text-center p-4 border rounded-lg"},$s={class:"text-xs text-gray-500"},Ms={key:2,class:"text-center py-8"},Cs={__name:"ProjectKPI",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(a,{emit:g}){const c=a,h=g,_=B(!1),m=B(!1),s=B({totalHours:0,workDays:0,totalCosts:0,costVariance:0,potentialRevenue:0,actualRevenue:0,marginPercentage:0}),u=B({budget:80,time:85,margin:15}),w=y(()=>{var o;return!((o=c.project)!=null&&o.budget)||s.value.totalCosts===0?0:Math.round(s.value.totalCosts/c.project.budget*100)}),x=y(()=>{var o;return!((o=c.project)!=null&&o.estimated_hours)||s.value.totalHours===0?0:Math.round(s.value.totalHours/c.project.estimated_hours*100)}),k=y(()=>{const o=s.value.costVariance;return o>0?"text-red-600":o<0?"text-green-600":"text-gray-600"}),V=y(()=>{const o=s.value.marginPercentage;return o>=u.value.margin?"text-green-600":o>=u.value.margin*.7?"text-yellow-600":"text-red-600"}),p=y(()=>{const o=s.value.marginPercentage;return o>=u.value.margin?"Ottimo":o>=u.value.margin*.7?"Accettabile":"Critico"}),f=y(()=>{const o=w.value;return o>=u.value.budget?"text-red-600":o>=u.value.budget*.8?"text-yellow-600":"text-green-600"}),d=y(()=>{const o=x.value;return o>=u.value.time?"text-red-600":o>=u.value.time*.8?"text-yellow-600":"text-green-600"}),e=y(()=>{const o=s.value.marginPercentage;return o>=u.value.margin?"text-green-600":o>=u.value.margin*.7?"text-yellow-600":"text-red-600"}),v=o=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(o||0),A=o=>`${o||0}h`,N=o=>`${(o||0).toFixed(1)}%`,R=async()=>{var o;(o=c.project)!=null&&o.id&&E()},E=()=>{const o=c.project;o&&(s.value={totalHours:o.total_hours||0,workDays:Math.ceil((o.total_hours||0)/8),totalCosts:(o.total_hours||0)*50,costVariance:(o.total_hours||0)*50-(o.budget||0),potentialRevenue:o.budget||0,actualRevenue:o.invoiced_amount||0,marginPercentage:o.budget?(o.budget-(o.total_hours||0)*50)/o.budget*100:0})},U=async()=>{_.value=!0;try{await R(),h("refresh")}catch(o){console.error("Error refreshing KPIs:",o)}finally{_.value=!1}};return z(()=>c.project,o=>{o&&R()},{immediate:!0}),O(()=>{c.project&&R()}),(o,r)=>(l(),i("div",Pe,[a.loading?(l(),i("div",Be,[t("div",Ve,[(l(),i(H,null,I(4,F=>t("div",{key:F,class:"bg-gray-200 rounded-lg h-24"})),64))]),r[1]||(r[1]=t("div",{class:"bg-gray-200 rounded-lg h-64"},null,-1))])):a.project?(l(),i("div",Te,[t("div",ze,[t("div",He,[r[4]||(r[4]=t("div",null,[t("h3",{class:"text-lg font-medium text-gray-900"},"KPI Progetto"),t("p",{class:"text-sm text-gray-600"},"Dashboard metriche e performance del progetto")],-1)),t("button",{onClick:U,disabled:_.value,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(l(),i("svg",{class:b(["w-4 h-4 mr-2",{"animate-spin":_.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},r[2]||(r[2]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]),2)),r[3]||(r[3]=P(" Aggiorna "))],8,Ie)])]),t("div",Se,[t("div",De,[t("div",Ae,[r[6]||(r[6]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),t("div",Re,[t("dl",null,[r[5]||(r[5]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ore Totali",-1)),t("dd",Ne,n(A(s.value.totalHours)),1),t("dd",Fe,n(s.value.workDays)+" giorni lavorati",1)])])])]),t("div",Oe,[t("div",Ee,[r[8]||(r[8]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),t("div",Ue,[t("dl",null,[r[7]||(r[7]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Costi Totali",-1)),t("dd",Ge,n(v(s.value.totalCosts)),1),t("dd",{class:b(["text-xs",k.value])},n(v(s.value.costVariance))+" vs budget",3)])])])]),t("div",Ke,[t("div",Le,[r[10]||(r[10]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),t("div",qe,[t("dl",null,[r[9]||(r[9]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ricavi Potenziali",-1)),t("dd",Je,n(v(s.value.potentialRevenue)),1),t("dd",Qe,n(v(s.value.actualRevenue))+" fatturati",1)])])])]),t("div",We,[t("div",Xe,[r[12]||(r[12]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),t("div",Ye,[t("dl",null,[r[11]||(r[11]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Margine",-1)),t("dd",Ze,n(N(s.value.marginPercentage)),1),t("dd",{class:b(["text-xs",V.value])},n(p.value),3)])])])])]),t("div",ts,[t("div",es,[r[14]||(r[14]=t("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Budget",-1)),t("div",ss,[t("div",os,[r[13]||(r[13]=t("span",{class:"text-gray-600"},"Budget Totale",-1)),t("span",rs,n(v(a.project.budget||0)),1)]),t("div",ns,[t("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:S({width:w.value+"%"})},null,4)]),t("div",as,[t("span",ls,"Utilizzato: "+n(v(s.value.totalCosts)),1),t("span",is,n(w.value)+"%",1)])])]),t("div",ds,[r[16]||(r[16]=t("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Tempo",-1)),t("div",cs,[t("div",us,[r[15]||(r[15]=t("span",{class:"text-gray-600"},"Ore Stimate",-1)),t("span",ms,n(A(a.project.estimated_hours||0)),1)]),t("div",gs,[t("div",{class:"bg-green-600 h-3 rounded-full transition-all duration-300",style:S({width:x.value+"%"})},null,4)]),t("div",vs,[t("span",ps,"Lavorate: "+n(A(s.value.totalHours)),1),t("span",hs,n(x.value)+"%",1)])])])]),t("div",xs,[t("div",fs,[r[17]||(r[17]=t("h4",{class:"text-lg font-medium text-gray-900"},"Soglie KPI",-1)),t("button",{onClick:r[0]||(r[0]=F=>m.value=!0),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Configura ")]),t("div",ys,[t("div",_s,[t("div",{class:b(["text-2xl font-bold",f.value])},n(w.value)+"% ",3),r[18]||(r[18]=t("div",{class:"text-sm text-gray-600"},"Budget Usage",-1)),t("div",ws,"Soglia: "+n(u.value.budget)+"%",1)]),t("div",bs,[t("div",{class:b(["text-2xl font-bold",d.value])},n(x.value)+"% ",3),r[19]||(r[19]=t("div",{class:"text-sm text-gray-600"},"Time Usage",-1)),t("div",ks,"Soglia: "+n(u.value.time)+"%",1)]),t("div",js,[t("div",{class:b(["text-2xl font-bold",e.value])},n(N(s.value.marginPercentage)),3),r[20]||(r[20]=t("div",{class:"text-sm text-gray-600"},"Margine",-1)),t("div",$s,"Soglia: "+n(u.value.margin)+"%",1)])])])])):(l(),i("div",Ms,r[21]||(r[21]=[t("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},Ps={class:"project-gantt"},Bs={__name:"ProjectGantt",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(a){return(g,c)=>(l(),i("div",Ps,c[0]||(c[0]=[T('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">Diagramma di Gantt</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Gantt Chart in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Integrazione Frappe Gantt in arrivo...</p></div></div>',1)])))}},Vs={class:"project-timesheet"},Ts={__name:"ProjectTimesheet",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(a){return(g,c)=>(l(),i("div",Vs,c[0]||(c[0]=[T('<div class="bg-white shadow rounded-lg p-6"><h3 class="text-lg font-medium text-gray-900 mb-6">Timesheet del Progetto</h3><div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">Timesheet in sviluppo</h3><p class="mt-1 text-sm text-gray-500">Gestione ore lavorate in arrivo...</p></div></div>',1)])))}},zs={class:"project-view"},Hs={class:"tab-content"},Is={__name:"ProjectView",setup(a){const g=J(),c=Q(),h=L(),_=q(),m=B(!0),s=B("overview"),u=y(()=>g.currentProject),w=y(()=>[{id:"overview",label:"Panoramica",icon:"chart-bar"},{id:"tasks",label:"Task",icon:"clipboard-list"},{id:"team",label:"Team",icon:"users"},{id:"gantt",label:"Gantt",icon:"calendar"},{id:"timesheet",label:"Timesheet",icon:"clock"},{id:"files",label:"File",icon:"folder"},{id:"kpi",label:"KPI",icon:"trending-up"}].filter(f=>!!(["overview","tasks","team","files"].includes(f.id)||f.id==="kpi"&&c.hasPermission("view_reports")||f.id==="gantt"&&c.hasPermission("view_all_projects")||f.id==="timesheet"&&c.hasPermission("view_own_timesheets")))),x=async()=>{m.value=!0;try{const p=h.params.id;await g.fetchProject(p)}catch(p){console.error("Error loading project:",p)}finally{m.value=!1}},k=()=>{_.push(`/projects/${h.params.id}/edit`)},V=async()=>{if(confirm("Sei sicuro di voler eliminare questo progetto?"))try{await g.deleteProject(h.params.id),_.push("/projects")}catch(p){console.error("Error deleting project:",p)}};return z(()=>h.params.id,p=>{p&&x()}),z(()=>h.hash,p=>{if(p){const f=p.replace("#","");w.value.find(d=>d.id===f)&&(s.value=f)}}),z(s,p=>{const f=`#${p}`;h.hash!==f&&_.replace({...h,hash:f})}),O(()=>{if(h.hash){const p=h.hash.replace("#","");w.value.find(f=>f.id===p)&&(s.value=p)}x()}),(p,f)=>(l(),i("div",zs,[j(ct,{project:u.value,loading:m.value,onEdit:k,onDelete:V},null,8,["project","loading"]),j(xt,{modelValue:s.value,"onUpdate:modelValue":f[0]||(f[0]=d=>s.value=d),tabs:w.value,class:"mb-6"},null,8,["modelValue","tabs"]),t("div",Hs,[M(j(we,{project:u.value,loading:m.value},null,8,["project","loading"]),[[C,s.value==="overview"]]),M(j(ke,{project:u.value,loading:m.value},null,8,["project","loading"]),[[C,s.value==="tasks"]]),M(j($e,{project:u.value,loading:m.value},null,8,["project","loading"]),[[C,s.value==="team"]]),M(j(Bs,{project:u.value,loading:m.value},null,8,["project","loading"]),[[C,s.value==="gantt"]]),M(j(Ts,{project:u.value,loading:m.value},null,8,["project","loading"]),[[C,s.value==="timesheet"]]),M(j(Ce,{project:u.value,loading:m.value},null,8,["project","loading"]),[[C,s.value==="files"]]),M(j(Cs,{project:u.value,loading:m.value},null,8,["project","loading"]),[[C,s.value==="kpi"]])])]))}},As=D(Is,[["__scopeId","data-v-b1b89f82"]]);export{As as default};
