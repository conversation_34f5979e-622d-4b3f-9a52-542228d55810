const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ProjectView.js","assets/vendor.js","assets/ProjectView.css"])))=>i.map(i=>d[i]);
import{c as n,a as h,r as B,o as a,b as Me,d as he,e as w,f as _,g as b,n as j,h as D,w as $,t as u,u as ee,i as e,F as V,j as P,k as C,l as G,m as X,p as fe,q as te,s as N,v as O,x as W,y as xe,z as L,T as ze,A as Se,B as R,C as Ae,D as ye,E as Q,G as Ie,H as Be,I as Ve,J as Pe,K as He,L as Te}from"./vendor.js";(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))t(s);new MutationObserver(s=>{for(const c of s)if(c.type==="childList")for(const v of c.addedNodes)v.tagName==="LINK"&&v.rel==="modulepreload"&&t(v)}).observe(document,{childList:!0,subtree:!0});function d(s){const c={};return s.integrity&&(c.integrity=s.integrity),s.referrerPolicy&&(c.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?c.credentials="include":s.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function t(s){if(s.ep)return;s.ep=!0;const c=d(s);fetch(s.href,c)}})();const Ee={id:"app"},Ne={__name:"App",setup(i){return(l,d)=>{const t=B("router-view");return a(),n("div",Ee,[h(t)])}}},De="modulepreload",Le=function(i){return"/"+i},me={},Re=function(l,d,t){let s=Promise.resolve();if(d&&d.length>0){document.getElementsByTagName("link");const v=document.querySelector("meta[property=csp-nonce]"),g=(v==null?void 0:v.nonce)||(v==null?void 0:v.getAttribute("nonce"));s=Promise.allSettled(d.map(m=>{if(m=Le(m),m in me)return;me[m]=!0;const r=m.endsWith(".css"),o=r?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${m}"]${o}`))return;const p=document.createElement("link");if(p.rel=r?"stylesheet":De,r||(p.as="script"),p.crossOrigin="",p.href=m,g&&p.setAttribute("nonce",g),document.head.appendChild(p),r)return new Promise((f,M)=>{p.addEventListener("load",f),p.addEventListener("error",()=>M(new Error(`Unable to preload CSS for ${m}`)))})}))}function c(v){const g=new Event("vite:preloadError",{cancelable:!0});if(g.payload=v,window.dispatchEvent(g),!g.defaultPrevented)throw v}return s.then(v=>{for(const g of v||[])g.status==="rejected"&&c(g.reason);return l().catch(c)})},F=Me.create({baseURL:"",timeout:1e4,withCredentials:!0,headers:{"Content-Type":"application/json"}});F.interceptors.request.use(i=>{var d,t;const l=(d=document.querySelector('meta[name="csrf-token"]'))==null?void 0:d.getAttribute("content");return l&&["post","put","patch","delete"].includes((t=i.method)==null?void 0:t.toLowerCase())&&(i.headers["X-CSRFToken"]=l),i},i=>Promise.reject(i));F.interceptors.response.use(i=>i,i=>{var l;return((l=i.response)==null?void 0:l.status)===401&&(localStorage.removeItem("user"),console.warn("Sessione scaduta, autenticazione richiesta")),Promise.reject(i)});const U=he("auth",()=>{const i=localStorage.getItem("user"),l=w(i?JSON.parse(i):null),d=w(!1),t=w(null),s=w(!1),c=_(()=>!!l.value&&s.value);async function v(p){var f,M;d.value=!0,t.value=null;try{const A=await F.post("/api/auth/login",p);return A.data.success?(l.value=A.data.data.user,localStorage.setItem("user",JSON.stringify(l.value)),s.value=!0,{success:!0}):(t.value=A.data.message||"Errore durante il login",{success:!1,error:t.value})}catch(A){return t.value=((M=(f=A.response)==null?void 0:f.data)==null?void 0:M.message)||"Errore di connessione",{success:!1,error:t.value}}finally{d.value=!1}}async function g(p){var f,M;d.value=!0,t.value=null;try{const A=await F.post("/api/auth/register",p);return A.data.success?{success:!0,message:A.data.message}:(t.value=A.data.message||"Errore durante la registrazione",{success:!1,error:t.value})}catch(A){return t.value=((M=(f=A.response)==null?void 0:f.data)==null?void 0:M.message)||"Errore di connessione",{success:!1,error:t.value}}finally{d.value=!1}}async function m(){try{await F.post("/api/auth/logout")}catch(p){console.warn("Errore durante il logout:",p)}finally{l.value=null,s.value=!1,localStorage.removeItem("user")}}async function r(){if(s.value)return c.value;try{const p=await F.get("/api/auth/me");return p.data.success?(l.value=p.data.data.user,localStorage.setItem("user",JSON.stringify(l.value)),s.value=!0,!0):(await m(),!1)}catch{return await m(),!1}}async function o(){return l.value?await r():(s.value=!0,!1)}return{user:l,loading:d,error:t,sessionChecked:s,isAuthenticated:c,login:v,register:g,logout:m,checkAuth:r,initializeAuth:o}}),q=he("tenant",()=>{const i=w(null),l=w(!1),d=w(null),t=_(()=>{var o;return((o=i.value)==null?void 0:o.company)||{}}),s=_(()=>{var o;return((o=i.value)==null?void 0:o.contact)||{}}),c=_(()=>{var o;return((o=i.value)==null?void 0:o.pages)||{}}),v=_(()=>{var o;return((o=i.value)==null?void 0:o.navigation)||{}}),g=_(()=>{var o;return((o=i.value)==null?void 0:o.footer)||{}});async function m(){try{if(l.value=!0,window.TENANT_CONFIG){i.value=window.TENANT_CONFIG;return}const o=await fetch("/api/config/tenant");i.value=await o.json()}catch(o){d.value="Errore nel caricamento della configurazione",console.error("Errore caricamento tenant config:",o)}finally{l.value=!1}}function r(o,p={}){if(!o||typeof o!="string")return o;let f=o;const M={"company.name":t.value.name||"DatVinci","company.tagline":t.value.tagline||"","company.description":t.value.description||"","company.mission":t.value.mission||"","company.vision":t.value.vision||"","company.founded":t.value.founded||"","company.team_size":t.value.team_size||"","contact.email":s.value.email||"","contact.phone":s.value.phone||"","contact.address":s.value.address||"",current_year:new Date().getFullYear().toString(),...p};for(const[A,H]of Object.entries(M)){const S=new RegExp(`\\{${A}\\}`,"g");f=f.replace(S,H||"")}return f}return{config:i,loading:l,error:d,company:t,contact:s,pages:c,navigation:v,footer:g,loadConfig:m,interpolateText:r}}),Ue={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"},qe={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},Fe={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},Oe={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},Ke={key:4,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"},Ge={key:5,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},We={key:6,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"},Je={key:7,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},Ye={key:8,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},Qe={key:9,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"},Xe={key:10,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},Ze={key:11,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},et={key:12,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"},Z={__name:"SidebarIcon",props:{icon:{type:String,required:!0},className:{type:String,default:"h-5 w-5"}},setup(i){return(l,d)=>(a(),n("svg",{class:j(i.className),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[i.icon==="dashboard"?(a(),n("path",Ue)):i.icon==="projects"?(a(),n("path",qe)):i.icon==="users"?(a(),n("path",Fe)):i.icon==="clients"?(a(),n("path",Oe)):i.icon==="products"?(a(),n("path",Ke)):i.icon==="reports"?(a(),n("path",Ge)):i.icon==="settings"?(a(),n("path",We)):b("",!0),i.icon==="settings"?(a(),n("path",Je)):i.icon==="user-management"?(a(),n("path",Ye)):i.icon==="communications"?(a(),n("path",Qe)):i.icon==="funding"?(a(),n("path",Xe)):i.icon==="reporting"?(a(),n("path",Ze)):(a(),n("path",et))],2))}},tt={key:0,class:"truncate"},st={key:0,class:"truncate"},T={__name:"SidebarNavItem",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(i){const l=_(()=>["text-gray-700 hover:bg-gray-50 hover:text-primary-600"]);return(d,t)=>{const s=B("router-link");return a(),n("div",null,[i.item.path!=="#"?(a(),D(s,{key:0,to:i.item.path,class:j(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[l.value,{"justify-center":i.isCollapsed}]]),"active-class":"text-primary-600 bg-primary-50 border-r-2 border-primary-600",onClick:t[0]||(t[0]=c=>d.$emit("click"))},{default:$(()=>[h(Z,{icon:i.item.icon,class:j(["flex-shrink-0 h-6 w-6",{"mr-0":i.isCollapsed,"mr-3":!i.isCollapsed}])},null,8,["icon","class"]),i.isCollapsed?b("",!0):(a(),n("span",tt,u(i.item.name),1))]),_:1},8,["to","class"])):(a(),n("div",{key:1,class:j(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 cursor-not-allowed opacity-75",["text-gray-400 hover:text-gray-500",{"justify-center":i.isCollapsed}]])},[h(Z,{icon:i.item.icon,class:j(["flex-shrink-0 h-6 w-6",{"mr-0":i.isCollapsed,"mr-3":!i.isCollapsed}])},null,8,["icon","class"]),i.isCollapsed?b("",!0):(a(),n("span",st,u(i.item.name),1))],2))])}}},ot={key:0,class:"flex-1 text-left truncate"},rt={key:0,class:"ml-6 space-y-1 mt-1"},at={__name:"SidebarNavItemCollapsible",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(i){const l=i,d=ee(),t=U(),s=w(!1),c=_(()=>["text-gray-700 hover:bg-gray-50 hover:text-primary-600",{"text-primary-600 bg-primary-50":v.value}]),v=_(()=>l.item.children?l.item.children.some(o=>o.path!=="#"&&d.path.startsWith(o.path)):!1),g=_(()=>l.item.children?l.item.children.filter(o=>{var p;return o.admin?((p=t.user)==null?void 0:p.role)==="admin":!0}):[]);v.value&&(s.value=!0);function m(){l.isCollapsed||(s.value=!s.value)}function r(o){if(o.path==="#")return!1}return(o,p)=>{const f=B("router-link");return a(),n("div",null,[e("button",{onClick:m,class:j(["group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[c.value,{"justify-center":i.isCollapsed}]])},[h(Z,{icon:i.item.icon,class:j(["flex-shrink-0 h-6 w-6",{"mr-0":i.isCollapsed,"mr-3":!i.isCollapsed}])},null,8,["icon","class"]),i.isCollapsed?b("",!0):(a(),n("span",ot,u(i.item.name),1)),i.isCollapsed?b("",!0):(a(),n("svg",{key:1,class:j([{"rotate-90":s.value},"ml-2 h-4 w-4 transition-transform duration-150"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},p[0]||(p[0]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"},null,-1)]),2))],2),s.value&&!i.isCollapsed?(a(),n("div",rt,[(a(!0),n(V,null,P(g.value,M=>(a(),D(f,{key:M.name,to:M.path,class:j(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",M.path==="#"?"text-gray-400 hover:text-gray-500 cursor-not-allowed opacity-75":"text-gray-600 hover:bg-gray-50 hover:text-primary-600"]),"active-class":"text-primary-600 bg-primary-50",onClick:A=>r(M)},{default:$(()=>[C(u(M.name),1)]),_:2},1032,["to","class","onClick"]))),128))])):b("",!0)])}}},nt={class:"mt-5 flex-grow flex flex-col overflow-hidden"},it={class:"flex-1 px-2 space-y-1"},pe={__name:"SidebarNavigation",props:{isCollapsed:{type:Boolean,default:!1}},emits:["item-click"],setup(i){const l=U(),d=_(()=>{var t;return((t=l.user)==null?void 0:t.role)==="admin"});return(t,s)=>(a(),n("div",nt,[e("nav",it,[h(T,{item:{name:"Dashboard",path:"/app/dashboard",icon:"dashboard"},"is-collapsed":i.isCollapsed,onClick:s[0]||(s[0]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(at,{item:{name:"Personale",icon:"users",children:[{name:"👥 Team",path:"/app/personnel"},{name:"📖 Directory",path:"/app/personnel/directory"},{name:"🏢 Organigramma",path:"/app/personnel/orgchart"},{name:"🎯 Competenze",path:"/app/personnel/skills"},{name:"🏢 Dipartimenti",path:"#",admin:!0},{name:"⚙️ Amministrazione",path:"#",admin:!0}]},"is-collapsed":i.isCollapsed,onClick:s[1]||(s[1]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"Progetti",path:"/app/projects",icon:"projects"},"is-collapsed":i.isCollapsed,onClick:s[2]||(s[2]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"CRM",path:"#",icon:"clients"},"is-collapsed":i.isCollapsed,onClick:s[3]||(s[3]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"Prodotti",path:"#",icon:"products"},"is-collapsed":i.isCollapsed,onClick:s[4]||(s[4]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"Performance",path:"#",icon:"reports"},"is-collapsed":i.isCollapsed,onClick:s[5]||(s[5]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"Comunicazione",path:"#",icon:"communications"},"is-collapsed":i.isCollapsed,onClick:s[6]||(s[6]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"Finanziamenti",path:"#",icon:"funding"},"is-collapsed":i.isCollapsed,onClick:s[7]||(s[7]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"Rendicontazione",path:"#",icon:"reporting"},"is-collapsed":i.isCollapsed,onClick:s[8]||(s[8]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),d.value?(a(),D(T,{key:0,item:{name:"Amministrazione",path:"#",icon:"settings"},"is-collapsed":i.isCollapsed,onClick:s[9]||(s[9]=c=>t.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0)])]))}},lt={class:"flex-shrink-0 border-t border-gray-200 p-4"},dt={class:"flex-shrink-0"},ct={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},ut={class:"text-sm font-medium text-primary-700"},mt={key:0,class:"ml-3 flex-1 min-w-0"},pt={class:"text-sm font-medium text-gray-900 truncate"},vt={class:"text-xs text-gray-500 truncate"},gt={class:"py-1"},ht={key:0,class:"mt-3 text-xs text-gray-400 text-center"},ve={__name:"SidebarFooter",props:{isCollapsed:{type:Boolean,default:!1}},setup(i){const l=G(),d=U(),t=w(!1),s=_(()=>d.user&&(d.user.name||d.user.username)||"Utente"),c=_(()=>d.user?s.value.charAt(0).toUpperCase():"U"),v=_(()=>d.user?{admin:"Amministratore",manager:"Manager",employee:"Dipendente",client:"Cliente"}[d.user.role]||d.user.role:""),g=_(()=>"1.0.0");async function m(){t.value=!1,await d.logout(),l.push("/auth/login")}return(r,o)=>{const p=B("router-link");return a(),n("div",lt,[e("div",{class:j(["flex items-center",{"justify-center":i.isCollapsed}])},[e("div",dt,[e("div",ct,[e("span",ut,u(c.value),1)])]),i.isCollapsed?b("",!0):(a(),n("div",mt,[e("p",pt,u(s.value),1),e("p",vt,u(v.value),1)])),e("div",{class:j(["relative",{"ml-3":!i.isCollapsed}])},[e("button",{onClick:o[0]||(o[0]=f=>t.value=!t.value),class:"p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"},o[4]||(o[4]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zM13 12a1 1 0 11-2 0 1 1 0 012 0zM20 12a1 1 0 11-2 0 1 1 0 012 0z"})],-1)])),t.value?(a(),n("div",{key:0,onClick:o[3]||(o[3]=f=>t.value=!1),class:"origin-bottom-left absolute bottom-full left-0 mb-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",gt,[h(p,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:o[1]||(o[1]=f=>t.value=!1)},{default:$(()=>o[5]||(o[5]=[C(" Il tuo profilo ")])),_:1,__:[5]}),h(p,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:o[2]||(o[2]=f=>t.value=!1)},{default:$(()=>o[6]||(o[6]=[C(" Impostazioni ")])),_:1,__:[6]}),o[7]||(o[7]=e("hr",{class:"my-1"},null,-1)),e("button",{onClick:m,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Esci ")])])):b("",!0)],2)],2),g.value&&!i.isCollapsed?(a(),n("div",ht," v"+u(g.value),1)):b("",!0)])}}},ft={class:"flex"},xt={class:"hidden lg:flex lg:flex-shrink-0 lg:fixed lg:inset-y-0 z-10"},yt={class:"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white border-r border-gray-200 shadow-sm"},_t={class:"flex items-center flex-shrink-0 px-4"},bt={class:"w-10 h-10 bg-primary-600 rounded flex items-center justify-center mr-3"},kt={class:"text-white font-bold text-lg"},wt={class:"text-xl font-semibold text-gray-900"},$t={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Ct={class:"text-white font-bold text-sm"},jt={class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Mt=["d"],zt={class:"flex flex-col h-full pt-5 pb-4 overflow-y-auto bg-white border-r border-gray-200 shadow-sm"},St={class:"flex items-center justify-between px-4 mb-4"},At={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center mr-3"},It={class:"text-white font-bold text-sm"},Bt={class:"text-xl font-semibold text-gray-900"},Vt={__name:"AppSidebar",props:{isMobileOpen:{type:Boolean,default:!1}},emits:["close"],setup(i){const l=q(),d=w(!1),t=_(()=>l.config||{}),s=_(()=>{var m;return((m=t.value.company)==null?void 0:m.name)||"DatPortal"}),c=_(()=>s.value.split(" ").map(r=>r[0]).join("").toUpperCase().slice(0,2));function v(){d.value=!d.value}function g(){d.value&&(d.value=!1)}return(m,r)=>{const o=B("router-link");return a(),n("div",ft,[e("div",xt,[e("div",{class:j(["flex flex-col transition-all duration-300",[d.value?"w-20":"w-64"]])},[e("div",yt,[e("div",_t,[e("div",{class:j(["flex items-center",{"justify-center":d.value}])},[h(o,{to:"/app/dashboard",class:j(["flex items-center",{hidden:d.value}])},{default:$(()=>[e("div",bt,[e("span",kt,u(c.value),1)]),e("h3",wt,u(s.value),1)]),_:1},8,["class"]),h(o,{to:"/app/dashboard",class:j(["flex items-center justify-center",{hidden:!d.value}])},{default:$(()=>[e("div",$t,[e("span",Ct,u(c.value),1)])]),_:1},8,["class"])],2),e("button",{onClick:v,class:"ml-auto text-gray-600 focus:outline-none hover:bg-gray-100 p-1 rounded"},[(a(),n("svg",jt,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:d.value?"M13 5l7 7-7 7M5 5l7 7-7 7":"M11 19l-7-7 7-7m8 14l-7-7 7-7"},null,8,Mt)]))])]),h(pe,{"is-collapsed":d.value,onItemClick:g},null,8,["is-collapsed"]),h(ve,{"is-collapsed":d.value},null,8,["is-collapsed"])])],2)]),e("div",{class:j(["fixed inset-y-0 left-0 z-30 w-64 bg-primary-700 transform transition-transform duration-300 ease-in-out lg:hidden",i.isMobileOpen?"translate-x-0":"-translate-x-full"])},[e("div",zt,[e("div",St,[h(o,{to:"/app/dashboard",class:"flex items-center"},{default:$(()=>[e("div",At,[e("span",It,u(c.value),1)]),e("h3",Bt,u(s.value),1)]),_:1}),e("button",{onClick:r[0]||(r[0]=p=>m.$emit("close")),class:"p-2 rounded-md text-gray-600 hover:bg-gray-100"},r[2]||(r[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),h(pe,{"is-collapsed":!1,onItemClick:r[1]||(r[1]=p=>m.$emit("close"))}),h(ve,{"is-collapsed":!1})])],2)])}}},Pt={class:"flex","aria-label":"Breadcrumb"},Ht={class:"flex items-center space-x-2 text-sm text-gray-500"},Tt={key:0,class:"mr-2"},Et={class:"flex items-center"},Nt={key:0,class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Dt=["d"],Lt={key:2,class:"font-medium text-gray-900"},Rt={__name:"HeaderBreadcrumbs",props:{breadcrumbs:{type:Array,required:!0}},setup(i){return(l,d)=>{const t=B("router-link");return a(),n("nav",Pt,[e("ol",Ht,[(a(!0),n(V,null,P(i.breadcrumbs,(s,c)=>(a(),n("li",{key:c,class:"flex items-center"},[c>0?(a(),n("div",Tt,d[0]||(d[0]=[e("svg",{class:"h-3 w-3 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]))):b("",!0),s.to&&c<i.breadcrumbs.length-1?(a(),D(t,{key:1,to:s.to,class:"hover:text-gray-700 transition-colors duration-150"},{default:$(()=>[e("span",Et,[s.icon?(a(),n("svg",Nt,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:s.icon},null,8,Dt)])):b("",!0),C(" "+u(s.label),1)])]),_:2},1032,["to"])):(a(),n("span",Lt,u(s.label),1))]))),128))])])}}},Ut={class:"flex items-center space-x-2"},qt={__name:"HeaderQuickActions",emits:["quick-create-project","quick-add-task","quick-search"],setup(i){const l=ee(),d=_(()=>{var s;return((s=l.name)==null?void 0:s.includes("projects"))||l.path.includes("/projects")}),t=_(()=>{var s,c;return((s=l.name)==null?void 0:s.includes("tasks"))||((c=l.name)==null?void 0:c.includes("projects"))||l.path.includes("/tasks")||l.path.includes("/projects")});return(s,c)=>(a(),n("div",Ut,[d.value?(a(),n("button",{key:0,onClick:c[0]||(c[0]=v=>s.$emit("quick-create-project")),class:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},c[3]||(c[3]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),C(" Nuovo Progetto ")]))):b("",!0),t.value?(a(),n("button",{key:1,onClick:c[1]||(c[1]=v=>s.$emit("quick-add-task")),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},c[4]||(c[4]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),C(" Nuovo Task ")]))):b("",!0),e("button",{onClick:c[2]||(c[2]=v=>s.$emit("quick-search")),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",title:"Ricerca rapida"},c[5]||(c[5]=[e("svg",{class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)]))]))}},Ft={class:"relative"},Ot={class:"relative"},Kt={key:0,class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center"},Gt={class:"py-1"},Wt={key:0,class:"px-4 py-8 text-center text-gray-500 text-sm"},Jt={key:1,class:"max-h-64 overflow-y-auto"},Yt=["onClick"],Qt={class:"flex items-start"},Xt={class:"flex-shrink-0"},Zt={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},es=["d"],ts={class:"ml-3 flex-1"},ss={class:"text-sm font-medium text-gray-900"},os={class:"text-xs text-gray-500 mt-1"},rs={class:"text-xs text-gray-400 mt-1"},as={key:0,class:"flex-shrink-0"},ns={key:2,class:"px-4 py-2 border-t border-gray-100"},is={__name:"HeaderNotifications",setup(i){const l=w(!1),d=w([{id:1,type:"task",title:"Nuovo task assegnato",message:'Ti è stato assegnato un nuovo task nel progetto "Website Redesign"',created_at:new Date().toISOString(),read:!1},{id:2,type:"project",title:"Progetto completato",message:'Il progetto "Mobile App" è stato completato con successo',created_at:new Date(Date.now()-36e5).toISOString(),read:!0}]),t=_(()=>d.value.filter(r=>!r.read).length);function s(r){const o={task:"h-6 w-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center",project:"h-6 w-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center",user:"h-6 w-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center",system:"h-6 w-6 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center"};return o[r]||o.system}function c(r){const o={task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2",project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",user:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",system:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return o[r]||o.system}function v(r){const o=new Date(r),f=new Date-o;return f<6e4?"Adesso":f<36e5?`${Math.floor(f/6e4)}m fa`:f<864e5?`${Math.floor(f/36e5)}h fa`:o.toLocaleDateString("it-IT")}function g(r){r.read||(r.read=!0),l.value=!1}function m(){d.value.forEach(r=>r.read=!0)}return(r,o)=>(a(),n("div",Ft,[e("button",{onClick:o[0]||(o[0]=p=>l.value=!l.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[o[3]||(o[3]=e("span",{class:"sr-only"},"Visualizza notifiche",-1)),e("div",Ot,[o[2]||(o[2]=e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM10 21a2 2 0 01-2-2V7a7 7 0 1114 0v12a2 2 0 01-2 2H10z"})],-1)),t.value>0?(a(),n("span",Kt,u(t.value>9?"9+":t.value),1)):b("",!0)])]),l.value?(a(),n("div",{key:0,onClick:o[1]||(o[1]=p=>l.value=!1),class:"origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",Gt,[o[5]||(o[5]=e("div",{class:"px-4 py-2 border-b border-gray-100"},[e("h3",{class:"text-sm font-medium text-gray-900"},"Notifiche")],-1)),d.value.length===0?(a(),n("div",Wt," Nessuna notifica ")):(a(),n("div",Jt,[(a(!0),n(V,null,P(d.value,p=>(a(),n("div",{key:p.id,class:"px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-50 last:border-b-0",onClick:f=>g(p)},[e("div",Qt,[e("div",Xt,[e("div",{class:j(s(p.type))},[(a(),n("svg",Zt,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:c(p.type)},null,8,es)]))],2)]),e("div",ts,[e("p",ss,u(p.title),1),e("p",os,u(p.message),1),e("p",rs,u(v(p.created_at)),1)]),p.read?b("",!0):(a(),n("div",as,o[4]||(o[4]=[e("div",{class:"h-2 w-2 bg-primary-500 rounded-full"},null,-1)])))])],8,Yt))),128))])),d.value.length>0?(a(),n("div",ns,[e("button",{onClick:m,class:"text-xs text-primary-600 hover:text-primary-800"}," Segna tutte come lette ")])):b("",!0)])])):b("",!0)]))}},ls={class:"relative"},ds={class:"flex items-start justify-center min-h-screen pt-16 px-4 pb-20 text-center sm:block sm:p-0"},cs={class:"inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"},us={class:"flex items-center"},ms={class:"flex-1"},ps={key:0,class:"mt-4 max-h-64 overflow-y-auto"},vs={class:"space-y-1"},gs=["onClick"],hs={class:"flex-shrink-0"},fs={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},xs=["d"],ys={class:"ml-3 flex-1 min-w-0"},_s={class:"text-sm font-medium text-gray-900 truncate"},bs={class:"text-xs text-gray-500 truncate"},ks={class:"ml-2 text-xs text-gray-400"},ws={key:1,class:"mt-4 text-center py-4"},$s={key:2,class:"mt-4 text-center py-4"},Cs={__name:"HeaderSearch",setup(i){const l=G(),d=w(!1),t=w(""),s=w([]),c=w(-1),v=w(!1),g=w(null),m=[{id:1,type:"project",title:"Website Redesign",description:"Progetto di redesign del sito web aziendale",path:"/app/projects/1"},{id:2,type:"person",title:"Mario Rossi",description:"Senior Developer",path:"/app/personnel/directory/1"},{id:3,type:"document",title:"Specifiche Tecniche",description:"Documento delle specifiche del progetto mobile",path:"/app/documents/1"},{id:4,type:"task",title:"Implementazione API",description:"Task per l'implementazione delle API REST",path:"/app/projects/1/tasks/5"}];X(d,async S=>{var k;S?(await fe(),(k=g.value)==null||k.focus()):(t.value="",s.value=[],c.value=-1)});function r(){if(!t.value.trim()){s.value=[];return}v.value=!0,setTimeout(()=>{s.value=m.filter(S=>S.title.toLowerCase().includes(t.value.toLowerCase())||S.description.toLowerCase().includes(t.value.toLowerCase())),c.value=-1,v.value=!1},200)}function o(S){if(s.value.length===0)return;const k=c.value+S;k>=0&&k<s.value.length&&(c.value=k)}function p(){c.value>=0&&s.value[c.value]&&f(s.value[c.value])}function f(S){d.value=!1,l.push(S.path)}function M(S){const k={project:"h-6 w-6 rounded bg-blue-100 text-blue-600 flex items-center justify-center",person:"h-6 w-6 rounded bg-green-100 text-green-600 flex items-center justify-center",document:"h-6 w-6 rounded bg-yellow-100 text-yellow-600 flex items-center justify-center",task:"h-6 w-6 rounded bg-purple-100 text-purple-600 flex items-center justify-center"};return k[S]||k.document}function A(S){const k={project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",person:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",document:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"};return k[S]||k.document}function H(S){return{project:"Progetto",person:"Persona",document:"Documento",task:"Task"}[S]||"Elemento"}return(S,k)=>(a(),n("div",ls,[e("button",{onClick:k[0]||(k[0]=I=>d.value=!d.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},k[7]||(k[7]=[e("span",{class:"sr-only"},"Cerca",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),d.value?(a(),n("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:k[6]||(k[6]=te(I=>d.value=!1,["self"]))},[e("div",ds,[k[11]||(k[11]=e("div",{class:"fixed inset-0 bg-black bg-opacity-25 transition-opacity"},null,-1)),e("div",cs,[e("div",null,[e("div",us,[e("div",ms,[N(e("input",{ref_key:"searchInput",ref:g,"onUpdate:modelValue":k[1]||(k[1]=I=>t.value=I),onInput:r,onKeydown:[k[2]||(k[2]=W(I=>d.value=!1,["escape"])),W(p,["enter"]),k[3]||(k[3]=W(I=>o(-1),["up"])),k[4]||(k[4]=W(I=>o(1),["down"]))],type:"text",placeholder:"Cerca progetti, persone, documenti...",class:"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"},null,544),[[O,t.value]])]),e("button",{onClick:k[5]||(k[5]=I=>d.value=!1),class:"ml-3 p-2 text-gray-400 hover:text-gray-600"},k[8]||(k[8]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),s.value.length>0?(a(),n("div",ps,[e("div",vs,[(a(!0),n(V,null,P(s.value,(I,K)=>(a(),n("div",{key:I.id,onClick:oe=>f(I),class:j(["flex items-center px-3 py-2 rounded-md cursor-pointer",K===c.value?"bg-primary-50":"hover:bg-gray-50"])},[e("div",hs,[e("div",{class:j(M(I.type))},[(a(),n("svg",fs,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:A(I.type)},null,8,xs)]))],2)]),e("div",ys,[e("p",_s,u(I.title),1),e("p",bs,u(I.description),1)]),e("div",ks,u(H(I.type)),1)],10,gs))),128))])])):t.value&&!v.value?(a(),n("div",ws,k[9]||(k[9]=[e("p",{class:"text-sm text-gray-500"},"Nessun risultato trovato",-1)]))):t.value?b("",!0):(a(),n("div",$s,k[10]||(k[10]=[e("p",{class:"text-xs text-gray-400"},"Inizia a digitare per cercare...",-1)])))])])])])):b("",!0)]))}},js={class:"relative"},Ms={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},zs={class:"text-sm font-medium text-primary-700"},Ss={class:"py-1"},As={class:"px-4 py-2 border-b border-gray-100"},Is={class:"text-sm font-medium text-gray-900"},Bs={class:"text-xs text-gray-500"},Vs={__name:"HeaderUserMenu",setup(i){const l=G(),d=U(),t=w(!1),s=_(()=>d.user&&(d.user.name||d.user.username)||"Utente"),c=_(()=>{var m;return((m=d.user)==null?void 0:m.email)||""}),v=_(()=>d.user?s.value.charAt(0).toUpperCase():"U");async function g(){t.value=!1,await d.logout(),l.push("/auth/login")}return(m,r)=>{const o=B("router-link");return a(),n("div",js,[e("button",{onClick:r[0]||(r[0]=p=>t.value=!t.value),class:"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[r[4]||(r[4]=e("span",{class:"sr-only"},"Apri menu utente",-1)),e("div",Ms,[e("span",zs,u(v.value),1)])]),t.value?(a(),n("div",{key:0,onClick:r[3]||(r[3]=p=>t.value=!1),class:"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",Ss,[e("div",As,[e("p",Is,u(s.value),1),e("p",Bs,u(c.value),1)]),h(o,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:r[1]||(r[1]=p=>t.value=!1)},{default:$(()=>r[5]||(r[5]=[C(" Il tuo profilo ")])),_:1,__:[5]}),h(o,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:r[2]||(r[2]=p=>t.value=!1)},{default:$(()=>r[6]||(r[6]=[C(" Impostazioni ")])),_:1,__:[6]}),r[7]||(r[7]=e("div",{class:"border-t border-gray-100 my-1"},null,-1)),e("button",{onClick:g,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Esci ")])])):b("",!0)])}}},Ps={class:"bg-white shadow-sm border-b border-gray-200"},Hs={class:"flex justify-between items-center px-4 py-4 sm:px-6 lg:px-8"},Ts={class:"flex items-center space-x-4"},Es={class:"flex flex-col"},Ns={class:"text-lg font-semibold text-gray-900"},Ds={class:"flex items-center space-x-4"},Ls={class:"hidden md:flex items-center space-x-2"},Rs={__name:"AppHeader",props:{pageTitle:{type:String,required:!0},breadcrumbs:{type:Array,default:()=>[]}},emits:["toggle-mobile-sidebar"],setup(i){return(l,d)=>(a(),n("header",Ps,[e("div",Hs,[e("div",Ts,[e("button",{onClick:d[0]||(d[0]=t=>l.$emit("toggle-mobile-sidebar")),class:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"},d[1]||(d[1]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)])),e("div",Es,[e("h2",Ns,u(i.pageTitle),1),i.breadcrumbs.length>0?(a(),D(Rt,{key:0,breadcrumbs:i.breadcrumbs},null,8,["breadcrumbs"])):b("",!0)])]),e("div",Ds,[e("div",Ls,[h(qt)]),h(is),h(Cs),h(Vs)])])]))}},Us={__name:"LoadingSpinner",props:{size:{type:String,default:"md",validator:i=>["sm","md","lg","xl"].includes(i)},message:{type:String,default:""},centered:{type:Boolean,default:!0}},setup(i){const l=i,d=_(()=>{const v={sm:"20px",md:"32px",lg:"48px",xl:"64px"};return`width: ${v[l.size]}; height: ${v[l.size]};`}),t=_(()=>["flex",l.centered?"items-center justify-center":"","space-y-2"]),s=_(()=>["flex items-center justify-center"]),c=_(()=>["text-sm text-gray-600 text-center"]);return(v,g)=>(a(),n("div",{class:j(t.value)},[e("div",{class:j(s.value)},[e("div",{class:"animate-spin rounded-full border-2 border-gray-300 border-t-primary-600",style:xe(d.value)},null,4)],2),i.message?(a(),n("p",{key:0,class:j(c.value)},u(i.message),3)):b("",!0)],2))}},Y=(i,l)=>{const d=i.__vccOpts||i;for(const[t,s]of l)d[t]=s;return d},qs={class:"fixed bottom-0 right-0 z-50 p-6 space-y-4"},Fs={class:"p-4"},Os={class:"flex items-start"},Ks={class:"flex-shrink-0"},Gs={class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ws=["d"],Js={class:"ml-3 w-0 flex-1 pt-0.5"},Ys={class:"text-sm font-medium text-gray-900"},Qs={class:"mt-1 text-sm text-gray-500"},Xs={class:"ml-4 flex-shrink-0 flex"},Zs=["onClick"],eo={__name:"NotificationManager",setup(i){const l=w([]);function d(g){const m=Date.now(),r={id:m,type:g.type||"info",title:g.title,message:g.message,duration:g.duration||5e3};l.value.push(r),r.duration>0&&setTimeout(()=>{t(m)},r.duration)}function t(g){const m=l.value.findIndex(r=>r.id===g);m>-1&&l.value.splice(m,1)}function s(g){const m={success:"border-l-4 border-green-400",error:"border-l-4 border-red-400",warning:"border-l-4 border-yellow-400",info:"border-l-4 border-blue-400"};return m[g]||m.info}function c(g){const m={success:"h-8 w-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center",error:"h-8 w-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center",warning:"h-8 w-8 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center",info:"h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center"};return m[g]||m.info}function v(g){const m={success:"M5 13l4 4L19 7",error:"M6 18L18 6M6 6l12 12",warning:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-2.694-.833-3.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z",info:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return m[g]||m.info}return window.showNotification=d,L(()=>{}),(g,m)=>(a(),n("div",qs,[h(ze,{name:"notification",tag:"div",class:"space-y-4"},{default:$(()=>[(a(!0),n(V,null,P(l.value,r=>(a(),n("div",{key:r.id,class:j([s(r.type),"max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"])},[e("div",Fs,[e("div",Os,[e("div",Ks,[e("div",{class:j(c(r.type))},[(a(),n("svg",Gs,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:v(r.type)},null,8,Ws)]))],2)]),e("div",Js,[e("p",Ys,u(r.title),1),e("p",Qs,u(r.message),1)]),e("div",Xs,[e("button",{onClick:o=>t(r.id),class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},m[0]||(m[0]=[e("span",{class:"sr-only"},"Chiudi",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Zs)])])])],2))),128))]),_:1})]))}},to=Y(eo,[["__scopeId","data-v-220f0827"]]),so={class:"h-screen flex bg-gray-50"},oo={class:"flex flex-col flex-1 overflow-hidden lg:ml-64"},ro={class:"flex-1 overflow-y-auto"},ao={class:"py-6"},no={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},io={key:0,class:"mb-6"},lo={key:1,class:"flex items-center justify-center h-64"},co={__name:"AppLayout",setup(i){const l=ee(),d=q(),t=w(!1),s=w(!1);_(()=>d.config||{});const c=_(()=>d.config!==null),v=_(()=>{var f;return(f=l.meta)!=null&&f.title?l.meta.title:{dashboard:"Dashboard",projects:"Progetti","projects-list":"Elenco Progetti","projects-view":"Dettaglio Progetto","projects-create":"Nuovo Progetto",personnel:"Personale","personnel-directory":"Rubrica Aziendale","personnel-orgchart":"Organigramma","personnel-skills":"Competenze"}[l.name]||"DatPortal"}),g=_(()=>{var p;return(p=l.meta)!=null&&p.breadcrumbs?l.meta.breadcrumbs.map(f=>({label:f.label,to:f.to,icon:f.icon})):[]}),m=_(()=>{var p;return((p=l.meta)==null?void 0:p.hasActions)||!1});function r(){t.value=!t.value}function o(){t.value=!1}return X(l,()=>{s.value=!0,setTimeout(()=>{s.value=!1},300)}),X(l,()=>{o()}),L(()=>{c.value||d.loadConfig()}),(p,f)=>{const M=B("router-view");return a(),n("div",so,[t.value?(a(),n("div",{key:0,onClick:o,class:"fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"})):b("",!0),h(Vt,{"is-mobile-open":t.value,onClose:o},null,8,["is-mobile-open"]),e("div",oo,[h(Rs,{"page-title":v.value,breadcrumbs:g.value,onToggleMobileSidebar:r},null,8,["page-title","breadcrumbs"]),e("main",ro,[e("div",ao,[e("div",no,[m.value?(a(),n("div",io,[Se(p.$slots,"page-actions")])):b("",!0),s.value?(a(),n("div",lo,[h(Us)])):(a(),D(M,{key:2}))])])])]),h(to)])}}},uo={class:"min-h-screen bg-gray-50"},mo={class:"bg-white shadow-sm border-b"},po={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},vo={class:"flex justify-between h-16"},go={class:"flex items-center"},ho={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},fo={class:"text-white font-bold text-sm"},xo={class:"text-xl font-semibold text-gray-900"},yo={class:"hidden md:flex items-center space-x-8"},_o={class:"flex items-center space-x-4 ml-8 pl-8 border-l border-gray-200"},bo={class:"md:hidden flex items-center"},ko={key:0,class:"md:hidden"},wo={class:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t"},$o={class:"bg-gray-800 text-white"},Co={class:"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8"},jo={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},Mo={class:"col-span-1 md:col-span-2"},zo={class:"flex items-center space-x-3 mb-4"},So={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Ao={class:"text-white font-bold text-sm"},Io={class:"text-xl font-semibold"},Bo={class:"text-gray-300 max-w-md"},Vo={class:"space-y-2"},Po={class:"space-y-2 text-gray-300"},Ho={key:0},To={key:1},Eo={key:2},No={class:"mt-8 pt-8 border-t border-gray-700 text-center text-gray-400"},ge={__name:"PublicLayout",setup(i){const l=q(),d=w(!1),t=_(()=>l.config||{}),s=_(()=>{var m;return((m=t.value.company)==null?void 0:m.name)||"DatVinci"}),c=_(()=>s.value.split(" ").map(r=>r[0]).join("").toUpperCase().slice(0,2)),v=_(()=>l.config!==null),g=new Date().getFullYear();return L(()=>{v.value||l.loadConfig()}),(m,r)=>{var f,M,A,H,S,k;const o=B("router-link"),p=B("router-view");return a(),n("div",uo,[e("nav",mo,[e("div",po,[e("div",vo,[e("div",go,[h(o,{to:"/",class:"flex items-center space-x-3"},{default:$(()=>[e("div",ho,[e("span",fo,u(c.value),1)]),e("span",xo,u(s.value),1)]),_:1})]),e("div",yo,[h(o,{to:"/",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:$(()=>r[1]||(r[1]=[C(" Home ")])),_:1,__:[1]}),h(o,{to:"/about",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:$(()=>r[2]||(r[2]=[C(" Chi Siamo ")])),_:1,__:[2]}),h(o,{to:"/services",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:$(()=>r[3]||(r[3]=[C(" Servizi ")])),_:1,__:[3]}),h(o,{to:"/contact",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:$(()=>r[4]||(r[4]=[C(" Contatti ")])),_:1,__:[4]}),e("div",_o,[h(o,{to:"/auth/login",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:$(()=>r[5]||(r[5]=[C(" Accedi ")])),_:1,__:[5]}),h(o,{to:"/auth/register",class:"bg-primary-600 text-white hover:bg-primary-700 px-4 py-2 rounded-md text-sm font-medium"},{default:$(()=>r[6]||(r[6]=[C(" Registrati ")])),_:1,__:[6]})])]),e("div",bo,[e("button",{onClick:r[0]||(r[0]=I=>d.value=!d.value),class:"text-gray-400 hover:text-gray-500"},r[7]||(r[7]=[e("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)]))])])]),d.value?(a(),n("div",ko,[e("div",wo,[h(o,{to:"/",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:$(()=>r[8]||(r[8]=[C(" Home ")])),_:1,__:[8]}),h(o,{to:"/about",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:$(()=>r[9]||(r[9]=[C(" Chi Siamo ")])),_:1,__:[9]}),h(o,{to:"/services",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:$(()=>r[10]||(r[10]=[C(" Servizi ")])),_:1,__:[10]}),h(o,{to:"/contact",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:$(()=>r[11]||(r[11]=[C(" Contatti ")])),_:1,__:[11]}),r[14]||(r[14]=e("hr",{class:"my-2"},null,-1)),h(o,{to:"/auth/login",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:$(()=>r[12]||(r[12]=[C(" Accedi ")])),_:1,__:[12]}),h(o,{to:"/auth/register",class:"block px-3 py-2 text-base font-medium bg-primary-600 text-white rounded-md"},{default:$(()=>r[13]||(r[13]=[C(" Registrati ")])),_:1,__:[13]})])])):b("",!0)]),e("main",null,[h(p)]),e("footer",$o,[e("div",Co,[e("div",jo,[e("div",Mo,[e("div",zo,[e("div",So,[e("span",Ao,u(c.value),1)]),e("span",Io,u(s.value),1)]),e("p",Bo,u(R(l).interpolateText((f=t.value.footer)==null?void 0:f.description)||((M=t.value.company)==null?void 0:M.description)||"Innovazione e tecnologia per il futuro digitale della tua azienda."),1)]),e("div",null,[r[19]||(r[19]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Link Rapidi",-1)),e("ul",Vo,[e("li",null,[h(o,{to:"/",class:"text-gray-300 hover:text-white"},{default:$(()=>r[15]||(r[15]=[C("Home")])),_:1,__:[15]})]),e("li",null,[h(o,{to:"/about",class:"text-gray-300 hover:text-white"},{default:$(()=>r[16]||(r[16]=[C("Chi Siamo")])),_:1,__:[16]})]),e("li",null,[h(o,{to:"/services",class:"text-gray-300 hover:text-white"},{default:$(()=>r[17]||(r[17]=[C("Servizi")])),_:1,__:[17]})]),e("li",null,[h(o,{to:"/contact",class:"text-gray-300 hover:text-white"},{default:$(()=>r[18]||(r[18]=[C("Contatti")])),_:1,__:[18]})])])]),e("div",null,[r[20]||(r[20]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Contatti",-1)),e("ul",Po,[(A=t.value.contact)!=null&&A.email?(a(),n("li",Ho,u(t.value.contact.email),1)):b("",!0),(H=t.value.contact)!=null&&H.phone?(a(),n("li",To,u(t.value.contact.phone),1)):b("",!0),(S=t.value.contact)!=null&&S.address?(a(),n("li",Eo,u(t.value.contact.address),1)):b("",!0)])])]),e("div",No,[e("p",null,u(R(l).interpolateText((k=t.value.footer)==null?void 0:k.copyright)||`© ${R(g)} ${s.value}. Tutti i diritti riservati.`),1)])])])])}}},Do={class:"bg-white"},Lo={class:"relative overflow-hidden"},Ro={class:"max-w-7xl mx-auto"},Uo={class:"relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32"},qo={class:"mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28"},Fo={class:"sm:text-center lg:text-left"},Oo={class:"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl"},Ko={class:"block xl:inline"},Go={class:"mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0"},Wo={class:"mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start"},Jo={class:"rounded-md shadow"},Yo={class:"mt-3 sm:mt-0 sm:ml-3"},Qo={class:"py-12 bg-white"},Xo={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Zo={class:"lg:text-center"},er={class:"text-base text-primary-600 font-semibold tracking-wide uppercase"},tr={class:"mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl"},sr={key:0,class:"mt-10"},or={class:"space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10"},rr={class:"absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white"},ar={class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},nr={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},ir={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},lr={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},dr={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"},cr={class:"ml-16 text-lg leading-6 font-medium text-gray-900"},ur={class:"mt-2 ml-16 text-base text-gray-500"},mr={__name:"Home",setup(i){const l=q(),d=_(()=>l.config||{}),t=_(()=>{var c;return((c=d.value.pages)==null?void 0:c.home)||{}}),s=_(()=>d.value.company||{});return L(()=>{l.config||l.loadConfig()}),(c,v)=>{var m,r,o,p;const g=B("router-link");return a(),n("div",Do,[e("div",Lo,[e("div",Ro,[e("div",Uo,[e("main",qo,[e("div",Fo,[e("h1",Oo,[e("span",Ko,u(((m=t.value.hero)==null?void 0:m.title)||"Innovazione per il futuro"),1)]),e("p",Go,u(((r=t.value.hero)==null?void 0:r.subtitle)||R(l).interpolateText(s.value.description)||"Supportiamo le aziende nel loro percorso di crescita attraverso soluzioni tecnologiche all'avanguardia"),1),e("div",Wo,[e("div",Jo,[h(g,{to:"/services",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 md:py-4 md:text-lg md:px-10"},{default:$(()=>{var f;return[C(u(((f=t.value.hero)==null?void 0:f.cta_primary)||"Scopri i nostri servizi"),1)]}),_:1})]),e("div",Yo,[h(g,{to:"/contact",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 md:py-4 md:text-lg md:px-10"},{default:$(()=>{var f;return[C(u(((f=t.value.hero)==null?void 0:f.cta_secondary)||"Contattaci"),1)]}),_:1})])])])])])]),v[0]||(v[0]=e("div",{class:"lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2"},[e("div",{class:"h-56 w-full bg-gradient-to-r from-primary-400 to-primary-600 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center"},[e("svg",{class:"h-24 w-24 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])])],-1))]),e("div",Qo,[e("div",Xo,[e("div",Zo,[e("h2",er,u(((o=t.value.services_section)==null?void 0:o.title)||"I nostri servizi"),1),e("p",tr,u(((p=t.value.services_section)==null?void 0:p.subtitle)||"Soluzioni innovative per ogni esigenza aziendale"),1)]),s.value.platform_features?(a(),n("div",sr,[e("div",or,[(a(!0),n(V,null,P(s.value.platform_features,f=>(a(),n("div",{key:f.title,class:"relative"},[e("div",rr,[(a(),n("svg",ar,[f.icon==="briefcase"?(a(),n("path",nr)):f.icon==="users"?(a(),n("path",ir)):f.icon==="chart"?(a(),n("path",lr)):(a(),n("path",dr))]))]),e("p",cr,u(f.title),1),e("p",ur,u(f.description),1)]))),128))])])):b("",!0)])])])}}},pr={class:"py-16 bg-white"},vr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},gr={class:"text-center"},hr={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},fr={class:"mt-4 text-xl text-gray-600"},xr={key:0,class:"mt-16"},yr={class:"max-w-3xl mx-auto"},_r={class:"text-3xl font-bold text-gray-900 text-center mb-8"},br={class:"text-lg text-gray-700 leading-relaxed"},kr={class:"mt-16 grid grid-cols-1 md:grid-cols-2 gap-12"},wr={key:0,class:"bg-gray-50 p-8 rounded-lg"},$r={class:"text-2xl font-bold text-gray-900 mb-4"},Cr={class:"text-gray-700"},jr={key:1,class:"bg-gray-50 p-8 rounded-lg"},Mr={class:"text-2xl font-bold text-gray-900 mb-4"},zr={class:"text-gray-700"},Sr={key:1,class:"mt-16"},Ar={class:"text-center mb-12"},Ir={class:"text-3xl font-bold text-gray-900"},Br={class:"mt-4 text-xl text-gray-600"},Vr={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Pr={class:"text-lg font-semibold text-gray-900"},Hr={key:2,class:"mt-16"},Tr={class:"text-center"},Er={class:"text-3xl font-bold text-gray-900"},Nr={class:"mt-4 text-xl text-gray-600"},Dr={class:"mt-8 inline-flex items-center px-6 py-3 bg-primary-50 rounded-lg"},Lr={class:"text-primary-900 font-medium"},Rr={__name:"About",setup(i){const l=q(),d=_(()=>l.config||{}),t=_(()=>{var c;return((c=d.value.pages)==null?void 0:c.about)||{}}),s=_(()=>d.value.company||{});return L(()=>{l.config||l.loadConfig()}),(c,v)=>{var g,m;return a(),n("div",pr,[e("div",vr,[e("div",gr,[e("h1",hr,u(((g=t.value.hero)==null?void 0:g.title)||"Chi Siamo"),1),e("p",fr,u(((m=t.value.hero)==null?void 0:m.subtitle)||"La nostra storia e i nostri valori"),1)]),t.value.story_section?(a(),n("div",xr,[e("div",yr,[e("h2",_r,u(t.value.story_section.title),1),e("p",br,u(R(l).interpolateText(t.value.story_section.content)),1)])])):b("",!0),e("div",kr,[t.value.mission_section?(a(),n("div",wr,[e("h3",$r,u(t.value.mission_section.title),1),e("p",Cr,u(R(l).interpolateText(t.value.mission_section.content)),1)])):b("",!0),t.value.vision_section?(a(),n("div",jr,[e("h3",Mr,u(t.value.vision_section.title),1),e("p",zr,u(R(l).interpolateText(t.value.vision_section.content)),1)])):b("",!0)]),t.value.expertise_section&&s.value.expertise?(a(),n("div",Sr,[e("div",Ar,[e("h2",Ir,u(t.value.expertise_section.title),1),e("p",Br,u(t.value.expertise_section.subtitle),1)]),e("div",Vr,[(a(!0),n(V,null,P(s.value.expertise,r=>(a(),n("div",{key:r,class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200 text-center"},[v[0]||(v[0]=e("div",{class:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-6 h-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Pr,u(r),1)]))),128))])])):b("",!0),t.value.team_section?(a(),n("div",Hr,[e("div",Tr,[e("h2",Er,u(t.value.team_section.title),1),e("p",Nr,u(t.value.team_section.subtitle),1),e("div",Dr,[v[1]||(v[1]=e("svg",{class:"w-5 h-5 text-primary-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),e("span",Lr,u(s.value.team_size),1)])])])):b("",!0)])])}}},Ur={class:"py-16 bg-white"},qr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Fr={class:"text-center"},Or={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Kr={class:"mt-4 text-xl text-gray-600"},Gr={key:0,class:"mt-8 text-center"},Wr={class:"text-lg text-gray-700 max-w-3xl mx-auto"},Jr={class:"mt-16 grid grid-cols-1 lg:grid-cols-2 gap-16"},Yr={key:0},Qr={class:"text-2xl font-bold text-gray-900 mb-8"},Xr={class:"block text-sm font-medium text-gray-700 mb-2"},Zr={class:"block text-sm font-medium text-gray-700 mb-2"},ea={class:"block text-sm font-medium text-gray-700 mb-2"},ta=["disabled"],sa={key:1},oa={class:"text-2xl font-bold text-gray-900 mb-8"},ra={class:"space-y-6"},aa={key:0,class:"flex items-start"},na={class:"font-medium text-gray-900"},ia={class:"text-gray-600"},la={key:1,class:"flex items-start"},da={class:"font-medium text-gray-900"},ca={class:"text-gray-600"},ua={key:2,class:"flex items-start"},ma={class:"font-medium text-gray-900"},pa={class:"text-gray-600"},va={key:3,class:"flex items-start"},ga={class:"font-medium text-gray-900"},ha={class:"text-gray-600"},fa={__name:"Contact",setup(i){const l=q(),d=_(()=>l.config||{}),t=_(()=>{var r;return((r=d.value.pages)==null?void 0:r.contact)||{}}),s=_(()=>d.value.contact||{}),c=w({name:"",email:"",message:""}),v=w(!1),g=w({text:"",type:""}),m=async()=>{var r,o;if(!c.value.name||!c.value.email||!c.value.message){g.value={text:((r=t.value.form)==null?void 0:r.error_message)||"Tutti i campi sono obbligatori",type:"error"};return}v.value=!0,g.value={text:"",type:""};try{await new Promise(p=>setTimeout(p,1e3)),g.value={text:((o=t.value.form)==null?void 0:o.success_message)||"Messaggio inviato con successo!",type:"success"},c.value={name:"",email:"",message:""}}catch{g.value={text:"Errore durante l'invio. Riprova più tardi.",type:"error"}}finally{v.value=!1}};return L(()=>{l.config||l.loadConfig()}),(r,o)=>{var p,f;return a(),n("div",Ur,[e("div",qr,[e("div",Fr,[e("h1",Or,u(((p=t.value.hero)==null?void 0:p.title)||"Contattaci"),1),e("p",Kr,u(((f=t.value.hero)==null?void 0:f.subtitle)||"Siamo qui per aiutarti"),1)]),t.value.intro?(a(),n("div",Gr,[e("p",Wr,u(t.value.intro.content),1)])):b("",!0),e("div",Jr,[t.value.form?(a(),n("div",Yr,[e("h2",Qr,u(t.value.form.title),1),e("form",{onSubmit:te(m,["prevent"]),class:"space-y-6"},[e("div",null,[e("label",Xr,u(t.value.form.name_label),1),N(e("input",{"onUpdate:modelValue":o[0]||(o[0]=M=>c.value.name=M),type:"text",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[O,c.value.name]])]),e("div",null,[e("label",Zr,u(t.value.form.email_label),1),N(e("input",{"onUpdate:modelValue":o[1]||(o[1]=M=>c.value.email=M),type:"email",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[O,c.value.email]])]),e("div",null,[e("label",ea,u(t.value.form.message_label),1),N(e("textarea",{"onUpdate:modelValue":o[2]||(o[2]=M=>c.value.message=M),rows:"6",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[O,c.value.message]])]),e("button",{type:"submit",disabled:v.value,class:"w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50"},u(v.value?"Invio in corso...":t.value.form.submit_button),9,ta),g.value.text?(a(),n("div",{key:0,class:j([g.value.type==="success"?"text-green-600":"text-red-600","text-sm mt-2"])},u(g.value.text),3)):b("",!0)],32)])):b("",!0),t.value.info?(a(),n("div",sa,[e("h2",oa,u(t.value.info.title),1),e("div",ra,[s.value.address?(a(),n("div",aa,[o[3]||(o[3]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),e("div",null,[e("h3",na,u(t.value.info.address_label),1),e("p",ia,u(s.value.address),1)])])):b("",!0),s.value.phone?(a(),n("div",la,[o[4]||(o[4]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})],-1)),e("div",null,[e("h3",da,u(t.value.info.phone_label),1),e("p",ca,u(s.value.phone),1)])])):b("",!0),s.value.email?(a(),n("div",ua,[o[5]||(o[5]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1)),e("div",null,[e("h3",ma,u(t.value.info.email_label),1),e("p",pa,u(s.value.email),1)])])):b("",!0),s.value.hours?(a(),n("div",va,[o[6]||(o[6]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",null,[e("h3",ga,u(t.value.info.hours_label),1),e("p",ha,u(s.value.hours),1)])])):b("",!0)])])):b("",!0)])])])}}},xa={class:"py-16 bg-white"},ya={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},_a={class:"text-center"},ba={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},ka={class:"mt-4 text-xl text-gray-600"},wa={key:0,class:"mt-8 text-center"},$a={class:"text-lg text-gray-700 max-w-3xl mx-auto"},Ca={key:1,class:"mt-16"},ja={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Ma={class:"text-xl font-bold text-gray-900 text-center mb-4"},za={class:"text-gray-600 text-center"},Sa={key:2,class:"mt-20"},Aa={class:"bg-primary-50 rounded-2xl p-12 text-center"},Ia={class:"text-3xl font-bold text-gray-900 mb-4"},Ba={class:"text-xl text-gray-600 mb-8"},Va={__name:"Services",setup(i){const l=q(),d=_(()=>l.config||{}),t=_(()=>{var v;return((v=d.value.pages)==null?void 0:v.services)||{}}),s=_(()=>d.value.company||{}),c=v=>({"Sviluppo Software":"Soluzioni software personalizzate per ottimizzare i processi aziendali e migliorare l'efficienza operativa.","Intelligenza Artificiale":"Implementazione di sistemi AI avanzati per automatizzare processi e analizzare dati complessi.","Consulenza IT":"Consulenza strategica per la trasformazione digitale e l'ottimizzazione dell'infrastruttura tecnologica.","Gestione Progetti Innovativi":"Coordinamento e gestione di progetti tecnologici complessi con metodologie agili.","Supporto su Bandi e Finanziamenti":"Assistenza nella ricerca e gestione di bandi pubblici e finanziamenti per l'innovazione."})[v]||"Servizio professionale di alta qualità per supportare la crescita della tua azienda.";return L(()=>{l.config||l.loadConfig()}),(v,g)=>{var r,o;const m=B("router-link");return a(),n("div",xa,[e("div",ya,[e("div",_a,[e("h1",ba,u(((r=t.value.hero)==null?void 0:r.title)||"I nostri servizi"),1),e("p",ka,u(((o=t.value.hero)==null?void 0:o.subtitle)||"Soluzioni complete per la tua azienda"),1)]),t.value.intro?(a(),n("div",wa,[e("p",$a,u(t.value.intro.content),1)])):b("",!0),s.value.expertise?(a(),n("div",Ca,[e("div",ja,[(a(!0),n(V,null,P(s.value.expertise,p=>(a(),n("div",{key:p,class:"bg-white p-8 rounded-lg shadow-lg border border-gray-200 hover:shadow-xl transition-shadow"},[g[0]||(g[0]=e("div",{class:"w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-6"},[e("svg",{class:"w-8 h-8 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Ma,u(p),1),e("p",za,u(c(p)),1)]))),128))])])):b("",!0),t.value.cta?(a(),n("div",Sa,[e("div",Aa,[e("h2",Ia,u(t.value.cta.title),1),e("p",Ba,u(t.value.cta.subtitle),1),h(m,{to:"/contact",class:"inline-flex items-center px-8 py-4 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"},{default:$(()=>[C(u(t.value.cta.button)+" ",1),g[1]||(g[1]=e("svg",{class:"ml-2 w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 8l4 4m0 0l-4 4m4-4H3"})],-1))]),_:1,__:[1]})])])):b("",!0)])])}}},Pa={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},Ha={class:"max-w-md w-full space-y-8"},Ta={class:"mt-2 text-center text-sm text-gray-600"},Ea={key:0,class:"rounded-md bg-red-50 p-4"},Na={class:"text-sm text-red-700"},Da={class:"rounded-md shadow-sm -space-y-px"},La={class:"flex items-center justify-between"},Ra={class:"flex items-center"},Ua=["disabled"],qa={key:0,class:"absolute left-0 inset-y-0 flex items-center pl-3"},Fa={__name:"Login",setup(i){const l=G(),d=U(),t=w({username:"",password:"",remember:!1}),s=_(()=>d.loading),c=_(()=>d.error);async function v(){(await d.login({username:t.value.username,password:t.value.password,remember:t.value.remember})).success&&l.push("/app/dashboard")}return(g,m)=>{const r=B("router-link");return a(),n("div",Pa,[e("div",Ha,[e("div",null,[m[5]||(m[5]=e("div",{class:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100"},[e("svg",{class:"h-6 w-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])],-1)),m[6]||(m[6]=e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Accedi al tuo account ",-1)),e("p",Ta,[m[4]||(m[4]=C(" Oppure ")),h(r,{to:"/auth/register",class:"font-medium text-primary-600 hover:text-primary-500"},{default:$(()=>m[3]||(m[3]=[C(" registrati per un nuovo account ")])),_:1,__:[3]})])]),e("form",{onSubmit:te(v,["prevent"]),class:"mt-8 space-y-6"},[c.value?(a(),n("div",Ea,[e("div",Na,u(c.value),1)])):b("",!0),e("div",Da,[e("div",null,[m[7]||(m[7]=e("label",{for:"username",class:"sr-only"},"Username",-1)),N(e("input",{id:"username","onUpdate:modelValue":m[0]||(m[0]=o=>t.value.username=o),name:"username",type:"text",autocomplete:"username",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Username"},null,512),[[O,t.value.username]])]),e("div",null,[m[8]||(m[8]=e("label",{for:"password",class:"sr-only"},"Password",-1)),N(e("input",{id:"password","onUpdate:modelValue":m[1]||(m[1]=o=>t.value.password=o),name:"password",type:"password",autocomplete:"current-password",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Password"},null,512),[[O,t.value.password]])])]),e("div",La,[e("div",Ra,[N(e("input",{id:"remember-me","onUpdate:modelValue":m[2]||(m[2]=o=>t.value.remember=o),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[Ae,t.value.remember]]),m[9]||(m[9]=e("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Ricordami ",-1))]),m[10]||(m[10]=e("div",{class:"text-sm"},[e("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500"}," Password dimenticata? ")],-1))]),e("div",null,[e("button",{type:"submit",disabled:s.value,class:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"},[s.value?(a(),n("span",qa,m[11]||(m[11]=[e("svg",{class:"h-5 w-5 text-primary-500 animate-spin",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)]))):b("",!0),C(" "+u(s.value?"Accesso in corso...":"Accedi"),1)],8,Ua)])],32)])])}}},Oa={},Ka={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"};function Ga(i,l){return a(),n("div",Ka,l[0]||(l[0]=[e("div",{class:"max-w-md w-full space-y-8"},[e("div",null,[e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Registra un nuovo account ")]),e("div",{class:"text-center text-gray-600"}," Registrazione in arrivo... ")],-1)]))}const Wa=Y(Oa,[["render",Ga]]),Ja={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Ya={class:"p-5"},Qa={class:"flex items-center"},Xa={class:"ml-5 w-0 flex-1"},Za={class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},en={class:"text-lg font-medium text-gray-900 dark:text-white"},tn={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},sn={key:0,class:"bg-gray-50 dark:bg-gray-700 px-5 py-3"},on={class:"text-sm"},J={__name:"StatsCard",props:{title:{type:String,required:!0},value:{type:[Number,String],required:!0},subtitle:{type:String,default:null},icon:{type:String,required:!0},color:{type:String,default:"primary"},link:{type:String,default:null}},setup(i){const l=t=>t==="project"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`}:t==="users"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>`}:t==="clock"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}:t==="team"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
      </svg>`}:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>`},d=t=>{const s={primary:"bg-primary-500",secondary:"bg-secondary-500",red:"bg-red-500",yellow:"bg-yellow-500",blue:"bg-blue-500",green:"bg-green-500"};return s[t]||s.primary};return(t,s)=>{const c=B("router-link");return a(),n("div",Ja,[e("div",Ya,[e("div",Qa,[e("div",{class:j(["flex-shrink-0 rounded-md p-3",d(i.color)])},[(a(),D(ye(l(i.icon)),{class:"h-6 w-6 text-white"}))],2),e("div",Xa,[e("dl",null,[e("dt",Za,u(i.title),1),e("dd",null,[e("div",en,u(i.value),1),i.subtitle?(a(),n("div",tn,u(i.subtitle),1)):b("",!0)])])])])]),i.link?(a(),n("div",sn,[e("div",on,[h(c,{to:i.link,class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300"},{default:$(()=>s[0]||(s[0]=[C(" Vedi tutti ")])),_:1,__:[0]},8,["to"])])])):b("",!0)])}}},rn={class:"py-6"},an={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},nn={class:"mt-4 md:mt-0 flex space-x-3"},ln={class:"relative"},dn=["disabled"],cn={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},un={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},mn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},pn={class:"relative h-64"},vn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},gn={class:"relative h-64"},hn={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},fn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},xn={class:"p-6"},yn={key:0,class:"text-center py-8 text-gray-500"},_n={key:1,class:"space-y-4"},bn={class:"flex justify-between items-start"},kn={class:"flex-1"},wn={class:"text-sm font-medium text-gray-900 dark:text-white"},$n={class:"text-xs text-gray-500 dark:text-gray-400"},Cn={class:"mt-2 flex justify-between items-center"},jn={class:"text-xs text-gray-500 dark:text-gray-400"},Mn={class:"bg-gray-50 dark:bg-gray-700 px-6 py-3"},zn={class:"text-sm"},Sn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},An={class:"p-6"},In={key:0,class:"text-center py-8 text-gray-500"},Bn={key:1,class:"space-y-4"},Vn={class:"flex-shrink-0"},Pn={class:"flex-1 min-w-0"},Hn={class:"text-sm font-medium text-gray-900 dark:text-white"},Tn={class:"text-xs text-gray-500 dark:text-gray-400"},En={class:"text-xs text-gray-400 dark:text-gray-500"},Nn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Dn={class:"p-6"},Ln={key:0,class:"text-center py-8 text-gray-500"},Rn={key:1,class:"space-y-4"},Un={class:"flex justify-between items-start"},qn={class:"flex-1"},Fn={class:"text-sm font-medium text-gray-900 dark:text-white"},On={class:"text-xs text-gray-500 dark:text-gray-400"},Kn={class:"text-right"},Gn={class:"text-sm font-bold text-gray-900 dark:text-white"},Wn={class:"text-xs text-gray-500"},Jn={class:"mt-2"},Yn={class:"w-full bg-gray-200 rounded-full h-2"},Qn={class:"text-xs text-gray-500 mt-1"},Xn={__name:"Dashboard",setup(i){Q.register(...Ie),G();const l=w(!1),d=w("7"),t=w({}),s=w([]),c=w([]),v=w([]),g=w(null),m=w(null);let r=null,o=null;const p=async()=>{try{const y=await fetch("/api/dashboard/stats");if(!y.ok)throw new Error("Failed to fetch stats");const x=await y.json();t.value=x.data}catch(y){console.error("Error fetching dashboard stats:",y),t.value={}}},f=async()=>{try{const y=await fetch(`/api/dashboard/upcoming-tasks?days=${d.value}&limit=5`);if(!y.ok)throw new Error("Failed to fetch upcoming tasks");const x=await y.json();s.value=x.data.tasks}catch(y){console.error("Error fetching upcoming tasks:",y),s.value=[]}},M=async()=>{try{const y=await fetch("/api/dashboard/recent-activities?limit=5");if(!y.ok)throw new Error("Failed to fetch recent activities");const x=await y.json();c.value=x.data.activities}catch(y){console.error("Error fetching recent activities:",y),c.value=[]}},A=async()=>{try{const y=await fetch("/api/dashboard/kpis?limit=3");if(!y.ok)throw new Error("Failed to fetch KPIs");const x=await y.json();v.value=x.data.kpis}catch(y){console.error("Error fetching KPIs:",y),v.value=[]}},H=async()=>{try{const y=await fetch("/api/dashboard/charts/project-status");if(!y.ok)throw new Error("Failed to fetch project chart data");const x=await y.json();k(x.data.chart)}catch(y){console.error("Error fetching project chart:",y)}},S=async()=>{try{const y=await fetch("/api/dashboard/charts/task-status");if(!y.ok)throw new Error("Failed to fetch task chart data");const x=await y.json();I(x.data.chart)}catch(y){console.error("Error fetching task chart:",y)}},k=y=>{if(!g.value)return;const x=g.value.getContext("2d");r&&r.destroy(),r=new Q(x,{type:"doughnut",data:{labels:y.labels,datasets:[{data:y.data,backgroundColor:["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6"],borderWidth:2,borderColor:"#ffffff"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}}}}})},I=y=>{if(!m.value)return;const x=m.value.getContext("2d");o&&o.destroy(),o=new Q(x,{type:"bar",data:{labels:y.labels,datasets:[{label:"Tasks",data:y.data,backgroundColor:["#60A5FA","#34D399","#FBBF24","#F87171"],borderColor:["#3B82F6","#10B981","#F59E0B","#EF4444"],borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,ticks:{stepSize:1}}}}})},K=async()=>{l.value=!0;try{await Promise.all([p(),f(),M(),A(),H(),S()])}finally{l.value=!1}},oe=y=>new Date(y).toLocaleDateString("it-IT"),be=y=>{const x=new Date(y),E=Math.floor((new Date-x)/(1e3*60));return E<60?`${E} minuti fa`:E<1440?`${Math.floor(E/60)} ore fa`:`${Math.floor(E/1440)} giorni fa`},ke=y=>{const x={high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return x[y]||x.medium},we=y=>{const x={todo:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300","in-progress":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",review:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",done:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return x[y]||x.todo},$e=y=>{const x={task:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`},timesheet:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`},event:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`}};return x[y]||x.task},Ce=y=>{const x={task:"bg-blue-100 text-blue-600",timesheet:"bg-green-100 text-green-600",event:"bg-purple-100 text-purple-600"};return x[y]||x.task},je=y=>y>=90?"bg-green-500":y>=70?"bg-yellow-500":"bg-red-500";return L(async()=>{await K(),await fe(),g.value&&m.value&&(await H(),await S())}),(y,x)=>{var E,ae,ne,ie,le,de,ce,ue;const re=B("router-link");return a(),n("div",rn,[e("div",an,[x[4]||(x[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Dashboard"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Benvenuto! Ecco una panoramica delle attività della tua azienda. ")],-1)),e("div",nn,[e("div",ln,[N(e("select",{"onUpdate:modelValue":x[0]||(x[0]=z=>d.value=z),onChange:K,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500"},x[1]||(x[1]=[e("option",{value:"7"},"Ultimi 7 giorni",-1),e("option",{value:"30"},"Ultimo mese",-1),e("option",{value:"90"},"Ultimi 3 mesi",-1)]),544),[[Be,d.value]])]),e("button",{onClick:K,disabled:l.value,type:"button",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(a(),n("svg",{xmlns:"http://www.w3.org/2000/svg",class:j(["h-4 w-4 mr-2",{"animate-spin":l.value}]),viewBox:"0 0 20 20",fill:"currentColor"},x[2]||(x[2]=[e("path",{"fill-rule":"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z","clip-rule":"evenodd"},null,-1)]),2)),x[3]||(x[3]=C(" Aggiorna "))],8,dn)])]),e("div",cn,[h(J,{title:"Progetti Attivi",value:((E=t.value.projects)==null?void 0:E.active)||0,subtitle:`di ${((ae=t.value.projects)==null?void 0:ae.total)||0} totali`,icon:"project",color:"primary",link:"/projects?status=active"},null,8,["value","subtitle"]),h(J,{title:"Clienti",value:((ne=t.value.team)==null?void 0:ne.clients)||0,icon:"users",color:"secondary",link:"/crm/clients"},null,8,["value"]),h(J,{title:"Task Pendenti",value:((ie=t.value.tasks)==null?void 0:ie.pending)||0,subtitle:`${((le=t.value.tasks)==null?void 0:le.overdue)||0} in ritardo`,icon:"clock",color:((de=t.value.tasks)==null?void 0:de.overdue)>0?"red":"yellow",link:"/tasks?status=pending"},null,8,["value","subtitle","color"]),h(J,{title:"Team Members",value:((ce=t.value.team)==null?void 0:ce.users)||0,subtitle:`${((ue=t.value.team)==null?void 0:ue.departments)||0} dipartimenti`,icon:"team",color:"blue",link:"/personnel"},null,8,["value","subtitle"])]),e("div",un,[e("div",mn,[x[5]||(x[5]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Progetti")],-1)),e("div",pn,[e("canvas",{ref_key:"projectChart",ref:g},null,512)])]),e("div",vn,[x[6]||(x[6]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Attività")],-1)),e("div",gn,[e("canvas",{ref_key:"taskChart",ref:m},null,512)])])]),e("div",hn,[e("div",fn,[e("div",xn,[x[7]||(x[7]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività in Scadenza",-1)),s.value.length===0?(a(),n("div",yn," Nessuna attività in scadenza ")):(a(),n("div",_n,[(a(!0),n(V,null,P(s.value,z=>(a(),n("div",{key:z.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",bn,[e("div",kn,[e("h3",wn,u(z.name),1),e("p",$n,u(z.project_name),1)]),e("span",{class:j(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",ke(z.priority)])},u(z.priority),3)]),e("div",Cn,[e("span",jn," Scadenza: "+u(oe(z.due_date)),1),e("span",{class:j(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",we(z.status)])},u(z.status),3)])]))),128))]))]),e("div",Mn,[e("div",zn,[h(re,{to:"/tasks",class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500"},{default:$(()=>x[8]||(x[8]=[C(" Vedi tutte le attività ")])),_:1,__:[8]})])])]),e("div",Sn,[e("div",An,[x[9]||(x[9]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività Recenti",-1)),c.value.length===0?(a(),n("div",In," Nessuna attività recente ")):(a(),n("div",Bn,[(a(!0),n(V,null,P(c.value,z=>(a(),n("div",{key:`${z.type}-${z.id}`,class:"flex items-start space-x-3"},[e("div",Vn,[e("div",{class:j(["w-8 h-8 rounded-full flex items-center justify-center",Ce(z.type)])},[(a(),D(ye($e(z.type)),{class:"w-4 h-4"}))],2)]),e("div",Pn,[e("p",Hn,u(z.title),1),e("p",Tn,u(z.description),1),e("p",En,u(be(z.timestamp)),1)])]))),128))]))])]),e("div",Nn,[e("div",Dn,[x[10]||(x[10]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"KPIs Principali",-1)),v.value.length===0?(a(),n("div",Ln," Nessun KPI configurato ")):(a(),n("div",Rn,[(a(!0),n(V,null,P(v.value,z=>(a(),n("div",{key:z.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",Un,[e("div",qn,[e("h3",Fn,u(z.name),1),e("p",On,u(z.description),1)]),e("div",Kn,[e("p",Gn,u(z.current_value)+u(z.unit),1),e("p",Wn," Target: "+u(z.target_value)+u(z.unit),1)])]),e("div",Jn,[e("div",Yn,[e("div",{class:j(["h-2 rounded-full",je(z.performance_percentage)]),style:xe({width:Math.min(z.performance_percentage,100)+"%"})},null,6)]),e("p",Qn,u(Math.round(z.performance_percentage))+"% del target",1)])]))),128))]))])])])])}}},Zn={};function ei(i,l){return a(),n("div",null,l[0]||(l[0]=[e("h1",{class:"text-2xl font-bold text-gray-900 mb-6"},"Progetti",-1),e("div",{class:"card"},[e("p",{class:"text-gray-600"},"Gestione progetti in fase di migrazione...")],-1)]))}const ti=Y(Zn,[["render",ei]]),si={};function oi(i,l){return a(),n("div",null,l[0]||(l[0]=[e("h1",{class:"text-2xl font-bold text-gray-900 mb-6"},"Personale",-1),e("div",{class:"card"},[e("p",{class:"text-gray-600"},"Gestione personale in fase di migrazione...")],-1)]))}const ri=Y(si,[["render",oi]]),ai=[{path:"/",component:ge,children:[{path:"",name:"home",component:mr},{path:"about",name:"about",component:Rr},{path:"contact",name:"contact",component:fa},{path:"services",name:"services",component:Va}]},{path:"/auth",component:ge,children:[{path:"login",name:"login",component:Fa},{path:"register",name:"register",component:Wa}]},{path:"/app",component:co,meta:{requiresAuth:!0},children:[{path:"",redirect:"/app/dashboard"},{path:"dashboard",name:"dashboard",component:Xn},{path:"projects",name:"projects",component:ti},{path:"projects/:id",name:"project-view",component:()=>Re(()=>import("./ProjectView.js"),__vite__mapDeps([0,1,2]))},{path:"personnel",name:"personnel",component:ri}]}],_e=Ve({history:Pe(),routes:ai});_e.beforeEach(async(i,l,d)=>{const t=U();if(i.meta.requiresAuth&&(t.sessionChecked||await t.initializeAuth(),!t.isAuthenticated)){d("/auth/login");return}d()});const se=He(Ne),ni=Te();se.use(ni);se.use(_e);const ii=U();ii.initializeAuth().then(()=>{se.mount("#app")});export{Y as _,F as a,U as u};
