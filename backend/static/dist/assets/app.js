const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ProjectView.js","assets/vendor.js","assets/ProjectView.css"])))=>i.map(i=>d[i]);
import{r as $,w as ee,c as a,a as x,b as D,o as s,d as Ae,e as $e,f as b,g as _,n as z,h as q,i as M,t as c,u as re,j as e,F as E,k as H,l as j,m as Y,p as T,q as Ce,s as ae,v as N,x as G,y as Z,z as ne,A as F,T as Be,B as Ve,C as De,D as Me,E as te,G as Te,H as se,I as Ee,J as He,K as Le,L as Ne,M as Re}from"./vendor.js";(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))t(o);new MutationObserver(o=>{for(const u of o)if(u.type==="childList")for(const g of u.addedNodes)g.tagName==="LINK"&&g.rel==="modulepreload"&&t(g)}).observe(document,{childList:!0,subtree:!0});function d(o){const u={};return o.integrity&&(u.integrity=o.integrity),o.referrerPolicy&&(u.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?u.credentials="include":o.crossOrigin==="anonymous"?u.credentials="omit":u.credentials="same-origin",u}function t(o){if(o.ep)return;o.ep=!0;const u=d(o);fetch(o.href,u)}})();const R=$(!1);let ye=!1;const je=n=>{n?(document.documentElement.classList.add("dark"),localStorage.setItem("darkMode","true")):(document.documentElement.classList.remove("dark"),localStorage.setItem("darkMode","false"))},Ue=()=>{ye||(ee(R,n=>{je(n)}),ye=!0)};function ie(){return Ue(),{isDarkMode:R,toggleDarkMode:()=>{R.value=!R.value},setDarkMode:t=>{R.value=t},initializeDarkMode:()=>{const t=localStorage.getItem("darkMode"),o=document.documentElement.classList.contains("dark");if(t==="true")R.value=!0;else if(t==="false")R.value=!1;else{const p=window.matchMedia("(prefers-color-scheme: dark)").matches;R.value=o||p}je(R.value);const u=window.matchMedia("(prefers-color-scheme: dark)"),g=p=>{const m=localStorage.getItem("darkMode");(!m||m==="null")&&(R.value=p.matches)};u.addEventListener("change",g)}}}const Fe={id:"app"},Oe={__name:"App",setup(n){const{initializeDarkMode:l}=ie();return l(),(d,t)=>{const o=D("router-view");return s(),a("div",Fe,[x(o)])}}},qe="modulepreload",Ke=function(n){return"/"+n},be={},Ge=function(l,d,t){let o=Promise.resolve();if(d&&d.length>0){document.getElementsByTagName("link");const g=document.querySelector("meta[property=csp-nonce]"),p=(g==null?void 0:g.nonce)||(g==null?void 0:g.getAttribute("nonce"));o=Promise.allSettled(d.map(m=>{if(m=Ke(m),m in be)return;be[m]=!0;const i=m.endsWith(".css"),r=i?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${m}"]${r}`))return;const v=document.createElement("link");if(v.rel=i?"stylesheet":qe,i||(v.as="script"),v.crossOrigin="",v.href=m,p&&v.setAttribute("nonce",p),document.head.appendChild(v),i)return new Promise((f,C)=>{v.addEventListener("load",f),v.addEventListener("error",()=>C(new Error(`Unable to preload CSS for ${m}`)))})}))}function u(g){const p=new Event("vite:preloadError",{cancelable:!0});if(p.payload=g,window.dispatchEvent(p),!p.defaultPrevented)throw g}return o.then(g=>{for(const p of g||[])p.status==="rejected"&&u(p.reason);return l().catch(u)})},J=Ae.create({baseURL:"",timeout:1e4,withCredentials:!0,headers:{"Content-Type":"application/json"}});J.interceptors.request.use(n=>{var d,t;const l=(d=document.querySelector('meta[name="csrf-token"]'))==null?void 0:d.getAttribute("content");return l&&["post","put","patch","delete"].includes((t=n.method)==null?void 0:t.toLowerCase())&&(n.headers["X-CSRFToken"]=l),n},n=>Promise.reject(n));J.interceptors.response.use(n=>n,n=>{var l;return((l=n.response)==null?void 0:l.status)===401&&(localStorage.removeItem("user"),console.warn("Sessione scaduta, autenticazione richiesta")),Promise.reject(n)});const W=$e("auth",()=>{const n=localStorage.getItem("user"),l=$(n?JSON.parse(n):null),d=$(!1),t=$(null),o=$(!1),u=b(()=>!!l.value&&o.value);async function g(v){var f,C;d.value=!0,t.value=null;try{const B=await J.post("/api/auth/login",v);return B.data.success?(l.value=B.data.data.user,localStorage.setItem("user",JSON.stringify(l.value)),o.value=!0,{success:!0}):(t.value=B.data.message||"Errore durante il login",{success:!1,error:t.value})}catch(B){return t.value=((C=(f=B.response)==null?void 0:f.data)==null?void 0:C.message)||"Errore di connessione",{success:!1,error:t.value}}finally{d.value=!1}}async function p(v){var f,C;d.value=!0,t.value=null;try{const B=await J.post("/api/auth/register",v);return B.data.success?{success:!0,message:B.data.message}:(t.value=B.data.message||"Errore durante la registrazione",{success:!1,error:t.value})}catch(B){return t.value=((C=(f=B.response)==null?void 0:f.data)==null?void 0:C.message)||"Errore di connessione",{success:!1,error:t.value}}finally{d.value=!1}}async function m(){try{await J.post("/api/auth/logout")}catch(v){console.warn("Errore durante il logout:",v)}finally{l.value=null,o.value=!1,localStorage.removeItem("user")}}async function i(){if(o.value)return u.value;try{const v=await J.get("/api/auth/me");return v.data.success?(l.value=v.data.data.user,localStorage.setItem("user",JSON.stringify(l.value)),o.value=!0,!0):(await m(),!1)}catch{return await m(),!1}}async function r(){return l.value?await i():(o.value=!0,!1)}return{user:l,loading:d,error:t,sessionChecked:o,isAuthenticated:u,login:g,register:p,logout:m,checkAuth:i,initializeAuth:r}}),Q=$e("tenant",()=>{const n=$(null),l=$(!1),d=$(null),t=b(()=>{var r;return((r=n.value)==null?void 0:r.company)||{}}),o=b(()=>{var r;return((r=n.value)==null?void 0:r.contact)||{}}),u=b(()=>{var r;return((r=n.value)==null?void 0:r.pages)||{}}),g=b(()=>{var r;return((r=n.value)==null?void 0:r.navigation)||{}}),p=b(()=>{var r;return((r=n.value)==null?void 0:r.footer)||{}});async function m(){try{if(l.value=!0,window.TENANT_CONFIG){n.value=window.TENANT_CONFIG;return}const r=await fetch("/api/config/tenant");n.value=await r.json()}catch(r){d.value="Errore nel caricamento della configurazione",console.error("Errore caricamento tenant config:",r)}finally{l.value=!1}}function i(r,v={}){if(!r||typeof r!="string")return r;let f=r;const C={"company.name":t.value.name||"DatVinci","company.tagline":t.value.tagline||"","company.description":t.value.description||"","company.mission":t.value.mission||"","company.vision":t.value.vision||"","company.founded":t.value.founded||"","company.team_size":t.value.team_size||"","contact.email":o.value.email||"","contact.phone":o.value.phone||"","contact.address":o.value.address||"",current_year:new Date().getFullYear().toString(),...v};for(const[B,L]of Object.entries(C)){const A=new RegExp(`\\{${B}\\}`,"g");f=f.replace(A,L||"")}return f}return{config:n,loading:l,error:d,company:t,contact:o,pages:u,navigation:g,footer:p,loadConfig:m,interpolateText:i}}),We={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"},Qe={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},Je={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},Ye={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},Ze={key:4,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"},Xe={key:5,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},et={key:6,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"},tt={key:7,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},st={key:8,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},ot={key:9,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"},rt={key:10,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},at={key:11,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},nt={key:12,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"},oe={__name:"SidebarIcon",props:{icon:{type:String,required:!0},className:{type:String,default:"h-5 w-5"}},setup(n){return(l,d)=>(s(),a("svg",{class:z(n.className),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[n.icon==="dashboard"?(s(),a("path",We)):n.icon==="projects"?(s(),a("path",Qe)):n.icon==="users"?(s(),a("path",Je)):n.icon==="clients"?(s(),a("path",Ye)):n.icon==="products"?(s(),a("path",Ze)):n.icon==="reports"?(s(),a("path",Xe)):n.icon==="settings"?(s(),a("path",et)):_("",!0),n.icon==="settings"?(s(),a("path",tt)):n.icon==="user-management"?(s(),a("path",st)):n.icon==="communications"?(s(),a("path",ot)):n.icon==="funding"?(s(),a("path",rt)):n.icon==="reporting"?(s(),a("path",at)):(s(),a("path",nt))],2))}},it={key:0,class:"truncate"},lt={key:0,class:"truncate"},U={__name:"SidebarNavItem",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(n){const l=b(()=>["text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400"]);return(d,t)=>{const o=D("router-link");return s(),a("div",null,[n.item.path!=="#"?(s(),q(o,{key:0,to:n.item.path,class:z(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[l.value,{"justify-center":n.isCollapsed}]]),"active-class":"text-primary-600 bg-primary-50 border-r-2 border-primary-600",onClick:t[0]||(t[0]=u=>d.$emit("click"))},{default:M(()=>[x(oe,{icon:n.item.icon,class:z(["flex-shrink-0 h-6 w-6",{"mr-0":n.isCollapsed,"mr-3":!n.isCollapsed}])},null,8,["icon","class"]),n.isCollapsed?_("",!0):(s(),a("span",it,c(n.item.name),1))]),_:1},8,["to","class"])):(s(),a("div",{key:1,class:z(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 cursor-not-allowed opacity-75",["text-gray-400 hover:text-gray-500",{"justify-center":n.isCollapsed}]])},[x(oe,{icon:n.item.icon,class:z(["flex-shrink-0 h-6 w-6",{"mr-0":n.isCollapsed,"mr-3":!n.isCollapsed}])},null,8,["icon","class"]),n.isCollapsed?_("",!0):(s(),a("span",lt,c(n.item.name),1))],2))])}}},dt={key:0,class:"flex-1 text-left truncate"},ct={key:0,class:"ml-6 space-y-1 mt-1"},ut={__name:"SidebarNavItemCollapsible",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(n){const l=n,d=re(),t=W(),o=$(!1),u=b(()=>["text-gray-700 hover:bg-gray-50 hover:text-primary-600",{"text-primary-600 bg-primary-50":g.value}]),g=b(()=>l.item.children?l.item.children.some(r=>r.path!=="#"&&d.path.startsWith(r.path)):!1),p=b(()=>l.item.children?l.item.children.filter(r=>{var v;return r.admin?((v=t.user)==null?void 0:v.role)==="admin":!0}):[]);g.value&&(o.value=!0);function m(){l.isCollapsed||(o.value=!o.value)}function i(r){if(r.path==="#")return!1}return(r,v)=>{const f=D("router-link");return s(),a("div",null,[e("button",{onClick:m,class:z(["group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[u.value,{"justify-center":n.isCollapsed}]])},[x(oe,{icon:n.item.icon,class:z(["flex-shrink-0 h-6 w-6",{"mr-0":n.isCollapsed,"mr-3":!n.isCollapsed}])},null,8,["icon","class"]),n.isCollapsed?_("",!0):(s(),a("span",dt,c(n.item.name),1)),n.isCollapsed?_("",!0):(s(),a("svg",{key:1,class:z([{"rotate-90":o.value},"ml-2 h-4 w-4 transition-transform duration-150"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},v[0]||(v[0]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"},null,-1)]),2))],2),o.value&&!n.isCollapsed?(s(),a("div",ct,[(s(!0),a(E,null,H(p.value,C=>(s(),q(f,{key:C.name,to:C.path,class:z(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",C.path==="#"?"text-gray-400 hover:text-gray-500 cursor-not-allowed opacity-75":"text-gray-600 hover:bg-gray-50 hover:text-primary-600"]),"active-class":"text-primary-600 bg-primary-50",onClick:B=>i(C)},{default:M(()=>[j(c(C.name),1)]),_:2},1032,["to","class","onClick"]))),128))])):_("",!0)])}}},mt={class:"mt-5 flex-grow flex flex-col overflow-hidden"},pt={class:"flex-1 px-2 space-y-1"},ke={__name:"SidebarNavigation",props:{isCollapsed:{type:Boolean,default:!1}},emits:["item-click"],setup(n){const l=W(),d=b(()=>{var t;return((t=l.user)==null?void 0:t.role)==="admin"});return(t,o)=>(s(),a("div",mt,[e("nav",pt,[x(U,{item:{name:"Dashboard",path:"/app/dashboard",icon:"dashboard"},"is-collapsed":n.isCollapsed,onClick:o[0]||(o[0]=u=>t.$emit("item-click"))},null,8,["is-collapsed"]),x(ut,{item:{name:"Personale",icon:"users",children:[{name:"👥 Team",path:"/app/personnel"},{name:"📖 Directory",path:"/app/personnel/directory"},{name:"🏢 Organigramma",path:"/app/personnel/orgchart"},{name:"🎯 Competenze",path:"/app/personnel/skills"},{name:"🏢 Dipartimenti",path:"#",admin:!0},{name:"⚙️ Amministrazione",path:"#",admin:!0}]},"is-collapsed":n.isCollapsed,onClick:o[1]||(o[1]=u=>t.$emit("item-click"))},null,8,["is-collapsed"]),x(U,{item:{name:"Progetti",path:"/app/projects",icon:"projects"},"is-collapsed":n.isCollapsed,onClick:o[2]||(o[2]=u=>t.$emit("item-click"))},null,8,["is-collapsed"]),x(U,{item:{name:"CRM",path:"#",icon:"clients"},"is-collapsed":n.isCollapsed,onClick:o[3]||(o[3]=u=>t.$emit("item-click"))},null,8,["is-collapsed"]),x(U,{item:{name:"Prodotti",path:"#",icon:"products"},"is-collapsed":n.isCollapsed,onClick:o[4]||(o[4]=u=>t.$emit("item-click"))},null,8,["is-collapsed"]),x(U,{item:{name:"Performance",path:"#",icon:"reports"},"is-collapsed":n.isCollapsed,onClick:o[5]||(o[5]=u=>t.$emit("item-click"))},null,8,["is-collapsed"]),x(U,{item:{name:"Comunicazione",path:"#",icon:"communications"},"is-collapsed":n.isCollapsed,onClick:o[6]||(o[6]=u=>t.$emit("item-click"))},null,8,["is-collapsed"]),x(U,{item:{name:"Finanziamenti",path:"#",icon:"funding"},"is-collapsed":n.isCollapsed,onClick:o[7]||(o[7]=u=>t.$emit("item-click"))},null,8,["is-collapsed"]),x(U,{item:{name:"Rendicontazione",path:"#",icon:"reporting"},"is-collapsed":n.isCollapsed,onClick:o[8]||(o[8]=u=>t.$emit("item-click"))},null,8,["is-collapsed"]),d.value?(s(),q(U,{key:0,item:{name:"Amministrazione",path:"#",icon:"settings"},"is-collapsed":n.isCollapsed,onClick:o[9]||(o[9]=u=>t.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0)])]))}},gt={class:"flex-shrink-0 border-t border-gray-200 p-4"},vt={class:"flex-shrink-0"},ft={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},xt={class:"text-sm font-medium text-primary-700"},ht={key:0,class:"ml-3 flex-1 min-w-0"},yt={class:"text-sm font-medium text-gray-900 truncate"},bt={class:"text-xs text-gray-500 truncate"},kt={class:"py-1"},_t={key:0,class:"mt-3 text-xs text-gray-400 text-center"},_e={__name:"SidebarFooter",props:{isCollapsed:{type:Boolean,default:!1}},setup(n){const l=Y(),d=W(),t=$(!1),o=b(()=>d.user&&(d.user.name||d.user.username)||"Utente"),u=b(()=>d.user?o.value.charAt(0).toUpperCase():"U"),g=b(()=>d.user?{admin:"Amministratore",manager:"Manager",employee:"Dipendente",client:"Cliente"}[d.user.role]||d.user.role:""),p=b(()=>"1.0.0");async function m(){t.value=!1,await d.logout(),l.push("/auth/login")}return(i,r)=>{const v=D("router-link");return s(),a("div",gt,[e("div",{class:z(["flex items-center",{"justify-center":n.isCollapsed}])},[e("div",vt,[e("div",ft,[e("span",xt,c(u.value),1)])]),n.isCollapsed?_("",!0):(s(),a("div",ht,[e("p",yt,c(o.value),1),e("p",bt,c(g.value),1)])),e("div",{class:z(["relative",{"ml-3":!n.isCollapsed}])},[e("button",{onClick:r[0]||(r[0]=f=>t.value=!t.value),class:"p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"},r[4]||(r[4]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zM13 12a1 1 0 11-2 0 1 1 0 012 0zM20 12a1 1 0 11-2 0 1 1 0 012 0z"})],-1)])),t.value?(s(),a("div",{key:0,onClick:r[3]||(r[3]=f=>t.value=!1),class:"origin-bottom-left absolute bottom-full left-0 mb-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",kt,[x(v,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:r[1]||(r[1]=f=>t.value=!1)},{default:M(()=>r[5]||(r[5]=[j(" Il tuo profilo ")])),_:1,__:[5]}),x(v,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:r[2]||(r[2]=f=>t.value=!1)},{default:M(()=>r[6]||(r[6]=[j(" Impostazioni ")])),_:1,__:[6]}),r[7]||(r[7]=e("hr",{class:"my-1"},null,-1)),e("button",{onClick:m,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Esci ")])])):_("",!0)],2)],2),p.value&&!n.isCollapsed?(s(),a("div",_t," v"+c(p.value),1)):_("",!0)])}}},wt={class:"flex"},$t={class:"hidden lg:flex lg:flex-shrink-0 lg:fixed lg:inset-y-0 z-10"},Ct={class:"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},Mt={class:"flex items-center flex-shrink-0 px-4"},jt={class:"w-10 h-10 bg-primary-600 rounded flex items-center justify-center mr-3"},zt={class:"text-white font-bold text-lg"},St={class:"text-xl font-semibold text-gray-900 dark:text-white"},It={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Pt={class:"text-white font-bold text-sm"},At={class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Bt=["d"],Vt={class:"flex flex-col h-full pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},Dt={class:"flex items-center justify-between px-4 mb-4"},Tt={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center mr-3"},Et={class:"text-white font-bold text-sm"},Ht={class:"text-xl font-semibold text-gray-900 dark:text-white"},Lt={__name:"AppSidebar",props:{isMobileOpen:{type:Boolean,default:!1}},emits:["close"],setup(n){const l=Q(),d=$(!1),t=b(()=>l.config||{}),o=b(()=>{var m;return((m=t.value.company)==null?void 0:m.name)||"DatPortal"}),u=b(()=>o.value.split(" ").map(i=>i[0]).join("").toUpperCase().slice(0,2));function g(){d.value=!d.value}function p(){d.value&&(d.value=!1)}return(m,i)=>{const r=D("router-link");return s(),a("div",wt,[e("div",$t,[e("div",{class:z(["flex flex-col transition-all duration-300",[d.value?"w-20":"w-64"]])},[e("div",Ct,[e("div",Mt,[e("div",{class:z(["flex items-center",{"justify-center":d.value}])},[x(r,{to:"/app/dashboard",class:z(["flex items-center",{hidden:d.value}])},{default:M(()=>[e("div",jt,[e("span",zt,c(u.value),1)]),e("h3",St,c(o.value),1)]),_:1},8,["class"]),x(r,{to:"/app/dashboard",class:z(["flex items-center justify-center",{hidden:!d.value}])},{default:M(()=>[e("div",It,[e("span",Pt,c(u.value),1)])]),_:1},8,["class"])],2),e("button",{onClick:g,class:"ml-auto text-gray-600 dark:text-gray-400 focus:outline-none hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded"},[(s(),a("svg",At,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:d.value?"M13 5l7 7-7 7M5 5l7 7-7 7":"M11 19l-7-7 7-7m8 14l-7-7 7-7"},null,8,Bt)]))])]),x(ke,{"is-collapsed":d.value,onItemClick:p},null,8,["is-collapsed"]),x(_e,{"is-collapsed":d.value},null,8,["is-collapsed"])])],2)]),e("div",{class:z(["fixed inset-y-0 left-0 z-30 w-64 bg-primary-700 transform transition-transform duration-300 ease-in-out lg:hidden",n.isMobileOpen?"translate-x-0":"-translate-x-full"])},[e("div",Vt,[e("div",Dt,[x(r,{to:"/app/dashboard",class:"flex items-center"},{default:M(()=>[e("div",Tt,[e("span",Et,c(u.value),1)]),e("h3",Ht,c(o.value),1)]),_:1}),e("button",{onClick:i[0]||(i[0]=v=>m.$emit("close")),class:"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"},i[2]||(i[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),x(ke,{"is-collapsed":!1,onItemClick:i[1]||(i[1]=v=>m.$emit("close"))}),x(_e,{"is-collapsed":!1})])],2)])}}},Nt={class:"flex","aria-label":"Breadcrumb"},Rt={class:"flex items-center space-x-2 text-sm text-gray-500"},Ut={key:0,class:"mr-2"},Ft={class:"flex items-center"},Ot={key:0,class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},qt=["d"],Kt={key:2,class:"font-medium text-gray-900"},Gt={__name:"HeaderBreadcrumbs",props:{breadcrumbs:{type:Array,required:!0}},setup(n){return(l,d)=>{const t=D("router-link");return s(),a("nav",Nt,[e("ol",Rt,[(s(!0),a(E,null,H(n.breadcrumbs,(o,u)=>(s(),a("li",{key:u,class:"flex items-center"},[u>0?(s(),a("div",Ut,d[0]||(d[0]=[e("svg",{class:"h-3 w-3 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]))):_("",!0),o.to&&u<n.breadcrumbs.length-1?(s(),q(t,{key:1,to:o.to,class:"hover:text-gray-700 transition-colors duration-150"},{default:M(()=>[e("span",Ft,[o.icon?(s(),a("svg",Ot,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:o.icon},null,8,qt)])):_("",!0),j(" "+c(o.label),1)])]),_:2},1032,["to"])):(s(),a("span",Kt,c(o.label),1))]))),128))])])}}},Wt={class:"flex items-center space-x-2"},Qt={key:0,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Jt={key:1,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Yt={__name:"HeaderQuickActions",emits:["quick-create-project","quick-add-task"],setup(n){const l=re(),{isDarkMode:d,toggleDarkMode:t}=ie(),o=b(()=>{var g;return((g=l.name)==null?void 0:g.includes("projects"))||l.path.includes("/projects")}),u=b(()=>{var g,p;return((g=l.name)==null?void 0:g.includes("tasks"))||((p=l.name)==null?void 0:p.includes("projects"))||l.path.includes("/tasks")||l.path.includes("/projects")});return(g,p)=>(s(),a("div",Wt,[o.value?(s(),a("button",{key:0,onClick:p[0]||(p[0]=m=>g.$emit("quick-create-project")),class:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},p[3]||(p[3]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),j(" Nuovo Progetto ")]))):_("",!0),u.value?(s(),a("button",{key:1,onClick:p[1]||(p[1]=m=>g.$emit("quick-add-task")),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},p[4]||(p[4]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),j(" Nuovo Task ")]))):_("",!0),e("button",{onClick:p[2]||(p[2]=(...m)=>T(t)&&T(t)(...m)),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",title:"Cambia tema"},[T(d)?(s(),a("svg",Jt,p[6]||(p[6]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"},null,-1)]))):(s(),a("svg",Qt,p[5]||(p[5]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"},null,-1)])))])]))}},Zt={class:"relative"},Xt={class:"relative"},es={key:0,class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center"},ts={class:"py-1"},ss={key:0,class:"px-4 py-8 text-center text-gray-500 text-sm"},os={key:1,class:"max-h-64 overflow-y-auto"},rs=["onClick"],as={class:"flex items-start"},ns={class:"flex-shrink-0"},is={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ls=["d"],ds={class:"ml-3 flex-1"},cs={class:"text-sm font-medium text-gray-900"},us={class:"text-xs text-gray-500 mt-1"},ms={class:"text-xs text-gray-400 mt-1"},ps={key:0,class:"flex-shrink-0"},gs={key:2,class:"px-4 py-2 border-t border-gray-100"},vs={__name:"HeaderNotifications",setup(n){const l=$(!1),d=$([{id:1,type:"task",title:"Nuovo task assegnato",message:'Ti è stato assegnato un nuovo task nel progetto "Website Redesign"',created_at:new Date().toISOString(),read:!1},{id:2,type:"project",title:"Progetto completato",message:'Il progetto "Mobile App" è stato completato con successo',created_at:new Date(Date.now()-36e5).toISOString(),read:!0}]),t=b(()=>d.value.filter(i=>!i.read).length);function o(i){const r={task:"h-6 w-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center",project:"h-6 w-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center",user:"h-6 w-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center",system:"h-6 w-6 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center"};return r[i]||r.system}function u(i){const r={task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2",project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",user:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",system:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return r[i]||r.system}function g(i){const r=new Date(i),f=new Date-r;return f<6e4?"Adesso":f<36e5?`${Math.floor(f/6e4)}m fa`:f<864e5?`${Math.floor(f/36e5)}h fa`:r.toLocaleDateString("it-IT")}function p(i){i.read||(i.read=!0),l.value=!1}function m(){d.value.forEach(i=>i.read=!0)}return(i,r)=>(s(),a("div",Zt,[e("button",{onClick:r[0]||(r[0]=v=>l.value=!l.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[r[3]||(r[3]=e("span",{class:"sr-only"},"Visualizza notifiche",-1)),e("div",Xt,[r[2]||(r[2]=e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM10 21a2 2 0 01-2-2V7a7 7 0 1114 0v12a2 2 0 01-2 2H10z"})],-1)),t.value>0?(s(),a("span",es,c(t.value>9?"9+":t.value),1)):_("",!0)])]),l.value?(s(),a("div",{key:0,onClick:r[1]||(r[1]=v=>l.value=!1),class:"origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",ts,[r[5]||(r[5]=e("div",{class:"px-4 py-2 border-b border-gray-100"},[e("h3",{class:"text-sm font-medium text-gray-900"},"Notifiche")],-1)),d.value.length===0?(s(),a("div",ss," Nessuna notifica ")):(s(),a("div",os,[(s(!0),a(E,null,H(d.value,v=>(s(),a("div",{key:v.id,class:"px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-50 last:border-b-0",onClick:f=>p(v)},[e("div",as,[e("div",ns,[e("div",{class:z(o(v.type))},[(s(),a("svg",is,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:u(v.type)},null,8,ls)]))],2)]),e("div",ds,[e("p",cs,c(v.title),1),e("p",us,c(v.message),1),e("p",ms,c(g(v.created_at)),1)]),v.read?_("",!0):(s(),a("div",ps,r[4]||(r[4]=[e("div",{class:"h-2 w-2 bg-primary-500 rounded-full"},null,-1)])))])],8,rs))),128))])),d.value.length>0?(s(),a("div",gs,[e("button",{onClick:m,class:"text-xs text-primary-600 hover:text-primary-800"}," Segna tutte come lette ")])):_("",!0)])])):_("",!0)]))}},fs={class:"relative"},xs={class:"flex items-start justify-center min-h-screen pt-16 px-4 pb-20 text-center sm:block sm:p-0"},hs={class:"inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"},ys={class:"flex items-center"},bs={class:"flex-1"},ks={key:0,class:"mt-4 max-h-64 overflow-y-auto"},_s={class:"space-y-1"},ws=["onClick"],$s={class:"flex-shrink-0"},Cs={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ms=["d"],js={class:"ml-3 flex-1 min-w-0"},zs={class:"text-sm font-medium text-gray-900 truncate"},Ss={class:"text-xs text-gray-500 truncate"},Is={class:"ml-2 text-xs text-gray-400"},Ps={key:1,class:"mt-4 text-center py-4"},As={key:2,class:"mt-4 text-center py-4"},Bs={__name:"HeaderSearch",setup(n){const l=Y(),d=$(!1),t=$(""),o=$([]),u=$(-1),g=$(!1),p=$(null),m=[{id:1,type:"project",title:"Website Redesign",description:"Progetto di redesign del sito web aziendale",path:"/app/projects/1"},{id:2,type:"person",title:"Mario Rossi",description:"Senior Developer",path:"/app/personnel/directory/1"},{id:3,type:"document",title:"Specifiche Tecniche",description:"Documento delle specifiche del progetto mobile",path:"/app/documents/1"},{id:4,type:"task",title:"Implementazione API",description:"Task per l'implementazione delle API REST",path:"/app/projects/1/tasks/5"}];ee(d,async A=>{var w;A?(await Ce(),(w=p.value)==null||w.focus()):(t.value="",o.value=[],u.value=-1)});function i(){if(!t.value.trim()){o.value=[];return}g.value=!0,setTimeout(()=>{o.value=m.filter(A=>A.title.toLowerCase().includes(t.value.toLowerCase())||A.description.toLowerCase().includes(t.value.toLowerCase())),u.value=-1,g.value=!1},200)}function r(A){if(o.value.length===0)return;const w=u.value+A;w>=0&&w<o.value.length&&(u.value=w)}function v(){u.value>=0&&o.value[u.value]&&f(o.value[u.value])}function f(A){d.value=!1,l.push(A.path)}function C(A){const w={project:"h-6 w-6 rounded bg-blue-100 text-blue-600 flex items-center justify-center",person:"h-6 w-6 rounded bg-green-100 text-green-600 flex items-center justify-center",document:"h-6 w-6 rounded bg-yellow-100 text-yellow-600 flex items-center justify-center",task:"h-6 w-6 rounded bg-purple-100 text-purple-600 flex items-center justify-center"};return w[A]||w.document}function B(A){const w={project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",person:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",document:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"};return w[A]||w.document}function L(A){return{project:"Progetto",person:"Persona",document:"Documento",task:"Task"}[A]||"Elemento"}return(A,w)=>(s(),a("div",fs,[e("button",{onClick:w[0]||(w[0]=V=>d.value=!d.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},w[7]||(w[7]=[e("span",{class:"sr-only"},"Cerca",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),d.value?(s(),a("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:w[6]||(w[6]=ae(V=>d.value=!1,["self"]))},[e("div",xs,[w[11]||(w[11]=e("div",{class:"fixed inset-0 bg-black bg-opacity-25 transition-opacity"},null,-1)),e("div",hs,[e("div",null,[e("div",ys,[e("div",bs,[N(e("input",{ref_key:"searchInput",ref:p,"onUpdate:modelValue":w[1]||(w[1]=V=>t.value=V),onInput:i,onKeydown:[w[2]||(w[2]=Z(V=>d.value=!1,["escape"])),Z(v,["enter"]),w[3]||(w[3]=Z(V=>r(-1),["up"])),w[4]||(w[4]=Z(V=>r(1),["down"]))],type:"text",placeholder:"Cerca progetti, persone, documenti...",class:"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"},null,544),[[G,t.value]])]),e("button",{onClick:w[5]||(w[5]=V=>d.value=!1),class:"ml-3 p-2 text-gray-400 hover:text-gray-600"},w[8]||(w[8]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),o.value.length>0?(s(),a("div",ks,[e("div",_s,[(s(!0),a(E,null,H(o.value,(V,K)=>(s(),a("div",{key:V.id,onClick:S=>f(V),class:z(["flex items-center px-3 py-2 rounded-md cursor-pointer",K===u.value?"bg-primary-50":"hover:bg-gray-50"])},[e("div",$s,[e("div",{class:z(C(V.type))},[(s(),a("svg",Cs,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:B(V.type)},null,8,Ms)]))],2)]),e("div",js,[e("p",zs,c(V.title),1),e("p",Ss,c(V.description),1)]),e("div",Is,c(L(V.type)),1)],10,ws))),128))])])):t.value&&!g.value?(s(),a("div",Ps,w[9]||(w[9]=[e("p",{class:"text-sm text-gray-500"},"Nessun risultato trovato",-1)]))):t.value?_("",!0):(s(),a("div",As,w[10]||(w[10]=[e("p",{class:"text-xs text-gray-400"},"Inizia a digitare per cercare...",-1)])))])])])])):_("",!0)]))}},Vs={class:"relative"},Ds={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},Ts={class:"text-sm font-medium text-primary-700"},Es={class:"py-1"},Hs={class:"px-4 py-2 border-b border-gray-100 dark:border-gray-700"},Ls={class:"text-sm font-medium text-gray-900 dark:text-white"},Ns={class:"text-xs text-gray-500 dark:text-gray-400"},Rs={__name:"HeaderUserMenu",setup(n){const l=Y(),d=W(),t=$(!1),{isDarkMode:o,toggleDarkMode:u}=ie(),g=b(()=>d.user&&(d.user.name||d.user.username)||"Utente"),p=b(()=>{var r;return((r=d.user)==null?void 0:r.email)||""}),m=b(()=>d.user?g.value.charAt(0).toUpperCase():"U");async function i(){t.value=!1,await d.logout(),l.push("/auth/login")}return(r,v)=>{const f=D("router-link");return s(),a("div",Vs,[e("button",{onClick:v[0]||(v[0]=C=>t.value=!t.value),class:"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[v[5]||(v[5]=e("span",{class:"sr-only"},"Apri menu utente",-1)),e("div",Ds,[e("span",Ts,c(m.value),1)])]),t.value?(s(),a("div",{key:0,onClick:v[4]||(v[4]=C=>t.value=!1),class:"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50"},[e("div",Es,[e("div",Hs,[e("p",Ls,c(g.value),1),e("p",Ns,c(p.value),1)]),x(f,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:v[1]||(v[1]=C=>t.value=!1)},{default:M(()=>v[6]||(v[6]=[j(" Il tuo profilo ")])),_:1,__:[6]}),x(f,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:v[2]||(v[2]=C=>t.value=!1)},{default:M(()=>v[7]||(v[7]=[j(" Impostazioni ")])),_:1,__:[7]}),v[8]||(v[8]=e("div",{class:"border-t border-gray-100 dark:border-gray-700 my-1"},null,-1)),e("button",{onClick:v[3]||(v[3]=(...C)=>T(u)&&T(u)(...C)),class:"flex items-center justify-between w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"},[e("span",null,c(T(o)?"Modalità chiara":"Modalità scura"),1),e("i",{class:z([T(o)?"fas fa-sun":"fas fa-moon","text-xs"])},null,2)]),e("button",{onClick:i,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}," Esci ")])])):_("",!0)])}}},Us={class:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"},Fs={class:"flex justify-between items-center px-4 py-4 sm:px-6 lg:px-8"},Os={class:"flex items-center space-x-4"},qs={class:"flex flex-col"},Ks={class:"text-lg font-semibold text-gray-900 dark:text-white"},Gs={class:"flex items-center space-x-4"},Ws={class:"hidden md:flex items-center space-x-2"},Qs={__name:"AppHeader",props:{pageTitle:{type:String,required:!0},breadcrumbs:{type:Array,default:()=>[]}},emits:["toggle-mobile-sidebar"],setup(n){return(l,d)=>(s(),a("header",Us,[e("div",Fs,[e("div",Os,[e("button",{onClick:d[0]||(d[0]=t=>l.$emit("toggle-mobile-sidebar")),class:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700"},d[1]||(d[1]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)])),e("div",qs,[e("h2",Ks,c(n.pageTitle),1),n.breadcrumbs.length>0?(s(),q(Gt,{key:0,breadcrumbs:n.breadcrumbs},null,8,["breadcrumbs"])):_("",!0)])]),e("div",Gs,[e("div",Ws,[x(Yt)]),x(vs),x(Bs),x(Rs)])])]))}},Js={__name:"LoadingSpinner",props:{size:{type:String,default:"md",validator:n=>["sm","md","lg","xl"].includes(n)},message:{type:String,default:""},centered:{type:Boolean,default:!0}},setup(n){const l=n,d=b(()=>{const g={sm:"20px",md:"32px",lg:"48px",xl:"64px"};return`width: ${g[l.size]}; height: ${g[l.size]};`}),t=b(()=>["flex",l.centered?"items-center justify-center":"","space-y-2"]),o=b(()=>["flex items-center justify-center"]),u=b(()=>["text-sm text-gray-600 text-center"]);return(g,p)=>(s(),a("div",{class:z(t.value)},[e("div",{class:z(o.value)},[e("div",{class:"animate-spin rounded-full border-2 border-gray-300 border-t-primary-600",style:ne(d.value)},null,4)],2),n.message?(s(),a("p",{key:0,class:z(u.value)},c(n.message),3)):_("",!0)],2))}},le=(n,l)=>{const d=n.__vccOpts||n;for(const[t,o]of l)d[t]=o;return d},Ys={class:"fixed bottom-0 right-0 z-50 p-6 space-y-4"},Zs={class:"p-4"},Xs={class:"flex items-start"},eo={class:"flex-shrink-0"},to={class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},so=["d"],oo={class:"ml-3 w-0 flex-1 pt-0.5"},ro={class:"text-sm font-medium text-gray-900"},ao={class:"mt-1 text-sm text-gray-500"},no={class:"ml-4 flex-shrink-0 flex"},io=["onClick"],lo={__name:"NotificationManager",setup(n){const l=$([]);function d(p){const m=Date.now(),i={id:m,type:p.type||"info",title:p.title,message:p.message,duration:p.duration||5e3};l.value.push(i),i.duration>0&&setTimeout(()=>{t(m)},i.duration)}function t(p){const m=l.value.findIndex(i=>i.id===p);m>-1&&l.value.splice(m,1)}function o(p){const m={success:"border-l-4 border-green-400",error:"border-l-4 border-red-400",warning:"border-l-4 border-yellow-400",info:"border-l-4 border-blue-400"};return m[p]||m.info}function u(p){const m={success:"h-8 w-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center",error:"h-8 w-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center",warning:"h-8 w-8 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center",info:"h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center"};return m[p]||m.info}function g(p){const m={success:"M5 13l4 4L19 7",error:"M6 18L18 6M6 6l12 12",warning:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-2.694-.833-3.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z",info:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return m[p]||m.info}return window.showNotification=d,F(()=>{}),(p,m)=>(s(),a("div",Ys,[x(Be,{name:"notification",tag:"div",class:"space-y-4"},{default:M(()=>[(s(!0),a(E,null,H(l.value,i=>(s(),a("div",{key:i.id,class:z([o(i.type),"max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"])},[e("div",Zs,[e("div",Xs,[e("div",eo,[e("div",{class:z(u(i.type))},[(s(),a("svg",to,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:g(i.type)},null,8,so)]))],2)]),e("div",oo,[e("p",ro,c(i.title),1),e("p",ao,c(i.message),1)]),e("div",no,[e("button",{onClick:r=>t(i.id),class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},m[0]||(m[0]=[e("span",{class:"sr-only"},"Chiudi",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,io)])])])],2))),128))]),_:1})]))}},co=le(lo,[["__scopeId","data-v-220f0827"]]),uo={class:"h-screen flex bg-gray-50 dark:bg-gray-900"},mo={class:"flex flex-col flex-1 overflow-hidden lg:ml-64"},po={class:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900"},go={class:"py-6"},vo={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},fo={key:0,class:"mb-6"},xo={key:1,class:"flex items-center justify-center h-64"},ho={__name:"AppLayout",setup(n){const l=re(),d=Q(),t=$(!1),o=$(!1);b(()=>d.config||{});const u=b(()=>d.config!==null),g=b(()=>{var f;return(f=l.meta)!=null&&f.title?l.meta.title:{dashboard:"Dashboard",projects:"Progetti","projects-list":"Elenco Progetti","projects-view":"Dettaglio Progetto","projects-create":"Nuovo Progetto",personnel:"Personale","personnel-directory":"Rubrica Aziendale","personnel-orgchart":"Organigramma","personnel-skills":"Competenze"}[l.name]||"DatPortal"}),p=b(()=>{var v;return(v=l.meta)!=null&&v.breadcrumbs?l.meta.breadcrumbs.map(f=>({label:f.label,to:f.to,icon:f.icon})):[]}),m=b(()=>{var v;return((v=l.meta)==null?void 0:v.hasActions)||!1});function i(){t.value=!t.value}function r(){t.value=!1}return ee(l,()=>{o.value=!0,setTimeout(()=>{o.value=!1},300)}),ee(l,()=>{r()}),F(()=>{u.value||d.loadConfig()}),(v,f)=>{const C=D("router-view");return s(),a("div",uo,[t.value?(s(),a("div",{key:0,onClick:r,class:"fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"})):_("",!0),x(Lt,{"is-mobile-open":t.value,onClose:r},null,8,["is-mobile-open"]),e("div",mo,[x(Qs,{"page-title":g.value,breadcrumbs:p.value,onToggleMobileSidebar:i},null,8,["page-title","breadcrumbs"]),e("main",po,[e("div",go,[e("div",vo,[m.value?(s(),a("div",fo,[Ve(v.$slots,"page-actions")])):_("",!0),o.value?(s(),a("div",xo,[x(Js)])):(s(),q(C,{key:2}))])])])]),x(co)])}}},yo={class:"min-h-screen bg-gray-50"},bo={class:"bg-white shadow-sm border-b"},ko={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},_o={class:"flex justify-between h-16"},wo={class:"flex items-center"},$o={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Co={class:"text-white font-bold text-sm"},Mo={class:"text-xl font-semibold text-gray-900"},jo={class:"hidden md:flex items-center space-x-8"},zo={class:"flex items-center space-x-4 ml-8 pl-8 border-l border-gray-200"},So={class:"md:hidden flex items-center"},Io={key:0,class:"md:hidden"},Po={class:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t"},Ao={class:"bg-gray-800 text-white"},Bo={class:"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8"},Vo={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},Do={class:"col-span-1 md:col-span-2"},To={class:"flex items-center space-x-3 mb-4"},Eo={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Ho={class:"text-white font-bold text-sm"},Lo={class:"text-xl font-semibold"},No={class:"text-gray-300 max-w-md"},Ro={class:"space-y-2"},Uo={class:"space-y-2 text-gray-300"},Fo={key:0},Oo={key:1},qo={key:2},Ko={class:"mt-8 pt-8 border-t border-gray-700 text-center text-gray-400"},we={__name:"PublicLayout",setup(n){const l=Q(),d=$(!1),t=b(()=>l.config||{}),o=b(()=>{var m;return((m=t.value.company)==null?void 0:m.name)||"DatVinci"}),u=b(()=>o.value.split(" ").map(i=>i[0]).join("").toUpperCase().slice(0,2)),g=b(()=>l.config!==null),p=new Date().getFullYear();return F(()=>{g.value||l.loadConfig()}),(m,i)=>{var f,C,B,L,A,w;const r=D("router-link"),v=D("router-view");return s(),a("div",yo,[e("nav",bo,[e("div",ko,[e("div",_o,[e("div",wo,[x(r,{to:"/",class:"flex items-center space-x-3"},{default:M(()=>[e("div",$o,[e("span",Co,c(u.value),1)]),e("span",Mo,c(o.value),1)]),_:1})]),e("div",jo,[x(r,{to:"/",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:M(()=>i[1]||(i[1]=[j(" Home ")])),_:1,__:[1]}),x(r,{to:"/about",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:M(()=>i[2]||(i[2]=[j(" Chi Siamo ")])),_:1,__:[2]}),x(r,{to:"/services",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:M(()=>i[3]||(i[3]=[j(" Servizi ")])),_:1,__:[3]}),x(r,{to:"/contact",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:M(()=>i[4]||(i[4]=[j(" Contatti ")])),_:1,__:[4]}),e("div",zo,[x(r,{to:"/auth/login",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:M(()=>i[5]||(i[5]=[j(" Accedi ")])),_:1,__:[5]}),x(r,{to:"/auth/register",class:"bg-primary-600 text-white hover:bg-primary-700 px-4 py-2 rounded-md text-sm font-medium"},{default:M(()=>i[6]||(i[6]=[j(" Registrati ")])),_:1,__:[6]})])]),e("div",So,[e("button",{onClick:i[0]||(i[0]=V=>d.value=!d.value),class:"text-gray-400 hover:text-gray-500"},i[7]||(i[7]=[e("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)]))])])]),d.value?(s(),a("div",Io,[e("div",Po,[x(r,{to:"/",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:M(()=>i[8]||(i[8]=[j(" Home ")])),_:1,__:[8]}),x(r,{to:"/about",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:M(()=>i[9]||(i[9]=[j(" Chi Siamo ")])),_:1,__:[9]}),x(r,{to:"/services",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:M(()=>i[10]||(i[10]=[j(" Servizi ")])),_:1,__:[10]}),x(r,{to:"/contact",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:M(()=>i[11]||(i[11]=[j(" Contatti ")])),_:1,__:[11]}),i[14]||(i[14]=e("hr",{class:"my-2"},null,-1)),x(r,{to:"/auth/login",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:M(()=>i[12]||(i[12]=[j(" Accedi ")])),_:1,__:[12]}),x(r,{to:"/auth/register",class:"block px-3 py-2 text-base font-medium bg-primary-600 text-white rounded-md"},{default:M(()=>i[13]||(i[13]=[j(" Registrati ")])),_:1,__:[13]})])])):_("",!0)]),e("main",null,[x(v)]),e("footer",Ao,[e("div",Bo,[e("div",Vo,[e("div",Do,[e("div",To,[e("div",Eo,[e("span",Ho,c(u.value),1)]),e("span",Lo,c(o.value),1)]),e("p",No,c(T(l).interpolateText((f=t.value.footer)==null?void 0:f.description)||((C=t.value.company)==null?void 0:C.description)||"Innovazione e tecnologia per il futuro digitale della tua azienda."),1)]),e("div",null,[i[19]||(i[19]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Link Rapidi",-1)),e("ul",Ro,[e("li",null,[x(r,{to:"/",class:"text-gray-300 hover:text-white"},{default:M(()=>i[15]||(i[15]=[j("Home")])),_:1,__:[15]})]),e("li",null,[x(r,{to:"/about",class:"text-gray-300 hover:text-white"},{default:M(()=>i[16]||(i[16]=[j("Chi Siamo")])),_:1,__:[16]})]),e("li",null,[x(r,{to:"/services",class:"text-gray-300 hover:text-white"},{default:M(()=>i[17]||(i[17]=[j("Servizi")])),_:1,__:[17]})]),e("li",null,[x(r,{to:"/contact",class:"text-gray-300 hover:text-white"},{default:M(()=>i[18]||(i[18]=[j("Contatti")])),_:1,__:[18]})])])]),e("div",null,[i[20]||(i[20]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Contatti",-1)),e("ul",Uo,[(B=t.value.contact)!=null&&B.email?(s(),a("li",Fo,c(t.value.contact.email),1)):_("",!0),(L=t.value.contact)!=null&&L.phone?(s(),a("li",Oo,c(t.value.contact.phone),1)):_("",!0),(A=t.value.contact)!=null&&A.address?(s(),a("li",qo,c(t.value.contact.address),1)):_("",!0)])])]),e("div",Ko,[e("p",null,c(T(l).interpolateText((w=t.value.footer)==null?void 0:w.copyright)||`© ${T(p)} ${o.value}. Tutti i diritti riservati.`),1)])])])])}}},Go={class:"bg-white"},Wo={class:"relative overflow-hidden"},Qo={class:"max-w-7xl mx-auto"},Jo={class:"relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32"},Yo={class:"mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28"},Zo={class:"sm:text-center lg:text-left"},Xo={class:"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl"},er={class:"block xl:inline"},tr={class:"mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0"},sr={class:"mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start"},or={class:"rounded-md shadow"},rr={class:"mt-3 sm:mt-0 sm:ml-3"},ar={class:"py-12 bg-white"},nr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ir={class:"lg:text-center"},lr={class:"text-base text-primary-600 font-semibold tracking-wide uppercase"},dr={class:"mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl"},cr={key:0,class:"mt-10"},ur={class:"space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10"},mr={class:"absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white"},pr={class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},gr={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},vr={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},fr={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},xr={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"},hr={class:"ml-16 text-lg leading-6 font-medium text-gray-900"},yr={class:"mt-2 ml-16 text-base text-gray-500"},br={__name:"Home",setup(n){const l=Q(),d=b(()=>l.config||{}),t=b(()=>{var u;return((u=d.value.pages)==null?void 0:u.home)||{}}),o=b(()=>d.value.company||{});return F(()=>{l.config||l.loadConfig()}),(u,g)=>{var m,i,r,v;const p=D("router-link");return s(),a("div",Go,[e("div",Wo,[e("div",Qo,[e("div",Jo,[e("main",Yo,[e("div",Zo,[e("h1",Xo,[e("span",er,c(((m=t.value.hero)==null?void 0:m.title)||"Innovazione per il futuro"),1)]),e("p",tr,c(((i=t.value.hero)==null?void 0:i.subtitle)||T(l).interpolateText(o.value.description)||"Supportiamo le aziende nel loro percorso di crescita attraverso soluzioni tecnologiche all'avanguardia"),1),e("div",sr,[e("div",or,[x(p,{to:"/services",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 md:py-4 md:text-lg md:px-10"},{default:M(()=>{var f;return[j(c(((f=t.value.hero)==null?void 0:f.cta_primary)||"Scopri i nostri servizi"),1)]}),_:1})]),e("div",rr,[x(p,{to:"/contact",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 md:py-4 md:text-lg md:px-10"},{default:M(()=>{var f;return[j(c(((f=t.value.hero)==null?void 0:f.cta_secondary)||"Contattaci"),1)]}),_:1})])])])])])]),g[0]||(g[0]=e("div",{class:"lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2"},[e("div",{class:"h-56 w-full bg-gradient-to-r from-primary-400 to-primary-600 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center"},[e("svg",{class:"h-24 w-24 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])])],-1))]),e("div",ar,[e("div",nr,[e("div",ir,[e("h2",lr,c(((r=t.value.services_section)==null?void 0:r.title)||"I nostri servizi"),1),e("p",dr,c(((v=t.value.services_section)==null?void 0:v.subtitle)||"Soluzioni innovative per ogni esigenza aziendale"),1)]),o.value.platform_features?(s(),a("div",cr,[e("div",ur,[(s(!0),a(E,null,H(o.value.platform_features,f=>(s(),a("div",{key:f.title,class:"relative"},[e("div",mr,[(s(),a("svg",pr,[f.icon==="briefcase"?(s(),a("path",gr)):f.icon==="users"?(s(),a("path",vr)):f.icon==="chart"?(s(),a("path",fr)):(s(),a("path",xr))]))]),e("p",hr,c(f.title),1),e("p",yr,c(f.description),1)]))),128))])])):_("",!0)])])])}}},kr={class:"py-16 bg-white"},_r={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},wr={class:"text-center"},$r={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Cr={class:"mt-4 text-xl text-gray-600"},Mr={key:0,class:"mt-16"},jr={class:"max-w-3xl mx-auto"},zr={class:"text-3xl font-bold text-gray-900 text-center mb-8"},Sr={class:"text-lg text-gray-700 leading-relaxed"},Ir={class:"mt-16 grid grid-cols-1 md:grid-cols-2 gap-12"},Pr={key:0,class:"bg-gray-50 p-8 rounded-lg"},Ar={class:"text-2xl font-bold text-gray-900 mb-4"},Br={class:"text-gray-700"},Vr={key:1,class:"bg-gray-50 p-8 rounded-lg"},Dr={class:"text-2xl font-bold text-gray-900 mb-4"},Tr={class:"text-gray-700"},Er={key:1,class:"mt-16"},Hr={class:"text-center mb-12"},Lr={class:"text-3xl font-bold text-gray-900"},Nr={class:"mt-4 text-xl text-gray-600"},Rr={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Ur={class:"text-lg font-semibold text-gray-900"},Fr={key:2,class:"mt-16"},Or={class:"text-center"},qr={class:"text-3xl font-bold text-gray-900"},Kr={class:"mt-4 text-xl text-gray-600"},Gr={class:"mt-8 inline-flex items-center px-6 py-3 bg-primary-50 rounded-lg"},Wr={class:"text-primary-900 font-medium"},Qr={__name:"About",setup(n){const l=Q(),d=b(()=>l.config||{}),t=b(()=>{var u;return((u=d.value.pages)==null?void 0:u.about)||{}}),o=b(()=>d.value.company||{});return F(()=>{l.config||l.loadConfig()}),(u,g)=>{var p,m;return s(),a("div",kr,[e("div",_r,[e("div",wr,[e("h1",$r,c(((p=t.value.hero)==null?void 0:p.title)||"Chi Siamo"),1),e("p",Cr,c(((m=t.value.hero)==null?void 0:m.subtitle)||"La nostra storia e i nostri valori"),1)]),t.value.story_section?(s(),a("div",Mr,[e("div",jr,[e("h2",zr,c(t.value.story_section.title),1),e("p",Sr,c(T(l).interpolateText(t.value.story_section.content)),1)])])):_("",!0),e("div",Ir,[t.value.mission_section?(s(),a("div",Pr,[e("h3",Ar,c(t.value.mission_section.title),1),e("p",Br,c(T(l).interpolateText(t.value.mission_section.content)),1)])):_("",!0),t.value.vision_section?(s(),a("div",Vr,[e("h3",Dr,c(t.value.vision_section.title),1),e("p",Tr,c(T(l).interpolateText(t.value.vision_section.content)),1)])):_("",!0)]),t.value.expertise_section&&o.value.expertise?(s(),a("div",Er,[e("div",Hr,[e("h2",Lr,c(t.value.expertise_section.title),1),e("p",Nr,c(t.value.expertise_section.subtitle),1)]),e("div",Rr,[(s(!0),a(E,null,H(o.value.expertise,i=>(s(),a("div",{key:i,class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200 text-center"},[g[0]||(g[0]=e("div",{class:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-6 h-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Ur,c(i),1)]))),128))])])):_("",!0),t.value.team_section?(s(),a("div",Fr,[e("div",Or,[e("h2",qr,c(t.value.team_section.title),1),e("p",Kr,c(t.value.team_section.subtitle),1),e("div",Gr,[g[1]||(g[1]=e("svg",{class:"w-5 h-5 text-primary-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),e("span",Wr,c(o.value.team_size),1)])])])):_("",!0)])])}}},Jr={class:"py-16 bg-white"},Yr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Zr={class:"text-center"},Xr={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},ea={class:"mt-4 text-xl text-gray-600"},ta={key:0,class:"mt-8 text-center"},sa={class:"text-lg text-gray-700 max-w-3xl mx-auto"},oa={class:"mt-16 grid grid-cols-1 lg:grid-cols-2 gap-16"},ra={key:0},aa={class:"text-2xl font-bold text-gray-900 mb-8"},na={class:"block text-sm font-medium text-gray-700 mb-2"},ia={class:"block text-sm font-medium text-gray-700 mb-2"},la={class:"block text-sm font-medium text-gray-700 mb-2"},da=["disabled"],ca={key:1},ua={class:"text-2xl font-bold text-gray-900 mb-8"},ma={class:"space-y-6"},pa={key:0,class:"flex items-start"},ga={class:"font-medium text-gray-900"},va={class:"text-gray-600"},fa={key:1,class:"flex items-start"},xa={class:"font-medium text-gray-900"},ha={class:"text-gray-600"},ya={key:2,class:"flex items-start"},ba={class:"font-medium text-gray-900"},ka={class:"text-gray-600"},_a={key:3,class:"flex items-start"},wa={class:"font-medium text-gray-900"},$a={class:"text-gray-600"},Ca={__name:"Contact",setup(n){const l=Q(),d=b(()=>l.config||{}),t=b(()=>{var i;return((i=d.value.pages)==null?void 0:i.contact)||{}}),o=b(()=>d.value.contact||{}),u=$({name:"",email:"",message:""}),g=$(!1),p=$({text:"",type:""}),m=async()=>{var i,r;if(!u.value.name||!u.value.email||!u.value.message){p.value={text:((i=t.value.form)==null?void 0:i.error_message)||"Tutti i campi sono obbligatori",type:"error"};return}g.value=!0,p.value={text:"",type:""};try{await new Promise(v=>setTimeout(v,1e3)),p.value={text:((r=t.value.form)==null?void 0:r.success_message)||"Messaggio inviato con successo!",type:"success"},u.value={name:"",email:"",message:""}}catch{p.value={text:"Errore durante l'invio. Riprova più tardi.",type:"error"}}finally{g.value=!1}};return F(()=>{l.config||l.loadConfig()}),(i,r)=>{var v,f;return s(),a("div",Jr,[e("div",Yr,[e("div",Zr,[e("h1",Xr,c(((v=t.value.hero)==null?void 0:v.title)||"Contattaci"),1),e("p",ea,c(((f=t.value.hero)==null?void 0:f.subtitle)||"Siamo qui per aiutarti"),1)]),t.value.intro?(s(),a("div",ta,[e("p",sa,c(t.value.intro.content),1)])):_("",!0),e("div",oa,[t.value.form?(s(),a("div",ra,[e("h2",aa,c(t.value.form.title),1),e("form",{onSubmit:ae(m,["prevent"]),class:"space-y-6"},[e("div",null,[e("label",na,c(t.value.form.name_label),1),N(e("input",{"onUpdate:modelValue":r[0]||(r[0]=C=>u.value.name=C),type:"text",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[G,u.value.name]])]),e("div",null,[e("label",ia,c(t.value.form.email_label),1),N(e("input",{"onUpdate:modelValue":r[1]||(r[1]=C=>u.value.email=C),type:"email",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[G,u.value.email]])]),e("div",null,[e("label",la,c(t.value.form.message_label),1),N(e("textarea",{"onUpdate:modelValue":r[2]||(r[2]=C=>u.value.message=C),rows:"6",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[G,u.value.message]])]),e("button",{type:"submit",disabled:g.value,class:"w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50"},c(g.value?"Invio in corso...":t.value.form.submit_button),9,da),p.value.text?(s(),a("div",{key:0,class:z([p.value.type==="success"?"text-green-600":"text-red-600","text-sm mt-2"])},c(p.value.text),3)):_("",!0)],32)])):_("",!0),t.value.info?(s(),a("div",ca,[e("h2",ua,c(t.value.info.title),1),e("div",ma,[o.value.address?(s(),a("div",pa,[r[3]||(r[3]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),e("div",null,[e("h3",ga,c(t.value.info.address_label),1),e("p",va,c(o.value.address),1)])])):_("",!0),o.value.phone?(s(),a("div",fa,[r[4]||(r[4]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})],-1)),e("div",null,[e("h3",xa,c(t.value.info.phone_label),1),e("p",ha,c(o.value.phone),1)])])):_("",!0),o.value.email?(s(),a("div",ya,[r[5]||(r[5]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1)),e("div",null,[e("h3",ba,c(t.value.info.email_label),1),e("p",ka,c(o.value.email),1)])])):_("",!0),o.value.hours?(s(),a("div",_a,[r[6]||(r[6]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",null,[e("h3",wa,c(t.value.info.hours_label),1),e("p",$a,c(o.value.hours),1)])])):_("",!0)])])):_("",!0)])])])}}},Ma={class:"py-16 bg-white"},ja={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},za={class:"text-center"},Sa={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Ia={class:"mt-4 text-xl text-gray-600"},Pa={key:0,class:"mt-8 text-center"},Aa={class:"text-lg text-gray-700 max-w-3xl mx-auto"},Ba={key:1,class:"mt-16"},Va={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Da={class:"text-xl font-bold text-gray-900 text-center mb-4"},Ta={class:"text-gray-600 text-center"},Ea={key:2,class:"mt-20"},Ha={class:"bg-primary-50 rounded-2xl p-12 text-center"},La={class:"text-3xl font-bold text-gray-900 mb-4"},Na={class:"text-xl text-gray-600 mb-8"},Ra={__name:"Services",setup(n){const l=Q(),d=b(()=>l.config||{}),t=b(()=>{var g;return((g=d.value.pages)==null?void 0:g.services)||{}}),o=b(()=>d.value.company||{}),u=g=>({"Sviluppo Software":"Soluzioni software personalizzate per ottimizzare i processi aziendali e migliorare l'efficienza operativa.","Intelligenza Artificiale":"Implementazione di sistemi AI avanzati per automatizzare processi e analizzare dati complessi.","Consulenza IT":"Consulenza strategica per la trasformazione digitale e l'ottimizzazione dell'infrastruttura tecnologica.","Gestione Progetti Innovativi":"Coordinamento e gestione di progetti tecnologici complessi con metodologie agili.","Supporto su Bandi e Finanziamenti":"Assistenza nella ricerca e gestione di bandi pubblici e finanziamenti per l'innovazione."})[g]||"Servizio professionale di alta qualità per supportare la crescita della tua azienda.";return F(()=>{l.config||l.loadConfig()}),(g,p)=>{var i,r;const m=D("router-link");return s(),a("div",Ma,[e("div",ja,[e("div",za,[e("h1",Sa,c(((i=t.value.hero)==null?void 0:i.title)||"I nostri servizi"),1),e("p",Ia,c(((r=t.value.hero)==null?void 0:r.subtitle)||"Soluzioni complete per la tua azienda"),1)]),t.value.intro?(s(),a("div",Pa,[e("p",Aa,c(t.value.intro.content),1)])):_("",!0),o.value.expertise?(s(),a("div",Ba,[e("div",Va,[(s(!0),a(E,null,H(o.value.expertise,v=>(s(),a("div",{key:v,class:"bg-white p-8 rounded-lg shadow-lg border border-gray-200 hover:shadow-xl transition-shadow"},[p[0]||(p[0]=e("div",{class:"w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-6"},[e("svg",{class:"w-8 h-8 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Da,c(v),1),e("p",Ta,c(u(v)),1)]))),128))])])):_("",!0),t.value.cta?(s(),a("div",Ea,[e("div",Ha,[e("h2",La,c(t.value.cta.title),1),e("p",Na,c(t.value.cta.subtitle),1),x(m,{to:"/contact",class:"inline-flex items-center px-8 py-4 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"},{default:M(()=>[j(c(t.value.cta.button)+" ",1),p[1]||(p[1]=e("svg",{class:"ml-2 w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 8l4 4m0 0l-4 4m4-4H3"})],-1))]),_:1,__:[1]})])])):_("",!0)])])}}},Ua={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},Fa={class:"max-w-md w-full space-y-8"},Oa={class:"mt-2 text-center text-sm text-gray-600"},qa={key:0,class:"rounded-md bg-red-50 p-4"},Ka={class:"text-sm text-red-700"},Ga={class:"rounded-md shadow-sm -space-y-px"},Wa={class:"flex items-center justify-between"},Qa={class:"flex items-center"},Ja=["disabled"],Ya={key:0,class:"absolute left-0 inset-y-0 flex items-center pl-3"},Za={__name:"Login",setup(n){const l=Y(),d=W(),t=$({username:"",password:"",remember:!1}),o=b(()=>d.loading),u=b(()=>d.error);async function g(){(await d.login({username:t.value.username,password:t.value.password,remember:t.value.remember})).success&&l.push("/app/dashboard")}return(p,m)=>{const i=D("router-link");return s(),a("div",Ua,[e("div",Fa,[e("div",null,[m[5]||(m[5]=e("div",{class:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100"},[e("svg",{class:"h-6 w-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])],-1)),m[6]||(m[6]=e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Accedi al tuo account ",-1)),e("p",Oa,[m[4]||(m[4]=j(" Oppure ")),x(i,{to:"/auth/register",class:"font-medium text-primary-600 hover:text-primary-500"},{default:M(()=>m[3]||(m[3]=[j(" registrati per un nuovo account ")])),_:1,__:[3]})])]),e("form",{onSubmit:ae(g,["prevent"]),class:"mt-8 space-y-6"},[u.value?(s(),a("div",qa,[e("div",Ka,c(u.value),1)])):_("",!0),e("div",Ga,[e("div",null,[m[7]||(m[7]=e("label",{for:"username",class:"sr-only"},"Username",-1)),N(e("input",{id:"username","onUpdate:modelValue":m[0]||(m[0]=r=>t.value.username=r),name:"username",type:"text",autocomplete:"username",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Username"},null,512),[[G,t.value.username]])]),e("div",null,[m[8]||(m[8]=e("label",{for:"password",class:"sr-only"},"Password",-1)),N(e("input",{id:"password","onUpdate:modelValue":m[1]||(m[1]=r=>t.value.password=r),name:"password",type:"password",autocomplete:"current-password",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Password"},null,512),[[G,t.value.password]])])]),e("div",Wa,[e("div",Qa,[N(e("input",{id:"remember-me","onUpdate:modelValue":m[2]||(m[2]=r=>t.value.remember=r),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[De,t.value.remember]]),m[9]||(m[9]=e("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Ricordami ",-1))]),m[10]||(m[10]=e("div",{class:"text-sm"},[e("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500"}," Password dimenticata? ")],-1))]),e("div",null,[e("button",{type:"submit",disabled:o.value,class:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"},[o.value?(s(),a("span",Ya,m[11]||(m[11]=[e("svg",{class:"h-5 w-5 text-primary-500 animate-spin",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)]))):_("",!0),j(" "+c(o.value?"Accesso in corso...":"Accedi"),1)],8,Ja)])],32)])])}}},Xa={},en={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"};function tn(n,l){return s(),a("div",en,l[0]||(l[0]=[e("div",{class:"max-w-md w-full space-y-8"},[e("div",null,[e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Registra un nuovo account ")]),e("div",{class:"text-center text-gray-600"}," Registrazione in arrivo... ")],-1)]))}const sn=le(Xa,[["render",tn]]),on={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},rn={class:"p-5"},an={class:"flex items-center"},nn={class:"ml-5 w-0 flex-1"},ln={class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},dn={class:"text-lg font-medium text-gray-900 dark:text-white"},cn={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},un={key:0,class:"bg-gray-50 dark:bg-gray-700 px-5 py-3"},mn={class:"text-sm"},X={__name:"StatsCard",props:{title:{type:String,required:!0},value:{type:[Number,String],required:!0},subtitle:{type:String,default:null},icon:{type:String,required:!0},color:{type:String,default:"primary"},link:{type:String,default:null}},setup(n){const l=t=>t==="project"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`}:t==="users"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>`}:t==="clock"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}:t==="team"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
      </svg>`}:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>`},d=t=>{const o={primary:"bg-primary-500",secondary:"bg-secondary-500",red:"bg-red-500",yellow:"bg-yellow-500",blue:"bg-blue-500",green:"bg-green-500"};return o[t]||o.primary};return(t,o)=>{const u=D("router-link");return s(),a("div",on,[e("div",rn,[e("div",an,[e("div",{class:z(["flex-shrink-0 rounded-md p-3",d(n.color)])},[(s(),q(Me(l(n.icon)),{class:"h-6 w-6 text-white"}))],2),e("div",nn,[e("dl",null,[e("dt",ln,c(n.title),1),e("dd",null,[e("div",dn,c(n.value),1),n.subtitle?(s(),a("div",cn,c(n.subtitle),1)):_("",!0)])])])])]),n.link?(s(),a("div",un,[e("div",mn,[x(u,{to:n.link,class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300"},{default:M(()=>o[0]||(o[0]=[j(" Vedi tutti ")])),_:1,__:[0]},8,["to"])])])):_("",!0)])}}},pn={class:"py-6"},gn={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},vn={class:"mt-4 md:mt-0 flex space-x-3"},fn={class:"relative"},xn=["disabled"],hn={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},yn={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},bn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},kn={class:"relative h-64"},_n={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},wn={class:"relative h-64"},$n={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},Cn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Mn={class:"p-6"},jn={key:0,class:"text-center py-8 text-gray-500"},zn={key:1,class:"space-y-4"},Sn={class:"flex justify-between items-start"},In={class:"flex-1"},Pn={class:"text-sm font-medium text-gray-900 dark:text-white"},An={class:"text-xs text-gray-500 dark:text-gray-400"},Bn={class:"mt-2 flex justify-between items-center"},Vn={class:"text-xs text-gray-500 dark:text-gray-400"},Dn={class:"bg-gray-50 dark:bg-gray-700 px-6 py-3"},Tn={class:"text-sm"},En={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Hn={class:"p-6"},Ln={key:0,class:"text-center py-8 text-gray-500"},Nn={key:1,class:"space-y-4"},Rn={class:"flex-shrink-0"},Un={class:"flex-1 min-w-0"},Fn={class:"text-sm font-medium text-gray-900 dark:text-white"},On={class:"text-xs text-gray-500 dark:text-gray-400"},qn={class:"text-xs text-gray-400 dark:text-gray-500"},Kn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Gn={class:"p-6"},Wn={key:0,class:"text-center py-8 text-gray-500"},Qn={key:1,class:"space-y-4"},Jn={class:"flex justify-between items-start"},Yn={class:"flex-1"},Zn={class:"text-sm font-medium text-gray-900 dark:text-white"},Xn={class:"text-xs text-gray-500 dark:text-gray-400"},ei={class:"text-right"},ti={class:"text-sm font-bold text-gray-900 dark:text-white"},si={class:"text-xs text-gray-500"},oi={class:"mt-2"},ri={class:"w-full bg-gray-200 rounded-full h-2"},ai={class:"text-xs text-gray-500 mt-1"},ni={__name:"Dashboard",setup(n){te.register(...Te),Y();const l=$(!1),d=$("7"),t=$({}),o=$([]),u=$([]),g=$([]),p=$(null),m=$(null);let i=null,r=null;const v=async()=>{try{const k=await fetch("/api/dashboard/stats");if(!k.ok)throw new Error("Failed to fetch stats");const y=await k.json();t.value=y.data}catch(k){console.error("Error fetching dashboard stats:",k),t.value={}}},f=async()=>{try{const k=await fetch(`/api/dashboard/upcoming-tasks?days=${d.value}&limit=5`);if(!k.ok)throw new Error("Failed to fetch upcoming tasks");const y=await k.json();o.value=y.data.tasks}catch(k){console.error("Error fetching upcoming tasks:",k),o.value=[]}},C=async()=>{try{const k=await fetch("/api/dashboard/recent-activities?limit=5");if(!k.ok)throw new Error("Failed to fetch recent activities");const y=await k.json();u.value=y.data.activities}catch(k){console.error("Error fetching recent activities:",k),u.value=[]}},B=async()=>{try{const k=await fetch("/api/dashboard/kpis?limit=3");if(!k.ok)throw new Error("Failed to fetch KPIs");const y=await k.json();g.value=y.data.kpis}catch(k){console.error("Error fetching KPIs:",k),g.value=[]}},L=async()=>{try{const k=await fetch("/api/dashboard/charts/project-status");if(!k.ok)throw new Error("Failed to fetch project chart data");const y=await k.json();w(y.data.chart)}catch(k){console.error("Error fetching project chart:",k)}},A=async()=>{try{const k=await fetch("/api/dashboard/charts/task-status");if(!k.ok)throw new Error("Failed to fetch task chart data");const y=await k.json();V(y.data.chart)}catch(k){console.error("Error fetching task chart:",k)}},w=k=>{if(!p.value)return;const y=p.value.getContext("2d");i&&i.destroy(),i=new te(y,{type:"doughnut",data:{labels:k.labels,datasets:[{data:k.data,backgroundColor:["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6"],borderWidth:2,borderColor:"#ffffff"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}}}}})},V=k=>{if(!m.value)return;const y=m.value.getContext("2d");r&&r.destroy(),r=new te(y,{type:"bar",data:{labels:k.labels,datasets:[{label:"Tasks",data:k.data,backgroundColor:["#60A5FA","#34D399","#FBBF24","#F87171"],borderColor:["#3B82F6","#10B981","#F59E0B","#EF4444"],borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,ticks:{stepSize:1}}}}})},K=async()=>{l.value=!0;try{await Promise.all([v(),f(),C(),B(),L(),A()])}finally{l.value=!1}},S=k=>new Date(k).toLocaleDateString("it-IT"),h=k=>{const y=new Date(k),O=Math.floor((new Date-y)/(1e3*60));return O<60?`${O} minuti fa`:O<1440?`${Math.floor(O/60)} ore fa`:`${Math.floor(O/1440)} giorni fa`},I=k=>{const y={high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return y[k]||y.medium},ce=k=>{const y={todo:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300","in-progress":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",review:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",done:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return y[k]||y.todo},Se=k=>{const y={task:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`},timesheet:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`},event:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`}};return y[k]||y.task},Ie=k=>{const y={task:"bg-blue-100 text-blue-600",timesheet:"bg-green-100 text-green-600",event:"bg-purple-100 text-purple-600"};return y[k]||y.task},Pe=k=>k>=90?"bg-green-500":k>=70?"bg-yellow-500":"bg-red-500";return F(async()=>{await K(),await Ce(),p.value&&m.value&&(await L(),await A())}),(k,y)=>{var O,me,pe,ge,ve,fe,xe,he;const ue=D("router-link");return s(),a("div",pn,[e("div",gn,[y[4]||(y[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Dashboard"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Benvenuto! Ecco una panoramica delle attività della tua azienda. ")],-1)),e("div",vn,[e("div",fn,[N(e("select",{"onUpdate:modelValue":y[0]||(y[0]=P=>d.value=P),onChange:K,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500"},y[1]||(y[1]=[e("option",{value:"7"},"Ultimi 7 giorni",-1),e("option",{value:"30"},"Ultimo mese",-1),e("option",{value:"90"},"Ultimi 3 mesi",-1)]),544),[[se,d.value]])]),e("button",{onClick:K,disabled:l.value,type:"button",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(s(),a("svg",{xmlns:"http://www.w3.org/2000/svg",class:z(["h-4 w-4 mr-2",{"animate-spin":l.value}]),viewBox:"0 0 20 20",fill:"currentColor"},y[2]||(y[2]=[e("path",{"fill-rule":"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z","clip-rule":"evenodd"},null,-1)]),2)),y[3]||(y[3]=j(" Aggiorna "))],8,xn)])]),e("div",hn,[x(X,{title:"Progetti Attivi",value:((O=t.value.projects)==null?void 0:O.active)||0,subtitle:`di ${((me=t.value.projects)==null?void 0:me.total)||0} totali`,icon:"project",color:"primary",link:"/projects?status=active"},null,8,["value","subtitle"]),x(X,{title:"Clienti",value:((pe=t.value.team)==null?void 0:pe.clients)||0,icon:"users",color:"secondary",link:"/crm/clients"},null,8,["value"]),x(X,{title:"Task Pendenti",value:((ge=t.value.tasks)==null?void 0:ge.pending)||0,subtitle:`${((ve=t.value.tasks)==null?void 0:ve.overdue)||0} in ritardo`,icon:"clock",color:((fe=t.value.tasks)==null?void 0:fe.overdue)>0?"red":"yellow",link:"/tasks?status=pending"},null,8,["value","subtitle","color"]),x(X,{title:"Team Members",value:((xe=t.value.team)==null?void 0:xe.users)||0,subtitle:`${((he=t.value.team)==null?void 0:he.departments)||0} dipartimenti`,icon:"team",color:"blue",link:"/personnel"},null,8,["value","subtitle"])]),e("div",yn,[e("div",bn,[y[5]||(y[5]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Progetti")],-1)),e("div",kn,[e("canvas",{ref_key:"projectChart",ref:p},null,512)])]),e("div",_n,[y[6]||(y[6]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Attività")],-1)),e("div",wn,[e("canvas",{ref_key:"taskChart",ref:m},null,512)])])]),e("div",$n,[e("div",Cn,[e("div",Mn,[y[7]||(y[7]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività in Scadenza",-1)),o.value.length===0?(s(),a("div",jn," Nessuna attività in scadenza ")):(s(),a("div",zn,[(s(!0),a(E,null,H(o.value,P=>(s(),a("div",{key:P.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",Sn,[e("div",In,[e("h3",Pn,c(P.name),1),e("p",An,c(P.project_name),1)]),e("span",{class:z(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",I(P.priority)])},c(P.priority),3)]),e("div",Bn,[e("span",Vn," Scadenza: "+c(S(P.due_date)),1),e("span",{class:z(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",ce(P.status)])},c(P.status),3)])]))),128))]))]),e("div",Dn,[e("div",Tn,[x(ue,{to:"/tasks",class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500"},{default:M(()=>y[8]||(y[8]=[j(" Vedi tutte le attività ")])),_:1,__:[8]})])])]),e("div",En,[e("div",Hn,[y[9]||(y[9]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività Recenti",-1)),u.value.length===0?(s(),a("div",Ln," Nessuna attività recente ")):(s(),a("div",Nn,[(s(!0),a(E,null,H(u.value,P=>(s(),a("div",{key:`${P.type}-${P.id}`,class:"flex items-start space-x-3"},[e("div",Rn,[e("div",{class:z(["w-8 h-8 rounded-full flex items-center justify-center",Ie(P.type)])},[(s(),q(Me(Se(P.type)),{class:"w-4 h-4"}))],2)]),e("div",Un,[e("p",Fn,c(P.title),1),e("p",On,c(P.description),1),e("p",qn,c(h(P.timestamp)),1)])]))),128))]))])]),e("div",Kn,[e("div",Gn,[y[10]||(y[10]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"KPIs Principali",-1)),g.value.length===0?(s(),a("div",Wn," Nessun KPI configurato ")):(s(),a("div",Qn,[(s(!0),a(E,null,H(g.value,P=>(s(),a("div",{key:P.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",Jn,[e("div",Yn,[e("h3",Zn,c(P.name),1),e("p",Xn,c(P.description),1)]),e("div",ei,[e("p",ti,c(P.current_value)+c(P.unit),1),e("p",si," Target: "+c(P.target_value)+c(P.unit),1)])]),e("div",oi,[e("div",ri,[e("div",{class:z(["h-2 rounded-full",Pe(P.performance_percentage)]),style:ne({width:Math.min(P.performance_percentage,100)+"%"})},null,6)]),e("p",ai,c(Math.round(P.performance_percentage))+"% del target",1)])]))),128))]))])])])])}}},ii={class:"p-6"},li={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},di={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ci=["value"],ui={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},mi={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},pi={class:"text-lg font-medium text-gray-900 dark:text-white"},gi={key:0,class:"p-6 text-center"},vi={key:1,class:"p-6 text-center"},fi={key:2,class:"divide-y divide-gray-200 dark:divide-gray-700"},xi=["onClick"],hi={class:"flex items-center justify-between"},yi={class:"flex-1"},bi={class:"flex items-center"},ki={class:"text-lg font-medium text-gray-900 dark:text-white"},_i={class:"mt-1 text-sm text-gray-600 dark:text-gray-400"},wi={class:"mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400"},$i={class:"ml-4 flex items-center space-x-2"},Ci={class:"text-right"},Mi={class:"text-sm font-medium text-gray-900 dark:text-white"},ji={class:"w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1"},zi={key:0,class:"fixed inset-0 z-50 overflow-y-auto"},Si={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Ii={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},Pi={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},Ai={__name:"Projects",setup(n){const l=Y(),d=$(!0),t=$([]),o=$([]),u=$(""),g=$({status:"",client:""}),p=[{id:1,name:"Progetto Alpha",description:"Sviluppo piattaforma web per gestione clienti",client:"Acme Corp",client_id:1,status:"active",progress:75,deadline:"2024-03-15",budget:5e4,team_members:[{id:1,full_name:"Mario Rossi",profile_image:null}]},{id:2,name:"Progetto Beta",description:"Migrazione sistema legacy verso cloud",client:"Tech Solutions",client_id:2,status:"planning",progress:25,deadline:"2024-04-20",budget:75e3,team_members:[{id:2,full_name:"Laura Bianchi",profile_image:null}]}],m=[{id:1,name:"Acme Corp"},{id:2,name:"Tech Solutions"}],i=b(()=>{let S=t.value;if(g.value.status&&(S=S.filter(h=>h.status===g.value.status)),g.value.client&&(S=S.filter(h=>h.client_id==g.value.client)),u.value){const h=u.value.toLowerCase();S=S.filter(I=>I.name.toLowerCase().includes(h)||I.description.toLowerCase().includes(h)||I.client.toLowerCase().includes(h))}return S}),r=async()=>{d.value=!0;try{await new Promise(S=>setTimeout(S,500)),t.value=p,o.value=m}catch(S){console.error("Error loading projects:",S)}finally{d.value=!1}},v=()=>{},f=()=>{},C=()=>{g.value={status:"",client:""},u.value=""},B=()=>{l.push("/app/projects/create")},L=S=>{l.push(`/app/projects/${S}`)},A=S=>({planning:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",active:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",completed:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400","on-hold":"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"})[S]||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",w=S=>({planning:"Pianificazione",active:"Attivo",completed:"Completato","on-hold":"In Pausa"})[S]||S,V=S=>new Date(S).toLocaleDateString("it-IT"),K=S=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(S);return F(()=>{r()}),(S,h)=>(s(),a("div",ii,[h[18]||(h[18]=e("h1",{class:"text-3xl font-bold text-red-500"},"TEST PROGETTI - SE VEDI QUESTO FUNZIONA!",-1)),h[19]||(h[19]=e("p",{class:"text-lg text-blue-500"},"Componente Projects.vue caricato correttamente",-1)),e("div",{class:"mb-6 mt-6"},[e("div",{class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},[h[6]||(h[6]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Progetti"),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci e monitora tutti i progetti aziendali ")],-1)),e("div",{class:"mt-4 sm:mt-0"},[e("button",{onClick:B,class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},h[5]||(h[5]=[e("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1),j(" Nuovo Progetto ")]))])])]),e("div",li,[e("div",di,[e("div",null,[h[8]||(h[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Stato",-1)),N(e("select",{"onUpdate:modelValue":h[0]||(h[0]=I=>g.value.status=I),onChange:f,class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},h[7]||(h[7]=[Ee('<option value="">Tutti gli stati</option><option value="planning">Pianificazione</option><option value="active">Attivo</option><option value="completed">Completato</option><option value="on-hold">In Pausa</option>',5)]),544),[[se,g.value.status]])]),e("div",null,[h[10]||(h[10]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Cliente",-1)),N(e("select",{"onUpdate:modelValue":h[1]||(h[1]=I=>g.value.client=I),onChange:f,class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},[h[9]||(h[9]=e("option",{value:""},"Tutti i clienti",-1)),(s(!0),a(E,null,H(o.value,I=>(s(),a("option",{key:I.id,value:I.id},c(I.name),9,ci))),128))],544),[[se,g.value.client]])]),e("div",null,[h[11]||(h[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Ricerca",-1)),N(e("input",{"onUpdate:modelValue":h[2]||(h[2]=I=>u.value=I),onInput:v,type:"text",placeholder:"Cerca progetti...",class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,544),[[G,u.value]])]),e("div",{class:"flex items-end"},[e("button",{onClick:C,class:"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}," Reset Filtri ")])])]),e("div",ui,[e("div",mi,[e("h3",pi," Progetti ("+c(i.value.length)+") ",1)]),d.value?(s(),a("div",gi,h[12]||(h[12]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-600 dark:text-gray-400"},"Caricamento progetti...",-1)]))):i.value.length===0?(s(),a("div",vi,h[13]||(h[13]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun progetto",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Inizia creando il tuo primo progetto.",-1)]))):(s(),a("div",fi,[(s(!0),a(E,null,H(i.value,I=>(s(),a("div",{key:I.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",onClick:ce=>L(I.id)},[e("div",hi,[e("div",yi,[e("div",bi,[e("h4",ki,c(I.name),1),e("span",{class:z([A(I.status),"ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},c(w(I.status)),3)]),e("p",_i,c(I.description),1),e("div",wi,[e("span",null,"Cliente: "+c(I.client),1),h[14]||(h[14]=e("span",{class:"mx-2"},"•",-1)),e("span",null,"Scadenza: "+c(V(I.deadline)),1),h[15]||(h[15]=e("span",{class:"mx-2"},"•",-1)),e("span",null,"Budget: "+c(K(I.budget)),1)])]),e("div",$i,[e("div",Ci,[e("div",Mi,c(I.progress)+"% ",1),e("div",ji,[e("div",{class:"bg-primary-600 h-2 rounded-full",style:ne({width:I.progress+"%"})},null,4)])]),h[16]||(h[16]=e("svg",{class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1))])])],8,xi))),128))]))]),S.showCreateModal?(s(),a("div",zi,[e("div",Si,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:h[3]||(h[3]=I=>S.showCreateModal=!1)}),e("div",Ii,[h[17]||(h[17]=e("div",{class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Nuovo Progetto"),e("p",{class:"text-gray-600 dark:text-gray-400"},"Funzionalità in fase di sviluppo...")],-1)),e("div",Pi,[e("button",{onClick:h[4]||(h[4]=I=>S.showCreateModal=!1),class:"w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 sm:ml-3 sm:w-auto sm:text-sm"}," Chiudi ")])])])])):_("",!0)]))}},Bi={};function Vi(n,l){return s(),a("div",null,l[0]||(l[0]=[e("h1",{class:"text-2xl font-bold text-gray-900 mb-6"},"Personale",-1),e("div",{class:"card"},[e("p",{class:"text-gray-600"},"Gestione personale in fase di migrazione...")],-1)]))}const Di=le(Bi,[["render",Vi]]),Ti=[{path:"/",component:we,children:[{path:"",name:"home",component:br},{path:"about",name:"about",component:Qr},{path:"contact",name:"contact",component:Ca},{path:"services",name:"services",component:Ra}]},{path:"/auth",component:we,children:[{path:"login",name:"login",component:Za},{path:"register",name:"register",component:sn}]},{path:"/app",component:ho,meta:{requiresAuth:!0},children:[{path:"",redirect:"/app/dashboard"},{path:"dashboard",name:"dashboard",component:ni},{path:"projects",name:"projects",component:Ai},{path:"projects/:id",name:"project-view",component:()=>Ge(()=>import("./ProjectView.js"),__vite__mapDeps([0,1,2]))},{path:"personnel",name:"personnel",component:Di}]}],ze=He({history:Le(),routes:Ti});ze.beforeEach(async(n,l,d)=>{const t=W();if(n.meta.requiresAuth&&(t.sessionChecked||await t.initializeAuth(),!t.isAuthenticated)){d("/auth/login");return}d()});const de=Ne(Oe),Ei=Re();de.use(Ei);de.use(ze);const Hi=W();Hi.initializeAuth().then(()=>{de.mount("#app")});export{le as _,J as a,W as u};
