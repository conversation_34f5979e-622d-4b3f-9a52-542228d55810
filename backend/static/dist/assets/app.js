import{c as n,a as h,r as B,o as r,b as je,d as ge,e as w,f as _,g as b,n as j,h as E,w as $,t as u,u as ee,i as e,F as V,j as H,k as C,l as G,m as X,p as he,q as te,s as D,v as O,x as W,y as fe,z as L,T as Me,A as ze,B as R,C as Se,D as xe,E as Q,G as Ie,H as Ae,I as Be,J as Ve,K as He,L as Pe}from"./vendor.js";(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))t(s);new MutationObserver(s=>{for(const c of s)if(c.type==="childList")for(const g of c.addedNodes)g.tagName==="LINK"&&g.rel==="modulepreload"&&t(g)}).observe(document,{childList:!0,subtree:!0});function d(s){const c={};return s.integrity&&(c.integrity=s.integrity),s.referrerPolicy&&(c.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?c.credentials="include":s.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function t(s){if(s.ep)return;s.ep=!0;const c=d(s);fetch(s.href,c)}})();const Te={id:"app"},Ne={__name:"App",setup(i){return(l,d)=>{const t=B("router-view");return r(),n("div",Te,[h(t)])}}},q=je.create({baseURL:"",timeout:1e4,withCredentials:!0,headers:{"Content-Type":"application/json"}});q.interceptors.request.use(i=>{var d,t;const l=(d=document.querySelector('meta[name="csrf-token"]'))==null?void 0:d.getAttribute("content");return l&&["post","put","patch","delete"].includes((t=i.method)==null?void 0:t.toLowerCase())&&(i.headers["X-CSRFToken"]=l),i},i=>Promise.reject(i));q.interceptors.response.use(i=>i,i=>{var l;return((l=i.response)==null?void 0:l.status)===401&&(localStorage.removeItem("user"),console.warn("Sessione scaduta, autenticazione richiesta")),Promise.reject(i)});const F=ge("auth",()=>{const i=localStorage.getItem("user"),l=w(i?JSON.parse(i):null),d=w(!1),t=w(null),s=w(!1),c=_(()=>!!l.value&&s.value);async function g(p){var f,z;d.value=!0,t.value=null;try{const I=await q.post("/api/auth/login",p);return I.data.success?(l.value=I.data.data.user,localStorage.setItem("user",JSON.stringify(l.value)),s.value=!0,{success:!0}):(t.value=I.data.message||"Errore durante il login",{success:!1,error:t.value})}catch(I){return t.value=((z=(f=I.response)==null?void 0:f.data)==null?void 0:z.message)||"Errore di connessione",{success:!1,error:t.value}}finally{d.value=!1}}async function v(p){var f,z;d.value=!0,t.value=null;try{const I=await q.post("/api/auth/register",p);return I.data.success?{success:!0,message:I.data.message}:(t.value=I.data.message||"Errore durante la registrazione",{success:!1,error:t.value})}catch(I){return t.value=((z=(f=I.response)==null?void 0:f.data)==null?void 0:z.message)||"Errore di connessione",{success:!1,error:t.value}}finally{d.value=!1}}async function m(){try{await q.post("/api/auth/logout")}catch(p){console.warn("Errore durante il logout:",p)}finally{l.value=null,s.value=!1,localStorage.removeItem("user")}}async function a(){if(s.value)return c.value;try{const p=await q.get("/api/auth/me");return p.data.success?(l.value=p.data.data.user,localStorage.setItem("user",JSON.stringify(l.value)),s.value=!0,!0):(await m(),!1)}catch{return await m(),!1}}async function o(){return l.value?await a():(s.value=!0,!1)}return{user:l,loading:d,error:t,sessionChecked:s,isAuthenticated:c,login:g,register:v,logout:m,checkAuth:a,initializeAuth:o}}),U=ge("tenant",()=>{const i=w(null),l=w(!1),d=w(null),t=_(()=>{var o;return((o=i.value)==null?void 0:o.company)||{}}),s=_(()=>{var o;return((o=i.value)==null?void 0:o.contact)||{}}),c=_(()=>{var o;return((o=i.value)==null?void 0:o.pages)||{}}),g=_(()=>{var o;return((o=i.value)==null?void 0:o.navigation)||{}}),v=_(()=>{var o;return((o=i.value)==null?void 0:o.footer)||{}});async function m(){try{if(l.value=!0,window.TENANT_CONFIG){i.value=window.TENANT_CONFIG;return}const o=await fetch("/api/config/tenant");i.value=await o.json()}catch(o){d.value="Errore nel caricamento della configurazione",console.error("Errore caricamento tenant config:",o)}finally{l.value=!1}}function a(o,p={}){if(!o||typeof o!="string")return o;let f=o;const z={"company.name":t.value.name||"DatVinci","company.tagline":t.value.tagline||"","company.description":t.value.description||"","company.mission":t.value.mission||"","company.vision":t.value.vision||"","company.founded":t.value.founded||"","company.team_size":t.value.team_size||"","contact.email":s.value.email||"","contact.phone":s.value.phone||"","contact.address":s.value.address||"",current_year:new Date().getFullYear().toString(),...p};for(const[I,P]of Object.entries(z)){const S=new RegExp(`\\{${I}\\}`,"g");f=f.replace(S,P||"")}return f}return{config:i,loading:l,error:d,company:t,contact:s,pages:c,navigation:g,footer:v,loadConfig:m,interpolateText:a}}),De={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"},Ee={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},Le={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},Re={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},Fe={key:4,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"},Ue={key:5,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},qe={key:6,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"},Oe={key:7,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},Ke={key:8,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},Ge={key:9,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"},We={key:10,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},Je={key:11,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},Ye={key:12,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"},Z={__name:"SidebarIcon",props:{icon:{type:String,required:!0},className:{type:String,default:"h-5 w-5"}},setup(i){return(l,d)=>(r(),n("svg",{class:j(i.className),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[i.icon==="dashboard"?(r(),n("path",De)):i.icon==="projects"?(r(),n("path",Ee)):i.icon==="users"?(r(),n("path",Le)):i.icon==="clients"?(r(),n("path",Re)):i.icon==="products"?(r(),n("path",Fe)):i.icon==="reports"?(r(),n("path",Ue)):i.icon==="settings"?(r(),n("path",qe)):b("",!0),i.icon==="settings"?(r(),n("path",Oe)):i.icon==="user-management"?(r(),n("path",Ke)):i.icon==="communications"?(r(),n("path",Ge)):i.icon==="funding"?(r(),n("path",We)):i.icon==="reporting"?(r(),n("path",Je)):(r(),n("path",Ye))],2))}},Qe={key:0,class:"truncate"},Xe={key:0,class:"truncate"},T={__name:"SidebarNavItem",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(i){const l=_(()=>["text-gray-700 hover:bg-gray-50 hover:text-primary-600"]);return(d,t)=>{const s=B("router-link");return r(),n("div",null,[i.item.path!=="#"?(r(),E(s,{key:0,to:i.item.path,class:j(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[l.value,{"justify-center":i.isCollapsed}]]),"active-class":"text-primary-600 bg-primary-50 border-r-2 border-primary-600",onClick:t[0]||(t[0]=c=>d.$emit("click"))},{default:$(()=>[h(Z,{icon:i.item.icon,class:j(["flex-shrink-0 h-6 w-6",{"mr-0":i.isCollapsed,"mr-3":!i.isCollapsed}])},null,8,["icon","class"]),i.isCollapsed?b("",!0):(r(),n("span",Qe,u(i.item.name),1))]),_:1},8,["to","class"])):(r(),n("div",{key:1,class:j(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 cursor-not-allowed opacity-75",["text-gray-400 hover:text-gray-500",{"justify-center":i.isCollapsed}]])},[h(Z,{icon:i.item.icon,class:j(["flex-shrink-0 h-6 w-6",{"mr-0":i.isCollapsed,"mr-3":!i.isCollapsed}])},null,8,["icon","class"]),i.isCollapsed?b("",!0):(r(),n("span",Xe,u(i.item.name),1))],2))])}}},Ze={key:0,class:"flex-1 text-left truncate"},et={key:0,class:"ml-6 space-y-1 mt-1"},tt={__name:"SidebarNavItemCollapsible",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(i){const l=i,d=ee(),t=F(),s=w(!1),c=_(()=>["text-gray-700 hover:bg-gray-50 hover:text-primary-600",{"text-primary-600 bg-primary-50":g.value}]),g=_(()=>l.item.children?l.item.children.some(o=>o.path!=="#"&&d.path.startsWith(o.path)):!1),v=_(()=>l.item.children?l.item.children.filter(o=>{var p;return o.admin?((p=t.user)==null?void 0:p.role)==="admin":!0}):[]);g.value&&(s.value=!0);function m(){l.isCollapsed||(s.value=!s.value)}function a(o){if(o.path==="#")return!1}return(o,p)=>{const f=B("router-link");return r(),n("div",null,[e("button",{onClick:m,class:j(["group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[c.value,{"justify-center":i.isCollapsed}]])},[h(Z,{icon:i.item.icon,class:j(["flex-shrink-0 h-6 w-6",{"mr-0":i.isCollapsed,"mr-3":!i.isCollapsed}])},null,8,["icon","class"]),i.isCollapsed?b("",!0):(r(),n("span",Ze,u(i.item.name),1)),i.isCollapsed?b("",!0):(r(),n("svg",{key:1,class:j([{"rotate-90":s.value},"ml-2 h-4 w-4 transition-transform duration-150"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},p[0]||(p[0]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"},null,-1)]),2))],2),s.value&&!i.isCollapsed?(r(),n("div",et,[(r(!0),n(V,null,H(v.value,z=>(r(),E(f,{key:z.name,to:z.path,class:j(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",z.path==="#"?"text-gray-400 hover:text-gray-500 cursor-not-allowed opacity-75":"text-gray-600 hover:bg-gray-50 hover:text-primary-600"]),"active-class":"text-primary-600 bg-primary-50",onClick:I=>a(z)},{default:$(()=>[C(u(z.name),1)]),_:2},1032,["to","class","onClick"]))),128))])):b("",!0)])}}},st={class:"mt-5 flex-grow flex flex-col overflow-hidden"},ot={class:"flex-1 px-2 space-y-1"},me={__name:"SidebarNavigation",props:{isCollapsed:{type:Boolean,default:!1}},emits:["item-click"],setup(i){const l=F(),d=_(()=>{var t;return((t=l.user)==null?void 0:t.role)==="admin"});return(t,s)=>(r(),n("div",st,[e("nav",ot,[h(T,{item:{name:"Dashboard",path:"/app/dashboard",icon:"dashboard"},"is-collapsed":i.isCollapsed,onClick:s[0]||(s[0]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(tt,{item:{name:"Personale",icon:"users",children:[{name:"👥 Team",path:"/app/personnel"},{name:"📖 Directory",path:"/app/personnel/directory"},{name:"🏢 Organigramma",path:"/app/personnel/orgchart"},{name:"🎯 Competenze",path:"/app/personnel/skills"},{name:"🏢 Dipartimenti",path:"#",admin:!0},{name:"⚙️ Amministrazione",path:"#",admin:!0}]},"is-collapsed":i.isCollapsed,onClick:s[1]||(s[1]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"Progetti",path:"/app/projects",icon:"projects"},"is-collapsed":i.isCollapsed,onClick:s[2]||(s[2]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"CRM",path:"#",icon:"clients"},"is-collapsed":i.isCollapsed,onClick:s[3]||(s[3]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"Prodotti",path:"#",icon:"products"},"is-collapsed":i.isCollapsed,onClick:s[4]||(s[4]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"Performance",path:"#",icon:"reports"},"is-collapsed":i.isCollapsed,onClick:s[5]||(s[5]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"Comunicazione",path:"#",icon:"communications"},"is-collapsed":i.isCollapsed,onClick:s[6]||(s[6]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"Finanziamenti",path:"#",icon:"funding"},"is-collapsed":i.isCollapsed,onClick:s[7]||(s[7]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"Rendicontazione",path:"#",icon:"reporting"},"is-collapsed":i.isCollapsed,onClick:s[8]||(s[8]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),d.value?(r(),E(T,{key:0,item:{name:"Amministrazione",path:"#",icon:"settings"},"is-collapsed":i.isCollapsed,onClick:s[9]||(s[9]=c=>t.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0)])]))}},rt={class:"flex-shrink-0 border-t border-gray-200 p-4"},at={class:"flex-shrink-0"},nt={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},it={class:"text-sm font-medium text-primary-700"},lt={key:0,class:"ml-3 flex-1 min-w-0"},dt={class:"text-sm font-medium text-gray-900 truncate"},ct={class:"text-xs text-gray-500 truncate"},ut={class:"py-1"},mt={key:0,class:"mt-3 text-xs text-gray-400 text-center"},pe={__name:"SidebarFooter",props:{isCollapsed:{type:Boolean,default:!1}},setup(i){const l=G(),d=F(),t=w(!1),s=_(()=>d.user&&(d.user.name||d.user.username)||"Utente"),c=_(()=>d.user?s.value.charAt(0).toUpperCase():"U"),g=_(()=>d.user?{admin:"Amministratore",manager:"Manager",employee:"Dipendente",client:"Cliente"}[d.user.role]||d.user.role:""),v=_(()=>"1.0.0");async function m(){t.value=!1,await d.logout(),l.push("/auth/login")}return(a,o)=>{const p=B("router-link");return r(),n("div",rt,[e("div",{class:j(["flex items-center",{"justify-center":i.isCollapsed}])},[e("div",at,[e("div",nt,[e("span",it,u(c.value),1)])]),i.isCollapsed?b("",!0):(r(),n("div",lt,[e("p",dt,u(s.value),1),e("p",ct,u(g.value),1)])),e("div",{class:j(["relative",{"ml-3":!i.isCollapsed}])},[e("button",{onClick:o[0]||(o[0]=f=>t.value=!t.value),class:"p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"},o[4]||(o[4]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zM13 12a1 1 0 11-2 0 1 1 0 012 0zM20 12a1 1 0 11-2 0 1 1 0 012 0z"})],-1)])),t.value?(r(),n("div",{key:0,onClick:o[3]||(o[3]=f=>t.value=!1),class:"origin-bottom-left absolute bottom-full left-0 mb-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",ut,[h(p,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:o[1]||(o[1]=f=>t.value=!1)},{default:$(()=>o[5]||(o[5]=[C(" Il tuo profilo ")])),_:1,__:[5]}),h(p,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:o[2]||(o[2]=f=>t.value=!1)},{default:$(()=>o[6]||(o[6]=[C(" Impostazioni ")])),_:1,__:[6]}),o[7]||(o[7]=e("hr",{class:"my-1"},null,-1)),e("button",{onClick:m,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Esci ")])])):b("",!0)],2)],2),v.value&&!i.isCollapsed?(r(),n("div",mt," v"+u(v.value),1)):b("",!0)])}}},pt={class:"flex"},vt={class:"hidden lg:flex lg:flex-shrink-0 lg:fixed lg:inset-y-0 z-10"},gt={class:"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white border-r border-gray-200 shadow-sm"},ht={class:"flex items-center flex-shrink-0 px-4"},ft={class:"w-10 h-10 bg-primary-600 rounded flex items-center justify-center mr-3"},xt={class:"text-white font-bold text-lg"},yt={class:"text-xl font-semibold text-gray-900"},_t={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},bt={class:"text-white font-bold text-sm"},kt={class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},wt=["d"],$t={class:"flex flex-col h-full pt-5 pb-4 overflow-y-auto bg-white border-r border-gray-200 shadow-sm"},Ct={class:"flex items-center justify-between px-4 mb-4"},jt={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center mr-3"},Mt={class:"text-white font-bold text-sm"},zt={class:"text-xl font-semibold text-gray-900"},St={__name:"AppSidebar",props:{isMobileOpen:{type:Boolean,default:!1}},emits:["close"],setup(i){const l=U(),d=w(!1),t=_(()=>l.config||{}),s=_(()=>{var m;return((m=t.value.company)==null?void 0:m.name)||"DatPortal"}),c=_(()=>s.value.split(" ").map(a=>a[0]).join("").toUpperCase().slice(0,2));function g(){d.value=!d.value}function v(){d.value&&(d.value=!1)}return(m,a)=>{const o=B("router-link");return r(),n("div",pt,[e("div",vt,[e("div",{class:j(["flex flex-col transition-all duration-300",[d.value?"w-20":"w-64"]])},[e("div",gt,[e("div",ht,[e("div",{class:j(["flex items-center",{"justify-center":d.value}])},[h(o,{to:"/app/dashboard",class:j(["flex items-center",{hidden:d.value}])},{default:$(()=>[e("div",ft,[e("span",xt,u(c.value),1)]),e("h3",yt,u(s.value),1)]),_:1},8,["class"]),h(o,{to:"/app/dashboard",class:j(["flex items-center justify-center",{hidden:!d.value}])},{default:$(()=>[e("div",_t,[e("span",bt,u(c.value),1)])]),_:1},8,["class"])],2),e("button",{onClick:g,class:"ml-auto text-gray-600 focus:outline-none hover:bg-gray-100 p-1 rounded"},[(r(),n("svg",kt,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:d.value?"M13 5l7 7-7 7M5 5l7 7-7 7":"M11 19l-7-7 7-7m8 14l-7-7 7-7"},null,8,wt)]))])]),h(me,{"is-collapsed":d.value,onItemClick:v},null,8,["is-collapsed"]),h(pe,{"is-collapsed":d.value},null,8,["is-collapsed"])])],2)]),e("div",{class:j(["fixed inset-y-0 left-0 z-30 w-64 bg-primary-700 transform transition-transform duration-300 ease-in-out lg:hidden",i.isMobileOpen?"translate-x-0":"-translate-x-full"])},[e("div",$t,[e("div",Ct,[h(o,{to:"/app/dashboard",class:"flex items-center"},{default:$(()=>[e("div",jt,[e("span",Mt,u(c.value),1)]),e("h3",zt,u(s.value),1)]),_:1}),e("button",{onClick:a[0]||(a[0]=p=>m.$emit("close")),class:"p-2 rounded-md text-gray-600 hover:bg-gray-100"},a[2]||(a[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),h(me,{"is-collapsed":!1,onItemClick:a[1]||(a[1]=p=>m.$emit("close"))}),h(pe,{"is-collapsed":!1})])],2)])}}},It={class:"flex","aria-label":"Breadcrumb"},At={class:"flex items-center space-x-2 text-sm text-gray-500"},Bt={key:0,class:"mr-2"},Vt={class:"flex items-center"},Ht={key:0,class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Pt=["d"],Tt={key:2,class:"font-medium text-gray-900"},Nt={__name:"HeaderBreadcrumbs",props:{breadcrumbs:{type:Array,required:!0}},setup(i){return(l,d)=>{const t=B("router-link");return r(),n("nav",It,[e("ol",At,[(r(!0),n(V,null,H(i.breadcrumbs,(s,c)=>(r(),n("li",{key:c,class:"flex items-center"},[c>0?(r(),n("div",Bt,d[0]||(d[0]=[e("svg",{class:"h-3 w-3 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]))):b("",!0),s.to&&c<i.breadcrumbs.length-1?(r(),E(t,{key:1,to:s.to,class:"hover:text-gray-700 transition-colors duration-150"},{default:$(()=>[e("span",Vt,[s.icon?(r(),n("svg",Ht,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:s.icon},null,8,Pt)])):b("",!0),C(" "+u(s.label),1)])]),_:2},1032,["to"])):(r(),n("span",Tt,u(s.label),1))]))),128))])])}}},Dt={class:"flex items-center space-x-2"},Et={__name:"HeaderQuickActions",emits:["quick-create-project","quick-add-task","quick-search"],setup(i){const l=ee(),d=_(()=>{var s;return((s=l.name)==null?void 0:s.includes("projects"))||l.path.includes("/projects")}),t=_(()=>{var s,c;return((s=l.name)==null?void 0:s.includes("tasks"))||((c=l.name)==null?void 0:c.includes("projects"))||l.path.includes("/tasks")||l.path.includes("/projects")});return(s,c)=>(r(),n("div",Dt,[d.value?(r(),n("button",{key:0,onClick:c[0]||(c[0]=g=>s.$emit("quick-create-project")),class:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},c[3]||(c[3]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),C(" Nuovo Progetto ")]))):b("",!0),t.value?(r(),n("button",{key:1,onClick:c[1]||(c[1]=g=>s.$emit("quick-add-task")),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},c[4]||(c[4]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),C(" Nuovo Task ")]))):b("",!0),e("button",{onClick:c[2]||(c[2]=g=>s.$emit("quick-search")),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",title:"Ricerca rapida"},c[5]||(c[5]=[e("svg",{class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)]))]))}},Lt={class:"relative"},Rt={class:"relative"},Ft={key:0,class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center"},Ut={class:"py-1"},qt={key:0,class:"px-4 py-8 text-center text-gray-500 text-sm"},Ot={key:1,class:"max-h-64 overflow-y-auto"},Kt=["onClick"],Gt={class:"flex items-start"},Wt={class:"flex-shrink-0"},Jt={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Yt=["d"],Qt={class:"ml-3 flex-1"},Xt={class:"text-sm font-medium text-gray-900"},Zt={class:"text-xs text-gray-500 mt-1"},es={class:"text-xs text-gray-400 mt-1"},ts={key:0,class:"flex-shrink-0"},ss={key:2,class:"px-4 py-2 border-t border-gray-100"},os={__name:"HeaderNotifications",setup(i){const l=w(!1),d=w([{id:1,type:"task",title:"Nuovo task assegnato",message:'Ti è stato assegnato un nuovo task nel progetto "Website Redesign"',created_at:new Date().toISOString(),read:!1},{id:2,type:"project",title:"Progetto completato",message:'Il progetto "Mobile App" è stato completato con successo',created_at:new Date(Date.now()-36e5).toISOString(),read:!0}]),t=_(()=>d.value.filter(a=>!a.read).length);function s(a){const o={task:"h-6 w-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center",project:"h-6 w-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center",user:"h-6 w-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center",system:"h-6 w-6 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center"};return o[a]||o.system}function c(a){const o={task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2",project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",user:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",system:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return o[a]||o.system}function g(a){const o=new Date(a),f=new Date-o;return f<6e4?"Adesso":f<36e5?`${Math.floor(f/6e4)}m fa`:f<864e5?`${Math.floor(f/36e5)}h fa`:o.toLocaleDateString("it-IT")}function v(a){a.read||(a.read=!0),l.value=!1}function m(){d.value.forEach(a=>a.read=!0)}return(a,o)=>(r(),n("div",Lt,[e("button",{onClick:o[0]||(o[0]=p=>l.value=!l.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[o[3]||(o[3]=e("span",{class:"sr-only"},"Visualizza notifiche",-1)),e("div",Rt,[o[2]||(o[2]=e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM10 21a2 2 0 01-2-2V7a7 7 0 1114 0v12a2 2 0 01-2 2H10z"})],-1)),t.value>0?(r(),n("span",Ft,u(t.value>9?"9+":t.value),1)):b("",!0)])]),l.value?(r(),n("div",{key:0,onClick:o[1]||(o[1]=p=>l.value=!1),class:"origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",Ut,[o[5]||(o[5]=e("div",{class:"px-4 py-2 border-b border-gray-100"},[e("h3",{class:"text-sm font-medium text-gray-900"},"Notifiche")],-1)),d.value.length===0?(r(),n("div",qt," Nessuna notifica ")):(r(),n("div",Ot,[(r(!0),n(V,null,H(d.value,p=>(r(),n("div",{key:p.id,class:"px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-50 last:border-b-0",onClick:f=>v(p)},[e("div",Gt,[e("div",Wt,[e("div",{class:j(s(p.type))},[(r(),n("svg",Jt,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:c(p.type)},null,8,Yt)]))],2)]),e("div",Qt,[e("p",Xt,u(p.title),1),e("p",Zt,u(p.message),1),e("p",es,u(g(p.created_at)),1)]),p.read?b("",!0):(r(),n("div",ts,o[4]||(o[4]=[e("div",{class:"h-2 w-2 bg-primary-500 rounded-full"},null,-1)])))])],8,Kt))),128))])),d.value.length>0?(r(),n("div",ss,[e("button",{onClick:m,class:"text-xs text-primary-600 hover:text-primary-800"}," Segna tutte come lette ")])):b("",!0)])])):b("",!0)]))}},rs={class:"relative"},as={class:"flex items-start justify-center min-h-screen pt-16 px-4 pb-20 text-center sm:block sm:p-0"},ns={class:"inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"},is={class:"flex items-center"},ls={class:"flex-1"},ds={key:0,class:"mt-4 max-h-64 overflow-y-auto"},cs={class:"space-y-1"},us=["onClick"],ms={class:"flex-shrink-0"},ps={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},vs=["d"],gs={class:"ml-3 flex-1 min-w-0"},hs={class:"text-sm font-medium text-gray-900 truncate"},fs={class:"text-xs text-gray-500 truncate"},xs={class:"ml-2 text-xs text-gray-400"},ys={key:1,class:"mt-4 text-center py-4"},_s={key:2,class:"mt-4 text-center py-4"},bs={__name:"HeaderSearch",setup(i){const l=G(),d=w(!1),t=w(""),s=w([]),c=w(-1),g=w(!1),v=w(null),m=[{id:1,type:"project",title:"Website Redesign",description:"Progetto di redesign del sito web aziendale",path:"/app/projects/1"},{id:2,type:"person",title:"Mario Rossi",description:"Senior Developer",path:"/app/personnel/directory/1"},{id:3,type:"document",title:"Specifiche Tecniche",description:"Documento delle specifiche del progetto mobile",path:"/app/documents/1"},{id:4,type:"task",title:"Implementazione API",description:"Task per l'implementazione delle API REST",path:"/app/projects/1/tasks/5"}];X(d,async S=>{var k;S?(await he(),(k=v.value)==null||k.focus()):(t.value="",s.value=[],c.value=-1)});function a(){if(!t.value.trim()){s.value=[];return}g.value=!0,setTimeout(()=>{s.value=m.filter(S=>S.title.toLowerCase().includes(t.value.toLowerCase())||S.description.toLowerCase().includes(t.value.toLowerCase())),c.value=-1,g.value=!1},200)}function o(S){if(s.value.length===0)return;const k=c.value+S;k>=0&&k<s.value.length&&(c.value=k)}function p(){c.value>=0&&s.value[c.value]&&f(s.value[c.value])}function f(S){d.value=!1,l.push(S.path)}function z(S){const k={project:"h-6 w-6 rounded bg-blue-100 text-blue-600 flex items-center justify-center",person:"h-6 w-6 rounded bg-green-100 text-green-600 flex items-center justify-center",document:"h-6 w-6 rounded bg-yellow-100 text-yellow-600 flex items-center justify-center",task:"h-6 w-6 rounded bg-purple-100 text-purple-600 flex items-center justify-center"};return k[S]||k.document}function I(S){const k={project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",person:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",document:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"};return k[S]||k.document}function P(S){return{project:"Progetto",person:"Persona",document:"Documento",task:"Task"}[S]||"Elemento"}return(S,k)=>(r(),n("div",rs,[e("button",{onClick:k[0]||(k[0]=A=>d.value=!d.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},k[7]||(k[7]=[e("span",{class:"sr-only"},"Cerca",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),d.value?(r(),n("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:k[6]||(k[6]=te(A=>d.value=!1,["self"]))},[e("div",as,[k[11]||(k[11]=e("div",{class:"fixed inset-0 bg-black bg-opacity-25 transition-opacity"},null,-1)),e("div",ns,[e("div",null,[e("div",is,[e("div",ls,[D(e("input",{ref_key:"searchInput",ref:v,"onUpdate:modelValue":k[1]||(k[1]=A=>t.value=A),onInput:a,onKeydown:[k[2]||(k[2]=W(A=>d.value=!1,["escape"])),W(p,["enter"]),k[3]||(k[3]=W(A=>o(-1),["up"])),k[4]||(k[4]=W(A=>o(1),["down"]))],type:"text",placeholder:"Cerca progetti, persone, documenti...",class:"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"},null,544),[[O,t.value]])]),e("button",{onClick:k[5]||(k[5]=A=>d.value=!1),class:"ml-3 p-2 text-gray-400 hover:text-gray-600"},k[8]||(k[8]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),s.value.length>0?(r(),n("div",ds,[e("div",cs,[(r(!0),n(V,null,H(s.value,(A,K)=>(r(),n("div",{key:A.id,onClick:oe=>f(A),class:j(["flex items-center px-3 py-2 rounded-md cursor-pointer",K===c.value?"bg-primary-50":"hover:bg-gray-50"])},[e("div",ms,[e("div",{class:j(z(A.type))},[(r(),n("svg",ps,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:I(A.type)},null,8,vs)]))],2)]),e("div",gs,[e("p",hs,u(A.title),1),e("p",fs,u(A.description),1)]),e("div",xs,u(P(A.type)),1)],10,us))),128))])])):t.value&&!g.value?(r(),n("div",ys,k[9]||(k[9]=[e("p",{class:"text-sm text-gray-500"},"Nessun risultato trovato",-1)]))):t.value?b("",!0):(r(),n("div",_s,k[10]||(k[10]=[e("p",{class:"text-xs text-gray-400"},"Inizia a digitare per cercare...",-1)])))])])])])):b("",!0)]))}},ks={class:"relative"},ws={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},$s={class:"text-sm font-medium text-primary-700"},Cs={class:"py-1"},js={class:"px-4 py-2 border-b border-gray-100"},Ms={class:"text-sm font-medium text-gray-900"},zs={class:"text-xs text-gray-500"},Ss={__name:"HeaderUserMenu",setup(i){const l=G(),d=F(),t=w(!1),s=_(()=>d.user&&(d.user.name||d.user.username)||"Utente"),c=_(()=>{var m;return((m=d.user)==null?void 0:m.email)||""}),g=_(()=>d.user?s.value.charAt(0).toUpperCase():"U");async function v(){t.value=!1,await d.logout(),l.push("/auth/login")}return(m,a)=>{const o=B("router-link");return r(),n("div",ks,[e("button",{onClick:a[0]||(a[0]=p=>t.value=!t.value),class:"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[a[4]||(a[4]=e("span",{class:"sr-only"},"Apri menu utente",-1)),e("div",ws,[e("span",$s,u(g.value),1)])]),t.value?(r(),n("div",{key:0,onClick:a[3]||(a[3]=p=>t.value=!1),class:"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",Cs,[e("div",js,[e("p",Ms,u(s.value),1),e("p",zs,u(c.value),1)]),h(o,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:a[1]||(a[1]=p=>t.value=!1)},{default:$(()=>a[5]||(a[5]=[C(" Il tuo profilo ")])),_:1,__:[5]}),h(o,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:a[2]||(a[2]=p=>t.value=!1)},{default:$(()=>a[6]||(a[6]=[C(" Impostazioni ")])),_:1,__:[6]}),a[7]||(a[7]=e("div",{class:"border-t border-gray-100 my-1"},null,-1)),e("button",{onClick:v,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Esci ")])])):b("",!0)])}}},Is={class:"bg-white shadow-sm border-b border-gray-200"},As={class:"flex justify-between items-center px-4 py-4 sm:px-6 lg:px-8"},Bs={class:"flex items-center space-x-4"},Vs={class:"flex flex-col"},Hs={class:"text-lg font-semibold text-gray-900"},Ps={class:"flex items-center space-x-4"},Ts={class:"hidden md:flex items-center space-x-2"},Ns={__name:"AppHeader",props:{pageTitle:{type:String,required:!0},breadcrumbs:{type:Array,default:()=>[]}},emits:["toggle-mobile-sidebar"],setup(i){return(l,d)=>(r(),n("header",Is,[e("div",As,[e("div",Bs,[e("button",{onClick:d[0]||(d[0]=t=>l.$emit("toggle-mobile-sidebar")),class:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"},d[1]||(d[1]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)])),e("div",Vs,[e("h2",Hs,u(i.pageTitle),1),i.breadcrumbs.length>0?(r(),E(Nt,{key:0,breadcrumbs:i.breadcrumbs},null,8,["breadcrumbs"])):b("",!0)])]),e("div",Ps,[e("div",Ts,[h(Et)]),h(os),h(bs),h(Ss)])])]))}},Ds={__name:"LoadingSpinner",props:{size:{type:String,default:"md",validator:i=>["sm","md","lg","xl"].includes(i)},message:{type:String,default:""},centered:{type:Boolean,default:!0}},setup(i){const l=i,d=_(()=>{const g={sm:"20px",md:"32px",lg:"48px",xl:"64px"};return`width: ${g[l.size]}; height: ${g[l.size]};`}),t=_(()=>["flex",l.centered?"items-center justify-center":"","space-y-2"]),s=_(()=>["flex items-center justify-center"]),c=_(()=>["text-sm text-gray-600 text-center"]);return(g,v)=>(r(),n("div",{class:j(t.value)},[e("div",{class:j(s.value)},[e("div",{class:"animate-spin rounded-full border-2 border-gray-300 border-t-primary-600",style:fe(d.value)},null,4)],2),i.message?(r(),n("p",{key:0,class:j(c.value)},u(i.message),3)):b("",!0)],2))}},Y=(i,l)=>{const d=i.__vccOpts||i;for(const[t,s]of l)d[t]=s;return d},Es={class:"fixed bottom-0 right-0 z-50 p-6 space-y-4"},Ls={class:"p-4"},Rs={class:"flex items-start"},Fs={class:"flex-shrink-0"},Us={class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},qs=["d"],Os={class:"ml-3 w-0 flex-1 pt-0.5"},Ks={class:"text-sm font-medium text-gray-900"},Gs={class:"mt-1 text-sm text-gray-500"},Ws={class:"ml-4 flex-shrink-0 flex"},Js=["onClick"],Ys={__name:"NotificationManager",setup(i){const l=w([]);function d(v){const m=Date.now(),a={id:m,type:v.type||"info",title:v.title,message:v.message,duration:v.duration||5e3};l.value.push(a),a.duration>0&&setTimeout(()=>{t(m)},a.duration)}function t(v){const m=l.value.findIndex(a=>a.id===v);m>-1&&l.value.splice(m,1)}function s(v){const m={success:"border-l-4 border-green-400",error:"border-l-4 border-red-400",warning:"border-l-4 border-yellow-400",info:"border-l-4 border-blue-400"};return m[v]||m.info}function c(v){const m={success:"h-8 w-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center",error:"h-8 w-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center",warning:"h-8 w-8 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center",info:"h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center"};return m[v]||m.info}function g(v){const m={success:"M5 13l4 4L19 7",error:"M6 18L18 6M6 6l12 12",warning:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-2.694-.833-3.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z",info:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return m[v]||m.info}return window.showNotification=d,L(()=>{}),(v,m)=>(r(),n("div",Es,[h(Me,{name:"notification",tag:"div",class:"space-y-4"},{default:$(()=>[(r(!0),n(V,null,H(l.value,a=>(r(),n("div",{key:a.id,class:j([s(a.type),"max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"])},[e("div",Ls,[e("div",Rs,[e("div",Fs,[e("div",{class:j(c(a.type))},[(r(),n("svg",Us,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:g(a.type)},null,8,qs)]))],2)]),e("div",Os,[e("p",Ks,u(a.title),1),e("p",Gs,u(a.message),1)]),e("div",Ws,[e("button",{onClick:o=>t(a.id),class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},m[0]||(m[0]=[e("span",{class:"sr-only"},"Chiudi",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Js)])])])],2))),128))]),_:1})]))}},Qs=Y(Ys,[["__scopeId","data-v-220f0827"]]),Xs={class:"h-screen flex bg-gray-50"},Zs={class:"flex flex-col flex-1 overflow-hidden lg:ml-64"},eo={class:"flex-1 overflow-y-auto"},to={class:"py-6"},so={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},oo={key:0,class:"mb-6"},ro={key:1,class:"flex items-center justify-center h-64"},ao={__name:"AppLayout",setup(i){const l=ee(),d=U(),t=w(!1),s=w(!1);_(()=>d.config||{});const c=_(()=>d.config!==null),g=_(()=>{var f;return(f=l.meta)!=null&&f.title?l.meta.title:{dashboard:"Dashboard",projects:"Progetti","projects-list":"Elenco Progetti","projects-view":"Dettaglio Progetto","projects-create":"Nuovo Progetto",personnel:"Personale","personnel-directory":"Rubrica Aziendale","personnel-orgchart":"Organigramma","personnel-skills":"Competenze"}[l.name]||"DatPortal"}),v=_(()=>{var p;return(p=l.meta)!=null&&p.breadcrumbs?l.meta.breadcrumbs.map(f=>({label:f.label,to:f.to,icon:f.icon})):[]}),m=_(()=>{var p;return((p=l.meta)==null?void 0:p.hasActions)||!1});function a(){t.value=!t.value}function o(){t.value=!1}return X(l,()=>{s.value=!0,setTimeout(()=>{s.value=!1},300)}),X(l,()=>{o()}),L(()=>{c.value||d.loadConfig()}),(p,f)=>{const z=B("router-view");return r(),n("div",Xs,[t.value?(r(),n("div",{key:0,onClick:o,class:"fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"})):b("",!0),h(St,{"is-mobile-open":t.value,onClose:o},null,8,["is-mobile-open"]),e("div",Zs,[h(Ns,{"page-title":g.value,breadcrumbs:v.value,onToggleMobileSidebar:a},null,8,["page-title","breadcrumbs"]),e("main",eo,[e("div",to,[e("div",so,[m.value?(r(),n("div",oo,[ze(p.$slots,"page-actions")])):b("",!0),s.value?(r(),n("div",ro,[h(Ds)])):(r(),E(z,{key:2}))])])])]),h(Qs)])}}},no={class:"min-h-screen bg-gray-50"},io={class:"bg-white shadow-sm border-b"},lo={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},co={class:"flex justify-between h-16"},uo={class:"flex items-center"},mo={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},po={class:"text-white font-bold text-sm"},vo={class:"text-xl font-semibold text-gray-900"},go={class:"hidden md:flex items-center space-x-8"},ho={class:"flex items-center space-x-4 ml-8 pl-8 border-l border-gray-200"},fo={class:"md:hidden flex items-center"},xo={key:0,class:"md:hidden"},yo={class:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t"},_o={class:"bg-gray-800 text-white"},bo={class:"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8"},ko={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},wo={class:"col-span-1 md:col-span-2"},$o={class:"flex items-center space-x-3 mb-4"},Co={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},jo={class:"text-white font-bold text-sm"},Mo={class:"text-xl font-semibold"},zo={class:"text-gray-300 max-w-md"},So={class:"space-y-2"},Io={class:"space-y-2 text-gray-300"},Ao={key:0},Bo={key:1},Vo={key:2},Ho={class:"mt-8 pt-8 border-t border-gray-700 text-center text-gray-400"},ve={__name:"PublicLayout",setup(i){const l=U(),d=w(!1),t=_(()=>l.config||{}),s=_(()=>{var m;return((m=t.value.company)==null?void 0:m.name)||"DatVinci"}),c=_(()=>s.value.split(" ").map(a=>a[0]).join("").toUpperCase().slice(0,2)),g=_(()=>l.config!==null),v=new Date().getFullYear();return L(()=>{g.value||l.loadConfig()}),(m,a)=>{var f,z,I,P,S,k;const o=B("router-link"),p=B("router-view");return r(),n("div",no,[e("nav",io,[e("div",lo,[e("div",co,[e("div",uo,[h(o,{to:"/",class:"flex items-center space-x-3"},{default:$(()=>[e("div",mo,[e("span",po,u(c.value),1)]),e("span",vo,u(s.value),1)]),_:1})]),e("div",go,[h(o,{to:"/",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:$(()=>a[1]||(a[1]=[C(" Home ")])),_:1,__:[1]}),h(o,{to:"/about",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:$(()=>a[2]||(a[2]=[C(" Chi Siamo ")])),_:1,__:[2]}),h(o,{to:"/services",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:$(()=>a[3]||(a[3]=[C(" Servizi ")])),_:1,__:[3]}),h(o,{to:"/contact",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:$(()=>a[4]||(a[4]=[C(" Contatti ")])),_:1,__:[4]}),e("div",ho,[h(o,{to:"/auth/login",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:$(()=>a[5]||(a[5]=[C(" Accedi ")])),_:1,__:[5]}),h(o,{to:"/auth/register",class:"bg-primary-600 text-white hover:bg-primary-700 px-4 py-2 rounded-md text-sm font-medium"},{default:$(()=>a[6]||(a[6]=[C(" Registrati ")])),_:1,__:[6]})])]),e("div",fo,[e("button",{onClick:a[0]||(a[0]=A=>d.value=!d.value),class:"text-gray-400 hover:text-gray-500"},a[7]||(a[7]=[e("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)]))])])]),d.value?(r(),n("div",xo,[e("div",yo,[h(o,{to:"/",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:$(()=>a[8]||(a[8]=[C(" Home ")])),_:1,__:[8]}),h(o,{to:"/about",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:$(()=>a[9]||(a[9]=[C(" Chi Siamo ")])),_:1,__:[9]}),h(o,{to:"/services",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:$(()=>a[10]||(a[10]=[C(" Servizi ")])),_:1,__:[10]}),h(o,{to:"/contact",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:$(()=>a[11]||(a[11]=[C(" Contatti ")])),_:1,__:[11]}),a[14]||(a[14]=e("hr",{class:"my-2"},null,-1)),h(o,{to:"/auth/login",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:$(()=>a[12]||(a[12]=[C(" Accedi ")])),_:1,__:[12]}),h(o,{to:"/auth/register",class:"block px-3 py-2 text-base font-medium bg-primary-600 text-white rounded-md"},{default:$(()=>a[13]||(a[13]=[C(" Registrati ")])),_:1,__:[13]})])])):b("",!0)]),e("main",null,[h(p)]),e("footer",_o,[e("div",bo,[e("div",ko,[e("div",wo,[e("div",$o,[e("div",Co,[e("span",jo,u(c.value),1)]),e("span",Mo,u(s.value),1)]),e("p",zo,u(R(l).interpolateText((f=t.value.footer)==null?void 0:f.description)||((z=t.value.company)==null?void 0:z.description)||"Innovazione e tecnologia per il futuro digitale della tua azienda."),1)]),e("div",null,[a[19]||(a[19]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Link Rapidi",-1)),e("ul",So,[e("li",null,[h(o,{to:"/",class:"text-gray-300 hover:text-white"},{default:$(()=>a[15]||(a[15]=[C("Home")])),_:1,__:[15]})]),e("li",null,[h(o,{to:"/about",class:"text-gray-300 hover:text-white"},{default:$(()=>a[16]||(a[16]=[C("Chi Siamo")])),_:1,__:[16]})]),e("li",null,[h(o,{to:"/services",class:"text-gray-300 hover:text-white"},{default:$(()=>a[17]||(a[17]=[C("Servizi")])),_:1,__:[17]})]),e("li",null,[h(o,{to:"/contact",class:"text-gray-300 hover:text-white"},{default:$(()=>a[18]||(a[18]=[C("Contatti")])),_:1,__:[18]})])])]),e("div",null,[a[20]||(a[20]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Contatti",-1)),e("ul",Io,[(I=t.value.contact)!=null&&I.email?(r(),n("li",Ao,u(t.value.contact.email),1)):b("",!0),(P=t.value.contact)!=null&&P.phone?(r(),n("li",Bo,u(t.value.contact.phone),1)):b("",!0),(S=t.value.contact)!=null&&S.address?(r(),n("li",Vo,u(t.value.contact.address),1)):b("",!0)])])]),e("div",Ho,[e("p",null,u(R(l).interpolateText((k=t.value.footer)==null?void 0:k.copyright)||`© ${R(v)} ${s.value}. Tutti i diritti riservati.`),1)])])])])}}},Po={class:"bg-white"},To={class:"relative overflow-hidden"},No={class:"max-w-7xl mx-auto"},Do={class:"relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32"},Eo={class:"mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28"},Lo={class:"sm:text-center lg:text-left"},Ro={class:"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl"},Fo={class:"block xl:inline"},Uo={class:"mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0"},qo={class:"mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start"},Oo={class:"rounded-md shadow"},Ko={class:"mt-3 sm:mt-0 sm:ml-3"},Go={class:"py-12 bg-white"},Wo={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Jo={class:"lg:text-center"},Yo={class:"text-base text-primary-600 font-semibold tracking-wide uppercase"},Qo={class:"mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl"},Xo={key:0,class:"mt-10"},Zo={class:"space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10"},er={class:"absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white"},tr={class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},sr={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},or={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},rr={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},ar={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"},nr={class:"ml-16 text-lg leading-6 font-medium text-gray-900"},ir={class:"mt-2 ml-16 text-base text-gray-500"},lr={__name:"Home",setup(i){const l=U(),d=_(()=>l.config||{}),t=_(()=>{var c;return((c=d.value.pages)==null?void 0:c.home)||{}}),s=_(()=>d.value.company||{});return L(()=>{l.config||l.loadConfig()}),(c,g)=>{var m,a,o,p;const v=B("router-link");return r(),n("div",Po,[e("div",To,[e("div",No,[e("div",Do,[e("main",Eo,[e("div",Lo,[e("h1",Ro,[e("span",Fo,u(((m=t.value.hero)==null?void 0:m.title)||"Innovazione per il futuro"),1)]),e("p",Uo,u(((a=t.value.hero)==null?void 0:a.subtitle)||R(l).interpolateText(s.value.description)||"Supportiamo le aziende nel loro percorso di crescita attraverso soluzioni tecnologiche all'avanguardia"),1),e("div",qo,[e("div",Oo,[h(v,{to:"/services",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 md:py-4 md:text-lg md:px-10"},{default:$(()=>{var f;return[C(u(((f=t.value.hero)==null?void 0:f.cta_primary)||"Scopri i nostri servizi"),1)]}),_:1})]),e("div",Ko,[h(v,{to:"/contact",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 md:py-4 md:text-lg md:px-10"},{default:$(()=>{var f;return[C(u(((f=t.value.hero)==null?void 0:f.cta_secondary)||"Contattaci"),1)]}),_:1})])])])])])]),g[0]||(g[0]=e("div",{class:"lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2"},[e("div",{class:"h-56 w-full bg-gradient-to-r from-primary-400 to-primary-600 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center"},[e("svg",{class:"h-24 w-24 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])])],-1))]),e("div",Go,[e("div",Wo,[e("div",Jo,[e("h2",Yo,u(((o=t.value.services_section)==null?void 0:o.title)||"I nostri servizi"),1),e("p",Qo,u(((p=t.value.services_section)==null?void 0:p.subtitle)||"Soluzioni innovative per ogni esigenza aziendale"),1)]),s.value.platform_features?(r(),n("div",Xo,[e("div",Zo,[(r(!0),n(V,null,H(s.value.platform_features,f=>(r(),n("div",{key:f.title,class:"relative"},[e("div",er,[(r(),n("svg",tr,[f.icon==="briefcase"?(r(),n("path",sr)):f.icon==="users"?(r(),n("path",or)):f.icon==="chart"?(r(),n("path",rr)):(r(),n("path",ar))]))]),e("p",nr,u(f.title),1),e("p",ir,u(f.description),1)]))),128))])])):b("",!0)])])])}}},dr={class:"py-16 bg-white"},cr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ur={class:"text-center"},mr={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},pr={class:"mt-4 text-xl text-gray-600"},vr={key:0,class:"mt-16"},gr={class:"max-w-3xl mx-auto"},hr={class:"text-3xl font-bold text-gray-900 text-center mb-8"},fr={class:"text-lg text-gray-700 leading-relaxed"},xr={class:"mt-16 grid grid-cols-1 md:grid-cols-2 gap-12"},yr={key:0,class:"bg-gray-50 p-8 rounded-lg"},_r={class:"text-2xl font-bold text-gray-900 mb-4"},br={class:"text-gray-700"},kr={key:1,class:"bg-gray-50 p-8 rounded-lg"},wr={class:"text-2xl font-bold text-gray-900 mb-4"},$r={class:"text-gray-700"},Cr={key:1,class:"mt-16"},jr={class:"text-center mb-12"},Mr={class:"text-3xl font-bold text-gray-900"},zr={class:"mt-4 text-xl text-gray-600"},Sr={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Ir={class:"text-lg font-semibold text-gray-900"},Ar={key:2,class:"mt-16"},Br={class:"text-center"},Vr={class:"text-3xl font-bold text-gray-900"},Hr={class:"mt-4 text-xl text-gray-600"},Pr={class:"mt-8 inline-flex items-center px-6 py-3 bg-primary-50 rounded-lg"},Tr={class:"text-primary-900 font-medium"},Nr={__name:"About",setup(i){const l=U(),d=_(()=>l.config||{}),t=_(()=>{var c;return((c=d.value.pages)==null?void 0:c.about)||{}}),s=_(()=>d.value.company||{});return L(()=>{l.config||l.loadConfig()}),(c,g)=>{var v,m;return r(),n("div",dr,[e("div",cr,[e("div",ur,[e("h1",mr,u(((v=t.value.hero)==null?void 0:v.title)||"Chi Siamo"),1),e("p",pr,u(((m=t.value.hero)==null?void 0:m.subtitle)||"La nostra storia e i nostri valori"),1)]),t.value.story_section?(r(),n("div",vr,[e("div",gr,[e("h2",hr,u(t.value.story_section.title),1),e("p",fr,u(R(l).interpolateText(t.value.story_section.content)),1)])])):b("",!0),e("div",xr,[t.value.mission_section?(r(),n("div",yr,[e("h3",_r,u(t.value.mission_section.title),1),e("p",br,u(R(l).interpolateText(t.value.mission_section.content)),1)])):b("",!0),t.value.vision_section?(r(),n("div",kr,[e("h3",wr,u(t.value.vision_section.title),1),e("p",$r,u(R(l).interpolateText(t.value.vision_section.content)),1)])):b("",!0)]),t.value.expertise_section&&s.value.expertise?(r(),n("div",Cr,[e("div",jr,[e("h2",Mr,u(t.value.expertise_section.title),1),e("p",zr,u(t.value.expertise_section.subtitle),1)]),e("div",Sr,[(r(!0),n(V,null,H(s.value.expertise,a=>(r(),n("div",{key:a,class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200 text-center"},[g[0]||(g[0]=e("div",{class:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-6 h-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Ir,u(a),1)]))),128))])])):b("",!0),t.value.team_section?(r(),n("div",Ar,[e("div",Br,[e("h2",Vr,u(t.value.team_section.title),1),e("p",Hr,u(t.value.team_section.subtitle),1),e("div",Pr,[g[1]||(g[1]=e("svg",{class:"w-5 h-5 text-primary-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),e("span",Tr,u(s.value.team_size),1)])])])):b("",!0)])])}}},Dr={class:"py-16 bg-white"},Er={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Lr={class:"text-center"},Rr={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Fr={class:"mt-4 text-xl text-gray-600"},Ur={key:0,class:"mt-8 text-center"},qr={class:"text-lg text-gray-700 max-w-3xl mx-auto"},Or={class:"mt-16 grid grid-cols-1 lg:grid-cols-2 gap-16"},Kr={key:0},Gr={class:"text-2xl font-bold text-gray-900 mb-8"},Wr={class:"block text-sm font-medium text-gray-700 mb-2"},Jr={class:"block text-sm font-medium text-gray-700 mb-2"},Yr={class:"block text-sm font-medium text-gray-700 mb-2"},Qr=["disabled"],Xr={key:1},Zr={class:"text-2xl font-bold text-gray-900 mb-8"},ea={class:"space-y-6"},ta={key:0,class:"flex items-start"},sa={class:"font-medium text-gray-900"},oa={class:"text-gray-600"},ra={key:1,class:"flex items-start"},aa={class:"font-medium text-gray-900"},na={class:"text-gray-600"},ia={key:2,class:"flex items-start"},la={class:"font-medium text-gray-900"},da={class:"text-gray-600"},ca={key:3,class:"flex items-start"},ua={class:"font-medium text-gray-900"},ma={class:"text-gray-600"},pa={__name:"Contact",setup(i){const l=U(),d=_(()=>l.config||{}),t=_(()=>{var a;return((a=d.value.pages)==null?void 0:a.contact)||{}}),s=_(()=>d.value.contact||{}),c=w({name:"",email:"",message:""}),g=w(!1),v=w({text:"",type:""}),m=async()=>{var a,o;if(!c.value.name||!c.value.email||!c.value.message){v.value={text:((a=t.value.form)==null?void 0:a.error_message)||"Tutti i campi sono obbligatori",type:"error"};return}g.value=!0,v.value={text:"",type:""};try{await new Promise(p=>setTimeout(p,1e3)),v.value={text:((o=t.value.form)==null?void 0:o.success_message)||"Messaggio inviato con successo!",type:"success"},c.value={name:"",email:"",message:""}}catch{v.value={text:"Errore durante l'invio. Riprova più tardi.",type:"error"}}finally{g.value=!1}};return L(()=>{l.config||l.loadConfig()}),(a,o)=>{var p,f;return r(),n("div",Dr,[e("div",Er,[e("div",Lr,[e("h1",Rr,u(((p=t.value.hero)==null?void 0:p.title)||"Contattaci"),1),e("p",Fr,u(((f=t.value.hero)==null?void 0:f.subtitle)||"Siamo qui per aiutarti"),1)]),t.value.intro?(r(),n("div",Ur,[e("p",qr,u(t.value.intro.content),1)])):b("",!0),e("div",Or,[t.value.form?(r(),n("div",Kr,[e("h2",Gr,u(t.value.form.title),1),e("form",{onSubmit:te(m,["prevent"]),class:"space-y-6"},[e("div",null,[e("label",Wr,u(t.value.form.name_label),1),D(e("input",{"onUpdate:modelValue":o[0]||(o[0]=z=>c.value.name=z),type:"text",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[O,c.value.name]])]),e("div",null,[e("label",Jr,u(t.value.form.email_label),1),D(e("input",{"onUpdate:modelValue":o[1]||(o[1]=z=>c.value.email=z),type:"email",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[O,c.value.email]])]),e("div",null,[e("label",Yr,u(t.value.form.message_label),1),D(e("textarea",{"onUpdate:modelValue":o[2]||(o[2]=z=>c.value.message=z),rows:"6",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[O,c.value.message]])]),e("button",{type:"submit",disabled:g.value,class:"w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50"},u(g.value?"Invio in corso...":t.value.form.submit_button),9,Qr),v.value.text?(r(),n("div",{key:0,class:j([v.value.type==="success"?"text-green-600":"text-red-600","text-sm mt-2"])},u(v.value.text),3)):b("",!0)],32)])):b("",!0),t.value.info?(r(),n("div",Xr,[e("h2",Zr,u(t.value.info.title),1),e("div",ea,[s.value.address?(r(),n("div",ta,[o[3]||(o[3]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),e("div",null,[e("h3",sa,u(t.value.info.address_label),1),e("p",oa,u(s.value.address),1)])])):b("",!0),s.value.phone?(r(),n("div",ra,[o[4]||(o[4]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})],-1)),e("div",null,[e("h3",aa,u(t.value.info.phone_label),1),e("p",na,u(s.value.phone),1)])])):b("",!0),s.value.email?(r(),n("div",ia,[o[5]||(o[5]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1)),e("div",null,[e("h3",la,u(t.value.info.email_label),1),e("p",da,u(s.value.email),1)])])):b("",!0),s.value.hours?(r(),n("div",ca,[o[6]||(o[6]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",null,[e("h3",ua,u(t.value.info.hours_label),1),e("p",ma,u(s.value.hours),1)])])):b("",!0)])])):b("",!0)])])])}}},va={class:"py-16 bg-white"},ga={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ha={class:"text-center"},fa={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},xa={class:"mt-4 text-xl text-gray-600"},ya={key:0,class:"mt-8 text-center"},_a={class:"text-lg text-gray-700 max-w-3xl mx-auto"},ba={key:1,class:"mt-16"},ka={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},wa={class:"text-xl font-bold text-gray-900 text-center mb-4"},$a={class:"text-gray-600 text-center"},Ca={key:2,class:"mt-20"},ja={class:"bg-primary-50 rounded-2xl p-12 text-center"},Ma={class:"text-3xl font-bold text-gray-900 mb-4"},za={class:"text-xl text-gray-600 mb-8"},Sa={__name:"Services",setup(i){const l=U(),d=_(()=>l.config||{}),t=_(()=>{var g;return((g=d.value.pages)==null?void 0:g.services)||{}}),s=_(()=>d.value.company||{}),c=g=>({"Sviluppo Software":"Soluzioni software personalizzate per ottimizzare i processi aziendali e migliorare l'efficienza operativa.","Intelligenza Artificiale":"Implementazione di sistemi AI avanzati per automatizzare processi e analizzare dati complessi.","Consulenza IT":"Consulenza strategica per la trasformazione digitale e l'ottimizzazione dell'infrastruttura tecnologica.","Gestione Progetti Innovativi":"Coordinamento e gestione di progetti tecnologici complessi con metodologie agili.","Supporto su Bandi e Finanziamenti":"Assistenza nella ricerca e gestione di bandi pubblici e finanziamenti per l'innovazione."})[g]||"Servizio professionale di alta qualità per supportare la crescita della tua azienda.";return L(()=>{l.config||l.loadConfig()}),(g,v)=>{var a,o;const m=B("router-link");return r(),n("div",va,[e("div",ga,[e("div",ha,[e("h1",fa,u(((a=t.value.hero)==null?void 0:a.title)||"I nostri servizi"),1),e("p",xa,u(((o=t.value.hero)==null?void 0:o.subtitle)||"Soluzioni complete per la tua azienda"),1)]),t.value.intro?(r(),n("div",ya,[e("p",_a,u(t.value.intro.content),1)])):b("",!0),s.value.expertise?(r(),n("div",ba,[e("div",ka,[(r(!0),n(V,null,H(s.value.expertise,p=>(r(),n("div",{key:p,class:"bg-white p-8 rounded-lg shadow-lg border border-gray-200 hover:shadow-xl transition-shadow"},[v[0]||(v[0]=e("div",{class:"w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-6"},[e("svg",{class:"w-8 h-8 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",wa,u(p),1),e("p",$a,u(c(p)),1)]))),128))])])):b("",!0),t.value.cta?(r(),n("div",Ca,[e("div",ja,[e("h2",Ma,u(t.value.cta.title),1),e("p",za,u(t.value.cta.subtitle),1),h(m,{to:"/contact",class:"inline-flex items-center px-8 py-4 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"},{default:$(()=>[C(u(t.value.cta.button)+" ",1),v[1]||(v[1]=e("svg",{class:"ml-2 w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 8l4 4m0 0l-4 4m4-4H3"})],-1))]),_:1,__:[1]})])])):b("",!0)])])}}},Ia={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},Aa={class:"max-w-md w-full space-y-8"},Ba={class:"mt-2 text-center text-sm text-gray-600"},Va={key:0,class:"rounded-md bg-red-50 p-4"},Ha={class:"text-sm text-red-700"},Pa={class:"rounded-md shadow-sm -space-y-px"},Ta={class:"flex items-center justify-between"},Na={class:"flex items-center"},Da=["disabled"],Ea={key:0,class:"absolute left-0 inset-y-0 flex items-center pl-3"},La={__name:"Login",setup(i){const l=G(),d=F(),t=w({username:"",password:"",remember:!1}),s=_(()=>d.loading),c=_(()=>d.error);async function g(){(await d.login({username:t.value.username,password:t.value.password,remember:t.value.remember})).success&&l.push("/app/dashboard")}return(v,m)=>{const a=B("router-link");return r(),n("div",Ia,[e("div",Aa,[e("div",null,[m[5]||(m[5]=e("div",{class:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100"},[e("svg",{class:"h-6 w-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])],-1)),m[6]||(m[6]=e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Accedi al tuo account ",-1)),e("p",Ba,[m[4]||(m[4]=C(" Oppure ")),h(a,{to:"/auth/register",class:"font-medium text-primary-600 hover:text-primary-500"},{default:$(()=>m[3]||(m[3]=[C(" registrati per un nuovo account ")])),_:1,__:[3]})])]),e("form",{onSubmit:te(g,["prevent"]),class:"mt-8 space-y-6"},[c.value?(r(),n("div",Va,[e("div",Ha,u(c.value),1)])):b("",!0),e("div",Pa,[e("div",null,[m[7]||(m[7]=e("label",{for:"username",class:"sr-only"},"Username",-1)),D(e("input",{id:"username","onUpdate:modelValue":m[0]||(m[0]=o=>t.value.username=o),name:"username",type:"text",autocomplete:"username",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Username"},null,512),[[O,t.value.username]])]),e("div",null,[m[8]||(m[8]=e("label",{for:"password",class:"sr-only"},"Password",-1)),D(e("input",{id:"password","onUpdate:modelValue":m[1]||(m[1]=o=>t.value.password=o),name:"password",type:"password",autocomplete:"current-password",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Password"},null,512),[[O,t.value.password]])])]),e("div",Ta,[e("div",Na,[D(e("input",{id:"remember-me","onUpdate:modelValue":m[2]||(m[2]=o=>t.value.remember=o),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[Se,t.value.remember]]),m[9]||(m[9]=e("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Ricordami ",-1))]),m[10]||(m[10]=e("div",{class:"text-sm"},[e("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500"}," Password dimenticata? ")],-1))]),e("div",null,[e("button",{type:"submit",disabled:s.value,class:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"},[s.value?(r(),n("span",Ea,m[11]||(m[11]=[e("svg",{class:"h-5 w-5 text-primary-500 animate-spin",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)]))):b("",!0),C(" "+u(s.value?"Accesso in corso...":"Accedi"),1)],8,Da)])],32)])])}}},Ra={},Fa={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"};function Ua(i,l){return r(),n("div",Fa,l[0]||(l[0]=[e("div",{class:"max-w-md w-full space-y-8"},[e("div",null,[e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Registra un nuovo account ")]),e("div",{class:"text-center text-gray-600"}," Registrazione in arrivo... ")],-1)]))}const qa=Y(Ra,[["render",Ua]]),Oa={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Ka={class:"p-5"},Ga={class:"flex items-center"},Wa={class:"ml-5 w-0 flex-1"},Ja={class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},Ya={class:"text-lg font-medium text-gray-900 dark:text-white"},Qa={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},Xa={key:0,class:"bg-gray-50 dark:bg-gray-700 px-5 py-3"},Za={class:"text-sm"},J={__name:"StatsCard",props:{title:{type:String,required:!0},value:{type:[Number,String],required:!0},subtitle:{type:String,default:null},icon:{type:String,required:!0},color:{type:String,default:"primary"},link:{type:String,default:null}},setup(i){const l=t=>t==="project"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`}:t==="users"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>`}:t==="clock"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}:t==="team"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
      </svg>`}:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>`},d=t=>{const s={primary:"bg-primary-500",secondary:"bg-secondary-500",red:"bg-red-500",yellow:"bg-yellow-500",blue:"bg-blue-500",green:"bg-green-500"};return s[t]||s.primary};return(t,s)=>{const c=B("router-link");return r(),n("div",Oa,[e("div",Ka,[e("div",Ga,[e("div",{class:j(["flex-shrink-0 rounded-md p-3",d(i.color)])},[(r(),E(xe(l(i.icon)),{class:"h-6 w-6 text-white"}))],2),e("div",Wa,[e("dl",null,[e("dt",Ja,u(i.title),1),e("dd",null,[e("div",Ya,u(i.value),1),i.subtitle?(r(),n("div",Qa,u(i.subtitle),1)):b("",!0)])])])])]),i.link?(r(),n("div",Xa,[e("div",Za,[h(c,{to:i.link,class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300"},{default:$(()=>s[0]||(s[0]=[C(" Vedi tutti ")])),_:1,__:[0]},8,["to"])])])):b("",!0)])}}},en={class:"py-6"},tn={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},sn={class:"mt-4 md:mt-0 flex space-x-3"},on={class:"relative"},rn=["disabled"],an={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},nn={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},ln={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},dn={class:"relative h-64"},cn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},un={class:"relative h-64"},mn={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},pn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},vn={class:"p-6"},gn={key:0,class:"text-center py-8 text-gray-500"},hn={key:1,class:"space-y-4"},fn={class:"flex justify-between items-start"},xn={class:"flex-1"},yn={class:"text-sm font-medium text-gray-900 dark:text-white"},_n={class:"text-xs text-gray-500 dark:text-gray-400"},bn={class:"mt-2 flex justify-between items-center"},kn={class:"text-xs text-gray-500 dark:text-gray-400"},wn={class:"bg-gray-50 dark:bg-gray-700 px-6 py-3"},$n={class:"text-sm"},Cn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},jn={class:"p-6"},Mn={key:0,class:"text-center py-8 text-gray-500"},zn={key:1,class:"space-y-4"},Sn={class:"flex-shrink-0"},In={class:"flex-1 min-w-0"},An={class:"text-sm font-medium text-gray-900 dark:text-white"},Bn={class:"text-xs text-gray-500 dark:text-gray-400"},Vn={class:"text-xs text-gray-400 dark:text-gray-500"},Hn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Pn={class:"p-6"},Tn={key:0,class:"text-center py-8 text-gray-500"},Nn={key:1,class:"space-y-4"},Dn={class:"flex justify-between items-start"},En={class:"flex-1"},Ln={class:"text-sm font-medium text-gray-900 dark:text-white"},Rn={class:"text-xs text-gray-500 dark:text-gray-400"},Fn={class:"text-right"},Un={class:"text-sm font-bold text-gray-900 dark:text-white"},qn={class:"text-xs text-gray-500"},On={class:"mt-2"},Kn={class:"w-full bg-gray-200 rounded-full h-2"},Gn={class:"text-xs text-gray-500 mt-1"},Wn={__name:"Dashboard",setup(i){Q.register(...Ie),G();const l=w(!1),d=w("7"),t=w({}),s=w([]),c=w([]),g=w([]),v=w(null),m=w(null);let a=null,o=null;const p=async()=>{try{const y=await fetch("/api/dashboard/stats");if(!y.ok)throw new Error("Failed to fetch stats");const x=await y.json();t.value=x.data}catch(y){console.error("Error fetching dashboard stats:",y),t.value={}}},f=async()=>{try{const y=await fetch(`/api/dashboard/upcoming-tasks?days=${d.value}&limit=5`);if(!y.ok)throw new Error("Failed to fetch upcoming tasks");const x=await y.json();s.value=x.data.tasks}catch(y){console.error("Error fetching upcoming tasks:",y),s.value=[]}},z=async()=>{try{const y=await fetch("/api/dashboard/recent-activities?limit=5");if(!y.ok)throw new Error("Failed to fetch recent activities");const x=await y.json();c.value=x.data.activities}catch(y){console.error("Error fetching recent activities:",y),c.value=[]}},I=async()=>{try{const y=await fetch("/api/dashboard/kpis?limit=3");if(!y.ok)throw new Error("Failed to fetch KPIs");const x=await y.json();g.value=x.data.kpis}catch(y){console.error("Error fetching KPIs:",y),g.value=[]}},P=async()=>{try{const y=await fetch("/api/dashboard/charts/project-status");if(!y.ok)throw new Error("Failed to fetch project chart data");const x=await y.json();k(x.data.chart)}catch(y){console.error("Error fetching project chart:",y)}},S=async()=>{try{const y=await fetch("/api/dashboard/charts/task-status");if(!y.ok)throw new Error("Failed to fetch task chart data");const x=await y.json();A(x.data.chart)}catch(y){console.error("Error fetching task chart:",y)}},k=y=>{if(!v.value)return;const x=v.value.getContext("2d");a&&a.destroy(),a=new Q(x,{type:"doughnut",data:{labels:y.labels,datasets:[{data:y.data,backgroundColor:["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6"],borderWidth:2,borderColor:"#ffffff"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}}}}})},A=y=>{if(!m.value)return;const x=m.value.getContext("2d");o&&o.destroy(),o=new Q(x,{type:"bar",data:{labels:y.labels,datasets:[{label:"Tasks",data:y.data,backgroundColor:["#60A5FA","#34D399","#FBBF24","#F87171"],borderColor:["#3B82F6","#10B981","#F59E0B","#EF4444"],borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,ticks:{stepSize:1}}}}})},K=async()=>{l.value=!0;try{await Promise.all([p(),f(),z(),I(),P(),S()])}finally{l.value=!1}},oe=y=>new Date(y).toLocaleDateString("it-IT"),_e=y=>{const x=new Date(y),N=Math.floor((new Date-x)/(1e3*60));return N<60?`${N} minuti fa`:N<1440?`${Math.floor(N/60)} ore fa`:`${Math.floor(N/1440)} giorni fa`},be=y=>{const x={high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return x[y]||x.medium},ke=y=>{const x={todo:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300","in-progress":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",review:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",done:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return x[y]||x.todo},we=y=>{const x={task:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`},timesheet:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`},event:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`}};return x[y]||x.task},$e=y=>{const x={task:"bg-blue-100 text-blue-600",timesheet:"bg-green-100 text-green-600",event:"bg-purple-100 text-purple-600"};return x[y]||x.task},Ce=y=>y>=90?"bg-green-500":y>=70?"bg-yellow-500":"bg-red-500";return L(async()=>{await K(),await he(),v.value&&m.value&&(await P(),await S())}),(y,x)=>{var N,ae,ne,ie,le,de,ce,ue;const re=B("router-link");return r(),n("div",en,[e("div",tn,[x[4]||(x[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Dashboard"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Benvenuto! Ecco una panoramica delle attività della tua azienda. ")],-1)),e("div",sn,[e("div",on,[D(e("select",{"onUpdate:modelValue":x[0]||(x[0]=M=>d.value=M),onChange:K,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500"},x[1]||(x[1]=[e("option",{value:"7"},"Ultimi 7 giorni",-1),e("option",{value:"30"},"Ultimo mese",-1),e("option",{value:"90"},"Ultimi 3 mesi",-1)]),544),[[Ae,d.value]])]),e("button",{onClick:K,disabled:l.value,type:"button",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(r(),n("svg",{xmlns:"http://www.w3.org/2000/svg",class:j(["h-4 w-4 mr-2",{"animate-spin":l.value}]),viewBox:"0 0 20 20",fill:"currentColor"},x[2]||(x[2]=[e("path",{"fill-rule":"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z","clip-rule":"evenodd"},null,-1)]),2)),x[3]||(x[3]=C(" Aggiorna "))],8,rn)])]),e("div",an,[h(J,{title:"Progetti Attivi",value:((N=t.value.projects)==null?void 0:N.active)||0,subtitle:`di ${((ae=t.value.projects)==null?void 0:ae.total)||0} totali`,icon:"project",color:"primary",link:"/projects?status=active"},null,8,["value","subtitle"]),h(J,{title:"Clienti",value:((ne=t.value.team)==null?void 0:ne.clients)||0,icon:"users",color:"secondary",link:"/crm/clients"},null,8,["value"]),h(J,{title:"Task Pendenti",value:((ie=t.value.tasks)==null?void 0:ie.pending)||0,subtitle:`${((le=t.value.tasks)==null?void 0:le.overdue)||0} in ritardo`,icon:"clock",color:((de=t.value.tasks)==null?void 0:de.overdue)>0?"red":"yellow",link:"/tasks?status=pending"},null,8,["value","subtitle","color"]),h(J,{title:"Team Members",value:((ce=t.value.team)==null?void 0:ce.users)||0,subtitle:`${((ue=t.value.team)==null?void 0:ue.departments)||0} dipartimenti`,icon:"team",color:"blue",link:"/personnel"},null,8,["value","subtitle"])]),e("div",nn,[e("div",ln,[x[5]||(x[5]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Progetti")],-1)),e("div",dn,[e("canvas",{ref_key:"projectChart",ref:v},null,512)])]),e("div",cn,[x[6]||(x[6]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Attività")],-1)),e("div",un,[e("canvas",{ref_key:"taskChart",ref:m},null,512)])])]),e("div",mn,[e("div",pn,[e("div",vn,[x[7]||(x[7]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività in Scadenza",-1)),s.value.length===0?(r(),n("div",gn," Nessuna attività in scadenza ")):(r(),n("div",hn,[(r(!0),n(V,null,H(s.value,M=>(r(),n("div",{key:M.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",fn,[e("div",xn,[e("h3",yn,u(M.name),1),e("p",_n,u(M.project_name),1)]),e("span",{class:j(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",be(M.priority)])},u(M.priority),3)]),e("div",bn,[e("span",kn," Scadenza: "+u(oe(M.due_date)),1),e("span",{class:j(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",ke(M.status)])},u(M.status),3)])]))),128))]))]),e("div",wn,[e("div",$n,[h(re,{to:"/tasks",class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500"},{default:$(()=>x[8]||(x[8]=[C(" Vedi tutte le attività ")])),_:1,__:[8]})])])]),e("div",Cn,[e("div",jn,[x[9]||(x[9]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività Recenti",-1)),c.value.length===0?(r(),n("div",Mn," Nessuna attività recente ")):(r(),n("div",zn,[(r(!0),n(V,null,H(c.value,M=>(r(),n("div",{key:`${M.type}-${M.id}`,class:"flex items-start space-x-3"},[e("div",Sn,[e("div",{class:j(["w-8 h-8 rounded-full flex items-center justify-center",$e(M.type)])},[(r(),E(xe(we(M.type)),{class:"w-4 h-4"}))],2)]),e("div",In,[e("p",An,u(M.title),1),e("p",Bn,u(M.description),1),e("p",Vn,u(_e(M.timestamp)),1)])]))),128))]))])]),e("div",Hn,[e("div",Pn,[x[10]||(x[10]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"KPIs Principali",-1)),g.value.length===0?(r(),n("div",Tn," Nessun KPI configurato ")):(r(),n("div",Nn,[(r(!0),n(V,null,H(g.value,M=>(r(),n("div",{key:M.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",Dn,[e("div",En,[e("h3",Ln,u(M.name),1),e("p",Rn,u(M.description),1)]),e("div",Fn,[e("p",Un,u(M.current_value)+u(M.unit),1),e("p",qn," Target: "+u(M.target_value)+u(M.unit),1)])]),e("div",On,[e("div",Kn,[e("div",{class:j(["h-2 rounded-full",Ce(M.performance_percentage)]),style:fe({width:Math.min(M.performance_percentage,100)+"%"})},null,6)]),e("p",Gn,u(Math.round(M.performance_percentage))+"% del target",1)])]))),128))]))])])])])}}},Jn={};function Yn(i,l){return r(),n("div",null,l[0]||(l[0]=[e("h1",{class:"text-2xl font-bold text-gray-900 mb-6"},"Progetti",-1),e("div",{class:"card"},[e("p",{class:"text-gray-600"},"Gestione progetti in fase di migrazione...")],-1)]))}const Qn=Y(Jn,[["render",Yn]]),Xn={};function Zn(i,l){return r(),n("div",null,l[0]||(l[0]=[e("h1",{class:"text-2xl font-bold text-gray-900 mb-6"},"Personale",-1),e("div",{class:"card"},[e("p",{class:"text-gray-600"},"Gestione personale in fase di migrazione...")],-1)]))}const ei=Y(Xn,[["render",Zn]]),ti=[{path:"/",component:ve,children:[{path:"",name:"home",component:lr},{path:"about",name:"about",component:Nr},{path:"contact",name:"contact",component:pa},{path:"services",name:"services",component:Sa}]},{path:"/auth",component:ve,children:[{path:"login",name:"login",component:La},{path:"register",name:"register",component:qa}]},{path:"/app",component:ao,meta:{requiresAuth:!0},children:[{path:"",redirect:"/app/dashboard"},{path:"dashboard",name:"dashboard",component:Wn},{path:"projects",name:"projects",component:Qn},{path:"personnel",name:"personnel",component:ei}]}],ye=Be({history:Ve(),routes:ti});ye.beforeEach(async(i,l,d)=>{const t=F();if(i.meta.requiresAuth&&(t.sessionChecked||await t.initializeAuth(),!t.isAuthenticated)){d("/auth/login");return}d()});const se=He(Ne),si=Pe();se.use(si);se.use(ye);const oi=F();oi.initializeAuth().then(()=>{se.mount("#app")});
