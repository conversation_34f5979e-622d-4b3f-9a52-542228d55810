const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ProjectView.js","assets/vendor.js","assets/ProjectView.css"])))=>i.map(i=>d[i]);
import{r as $,w as Q,c as n,a as f,b as B,o as r,d as Ae,e as ye,f as b,g as _,n as j,h as R,i as C,t as c,u as te,j as e,F as P,k as D,l as M,m as G,p as V,q as be,s as se,v as N,x as K,y as J,z as _e,A as U,T as Be,B as Ve,C as Pe,D as ke,E as Z,G as De,H as He,I as Ee,J as Te,K as Le,L as Ne}from"./vendor.js";(function(){const i=document.createElement("link").relList;if(i&&i.supports&&i.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))t(s);new MutationObserver(s=>{for(const m of s)if(m.type==="childList")for(const v of m.addedNodes)v.tagName==="LINK"&&v.rel==="modulepreload"&&t(v)}).observe(document,{childList:!0,subtree:!0});function d(s){const m={};return s.integrity&&(m.integrity=s.integrity),s.referrerPolicy&&(m.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?m.credentials="include":s.crossOrigin==="anonymous"?m.credentials="omit":m.credentials="same-origin",m}function t(s){if(s.ep)return;s.ep=!0;const m=d(s);fetch(s.href,m)}})();const H=$(!1);let ge=!1;const we=a=>{console.log("Applying dark mode:",a),a?(document.documentElement.classList.add("dark"),localStorage.setItem("darkMode","true")):(document.documentElement.classList.remove("dark"),localStorage.setItem("darkMode","false"))},Re=()=>{ge||(Q(H,a=>{console.log("Dark mode changed to:",a),we(a)}),ge=!0)};function oe(){return Re(),{isDarkMode:H,toggleDarkMode:()=>{console.log("Toggling dark mode from:",H.value),H.value=!H.value},setDarkMode:t=>{H.value=t},initializeDarkMode:()=>{console.log("Initializing dark mode...");const t=localStorage.getItem("darkMode");if(t==="true")H.value=!0;else if(t==="false")H.value=!1;else{const s=window.matchMedia("(prefers-color-scheme: dark)").matches;H.value=s}we(H.value)}}}const Ue={id:"app"},Fe={__name:"App",setup(a){const{initializeDarkMode:i}=oe();return i(),(d,t)=>{const s=B("router-view");return r(),n("div",Ue,[f(s)])}}},qe="modulepreload",Oe=function(a){return"/"+a},ve={},Ke=function(i,d,t){let s=Promise.resolve();if(d&&d.length>0){document.getElementsByTagName("link");const v=document.querySelector("meta[property=csp-nonce]"),p=(v==null?void 0:v.nonce)||(v==null?void 0:v.getAttribute("nonce"));s=Promise.allSettled(d.map(u=>{if(u=Oe(u),u in ve)return;ve[u]=!0;const l=u.endsWith(".css"),o=l?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${u}"]${o}`))return;const g=document.createElement("link");if(g.rel=l?"stylesheet":qe,l||(g.as="script"),g.crossOrigin="",g.href=u,p&&g.setAttribute("nonce",p),document.head.appendChild(g),l)return new Promise((h,w)=>{g.addEventListener("load",h),g.addEventListener("error",()=>w(new Error(`Unable to preload CSS for ${u}`)))})}))}function m(v){const p=new Event("vite:preloadError",{cancelable:!0});if(p.payload=v,window.dispatchEvent(p),!p.defaultPrevented)throw v}return s.then(v=>{for(const p of v||[])p.status==="rejected"&&m(p.reason);return i().catch(m)})},O=Ae.create({baseURL:"",timeout:1e4,withCredentials:!0,headers:{"Content-Type":"application/json"}});O.interceptors.request.use(a=>{var d,t;const i=(d=document.querySelector('meta[name="csrf-token"]'))==null?void 0:d.getAttribute("content");return i&&["post","put","patch","delete"].includes((t=a.method)==null?void 0:t.toLowerCase())&&(a.headers["X-CSRFToken"]=i),a},a=>Promise.reject(a));O.interceptors.response.use(a=>a,a=>{var i;return((i=a.response)==null?void 0:i.status)===401&&(localStorage.removeItem("user"),console.warn("Sessione scaduta, autenticazione richiesta")),Promise.reject(a)});const F=ye("auth",()=>{const a=localStorage.getItem("user"),i=$(a?JSON.parse(a):null),d=$(!1),t=$(null),s=$(!1),m=b(()=>!!i.value&&s.value);async function v(g){var h,w;d.value=!0,t.value=null;try{const I=await O.post("/api/auth/login",g);return I.data.success?(i.value=I.data.data.user,localStorage.setItem("user",JSON.stringify(i.value)),s.value=!0,{success:!0}):(t.value=I.data.message||"Errore durante il login",{success:!1,error:t.value})}catch(I){return t.value=((w=(h=I.response)==null?void 0:h.data)==null?void 0:w.message)||"Errore di connessione",{success:!1,error:t.value}}finally{d.value=!1}}async function p(g){var h,w;d.value=!0,t.value=null;try{const I=await O.post("/api/auth/register",g);return I.data.success?{success:!0,message:I.data.message}:(t.value=I.data.message||"Errore durante la registrazione",{success:!1,error:t.value})}catch(I){return t.value=((w=(h=I.response)==null?void 0:h.data)==null?void 0:w.message)||"Errore di connessione",{success:!1,error:t.value}}finally{d.value=!1}}async function u(){try{await O.post("/api/auth/logout")}catch(g){console.warn("Errore durante il logout:",g)}finally{i.value=null,s.value=!1,localStorage.removeItem("user")}}async function l(){if(s.value)return m.value;try{const g=await O.get("/api/auth/me");return g.data.success?(i.value=g.data.data.user,localStorage.setItem("user",JSON.stringify(i.value)),s.value=!0,!0):(await u(),!1)}catch{return await u(),!1}}async function o(){return i.value?await l():(s.value=!0,!1)}return{user:i,loading:d,error:t,sessionChecked:s,isAuthenticated:m,login:v,register:p,logout:u,checkAuth:l,initializeAuth:o}}),q=ye("tenant",()=>{const a=$(null),i=$(!1),d=$(null),t=b(()=>{var o;return((o=a.value)==null?void 0:o.company)||{}}),s=b(()=>{var o;return((o=a.value)==null?void 0:o.contact)||{}}),m=b(()=>{var o;return((o=a.value)==null?void 0:o.pages)||{}}),v=b(()=>{var o;return((o=a.value)==null?void 0:o.navigation)||{}}),p=b(()=>{var o;return((o=a.value)==null?void 0:o.footer)||{}});async function u(){try{if(i.value=!0,window.TENANT_CONFIG){a.value=window.TENANT_CONFIG;return}const o=await fetch("/api/config/tenant");a.value=await o.json()}catch(o){d.value="Errore nel caricamento della configurazione",console.error("Errore caricamento tenant config:",o)}finally{i.value=!1}}function l(o,g={}){if(!o||typeof o!="string")return o;let h=o;const w={"company.name":t.value.name||"DatVinci","company.tagline":t.value.tagline||"","company.description":t.value.description||"","company.mission":t.value.mission||"","company.vision":t.value.vision||"","company.founded":t.value.founded||"","company.team_size":t.value.team_size||"","contact.email":s.value.email||"","contact.phone":s.value.phone||"","contact.address":s.value.address||"",current_year:new Date().getFullYear().toString(),...g};for(const[I,E]of Object.entries(w)){const S=new RegExp(`\\{${I}\\}`,"g");h=h.replace(S,E||"")}return h}return{config:a,loading:i,error:d,company:t,contact:s,pages:m,navigation:v,footer:p,loadConfig:u,interpolateText:l}}),We={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"},Ge={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},Je={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},Ye={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},Qe={key:4,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"},Xe={key:5,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},Ze={key:6,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"},et={key:7,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},tt={key:8,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},st={key:9,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"},ot={key:10,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},rt={key:11,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},at={key:12,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"},ee={__name:"SidebarIcon",props:{icon:{type:String,required:!0},className:{type:String,default:"h-5 w-5"}},setup(a){return(i,d)=>(r(),n("svg",{class:j(a.className),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a.icon==="dashboard"?(r(),n("path",We)):a.icon==="projects"?(r(),n("path",Ge)):a.icon==="users"?(r(),n("path",Je)):a.icon==="clients"?(r(),n("path",Ye)):a.icon==="products"?(r(),n("path",Qe)):a.icon==="reports"?(r(),n("path",Xe)):a.icon==="settings"?(r(),n("path",Ze)):_("",!0),a.icon==="settings"?(r(),n("path",et)):a.icon==="user-management"?(r(),n("path",tt)):a.icon==="communications"?(r(),n("path",st)):a.icon==="funding"?(r(),n("path",ot)):a.icon==="reporting"?(r(),n("path",rt)):(r(),n("path",at))],2))}},nt={key:0,class:"truncate"},it={key:0,class:"truncate"},T={__name:"SidebarNavItem",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(a){const i=b(()=>["text-gray-700 hover:bg-gray-50 hover:text-primary-600"]);return(d,t)=>{const s=B("router-link");return r(),n("div",null,[a.item.path!=="#"?(r(),R(s,{key:0,to:a.item.path,class:j(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[i.value,{"justify-center":a.isCollapsed}]]),"active-class":"text-primary-600 bg-primary-50 border-r-2 border-primary-600",onClick:t[0]||(t[0]=m=>d.$emit("click"))},{default:C(()=>[f(ee,{icon:a.item.icon,class:j(["flex-shrink-0 h-6 w-6",{"mr-0":a.isCollapsed,"mr-3":!a.isCollapsed}])},null,8,["icon","class"]),a.isCollapsed?_("",!0):(r(),n("span",nt,c(a.item.name),1))]),_:1},8,["to","class"])):(r(),n("div",{key:1,class:j(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 cursor-not-allowed opacity-75",["text-gray-400 hover:text-gray-500",{"justify-center":a.isCollapsed}]])},[f(ee,{icon:a.item.icon,class:j(["flex-shrink-0 h-6 w-6",{"mr-0":a.isCollapsed,"mr-3":!a.isCollapsed}])},null,8,["icon","class"]),a.isCollapsed?_("",!0):(r(),n("span",it,c(a.item.name),1))],2))])}}},lt={key:0,class:"flex-1 text-left truncate"},dt={key:0,class:"ml-6 space-y-1 mt-1"},ct={__name:"SidebarNavItemCollapsible",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(a){const i=a,d=te(),t=F(),s=$(!1),m=b(()=>["text-gray-700 hover:bg-gray-50 hover:text-primary-600",{"text-primary-600 bg-primary-50":v.value}]),v=b(()=>i.item.children?i.item.children.some(o=>o.path!=="#"&&d.path.startsWith(o.path)):!1),p=b(()=>i.item.children?i.item.children.filter(o=>{var g;return o.admin?((g=t.user)==null?void 0:g.role)==="admin":!0}):[]);v.value&&(s.value=!0);function u(){i.isCollapsed||(s.value=!s.value)}function l(o){if(o.path==="#")return!1}return(o,g)=>{const h=B("router-link");return r(),n("div",null,[e("button",{onClick:u,class:j(["group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[m.value,{"justify-center":a.isCollapsed}]])},[f(ee,{icon:a.item.icon,class:j(["flex-shrink-0 h-6 w-6",{"mr-0":a.isCollapsed,"mr-3":!a.isCollapsed}])},null,8,["icon","class"]),a.isCollapsed?_("",!0):(r(),n("span",lt,c(a.item.name),1)),a.isCollapsed?_("",!0):(r(),n("svg",{key:1,class:j([{"rotate-90":s.value},"ml-2 h-4 w-4 transition-transform duration-150"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},g[0]||(g[0]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"},null,-1)]),2))],2),s.value&&!a.isCollapsed?(r(),n("div",dt,[(r(!0),n(P,null,D(p.value,w=>(r(),R(h,{key:w.name,to:w.path,class:j(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",w.path==="#"?"text-gray-400 hover:text-gray-500 cursor-not-allowed opacity-75":"text-gray-600 hover:bg-gray-50 hover:text-primary-600"]),"active-class":"text-primary-600 bg-primary-50",onClick:I=>l(w)},{default:C(()=>[M(c(w.name),1)]),_:2},1032,["to","class","onClick"]))),128))])):_("",!0)])}}},ut={class:"mt-5 flex-grow flex flex-col overflow-hidden"},mt={class:"flex-1 px-2 space-y-1"},fe={__name:"SidebarNavigation",props:{isCollapsed:{type:Boolean,default:!1}},emits:["item-click"],setup(a){const i=F(),d=b(()=>{var t;return((t=i.user)==null?void 0:t.role)==="admin"});return(t,s)=>(r(),n("div",ut,[e("nav",mt,[f(T,{item:{name:"Dashboard",path:"/app/dashboard",icon:"dashboard"},"is-collapsed":a.isCollapsed,onClick:s[0]||(s[0]=m=>t.$emit("item-click"))},null,8,["is-collapsed"]),f(ct,{item:{name:"Personale",icon:"users",children:[{name:"👥 Team",path:"/app/personnel"},{name:"📖 Directory",path:"/app/personnel/directory"},{name:"🏢 Organigramma",path:"/app/personnel/orgchart"},{name:"🎯 Competenze",path:"/app/personnel/skills"},{name:"🏢 Dipartimenti",path:"#",admin:!0},{name:"⚙️ Amministrazione",path:"#",admin:!0}]},"is-collapsed":a.isCollapsed,onClick:s[1]||(s[1]=m=>t.$emit("item-click"))},null,8,["is-collapsed"]),f(T,{item:{name:"Progetti",path:"/app/projects",icon:"projects"},"is-collapsed":a.isCollapsed,onClick:s[2]||(s[2]=m=>t.$emit("item-click"))},null,8,["is-collapsed"]),f(T,{item:{name:"CRM",path:"#",icon:"clients"},"is-collapsed":a.isCollapsed,onClick:s[3]||(s[3]=m=>t.$emit("item-click"))},null,8,["is-collapsed"]),f(T,{item:{name:"Prodotti",path:"#",icon:"products"},"is-collapsed":a.isCollapsed,onClick:s[4]||(s[4]=m=>t.$emit("item-click"))},null,8,["is-collapsed"]),f(T,{item:{name:"Performance",path:"#",icon:"reports"},"is-collapsed":a.isCollapsed,onClick:s[5]||(s[5]=m=>t.$emit("item-click"))},null,8,["is-collapsed"]),f(T,{item:{name:"Comunicazione",path:"#",icon:"communications"},"is-collapsed":a.isCollapsed,onClick:s[6]||(s[6]=m=>t.$emit("item-click"))},null,8,["is-collapsed"]),f(T,{item:{name:"Finanziamenti",path:"#",icon:"funding"},"is-collapsed":a.isCollapsed,onClick:s[7]||(s[7]=m=>t.$emit("item-click"))},null,8,["is-collapsed"]),f(T,{item:{name:"Rendicontazione",path:"#",icon:"reporting"},"is-collapsed":a.isCollapsed,onClick:s[8]||(s[8]=m=>t.$emit("item-click"))},null,8,["is-collapsed"]),d.value?(r(),R(T,{key:0,item:{name:"Amministrazione",path:"#",icon:"settings"},"is-collapsed":a.isCollapsed,onClick:s[9]||(s[9]=m=>t.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0)])]))}},pt={class:"flex-shrink-0 border-t border-gray-200 p-4"},gt={class:"flex-shrink-0"},vt={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},ft={class:"text-sm font-medium text-primary-700"},ht={key:0,class:"ml-3 flex-1 min-w-0"},xt={class:"text-sm font-medium text-gray-900 truncate"},yt={class:"text-xs text-gray-500 truncate"},bt={class:"py-1"},_t={key:0,class:"mt-3 text-xs text-gray-400 text-center"},he={__name:"SidebarFooter",props:{isCollapsed:{type:Boolean,default:!1}},setup(a){const i=G(),d=F(),t=$(!1),s=b(()=>d.user&&(d.user.name||d.user.username)||"Utente"),m=b(()=>d.user?s.value.charAt(0).toUpperCase():"U"),v=b(()=>d.user?{admin:"Amministratore",manager:"Manager",employee:"Dipendente",client:"Cliente"}[d.user.role]||d.user.role:""),p=b(()=>"1.0.0");async function u(){t.value=!1,await d.logout(),i.push("/auth/login")}return(l,o)=>{const g=B("router-link");return r(),n("div",pt,[e("div",{class:j(["flex items-center",{"justify-center":a.isCollapsed}])},[e("div",gt,[e("div",vt,[e("span",ft,c(m.value),1)])]),a.isCollapsed?_("",!0):(r(),n("div",ht,[e("p",xt,c(s.value),1),e("p",yt,c(v.value),1)])),e("div",{class:j(["relative",{"ml-3":!a.isCollapsed}])},[e("button",{onClick:o[0]||(o[0]=h=>t.value=!t.value),class:"p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"},o[4]||(o[4]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zM13 12a1 1 0 11-2 0 1 1 0 012 0zM20 12a1 1 0 11-2 0 1 1 0 012 0z"})],-1)])),t.value?(r(),n("div",{key:0,onClick:o[3]||(o[3]=h=>t.value=!1),class:"origin-bottom-left absolute bottom-full left-0 mb-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",bt,[f(g,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:o[1]||(o[1]=h=>t.value=!1)},{default:C(()=>o[5]||(o[5]=[M(" Il tuo profilo ")])),_:1,__:[5]}),f(g,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:o[2]||(o[2]=h=>t.value=!1)},{default:C(()=>o[6]||(o[6]=[M(" Impostazioni ")])),_:1,__:[6]}),o[7]||(o[7]=e("hr",{class:"my-1"},null,-1)),e("button",{onClick:u,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Esci ")])])):_("",!0)],2)],2),p.value&&!a.isCollapsed?(r(),n("div",_t," v"+c(p.value),1)):_("",!0)])}}},kt={class:"flex"},wt={class:"hidden lg:flex lg:flex-shrink-0 lg:fixed lg:inset-y-0 z-10"},$t={class:"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},Ct={class:"flex items-center flex-shrink-0 px-4"},Mt={class:"w-10 h-10 bg-primary-600 rounded flex items-center justify-center mr-3"},jt={class:"text-white font-bold text-lg"},zt={class:"text-xl font-semibold text-gray-900 dark:text-white"},St={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},It={class:"text-white font-bold text-sm"},At={class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Bt=["d"],Vt={class:"flex flex-col h-full pt-5 pb-4 overflow-y-auto bg-white border-r border-gray-200 shadow-sm"},Pt={class:"flex items-center justify-between px-4 mb-4"},Dt={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center mr-3"},Ht={class:"text-white font-bold text-sm"},Et={class:"text-xl font-semibold text-gray-900"},Tt={__name:"AppSidebar",props:{isMobileOpen:{type:Boolean,default:!1}},emits:["close"],setup(a){const i=q(),d=$(!1),t=b(()=>i.config||{}),s=b(()=>{var u;return((u=t.value.company)==null?void 0:u.name)||"DatPortal"}),m=b(()=>s.value.split(" ").map(l=>l[0]).join("").toUpperCase().slice(0,2));function v(){d.value=!d.value}function p(){d.value&&(d.value=!1)}return(u,l)=>{const o=B("router-link");return r(),n("div",kt,[e("div",wt,[e("div",{class:j(["flex flex-col transition-all duration-300",[d.value?"w-20":"w-64"]])},[e("div",$t,[e("div",Ct,[e("div",{class:j(["flex items-center",{"justify-center":d.value}])},[f(o,{to:"/app/dashboard",class:j(["flex items-center",{hidden:d.value}])},{default:C(()=>[e("div",Mt,[e("span",jt,c(m.value),1)]),e("h3",zt,c(s.value),1)]),_:1},8,["class"]),f(o,{to:"/app/dashboard",class:j(["flex items-center justify-center",{hidden:!d.value}])},{default:C(()=>[e("div",St,[e("span",It,c(m.value),1)])]),_:1},8,["class"])],2),e("button",{onClick:v,class:"ml-auto text-gray-600 dark:text-gray-400 focus:outline-none hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded"},[(r(),n("svg",At,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:d.value?"M13 5l7 7-7 7M5 5l7 7-7 7":"M11 19l-7-7 7-7m8 14l-7-7 7-7"},null,8,Bt)]))])]),f(fe,{"is-collapsed":d.value,onItemClick:p},null,8,["is-collapsed"]),f(he,{"is-collapsed":d.value},null,8,["is-collapsed"])])],2)]),e("div",{class:j(["fixed inset-y-0 left-0 z-30 w-64 bg-primary-700 transform transition-transform duration-300 ease-in-out lg:hidden",a.isMobileOpen?"translate-x-0":"-translate-x-full"])},[e("div",Vt,[e("div",Pt,[f(o,{to:"/app/dashboard",class:"flex items-center"},{default:C(()=>[e("div",Dt,[e("span",Ht,c(m.value),1)]),e("h3",Et,c(s.value),1)]),_:1}),e("button",{onClick:l[0]||(l[0]=g=>u.$emit("close")),class:"p-2 rounded-md text-gray-600 hover:bg-gray-100"},l[2]||(l[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),f(fe,{"is-collapsed":!1,onItemClick:l[1]||(l[1]=g=>u.$emit("close"))}),f(he,{"is-collapsed":!1})])],2)])}}},Lt={class:"flex","aria-label":"Breadcrumb"},Nt={class:"flex items-center space-x-2 text-sm text-gray-500"},Rt={key:0,class:"mr-2"},Ut={class:"flex items-center"},Ft={key:0,class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},qt=["d"],Ot={key:2,class:"font-medium text-gray-900"},Kt={__name:"HeaderBreadcrumbs",props:{breadcrumbs:{type:Array,required:!0}},setup(a){return(i,d)=>{const t=B("router-link");return r(),n("nav",Lt,[e("ol",Nt,[(r(!0),n(P,null,D(a.breadcrumbs,(s,m)=>(r(),n("li",{key:m,class:"flex items-center"},[m>0?(r(),n("div",Rt,d[0]||(d[0]=[e("svg",{class:"h-3 w-3 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]))):_("",!0),s.to&&m<a.breadcrumbs.length-1?(r(),R(t,{key:1,to:s.to,class:"hover:text-gray-700 transition-colors duration-150"},{default:C(()=>[e("span",Ut,[s.icon?(r(),n("svg",Ft,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:s.icon},null,8,qt)])):_("",!0),M(" "+c(s.label),1)])]),_:2},1032,["to"])):(r(),n("span",Ot,c(s.label),1))]))),128))])])}}},Wt={class:"flex items-center space-x-2"},Gt={key:0,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Jt={key:1,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Yt={__name:"HeaderQuickActions",emits:["quick-create-project","quick-add-task"],setup(a){const i=te(),{isDarkMode:d,toggleDarkMode:t}=oe(),s=b(()=>{var v;return((v=i.name)==null?void 0:v.includes("projects"))||i.path.includes("/projects")}),m=b(()=>{var v,p;return((v=i.name)==null?void 0:v.includes("tasks"))||((p=i.name)==null?void 0:p.includes("projects"))||i.path.includes("/tasks")||i.path.includes("/projects")});return(v,p)=>(r(),n("div",Wt,[s.value?(r(),n("button",{key:0,onClick:p[0]||(p[0]=u=>v.$emit("quick-create-project")),class:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},p[3]||(p[3]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),M(" Nuovo Progetto ")]))):_("",!0),m.value?(r(),n("button",{key:1,onClick:p[1]||(p[1]=u=>v.$emit("quick-add-task")),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},p[4]||(p[4]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),M(" Nuovo Task ")]))):_("",!0),e("button",{onClick:p[2]||(p[2]=(...u)=>V(t)&&V(t)(...u)),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",title:"Cambia tema"},[V(d)?(r(),n("svg",Jt,p[6]||(p[6]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"},null,-1)]))):(r(),n("svg",Gt,p[5]||(p[5]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"},null,-1)])))])]))}},Qt={class:"relative"},Xt={class:"relative"},Zt={key:0,class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center"},es={class:"py-1"},ts={key:0,class:"px-4 py-8 text-center text-gray-500 text-sm"},ss={key:1,class:"max-h-64 overflow-y-auto"},os=["onClick"],rs={class:"flex items-start"},as={class:"flex-shrink-0"},ns={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},is=["d"],ls={class:"ml-3 flex-1"},ds={class:"text-sm font-medium text-gray-900"},cs={class:"text-xs text-gray-500 mt-1"},us={class:"text-xs text-gray-400 mt-1"},ms={key:0,class:"flex-shrink-0"},ps={key:2,class:"px-4 py-2 border-t border-gray-100"},gs={__name:"HeaderNotifications",setup(a){const i=$(!1),d=$([{id:1,type:"task",title:"Nuovo task assegnato",message:'Ti è stato assegnato un nuovo task nel progetto "Website Redesign"',created_at:new Date().toISOString(),read:!1},{id:2,type:"project",title:"Progetto completato",message:'Il progetto "Mobile App" è stato completato con successo',created_at:new Date(Date.now()-36e5).toISOString(),read:!0}]),t=b(()=>d.value.filter(l=>!l.read).length);function s(l){const o={task:"h-6 w-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center",project:"h-6 w-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center",user:"h-6 w-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center",system:"h-6 w-6 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center"};return o[l]||o.system}function m(l){const o={task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2",project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",user:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",system:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return o[l]||o.system}function v(l){const o=new Date(l),h=new Date-o;return h<6e4?"Adesso":h<36e5?`${Math.floor(h/6e4)}m fa`:h<864e5?`${Math.floor(h/36e5)}h fa`:o.toLocaleDateString("it-IT")}function p(l){l.read||(l.read=!0),i.value=!1}function u(){d.value.forEach(l=>l.read=!0)}return(l,o)=>(r(),n("div",Qt,[e("button",{onClick:o[0]||(o[0]=g=>i.value=!i.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[o[3]||(o[3]=e("span",{class:"sr-only"},"Visualizza notifiche",-1)),e("div",Xt,[o[2]||(o[2]=e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM10 21a2 2 0 01-2-2V7a7 7 0 1114 0v12a2 2 0 01-2 2H10z"})],-1)),t.value>0?(r(),n("span",Zt,c(t.value>9?"9+":t.value),1)):_("",!0)])]),i.value?(r(),n("div",{key:0,onClick:o[1]||(o[1]=g=>i.value=!1),class:"origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",es,[o[5]||(o[5]=e("div",{class:"px-4 py-2 border-b border-gray-100"},[e("h3",{class:"text-sm font-medium text-gray-900"},"Notifiche")],-1)),d.value.length===0?(r(),n("div",ts," Nessuna notifica ")):(r(),n("div",ss,[(r(!0),n(P,null,D(d.value,g=>(r(),n("div",{key:g.id,class:"px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-50 last:border-b-0",onClick:h=>p(g)},[e("div",rs,[e("div",as,[e("div",{class:j(s(g.type))},[(r(),n("svg",ns,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:m(g.type)},null,8,is)]))],2)]),e("div",ls,[e("p",ds,c(g.title),1),e("p",cs,c(g.message),1),e("p",us,c(v(g.created_at)),1)]),g.read?_("",!0):(r(),n("div",ms,o[4]||(o[4]=[e("div",{class:"h-2 w-2 bg-primary-500 rounded-full"},null,-1)])))])],8,os))),128))])),d.value.length>0?(r(),n("div",ps,[e("button",{onClick:u,class:"text-xs text-primary-600 hover:text-primary-800"}," Segna tutte come lette ")])):_("",!0)])])):_("",!0)]))}},vs={class:"relative"},fs={class:"flex items-start justify-center min-h-screen pt-16 px-4 pb-20 text-center sm:block sm:p-0"},hs={class:"inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"},xs={class:"flex items-center"},ys={class:"flex-1"},bs={key:0,class:"mt-4 max-h-64 overflow-y-auto"},_s={class:"space-y-1"},ks=["onClick"],ws={class:"flex-shrink-0"},$s={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Cs=["d"],Ms={class:"ml-3 flex-1 min-w-0"},js={class:"text-sm font-medium text-gray-900 truncate"},zs={class:"text-xs text-gray-500 truncate"},Ss={class:"ml-2 text-xs text-gray-400"},Is={key:1,class:"mt-4 text-center py-4"},As={key:2,class:"mt-4 text-center py-4"},Bs={__name:"HeaderSearch",setup(a){const i=G(),d=$(!1),t=$(""),s=$([]),m=$(-1),v=$(!1),p=$(null),u=[{id:1,type:"project",title:"Website Redesign",description:"Progetto di redesign del sito web aziendale",path:"/app/projects/1"},{id:2,type:"person",title:"Mario Rossi",description:"Senior Developer",path:"/app/personnel/directory/1"},{id:3,type:"document",title:"Specifiche Tecniche",description:"Documento delle specifiche del progetto mobile",path:"/app/documents/1"},{id:4,type:"task",title:"Implementazione API",description:"Task per l'implementazione delle API REST",path:"/app/projects/1/tasks/5"}];Q(d,async S=>{var k;S?(await be(),(k=p.value)==null||k.focus()):(t.value="",s.value=[],m.value=-1)});function l(){if(!t.value.trim()){s.value=[];return}v.value=!0,setTimeout(()=>{s.value=u.filter(S=>S.title.toLowerCase().includes(t.value.toLowerCase())||S.description.toLowerCase().includes(t.value.toLowerCase())),m.value=-1,v.value=!1},200)}function o(S){if(s.value.length===0)return;const k=m.value+S;k>=0&&k<s.value.length&&(m.value=k)}function g(){m.value>=0&&s.value[m.value]&&h(s.value[m.value])}function h(S){d.value=!1,i.push(S.path)}function w(S){const k={project:"h-6 w-6 rounded bg-blue-100 text-blue-600 flex items-center justify-center",person:"h-6 w-6 rounded bg-green-100 text-green-600 flex items-center justify-center",document:"h-6 w-6 rounded bg-yellow-100 text-yellow-600 flex items-center justify-center",task:"h-6 w-6 rounded bg-purple-100 text-purple-600 flex items-center justify-center"};return k[S]||k.document}function I(S){const k={project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",person:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",document:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"};return k[S]||k.document}function E(S){return{project:"Progetto",person:"Persona",document:"Documento",task:"Task"}[S]||"Elemento"}return(S,k)=>(r(),n("div",vs,[e("button",{onClick:k[0]||(k[0]=A=>d.value=!d.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},k[7]||(k[7]=[e("span",{class:"sr-only"},"Cerca",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),d.value?(r(),n("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:k[6]||(k[6]=se(A=>d.value=!1,["self"]))},[e("div",fs,[k[11]||(k[11]=e("div",{class:"fixed inset-0 bg-black bg-opacity-25 transition-opacity"},null,-1)),e("div",hs,[e("div",null,[e("div",xs,[e("div",ys,[N(e("input",{ref_key:"searchInput",ref:p,"onUpdate:modelValue":k[1]||(k[1]=A=>t.value=A),onInput:l,onKeydown:[k[2]||(k[2]=J(A=>d.value=!1,["escape"])),J(g,["enter"]),k[3]||(k[3]=J(A=>o(-1),["up"])),k[4]||(k[4]=J(A=>o(1),["down"]))],type:"text",placeholder:"Cerca progetti, persone, documenti...",class:"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"},null,544),[[K,t.value]])]),e("button",{onClick:k[5]||(k[5]=A=>d.value=!1),class:"ml-3 p-2 text-gray-400 hover:text-gray-600"},k[8]||(k[8]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),s.value.length>0?(r(),n("div",bs,[e("div",_s,[(r(!0),n(P,null,D(s.value,(A,W)=>(r(),n("div",{key:A.id,onClick:ae=>h(A),class:j(["flex items-center px-3 py-2 rounded-md cursor-pointer",W===m.value?"bg-primary-50":"hover:bg-gray-50"])},[e("div",ws,[e("div",{class:j(w(A.type))},[(r(),n("svg",$s,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:I(A.type)},null,8,Cs)]))],2)]),e("div",Ms,[e("p",js,c(A.title),1),e("p",zs,c(A.description),1)]),e("div",Ss,c(E(A.type)),1)],10,ks))),128))])])):t.value&&!v.value?(r(),n("div",Is,k[9]||(k[9]=[e("p",{class:"text-sm text-gray-500"},"Nessun risultato trovato",-1)]))):t.value?_("",!0):(r(),n("div",As,k[10]||(k[10]=[e("p",{class:"text-xs text-gray-400"},"Inizia a digitare per cercare...",-1)])))])])])])):_("",!0)]))}},Vs={class:"relative"},Ps={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},Ds={class:"text-sm font-medium text-primary-700"},Hs={class:"py-1"},Es={class:"px-4 py-2 border-b border-gray-100 dark:border-gray-700"},Ts={class:"text-sm font-medium text-gray-900 dark:text-white"},Ls={class:"text-xs text-gray-500 dark:text-gray-400"},Ns={__name:"HeaderUserMenu",setup(a){const i=G(),d=F(),t=$(!1),{isDarkMode:s,toggleDarkMode:m}=oe(),v=b(()=>d.user&&(d.user.name||d.user.username)||"Utente"),p=b(()=>{var o;return((o=d.user)==null?void 0:o.email)||""}),u=b(()=>d.user?v.value.charAt(0).toUpperCase():"U");async function l(){t.value=!1,await d.logout(),i.push("/auth/login")}return(o,g)=>{const h=B("router-link");return r(),n("div",Vs,[e("button",{onClick:g[0]||(g[0]=w=>t.value=!t.value),class:"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[g[5]||(g[5]=e("span",{class:"sr-only"},"Apri menu utente",-1)),e("div",Ps,[e("span",Ds,c(u.value),1)])]),t.value?(r(),n("div",{key:0,onClick:g[4]||(g[4]=w=>t.value=!1),class:"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50"},[e("div",Hs,[e("div",Es,[e("p",Ts,c(v.value),1),e("p",Ls,c(p.value),1)]),f(h,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:g[1]||(g[1]=w=>t.value=!1)},{default:C(()=>g[6]||(g[6]=[M(" Il tuo profilo ")])),_:1,__:[6]}),f(h,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:g[2]||(g[2]=w=>t.value=!1)},{default:C(()=>g[7]||(g[7]=[M(" Impostazioni ")])),_:1,__:[7]}),g[8]||(g[8]=e("div",{class:"border-t border-gray-100 dark:border-gray-700 my-1"},null,-1)),e("button",{onClick:g[3]||(g[3]=(...w)=>V(m)&&V(m)(...w)),class:"flex items-center justify-between w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"},[e("span",null,c(V(s)?"Modalità chiara":"Modalità scura"),1),e("i",{class:j([V(s)?"fas fa-sun":"fas fa-moon","text-xs"])},null,2)]),e("button",{onClick:l,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}," Esci ")])])):_("",!0)])}}},Rs={class:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"},Us={class:"flex justify-between items-center px-4 py-4 sm:px-6 lg:px-8"},Fs={class:"flex items-center space-x-4"},qs={class:"flex flex-col"},Os={class:"text-lg font-semibold text-gray-900 dark:text-white"},Ks={class:"flex items-center space-x-4"},Ws={class:"hidden md:flex items-center space-x-2"},Gs={__name:"AppHeader",props:{pageTitle:{type:String,required:!0},breadcrumbs:{type:Array,default:()=>[]}},emits:["toggle-mobile-sidebar"],setup(a){return(i,d)=>(r(),n("header",Rs,[e("div",Us,[e("div",Fs,[e("button",{onClick:d[0]||(d[0]=t=>i.$emit("toggle-mobile-sidebar")),class:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700"},d[1]||(d[1]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)])),e("div",qs,[e("h2",Os,c(a.pageTitle),1),a.breadcrumbs.length>0?(r(),R(Kt,{key:0,breadcrumbs:a.breadcrumbs},null,8,["breadcrumbs"])):_("",!0)])]),e("div",Ks,[e("div",Ws,[f(Yt)]),f(gs),f(Bs),f(Ns)])])]))}},Js={__name:"LoadingSpinner",props:{size:{type:String,default:"md",validator:a=>["sm","md","lg","xl"].includes(a)},message:{type:String,default:""},centered:{type:Boolean,default:!0}},setup(a){const i=a,d=b(()=>{const v={sm:"20px",md:"32px",lg:"48px",xl:"64px"};return`width: ${v[i.size]}; height: ${v[i.size]};`}),t=b(()=>["flex",i.centered?"items-center justify-center":"","space-y-2"]),s=b(()=>["flex items-center justify-center"]),m=b(()=>["text-sm text-gray-600 text-center"]);return(v,p)=>(r(),n("div",{class:j(t.value)},[e("div",{class:j(s.value)},[e("div",{class:"animate-spin rounded-full border-2 border-gray-300 border-t-primary-600",style:_e(d.value)},null,4)],2),a.message?(r(),n("p",{key:0,class:j(m.value)},c(a.message),3)):_("",!0)],2))}},X=(a,i)=>{const d=a.__vccOpts||a;for(const[t,s]of i)d[t]=s;return d},Ys={class:"fixed bottom-0 right-0 z-50 p-6 space-y-4"},Qs={class:"p-4"},Xs={class:"flex items-start"},Zs={class:"flex-shrink-0"},eo={class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},to=["d"],so={class:"ml-3 w-0 flex-1 pt-0.5"},oo={class:"text-sm font-medium text-gray-900"},ro={class:"mt-1 text-sm text-gray-500"},ao={class:"ml-4 flex-shrink-0 flex"},no=["onClick"],io={__name:"NotificationManager",setup(a){const i=$([]);function d(p){const u=Date.now(),l={id:u,type:p.type||"info",title:p.title,message:p.message,duration:p.duration||5e3};i.value.push(l),l.duration>0&&setTimeout(()=>{t(u)},l.duration)}function t(p){const u=i.value.findIndex(l=>l.id===p);u>-1&&i.value.splice(u,1)}function s(p){const u={success:"border-l-4 border-green-400",error:"border-l-4 border-red-400",warning:"border-l-4 border-yellow-400",info:"border-l-4 border-blue-400"};return u[p]||u.info}function m(p){const u={success:"h-8 w-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center",error:"h-8 w-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center",warning:"h-8 w-8 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center",info:"h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center"};return u[p]||u.info}function v(p){const u={success:"M5 13l4 4L19 7",error:"M6 18L18 6M6 6l12 12",warning:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-2.694-.833-3.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z",info:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return u[p]||u.info}return window.showNotification=d,U(()=>{}),(p,u)=>(r(),n("div",Ys,[f(Be,{name:"notification",tag:"div",class:"space-y-4"},{default:C(()=>[(r(!0),n(P,null,D(i.value,l=>(r(),n("div",{key:l.id,class:j([s(l.type),"max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"])},[e("div",Qs,[e("div",Xs,[e("div",Zs,[e("div",{class:j(m(l.type))},[(r(),n("svg",eo,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:v(l.type)},null,8,to)]))],2)]),e("div",so,[e("p",oo,c(l.title),1),e("p",ro,c(l.message),1)]),e("div",ao,[e("button",{onClick:o=>t(l.id),class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},u[0]||(u[0]=[e("span",{class:"sr-only"},"Chiudi",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,no)])])])],2))),128))]),_:1})]))}},lo=X(io,[["__scopeId","data-v-220f0827"]]),co={class:"h-screen flex bg-gray-50 dark:bg-gray-900"},uo={class:"flex flex-col flex-1 overflow-hidden lg:ml-64"},mo={class:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900"},po={class:"py-6"},go={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},vo={key:0,class:"mb-6"},fo={key:1,class:"flex items-center justify-center h-64"},ho={__name:"AppLayout",setup(a){const i=te(),d=q(),t=$(!1),s=$(!1);b(()=>d.config||{});const m=b(()=>d.config!==null),v=b(()=>{var h;return(h=i.meta)!=null&&h.title?i.meta.title:{dashboard:"Dashboard",projects:"Progetti","projects-list":"Elenco Progetti","projects-view":"Dettaglio Progetto","projects-create":"Nuovo Progetto",personnel:"Personale","personnel-directory":"Rubrica Aziendale","personnel-orgchart":"Organigramma","personnel-skills":"Competenze"}[i.name]||"DatPortal"}),p=b(()=>{var g;return(g=i.meta)!=null&&g.breadcrumbs?i.meta.breadcrumbs.map(h=>({label:h.label,to:h.to,icon:h.icon})):[]}),u=b(()=>{var g;return((g=i.meta)==null?void 0:g.hasActions)||!1});function l(){t.value=!t.value}function o(){t.value=!1}return Q(i,()=>{s.value=!0,setTimeout(()=>{s.value=!1},300)}),Q(i,()=>{o()}),U(()=>{m.value||d.loadConfig()}),(g,h)=>{const w=B("router-view");return r(),n("div",co,[t.value?(r(),n("div",{key:0,onClick:o,class:"fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"})):_("",!0),f(Tt,{"is-mobile-open":t.value,onClose:o},null,8,["is-mobile-open"]),e("div",uo,[f(Gs,{"page-title":v.value,breadcrumbs:p.value,onToggleMobileSidebar:l},null,8,["page-title","breadcrumbs"]),e("main",mo,[e("div",po,[e("div",go,[u.value?(r(),n("div",vo,[Ve(g.$slots,"page-actions")])):_("",!0),s.value?(r(),n("div",fo,[f(Js)])):(r(),R(w,{key:2}))])])])]),f(lo)])}}},xo={class:"min-h-screen bg-gray-50"},yo={class:"bg-white shadow-sm border-b"},bo={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},_o={class:"flex justify-between h-16"},ko={class:"flex items-center"},wo={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},$o={class:"text-white font-bold text-sm"},Co={class:"text-xl font-semibold text-gray-900"},Mo={class:"hidden md:flex items-center space-x-8"},jo={class:"flex items-center space-x-4 ml-8 pl-8 border-l border-gray-200"},zo={class:"md:hidden flex items-center"},So={key:0,class:"md:hidden"},Io={class:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t"},Ao={class:"bg-gray-800 text-white"},Bo={class:"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8"},Vo={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},Po={class:"col-span-1 md:col-span-2"},Do={class:"flex items-center space-x-3 mb-4"},Ho={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Eo={class:"text-white font-bold text-sm"},To={class:"text-xl font-semibold"},Lo={class:"text-gray-300 max-w-md"},No={class:"space-y-2"},Ro={class:"space-y-2 text-gray-300"},Uo={key:0},Fo={key:1},qo={key:2},Oo={class:"mt-8 pt-8 border-t border-gray-700 text-center text-gray-400"},xe={__name:"PublicLayout",setup(a){const i=q(),d=$(!1),t=b(()=>i.config||{}),s=b(()=>{var u;return((u=t.value.company)==null?void 0:u.name)||"DatVinci"}),m=b(()=>s.value.split(" ").map(l=>l[0]).join("").toUpperCase().slice(0,2)),v=b(()=>i.config!==null),p=new Date().getFullYear();return U(()=>{v.value||i.loadConfig()}),(u,l)=>{var h,w,I,E,S,k;const o=B("router-link"),g=B("router-view");return r(),n("div",xo,[e("nav",yo,[e("div",bo,[e("div",_o,[e("div",ko,[f(o,{to:"/",class:"flex items-center space-x-3"},{default:C(()=>[e("div",wo,[e("span",$o,c(m.value),1)]),e("span",Co,c(s.value),1)]),_:1})]),e("div",Mo,[f(o,{to:"/",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:C(()=>l[1]||(l[1]=[M(" Home ")])),_:1,__:[1]}),f(o,{to:"/about",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:C(()=>l[2]||(l[2]=[M(" Chi Siamo ")])),_:1,__:[2]}),f(o,{to:"/services",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:C(()=>l[3]||(l[3]=[M(" Servizi ")])),_:1,__:[3]}),f(o,{to:"/contact",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:C(()=>l[4]||(l[4]=[M(" Contatti ")])),_:1,__:[4]}),e("div",jo,[f(o,{to:"/auth/login",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:C(()=>l[5]||(l[5]=[M(" Accedi ")])),_:1,__:[5]}),f(o,{to:"/auth/register",class:"bg-primary-600 text-white hover:bg-primary-700 px-4 py-2 rounded-md text-sm font-medium"},{default:C(()=>l[6]||(l[6]=[M(" Registrati ")])),_:1,__:[6]})])]),e("div",zo,[e("button",{onClick:l[0]||(l[0]=A=>d.value=!d.value),class:"text-gray-400 hover:text-gray-500"},l[7]||(l[7]=[e("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)]))])])]),d.value?(r(),n("div",So,[e("div",Io,[f(o,{to:"/",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:C(()=>l[8]||(l[8]=[M(" Home ")])),_:1,__:[8]}),f(o,{to:"/about",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:C(()=>l[9]||(l[9]=[M(" Chi Siamo ")])),_:1,__:[9]}),f(o,{to:"/services",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:C(()=>l[10]||(l[10]=[M(" Servizi ")])),_:1,__:[10]}),f(o,{to:"/contact",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:C(()=>l[11]||(l[11]=[M(" Contatti ")])),_:1,__:[11]}),l[14]||(l[14]=e("hr",{class:"my-2"},null,-1)),f(o,{to:"/auth/login",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:C(()=>l[12]||(l[12]=[M(" Accedi ")])),_:1,__:[12]}),f(o,{to:"/auth/register",class:"block px-3 py-2 text-base font-medium bg-primary-600 text-white rounded-md"},{default:C(()=>l[13]||(l[13]=[M(" Registrati ")])),_:1,__:[13]})])])):_("",!0)]),e("main",null,[f(g)]),e("footer",Ao,[e("div",Bo,[e("div",Vo,[e("div",Po,[e("div",Do,[e("div",Ho,[e("span",Eo,c(m.value),1)]),e("span",To,c(s.value),1)]),e("p",Lo,c(V(i).interpolateText((h=t.value.footer)==null?void 0:h.description)||((w=t.value.company)==null?void 0:w.description)||"Innovazione e tecnologia per il futuro digitale della tua azienda."),1)]),e("div",null,[l[19]||(l[19]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Link Rapidi",-1)),e("ul",No,[e("li",null,[f(o,{to:"/",class:"text-gray-300 hover:text-white"},{default:C(()=>l[15]||(l[15]=[M("Home")])),_:1,__:[15]})]),e("li",null,[f(o,{to:"/about",class:"text-gray-300 hover:text-white"},{default:C(()=>l[16]||(l[16]=[M("Chi Siamo")])),_:1,__:[16]})]),e("li",null,[f(o,{to:"/services",class:"text-gray-300 hover:text-white"},{default:C(()=>l[17]||(l[17]=[M("Servizi")])),_:1,__:[17]})]),e("li",null,[f(o,{to:"/contact",class:"text-gray-300 hover:text-white"},{default:C(()=>l[18]||(l[18]=[M("Contatti")])),_:1,__:[18]})])])]),e("div",null,[l[20]||(l[20]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Contatti",-1)),e("ul",Ro,[(I=t.value.contact)!=null&&I.email?(r(),n("li",Uo,c(t.value.contact.email),1)):_("",!0),(E=t.value.contact)!=null&&E.phone?(r(),n("li",Fo,c(t.value.contact.phone),1)):_("",!0),(S=t.value.contact)!=null&&S.address?(r(),n("li",qo,c(t.value.contact.address),1)):_("",!0)])])]),e("div",Oo,[e("p",null,c(V(i).interpolateText((k=t.value.footer)==null?void 0:k.copyright)||`© ${V(p)} ${s.value}. Tutti i diritti riservati.`),1)])])])])}}},Ko={class:"bg-white"},Wo={class:"relative overflow-hidden"},Go={class:"max-w-7xl mx-auto"},Jo={class:"relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32"},Yo={class:"mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28"},Qo={class:"sm:text-center lg:text-left"},Xo={class:"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl"},Zo={class:"block xl:inline"},er={class:"mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0"},tr={class:"mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start"},sr={class:"rounded-md shadow"},or={class:"mt-3 sm:mt-0 sm:ml-3"},rr={class:"py-12 bg-white"},ar={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},nr={class:"lg:text-center"},ir={class:"text-base text-primary-600 font-semibold tracking-wide uppercase"},lr={class:"mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl"},dr={key:0,class:"mt-10"},cr={class:"space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10"},ur={class:"absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white"},mr={class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},pr={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},gr={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},vr={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},fr={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"},hr={class:"ml-16 text-lg leading-6 font-medium text-gray-900"},xr={class:"mt-2 ml-16 text-base text-gray-500"},yr={__name:"Home",setup(a){const i=q(),d=b(()=>i.config||{}),t=b(()=>{var m;return((m=d.value.pages)==null?void 0:m.home)||{}}),s=b(()=>d.value.company||{});return U(()=>{i.config||i.loadConfig()}),(m,v)=>{var u,l,o,g;const p=B("router-link");return r(),n("div",Ko,[e("div",Wo,[e("div",Go,[e("div",Jo,[e("main",Yo,[e("div",Qo,[e("h1",Xo,[e("span",Zo,c(((u=t.value.hero)==null?void 0:u.title)||"Innovazione per il futuro"),1)]),e("p",er,c(((l=t.value.hero)==null?void 0:l.subtitle)||V(i).interpolateText(s.value.description)||"Supportiamo le aziende nel loro percorso di crescita attraverso soluzioni tecnologiche all'avanguardia"),1),e("div",tr,[e("div",sr,[f(p,{to:"/services",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 md:py-4 md:text-lg md:px-10"},{default:C(()=>{var h;return[M(c(((h=t.value.hero)==null?void 0:h.cta_primary)||"Scopri i nostri servizi"),1)]}),_:1})]),e("div",or,[f(p,{to:"/contact",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 md:py-4 md:text-lg md:px-10"},{default:C(()=>{var h;return[M(c(((h=t.value.hero)==null?void 0:h.cta_secondary)||"Contattaci"),1)]}),_:1})])])])])])]),v[0]||(v[0]=e("div",{class:"lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2"},[e("div",{class:"h-56 w-full bg-gradient-to-r from-primary-400 to-primary-600 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center"},[e("svg",{class:"h-24 w-24 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])])],-1))]),e("div",rr,[e("div",ar,[e("div",nr,[e("h2",ir,c(((o=t.value.services_section)==null?void 0:o.title)||"I nostri servizi"),1),e("p",lr,c(((g=t.value.services_section)==null?void 0:g.subtitle)||"Soluzioni innovative per ogni esigenza aziendale"),1)]),s.value.platform_features?(r(),n("div",dr,[e("div",cr,[(r(!0),n(P,null,D(s.value.platform_features,h=>(r(),n("div",{key:h.title,class:"relative"},[e("div",ur,[(r(),n("svg",mr,[h.icon==="briefcase"?(r(),n("path",pr)):h.icon==="users"?(r(),n("path",gr)):h.icon==="chart"?(r(),n("path",vr)):(r(),n("path",fr))]))]),e("p",hr,c(h.title),1),e("p",xr,c(h.description),1)]))),128))])])):_("",!0)])])])}}},br={class:"py-16 bg-white"},_r={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},kr={class:"text-center"},wr={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},$r={class:"mt-4 text-xl text-gray-600"},Cr={key:0,class:"mt-16"},Mr={class:"max-w-3xl mx-auto"},jr={class:"text-3xl font-bold text-gray-900 text-center mb-8"},zr={class:"text-lg text-gray-700 leading-relaxed"},Sr={class:"mt-16 grid grid-cols-1 md:grid-cols-2 gap-12"},Ir={key:0,class:"bg-gray-50 p-8 rounded-lg"},Ar={class:"text-2xl font-bold text-gray-900 mb-4"},Br={class:"text-gray-700"},Vr={key:1,class:"bg-gray-50 p-8 rounded-lg"},Pr={class:"text-2xl font-bold text-gray-900 mb-4"},Dr={class:"text-gray-700"},Hr={key:1,class:"mt-16"},Er={class:"text-center mb-12"},Tr={class:"text-3xl font-bold text-gray-900"},Lr={class:"mt-4 text-xl text-gray-600"},Nr={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Rr={class:"text-lg font-semibold text-gray-900"},Ur={key:2,class:"mt-16"},Fr={class:"text-center"},qr={class:"text-3xl font-bold text-gray-900"},Or={class:"mt-4 text-xl text-gray-600"},Kr={class:"mt-8 inline-flex items-center px-6 py-3 bg-primary-50 rounded-lg"},Wr={class:"text-primary-900 font-medium"},Gr={__name:"About",setup(a){const i=q(),d=b(()=>i.config||{}),t=b(()=>{var m;return((m=d.value.pages)==null?void 0:m.about)||{}}),s=b(()=>d.value.company||{});return U(()=>{i.config||i.loadConfig()}),(m,v)=>{var p,u;return r(),n("div",br,[e("div",_r,[e("div",kr,[e("h1",wr,c(((p=t.value.hero)==null?void 0:p.title)||"Chi Siamo"),1),e("p",$r,c(((u=t.value.hero)==null?void 0:u.subtitle)||"La nostra storia e i nostri valori"),1)]),t.value.story_section?(r(),n("div",Cr,[e("div",Mr,[e("h2",jr,c(t.value.story_section.title),1),e("p",zr,c(V(i).interpolateText(t.value.story_section.content)),1)])])):_("",!0),e("div",Sr,[t.value.mission_section?(r(),n("div",Ir,[e("h3",Ar,c(t.value.mission_section.title),1),e("p",Br,c(V(i).interpolateText(t.value.mission_section.content)),1)])):_("",!0),t.value.vision_section?(r(),n("div",Vr,[e("h3",Pr,c(t.value.vision_section.title),1),e("p",Dr,c(V(i).interpolateText(t.value.vision_section.content)),1)])):_("",!0)]),t.value.expertise_section&&s.value.expertise?(r(),n("div",Hr,[e("div",Er,[e("h2",Tr,c(t.value.expertise_section.title),1),e("p",Lr,c(t.value.expertise_section.subtitle),1)]),e("div",Nr,[(r(!0),n(P,null,D(s.value.expertise,l=>(r(),n("div",{key:l,class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200 text-center"},[v[0]||(v[0]=e("div",{class:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-6 h-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Rr,c(l),1)]))),128))])])):_("",!0),t.value.team_section?(r(),n("div",Ur,[e("div",Fr,[e("h2",qr,c(t.value.team_section.title),1),e("p",Or,c(t.value.team_section.subtitle),1),e("div",Kr,[v[1]||(v[1]=e("svg",{class:"w-5 h-5 text-primary-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),e("span",Wr,c(s.value.team_size),1)])])])):_("",!0)])])}}},Jr={class:"py-16 bg-white"},Yr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Qr={class:"text-center"},Xr={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Zr={class:"mt-4 text-xl text-gray-600"},ea={key:0,class:"mt-8 text-center"},ta={class:"text-lg text-gray-700 max-w-3xl mx-auto"},sa={class:"mt-16 grid grid-cols-1 lg:grid-cols-2 gap-16"},oa={key:0},ra={class:"text-2xl font-bold text-gray-900 mb-8"},aa={class:"block text-sm font-medium text-gray-700 mb-2"},na={class:"block text-sm font-medium text-gray-700 mb-2"},ia={class:"block text-sm font-medium text-gray-700 mb-2"},la=["disabled"],da={key:1},ca={class:"text-2xl font-bold text-gray-900 mb-8"},ua={class:"space-y-6"},ma={key:0,class:"flex items-start"},pa={class:"font-medium text-gray-900"},ga={class:"text-gray-600"},va={key:1,class:"flex items-start"},fa={class:"font-medium text-gray-900"},ha={class:"text-gray-600"},xa={key:2,class:"flex items-start"},ya={class:"font-medium text-gray-900"},ba={class:"text-gray-600"},_a={key:3,class:"flex items-start"},ka={class:"font-medium text-gray-900"},wa={class:"text-gray-600"},$a={__name:"Contact",setup(a){const i=q(),d=b(()=>i.config||{}),t=b(()=>{var l;return((l=d.value.pages)==null?void 0:l.contact)||{}}),s=b(()=>d.value.contact||{}),m=$({name:"",email:"",message:""}),v=$(!1),p=$({text:"",type:""}),u=async()=>{var l,o;if(!m.value.name||!m.value.email||!m.value.message){p.value={text:((l=t.value.form)==null?void 0:l.error_message)||"Tutti i campi sono obbligatori",type:"error"};return}v.value=!0,p.value={text:"",type:""};try{await new Promise(g=>setTimeout(g,1e3)),p.value={text:((o=t.value.form)==null?void 0:o.success_message)||"Messaggio inviato con successo!",type:"success"},m.value={name:"",email:"",message:""}}catch{p.value={text:"Errore durante l'invio. Riprova più tardi.",type:"error"}}finally{v.value=!1}};return U(()=>{i.config||i.loadConfig()}),(l,o)=>{var g,h;return r(),n("div",Jr,[e("div",Yr,[e("div",Qr,[e("h1",Xr,c(((g=t.value.hero)==null?void 0:g.title)||"Contattaci"),1),e("p",Zr,c(((h=t.value.hero)==null?void 0:h.subtitle)||"Siamo qui per aiutarti"),1)]),t.value.intro?(r(),n("div",ea,[e("p",ta,c(t.value.intro.content),1)])):_("",!0),e("div",sa,[t.value.form?(r(),n("div",oa,[e("h2",ra,c(t.value.form.title),1),e("form",{onSubmit:se(u,["prevent"]),class:"space-y-6"},[e("div",null,[e("label",aa,c(t.value.form.name_label),1),N(e("input",{"onUpdate:modelValue":o[0]||(o[0]=w=>m.value.name=w),type:"text",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[K,m.value.name]])]),e("div",null,[e("label",na,c(t.value.form.email_label),1),N(e("input",{"onUpdate:modelValue":o[1]||(o[1]=w=>m.value.email=w),type:"email",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[K,m.value.email]])]),e("div",null,[e("label",ia,c(t.value.form.message_label),1),N(e("textarea",{"onUpdate:modelValue":o[2]||(o[2]=w=>m.value.message=w),rows:"6",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[K,m.value.message]])]),e("button",{type:"submit",disabled:v.value,class:"w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50"},c(v.value?"Invio in corso...":t.value.form.submit_button),9,la),p.value.text?(r(),n("div",{key:0,class:j([p.value.type==="success"?"text-green-600":"text-red-600","text-sm mt-2"])},c(p.value.text),3)):_("",!0)],32)])):_("",!0),t.value.info?(r(),n("div",da,[e("h2",ca,c(t.value.info.title),1),e("div",ua,[s.value.address?(r(),n("div",ma,[o[3]||(o[3]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),e("div",null,[e("h3",pa,c(t.value.info.address_label),1),e("p",ga,c(s.value.address),1)])])):_("",!0),s.value.phone?(r(),n("div",va,[o[4]||(o[4]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})],-1)),e("div",null,[e("h3",fa,c(t.value.info.phone_label),1),e("p",ha,c(s.value.phone),1)])])):_("",!0),s.value.email?(r(),n("div",xa,[o[5]||(o[5]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1)),e("div",null,[e("h3",ya,c(t.value.info.email_label),1),e("p",ba,c(s.value.email),1)])])):_("",!0),s.value.hours?(r(),n("div",_a,[o[6]||(o[6]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",null,[e("h3",ka,c(t.value.info.hours_label),1),e("p",wa,c(s.value.hours),1)])])):_("",!0)])])):_("",!0)])])])}}},Ca={class:"py-16 bg-white"},Ma={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ja={class:"text-center"},za={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Sa={class:"mt-4 text-xl text-gray-600"},Ia={key:0,class:"mt-8 text-center"},Aa={class:"text-lg text-gray-700 max-w-3xl mx-auto"},Ba={key:1,class:"mt-16"},Va={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Pa={class:"text-xl font-bold text-gray-900 text-center mb-4"},Da={class:"text-gray-600 text-center"},Ha={key:2,class:"mt-20"},Ea={class:"bg-primary-50 rounded-2xl p-12 text-center"},Ta={class:"text-3xl font-bold text-gray-900 mb-4"},La={class:"text-xl text-gray-600 mb-8"},Na={__name:"Services",setup(a){const i=q(),d=b(()=>i.config||{}),t=b(()=>{var v;return((v=d.value.pages)==null?void 0:v.services)||{}}),s=b(()=>d.value.company||{}),m=v=>({"Sviluppo Software":"Soluzioni software personalizzate per ottimizzare i processi aziendali e migliorare l'efficienza operativa.","Intelligenza Artificiale":"Implementazione di sistemi AI avanzati per automatizzare processi e analizzare dati complessi.","Consulenza IT":"Consulenza strategica per la trasformazione digitale e l'ottimizzazione dell'infrastruttura tecnologica.","Gestione Progetti Innovativi":"Coordinamento e gestione di progetti tecnologici complessi con metodologie agili.","Supporto su Bandi e Finanziamenti":"Assistenza nella ricerca e gestione di bandi pubblici e finanziamenti per l'innovazione."})[v]||"Servizio professionale di alta qualità per supportare la crescita della tua azienda.";return U(()=>{i.config||i.loadConfig()}),(v,p)=>{var l,o;const u=B("router-link");return r(),n("div",Ca,[e("div",Ma,[e("div",ja,[e("h1",za,c(((l=t.value.hero)==null?void 0:l.title)||"I nostri servizi"),1),e("p",Sa,c(((o=t.value.hero)==null?void 0:o.subtitle)||"Soluzioni complete per la tua azienda"),1)]),t.value.intro?(r(),n("div",Ia,[e("p",Aa,c(t.value.intro.content),1)])):_("",!0),s.value.expertise?(r(),n("div",Ba,[e("div",Va,[(r(!0),n(P,null,D(s.value.expertise,g=>(r(),n("div",{key:g,class:"bg-white p-8 rounded-lg shadow-lg border border-gray-200 hover:shadow-xl transition-shadow"},[p[0]||(p[0]=e("div",{class:"w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-6"},[e("svg",{class:"w-8 h-8 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Pa,c(g),1),e("p",Da,c(m(g)),1)]))),128))])])):_("",!0),t.value.cta?(r(),n("div",Ha,[e("div",Ea,[e("h2",Ta,c(t.value.cta.title),1),e("p",La,c(t.value.cta.subtitle),1),f(u,{to:"/contact",class:"inline-flex items-center px-8 py-4 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"},{default:C(()=>[M(c(t.value.cta.button)+" ",1),p[1]||(p[1]=e("svg",{class:"ml-2 w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 8l4 4m0 0l-4 4m4-4H3"})],-1))]),_:1,__:[1]})])])):_("",!0)])])}}},Ra={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},Ua={class:"max-w-md w-full space-y-8"},Fa={class:"mt-2 text-center text-sm text-gray-600"},qa={key:0,class:"rounded-md bg-red-50 p-4"},Oa={class:"text-sm text-red-700"},Ka={class:"rounded-md shadow-sm -space-y-px"},Wa={class:"flex items-center justify-between"},Ga={class:"flex items-center"},Ja=["disabled"],Ya={key:0,class:"absolute left-0 inset-y-0 flex items-center pl-3"},Qa={__name:"Login",setup(a){const i=G(),d=F(),t=$({username:"",password:"",remember:!1}),s=b(()=>d.loading),m=b(()=>d.error);async function v(){(await d.login({username:t.value.username,password:t.value.password,remember:t.value.remember})).success&&i.push("/app/dashboard")}return(p,u)=>{const l=B("router-link");return r(),n("div",Ra,[e("div",Ua,[e("div",null,[u[5]||(u[5]=e("div",{class:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100"},[e("svg",{class:"h-6 w-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])],-1)),u[6]||(u[6]=e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Accedi al tuo account ",-1)),e("p",Fa,[u[4]||(u[4]=M(" Oppure ")),f(l,{to:"/auth/register",class:"font-medium text-primary-600 hover:text-primary-500"},{default:C(()=>u[3]||(u[3]=[M(" registrati per un nuovo account ")])),_:1,__:[3]})])]),e("form",{onSubmit:se(v,["prevent"]),class:"mt-8 space-y-6"},[m.value?(r(),n("div",qa,[e("div",Oa,c(m.value),1)])):_("",!0),e("div",Ka,[e("div",null,[u[7]||(u[7]=e("label",{for:"username",class:"sr-only"},"Username",-1)),N(e("input",{id:"username","onUpdate:modelValue":u[0]||(u[0]=o=>t.value.username=o),name:"username",type:"text",autocomplete:"username",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Username"},null,512),[[K,t.value.username]])]),e("div",null,[u[8]||(u[8]=e("label",{for:"password",class:"sr-only"},"Password",-1)),N(e("input",{id:"password","onUpdate:modelValue":u[1]||(u[1]=o=>t.value.password=o),name:"password",type:"password",autocomplete:"current-password",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Password"},null,512),[[K,t.value.password]])])]),e("div",Wa,[e("div",Ga,[N(e("input",{id:"remember-me","onUpdate:modelValue":u[2]||(u[2]=o=>t.value.remember=o),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[Pe,t.value.remember]]),u[9]||(u[9]=e("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Ricordami ",-1))]),u[10]||(u[10]=e("div",{class:"text-sm"},[e("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500"}," Password dimenticata? ")],-1))]),e("div",null,[e("button",{type:"submit",disabled:s.value,class:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"},[s.value?(r(),n("span",Ya,u[11]||(u[11]=[e("svg",{class:"h-5 w-5 text-primary-500 animate-spin",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)]))):_("",!0),M(" "+c(s.value?"Accesso in corso...":"Accedi"),1)],8,Ja)])],32)])])}}},Xa={},Za={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"};function en(a,i){return r(),n("div",Za,i[0]||(i[0]=[e("div",{class:"max-w-md w-full space-y-8"},[e("div",null,[e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Registra un nuovo account ")]),e("div",{class:"text-center text-gray-600"}," Registrazione in arrivo... ")],-1)]))}const tn=X(Xa,[["render",en]]),sn={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},on={class:"p-5"},rn={class:"flex items-center"},an={class:"ml-5 w-0 flex-1"},nn={class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},ln={class:"text-lg font-medium text-gray-900 dark:text-white"},dn={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},cn={key:0,class:"bg-gray-50 dark:bg-gray-700 px-5 py-3"},un={class:"text-sm"},Y={__name:"StatsCard",props:{title:{type:String,required:!0},value:{type:[Number,String],required:!0},subtitle:{type:String,default:null},icon:{type:String,required:!0},color:{type:String,default:"primary"},link:{type:String,default:null}},setup(a){const i=t=>t==="project"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`}:t==="users"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>`}:t==="clock"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}:t==="team"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
      </svg>`}:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>`},d=t=>{const s={primary:"bg-primary-500",secondary:"bg-secondary-500",red:"bg-red-500",yellow:"bg-yellow-500",blue:"bg-blue-500",green:"bg-green-500"};return s[t]||s.primary};return(t,s)=>{const m=B("router-link");return r(),n("div",sn,[e("div",on,[e("div",rn,[e("div",{class:j(["flex-shrink-0 rounded-md p-3",d(a.color)])},[(r(),R(ke(i(a.icon)),{class:"h-6 w-6 text-white"}))],2),e("div",an,[e("dl",null,[e("dt",nn,c(a.title),1),e("dd",null,[e("div",ln,c(a.value),1),a.subtitle?(r(),n("div",dn,c(a.subtitle),1)):_("",!0)])])])])]),a.link?(r(),n("div",cn,[e("div",un,[f(m,{to:a.link,class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300"},{default:C(()=>s[0]||(s[0]=[M(" Vedi tutti ")])),_:1,__:[0]},8,["to"])])])):_("",!0)])}}},mn={class:"py-6"},pn={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},gn={class:"mt-4 md:mt-0 flex space-x-3"},vn={class:"relative"},fn=["disabled"],hn={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},xn={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},yn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},bn={class:"relative h-64"},_n={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},kn={class:"relative h-64"},wn={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},$n={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Cn={class:"p-6"},Mn={key:0,class:"text-center py-8 text-gray-500"},jn={key:1,class:"space-y-4"},zn={class:"flex justify-between items-start"},Sn={class:"flex-1"},In={class:"text-sm font-medium text-gray-900 dark:text-white"},An={class:"text-xs text-gray-500 dark:text-gray-400"},Bn={class:"mt-2 flex justify-between items-center"},Vn={class:"text-xs text-gray-500 dark:text-gray-400"},Pn={class:"bg-gray-50 dark:bg-gray-700 px-6 py-3"},Dn={class:"text-sm"},Hn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},En={class:"p-6"},Tn={key:0,class:"text-center py-8 text-gray-500"},Ln={key:1,class:"space-y-4"},Nn={class:"flex-shrink-0"},Rn={class:"flex-1 min-w-0"},Un={class:"text-sm font-medium text-gray-900 dark:text-white"},Fn={class:"text-xs text-gray-500 dark:text-gray-400"},qn={class:"text-xs text-gray-400 dark:text-gray-500"},On={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Kn={class:"p-6"},Wn={key:0,class:"text-center py-8 text-gray-500"},Gn={key:1,class:"space-y-4"},Jn={class:"flex justify-between items-start"},Yn={class:"flex-1"},Qn={class:"text-sm font-medium text-gray-900 dark:text-white"},Xn={class:"text-xs text-gray-500 dark:text-gray-400"},Zn={class:"text-right"},ei={class:"text-sm font-bold text-gray-900 dark:text-white"},ti={class:"text-xs text-gray-500"},si={class:"mt-2"},oi={class:"w-full bg-gray-200 rounded-full h-2"},ri={class:"text-xs text-gray-500 mt-1"},ai={__name:"Dashboard",setup(a){Z.register(...De),G();const i=$(!1),d=$("7"),t=$({}),s=$([]),m=$([]),v=$([]),p=$(null),u=$(null);let l=null,o=null;const g=async()=>{try{const y=await fetch("/api/dashboard/stats");if(!y.ok)throw new Error("Failed to fetch stats");const x=await y.json();t.value=x.data}catch(y){console.error("Error fetching dashboard stats:",y),t.value={}}},h=async()=>{try{const y=await fetch(`/api/dashboard/upcoming-tasks?days=${d.value}&limit=5`);if(!y.ok)throw new Error("Failed to fetch upcoming tasks");const x=await y.json();s.value=x.data.tasks}catch(y){console.error("Error fetching upcoming tasks:",y),s.value=[]}},w=async()=>{try{const y=await fetch("/api/dashboard/recent-activities?limit=5");if(!y.ok)throw new Error("Failed to fetch recent activities");const x=await y.json();m.value=x.data.activities}catch(y){console.error("Error fetching recent activities:",y),m.value=[]}},I=async()=>{try{const y=await fetch("/api/dashboard/kpis?limit=3");if(!y.ok)throw new Error("Failed to fetch KPIs");const x=await y.json();v.value=x.data.kpis}catch(y){console.error("Error fetching KPIs:",y),v.value=[]}},E=async()=>{try{const y=await fetch("/api/dashboard/charts/project-status");if(!y.ok)throw new Error("Failed to fetch project chart data");const x=await y.json();k(x.data.chart)}catch(y){console.error("Error fetching project chart:",y)}},S=async()=>{try{const y=await fetch("/api/dashboard/charts/task-status");if(!y.ok)throw new Error("Failed to fetch task chart data");const x=await y.json();A(x.data.chart)}catch(y){console.error("Error fetching task chart:",y)}},k=y=>{if(!p.value)return;const x=p.value.getContext("2d");l&&l.destroy(),l=new Z(x,{type:"doughnut",data:{labels:y.labels,datasets:[{data:y.data,backgroundColor:["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6"],borderWidth:2,borderColor:"#ffffff"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}}}}})},A=y=>{if(!u.value)return;const x=u.value.getContext("2d");o&&o.destroy(),o=new Z(x,{type:"bar",data:{labels:y.labels,datasets:[{label:"Tasks",data:y.data,backgroundColor:["#60A5FA","#34D399","#FBBF24","#F87171"],borderColor:["#3B82F6","#10B981","#F59E0B","#EF4444"],borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,ticks:{stepSize:1}}}}})},W=async()=>{i.value=!0;try{await Promise.all([g(),h(),w(),I(),E(),S()])}finally{i.value=!1}},ae=y=>new Date(y).toLocaleDateString("it-IT"),Ce=y=>{const x=new Date(y),L=Math.floor((new Date-x)/(1e3*60));return L<60?`${L} minuti fa`:L<1440?`${Math.floor(L/60)} ore fa`:`${Math.floor(L/1440)} giorni fa`},Me=y=>{const x={high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return x[y]||x.medium},je=y=>{const x={todo:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300","in-progress":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",review:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",done:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return x[y]||x.todo},ze=y=>{const x={task:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`},timesheet:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`},event:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`}};return x[y]||x.task},Se=y=>{const x={task:"bg-blue-100 text-blue-600",timesheet:"bg-green-100 text-green-600",event:"bg-purple-100 text-purple-600"};return x[y]||x.task},Ie=y=>y>=90?"bg-green-500":y>=70?"bg-yellow-500":"bg-red-500";return U(async()=>{await W(),await be(),p.value&&u.value&&(await E(),await S())}),(y,x)=>{var L,ie,le,de,ce,ue,me,pe;const ne=B("router-link");return r(),n("div",mn,[e("div",pn,[x[4]||(x[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Dashboard"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Benvenuto! Ecco una panoramica delle attività della tua azienda. ")],-1)),e("div",gn,[e("div",vn,[N(e("select",{"onUpdate:modelValue":x[0]||(x[0]=z=>d.value=z),onChange:W,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500"},x[1]||(x[1]=[e("option",{value:"7"},"Ultimi 7 giorni",-1),e("option",{value:"30"},"Ultimo mese",-1),e("option",{value:"90"},"Ultimi 3 mesi",-1)]),544),[[He,d.value]])]),e("button",{onClick:W,disabled:i.value,type:"button",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(r(),n("svg",{xmlns:"http://www.w3.org/2000/svg",class:j(["h-4 w-4 mr-2",{"animate-spin":i.value}]),viewBox:"0 0 20 20",fill:"currentColor"},x[2]||(x[2]=[e("path",{"fill-rule":"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z","clip-rule":"evenodd"},null,-1)]),2)),x[3]||(x[3]=M(" Aggiorna "))],8,fn)])]),e("div",hn,[f(Y,{title:"Progetti Attivi",value:((L=t.value.projects)==null?void 0:L.active)||0,subtitle:`di ${((ie=t.value.projects)==null?void 0:ie.total)||0} totali`,icon:"project",color:"primary",link:"/projects?status=active"},null,8,["value","subtitle"]),f(Y,{title:"Clienti",value:((le=t.value.team)==null?void 0:le.clients)||0,icon:"users",color:"secondary",link:"/crm/clients"},null,8,["value"]),f(Y,{title:"Task Pendenti",value:((de=t.value.tasks)==null?void 0:de.pending)||0,subtitle:`${((ce=t.value.tasks)==null?void 0:ce.overdue)||0} in ritardo`,icon:"clock",color:((ue=t.value.tasks)==null?void 0:ue.overdue)>0?"red":"yellow",link:"/tasks?status=pending"},null,8,["value","subtitle","color"]),f(Y,{title:"Team Members",value:((me=t.value.team)==null?void 0:me.users)||0,subtitle:`${((pe=t.value.team)==null?void 0:pe.departments)||0} dipartimenti`,icon:"team",color:"blue",link:"/personnel"},null,8,["value","subtitle"])]),e("div",xn,[e("div",yn,[x[5]||(x[5]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Progetti")],-1)),e("div",bn,[e("canvas",{ref_key:"projectChart",ref:p},null,512)])]),e("div",_n,[x[6]||(x[6]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Attività")],-1)),e("div",kn,[e("canvas",{ref_key:"taskChart",ref:u},null,512)])])]),e("div",wn,[e("div",$n,[e("div",Cn,[x[7]||(x[7]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività in Scadenza",-1)),s.value.length===0?(r(),n("div",Mn," Nessuna attività in scadenza ")):(r(),n("div",jn,[(r(!0),n(P,null,D(s.value,z=>(r(),n("div",{key:z.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",zn,[e("div",Sn,[e("h3",In,c(z.name),1),e("p",An,c(z.project_name),1)]),e("span",{class:j(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",Me(z.priority)])},c(z.priority),3)]),e("div",Bn,[e("span",Vn," Scadenza: "+c(ae(z.due_date)),1),e("span",{class:j(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",je(z.status)])},c(z.status),3)])]))),128))]))]),e("div",Pn,[e("div",Dn,[f(ne,{to:"/tasks",class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500"},{default:C(()=>x[8]||(x[8]=[M(" Vedi tutte le attività ")])),_:1,__:[8]})])])]),e("div",Hn,[e("div",En,[x[9]||(x[9]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività Recenti",-1)),m.value.length===0?(r(),n("div",Tn," Nessuna attività recente ")):(r(),n("div",Ln,[(r(!0),n(P,null,D(m.value,z=>(r(),n("div",{key:`${z.type}-${z.id}`,class:"flex items-start space-x-3"},[e("div",Nn,[e("div",{class:j(["w-8 h-8 rounded-full flex items-center justify-center",Se(z.type)])},[(r(),R(ke(ze(z.type)),{class:"w-4 h-4"}))],2)]),e("div",Rn,[e("p",Un,c(z.title),1),e("p",Fn,c(z.description),1),e("p",qn,c(Ce(z.timestamp)),1)])]))),128))]))])]),e("div",On,[e("div",Kn,[x[10]||(x[10]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"KPIs Principali",-1)),v.value.length===0?(r(),n("div",Wn," Nessun KPI configurato ")):(r(),n("div",Gn,[(r(!0),n(P,null,D(v.value,z=>(r(),n("div",{key:z.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",Jn,[e("div",Yn,[e("h3",Qn,c(z.name),1),e("p",Xn,c(z.description),1)]),e("div",Zn,[e("p",ei,c(z.current_value)+c(z.unit),1),e("p",ti," Target: "+c(z.target_value)+c(z.unit),1)])]),e("div",si,[e("div",oi,[e("div",{class:j(["h-2 rounded-full",Ie(z.performance_percentage)]),style:_e({width:Math.min(z.performance_percentage,100)+"%"})},null,6)]),e("p",ri,c(Math.round(z.performance_percentage))+"% del target",1)])]))),128))]))])])])])}}},ni={};function ii(a,i){return r(),n("div",null,i[0]||(i[0]=[e("h1",{class:"text-2xl font-bold text-gray-900 mb-6"},"Progetti",-1),e("div",{class:"card"},[e("p",{class:"text-gray-600"},"Gestione progetti in fase di migrazione...")],-1)]))}const li=X(ni,[["render",ii]]),di={};function ci(a,i){return r(),n("div",null,i[0]||(i[0]=[e("h1",{class:"text-2xl font-bold text-gray-900 mb-6"},"Personale",-1),e("div",{class:"card"},[e("p",{class:"text-gray-600"},"Gestione personale in fase di migrazione...")],-1)]))}const ui=X(di,[["render",ci]]),mi=[{path:"/",component:xe,children:[{path:"",name:"home",component:yr},{path:"about",name:"about",component:Gr},{path:"contact",name:"contact",component:$a},{path:"services",name:"services",component:Na}]},{path:"/auth",component:xe,children:[{path:"login",name:"login",component:Qa},{path:"register",name:"register",component:tn}]},{path:"/app",component:ho,meta:{requiresAuth:!0},children:[{path:"",redirect:"/app/dashboard"},{path:"dashboard",name:"dashboard",component:ai},{path:"projects",name:"projects",component:li},{path:"projects/:id",name:"project-view",component:()=>Ke(()=>import("./ProjectView.js"),__vite__mapDeps([0,1,2]))},{path:"personnel",name:"personnel",component:ui}]}],$e=Ee({history:Te(),routes:mi});$e.beforeEach(async(a,i,d)=>{const t=F();if(a.meta.requiresAuth&&(t.sessionChecked||await t.initializeAuth(),!t.isAuthenticated)){d("/auth/login");return}d()});const re=Le(Fe),pi=Ne();re.use(pi);re.use($e);const gi=F();gi.initializeAuth().then(()=>{re.mount("#app")});export{X as _,O as a,F as u};
