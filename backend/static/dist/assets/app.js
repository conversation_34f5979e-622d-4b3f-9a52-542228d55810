const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ProjectView.js","assets/vendor.js","assets/ProjectView.css"])))=>i.map(i=>d[i]);
import{r as w,w as Y,o as L,c as n,a as h,b as B,d as r,e as Se,f as fe,g as _,h as b,n as M,i as U,j as $,t as u,u as te,k as e,F as V,l as P,m as C,p as W,q as D,s as xe,v as se,x as R,y as K,z as J,A as ye,T as Ie,B as Ae,C as Be,D as _e,E as Z,G as Ve,H as Pe,I as De,J as He,K as Ee,L as Te}from"./vendor.js";(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))t(s);new MutationObserver(s=>{for(const c of s)if(c.type==="childList")for(const v of c.addedNodes)v.tagName==="LINK"&&v.rel==="modulepreload"&&t(v)}).observe(document,{childList:!0,subtree:!0});function d(s){const c={};return s.integrity&&(c.integrity=s.integrity),s.referrerPolicy&&(c.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?c.credentials="include":s.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function t(s){if(s.ep)return;s.ep=!0;const c=d(s);fetch(s.href,c)}})();const E=w(!1);function be(){const i=()=>{E.value=!E.value},l=c=>{E.value=c},d=()=>{const c=localStorage.getItem("darkMode");if(c==="true")E.value=!0;else if(c==="false")E.value=!1;else{const v=window.matchMedia("(prefers-color-scheme: dark)").matches;E.value=v}};Y(E,c=>{c?(document.documentElement.classList.add("dark"),localStorage.setItem("darkMode","true")):(document.documentElement.classList.remove("dark"),localStorage.setItem("darkMode","false"))},{immediate:!0});const t=window.matchMedia("(prefers-color-scheme: dark)"),s=c=>{localStorage.getItem("darkMode")||(E.value=c.matches)};return L(()=>{d(),t.addEventListener("change",s)}),{isDarkMode:E,toggleDarkMode:i,setDarkMode:l,initializeDarkMode:d}}const Le={id:"app"},Ne={__name:"App",setup(i){const{initializeDarkMode:l}=be();return l(),(d,t)=>{const s=B("router-view");return r(),n("div",Le,[h(s)])}}},Re="modulepreload",Ue=function(i){return"/"+i},pe={},Fe=function(l,d,t){let s=Promise.resolve();if(d&&d.length>0){document.getElementsByTagName("link");const v=document.querySelector("meta[property=csp-nonce]"),p=(v==null?void 0:v.nonce)||(v==null?void 0:v.getAttribute("nonce"));s=Promise.allSettled(d.map(m=>{if(m=Ue(m),m in pe)return;pe[m]=!0;const a=m.endsWith(".css"),o=a?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${m}"]${o}`))return;const g=document.createElement("link");if(g.rel=a?"stylesheet":Re,a||(g.as="script"),g.crossOrigin="",g.href=m,p&&g.setAttribute("nonce",p),document.head.appendChild(g),a)return new Promise((f,j)=>{g.addEventListener("load",f),g.addEventListener("error",()=>j(new Error(`Unable to preload CSS for ${m}`)))})}))}function c(v){const p=new Event("vite:preloadError",{cancelable:!0});if(p.payload=v,window.dispatchEvent(p),!p.defaultPrevented)throw v}return s.then(v=>{for(const p of v||[])p.status==="rejected"&&c(p.reason);return l().catch(c)})},O=Se.create({baseURL:"",timeout:1e4,withCredentials:!0,headers:{"Content-Type":"application/json"}});O.interceptors.request.use(i=>{var d,t;const l=(d=document.querySelector('meta[name="csrf-token"]'))==null?void 0:d.getAttribute("content");return l&&["post","put","patch","delete"].includes((t=i.method)==null?void 0:t.toLowerCase())&&(i.headers["X-CSRFToken"]=l),i},i=>Promise.reject(i));O.interceptors.response.use(i=>i,i=>{var l;return((l=i.response)==null?void 0:l.status)===401&&(localStorage.removeItem("user"),console.warn("Sessione scaduta, autenticazione richiesta")),Promise.reject(i)});const F=fe("auth",()=>{const i=localStorage.getItem("user"),l=w(i?JSON.parse(i):null),d=w(!1),t=w(null),s=w(!1),c=_(()=>!!l.value&&s.value);async function v(g){var f,j;d.value=!0,t.value=null;try{const I=await O.post("/api/auth/login",g);return I.data.success?(l.value=I.data.data.user,localStorage.setItem("user",JSON.stringify(l.value)),s.value=!0,{success:!0}):(t.value=I.data.message||"Errore durante il login",{success:!1,error:t.value})}catch(I){return t.value=((j=(f=I.response)==null?void 0:f.data)==null?void 0:j.message)||"Errore di connessione",{success:!1,error:t.value}}finally{d.value=!1}}async function p(g){var f,j;d.value=!0,t.value=null;try{const I=await O.post("/api/auth/register",g);return I.data.success?{success:!0,message:I.data.message}:(t.value=I.data.message||"Errore durante la registrazione",{success:!1,error:t.value})}catch(I){return t.value=((j=(f=I.response)==null?void 0:f.data)==null?void 0:j.message)||"Errore di connessione",{success:!1,error:t.value}}finally{d.value=!1}}async function m(){try{await O.post("/api/auth/logout")}catch(g){console.warn("Errore durante il logout:",g)}finally{l.value=null,s.value=!1,localStorage.removeItem("user")}}async function a(){if(s.value)return c.value;try{const g=await O.get("/api/auth/me");return g.data.success?(l.value=g.data.data.user,localStorage.setItem("user",JSON.stringify(l.value)),s.value=!0,!0):(await m(),!1)}catch{return await m(),!1}}async function o(){return l.value?await a():(s.value=!0,!1)}return{user:l,loading:d,error:t,sessionChecked:s,isAuthenticated:c,login:v,register:p,logout:m,checkAuth:a,initializeAuth:o}}),q=fe("tenant",()=>{const i=w(null),l=w(!1),d=w(null),t=_(()=>{var o;return((o=i.value)==null?void 0:o.company)||{}}),s=_(()=>{var o;return((o=i.value)==null?void 0:o.contact)||{}}),c=_(()=>{var o;return((o=i.value)==null?void 0:o.pages)||{}}),v=_(()=>{var o;return((o=i.value)==null?void 0:o.navigation)||{}}),p=_(()=>{var o;return((o=i.value)==null?void 0:o.footer)||{}});async function m(){try{if(l.value=!0,window.TENANT_CONFIG){i.value=window.TENANT_CONFIG;return}const o=await fetch("/api/config/tenant");i.value=await o.json()}catch(o){d.value="Errore nel caricamento della configurazione",console.error("Errore caricamento tenant config:",o)}finally{l.value=!1}}function a(o,g={}){if(!o||typeof o!="string")return o;let f=o;const j={"company.name":t.value.name||"DatVinci","company.tagline":t.value.tagline||"","company.description":t.value.description||"","company.mission":t.value.mission||"","company.vision":t.value.vision||"","company.founded":t.value.founded||"","company.team_size":t.value.team_size||"","contact.email":s.value.email||"","contact.phone":s.value.phone||"","contact.address":s.value.address||"",current_year:new Date().getFullYear().toString(),...g};for(const[I,H]of Object.entries(j)){const S=new RegExp(`\\{${I}\\}`,"g");f=f.replace(S,H||"")}return f}return{config:i,loading:l,error:d,company:t,contact:s,pages:c,navigation:v,footer:p,loadConfig:m,interpolateText:a}}),qe={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"},Oe={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},Ke={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},Ge={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},We={key:4,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"},Je={key:5,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},Qe={key:6,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"},Ye={key:7,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},Xe={key:8,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},Ze={key:9,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"},et={key:10,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},tt={key:11,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},st={key:12,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"},ee={__name:"SidebarIcon",props:{icon:{type:String,required:!0},className:{type:String,default:"h-5 w-5"}},setup(i){return(l,d)=>(r(),n("svg",{class:M(i.className),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[i.icon==="dashboard"?(r(),n("path",qe)):i.icon==="projects"?(r(),n("path",Oe)):i.icon==="users"?(r(),n("path",Ke)):i.icon==="clients"?(r(),n("path",Ge)):i.icon==="products"?(r(),n("path",We)):i.icon==="reports"?(r(),n("path",Je)):i.icon==="settings"?(r(),n("path",Qe)):b("",!0),i.icon==="settings"?(r(),n("path",Ye)):i.icon==="user-management"?(r(),n("path",Xe)):i.icon==="communications"?(r(),n("path",Ze)):i.icon==="funding"?(r(),n("path",et)):i.icon==="reporting"?(r(),n("path",tt)):(r(),n("path",st))],2))}},ot={key:0,class:"truncate"},rt={key:0,class:"truncate"},T={__name:"SidebarNavItem",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(i){const l=_(()=>["text-gray-700 hover:bg-gray-50 hover:text-primary-600"]);return(d,t)=>{const s=B("router-link");return r(),n("div",null,[i.item.path!=="#"?(r(),U(s,{key:0,to:i.item.path,class:M(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[l.value,{"justify-center":i.isCollapsed}]]),"active-class":"text-primary-600 bg-primary-50 border-r-2 border-primary-600",onClick:t[0]||(t[0]=c=>d.$emit("click"))},{default:$(()=>[h(ee,{icon:i.item.icon,class:M(["flex-shrink-0 h-6 w-6",{"mr-0":i.isCollapsed,"mr-3":!i.isCollapsed}])},null,8,["icon","class"]),i.isCollapsed?b("",!0):(r(),n("span",ot,u(i.item.name),1))]),_:1},8,["to","class"])):(r(),n("div",{key:1,class:M(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 cursor-not-allowed opacity-75",["text-gray-400 hover:text-gray-500",{"justify-center":i.isCollapsed}]])},[h(ee,{icon:i.item.icon,class:M(["flex-shrink-0 h-6 w-6",{"mr-0":i.isCollapsed,"mr-3":!i.isCollapsed}])},null,8,["icon","class"]),i.isCollapsed?b("",!0):(r(),n("span",rt,u(i.item.name),1))],2))])}}},at={key:0,class:"flex-1 text-left truncate"},nt={key:0,class:"ml-6 space-y-1 mt-1"},it={__name:"SidebarNavItemCollapsible",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(i){const l=i,d=te(),t=F(),s=w(!1),c=_(()=>["text-gray-700 hover:bg-gray-50 hover:text-primary-600",{"text-primary-600 bg-primary-50":v.value}]),v=_(()=>l.item.children?l.item.children.some(o=>o.path!=="#"&&d.path.startsWith(o.path)):!1),p=_(()=>l.item.children?l.item.children.filter(o=>{var g;return o.admin?((g=t.user)==null?void 0:g.role)==="admin":!0}):[]);v.value&&(s.value=!0);function m(){l.isCollapsed||(s.value=!s.value)}function a(o){if(o.path==="#")return!1}return(o,g)=>{const f=B("router-link");return r(),n("div",null,[e("button",{onClick:m,class:M(["group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[c.value,{"justify-center":i.isCollapsed}]])},[h(ee,{icon:i.item.icon,class:M(["flex-shrink-0 h-6 w-6",{"mr-0":i.isCollapsed,"mr-3":!i.isCollapsed}])},null,8,["icon","class"]),i.isCollapsed?b("",!0):(r(),n("span",at,u(i.item.name),1)),i.isCollapsed?b("",!0):(r(),n("svg",{key:1,class:M([{"rotate-90":s.value},"ml-2 h-4 w-4 transition-transform duration-150"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},g[0]||(g[0]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"},null,-1)]),2))],2),s.value&&!i.isCollapsed?(r(),n("div",nt,[(r(!0),n(V,null,P(p.value,j=>(r(),U(f,{key:j.name,to:j.path,class:M(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",j.path==="#"?"text-gray-400 hover:text-gray-500 cursor-not-allowed opacity-75":"text-gray-600 hover:bg-gray-50 hover:text-primary-600"]),"active-class":"text-primary-600 bg-primary-50",onClick:I=>a(j)},{default:$(()=>[C(u(j.name),1)]),_:2},1032,["to","class","onClick"]))),128))])):b("",!0)])}}},lt={class:"mt-5 flex-grow flex flex-col overflow-hidden"},dt={class:"flex-1 px-2 space-y-1"},ve={__name:"SidebarNavigation",props:{isCollapsed:{type:Boolean,default:!1}},emits:["item-click"],setup(i){const l=F(),d=_(()=>{var t;return((t=l.user)==null?void 0:t.role)==="admin"});return(t,s)=>(r(),n("div",lt,[e("nav",dt,[h(T,{item:{name:"Dashboard",path:"/app/dashboard",icon:"dashboard"},"is-collapsed":i.isCollapsed,onClick:s[0]||(s[0]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(it,{item:{name:"Personale",icon:"users",children:[{name:"👥 Team",path:"/app/personnel"},{name:"📖 Directory",path:"/app/personnel/directory"},{name:"🏢 Organigramma",path:"/app/personnel/orgchart"},{name:"🎯 Competenze",path:"/app/personnel/skills"},{name:"🏢 Dipartimenti",path:"#",admin:!0},{name:"⚙️ Amministrazione",path:"#",admin:!0}]},"is-collapsed":i.isCollapsed,onClick:s[1]||(s[1]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"Progetti",path:"/app/projects",icon:"projects"},"is-collapsed":i.isCollapsed,onClick:s[2]||(s[2]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"CRM",path:"#",icon:"clients"},"is-collapsed":i.isCollapsed,onClick:s[3]||(s[3]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"Prodotti",path:"#",icon:"products"},"is-collapsed":i.isCollapsed,onClick:s[4]||(s[4]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"Performance",path:"#",icon:"reports"},"is-collapsed":i.isCollapsed,onClick:s[5]||(s[5]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"Comunicazione",path:"#",icon:"communications"},"is-collapsed":i.isCollapsed,onClick:s[6]||(s[6]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"Finanziamenti",path:"#",icon:"funding"},"is-collapsed":i.isCollapsed,onClick:s[7]||(s[7]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(T,{item:{name:"Rendicontazione",path:"#",icon:"reporting"},"is-collapsed":i.isCollapsed,onClick:s[8]||(s[8]=c=>t.$emit("item-click"))},null,8,["is-collapsed"]),d.value?(r(),U(T,{key:0,item:{name:"Amministrazione",path:"#",icon:"settings"},"is-collapsed":i.isCollapsed,onClick:s[9]||(s[9]=c=>t.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0)])]))}},ct={class:"flex-shrink-0 border-t border-gray-200 p-4"},ut={class:"flex-shrink-0"},mt={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},pt={class:"text-sm font-medium text-primary-700"},vt={key:0,class:"ml-3 flex-1 min-w-0"},gt={class:"text-sm font-medium text-gray-900 truncate"},ht={class:"text-xs text-gray-500 truncate"},ft={class:"py-1"},xt={key:0,class:"mt-3 text-xs text-gray-400 text-center"},ge={__name:"SidebarFooter",props:{isCollapsed:{type:Boolean,default:!1}},setup(i){const l=W(),d=F(),t=w(!1),s=_(()=>d.user&&(d.user.name||d.user.username)||"Utente"),c=_(()=>d.user?s.value.charAt(0).toUpperCase():"U"),v=_(()=>d.user?{admin:"Amministratore",manager:"Manager",employee:"Dipendente",client:"Cliente"}[d.user.role]||d.user.role:""),p=_(()=>"1.0.0");async function m(){t.value=!1,await d.logout(),l.push("/auth/login")}return(a,o)=>{const g=B("router-link");return r(),n("div",ct,[e("div",{class:M(["flex items-center",{"justify-center":i.isCollapsed}])},[e("div",ut,[e("div",mt,[e("span",pt,u(c.value),1)])]),i.isCollapsed?b("",!0):(r(),n("div",vt,[e("p",gt,u(s.value),1),e("p",ht,u(v.value),1)])),e("div",{class:M(["relative",{"ml-3":!i.isCollapsed}])},[e("button",{onClick:o[0]||(o[0]=f=>t.value=!t.value),class:"p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"},o[4]||(o[4]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zM13 12a1 1 0 11-2 0 1 1 0 012 0zM20 12a1 1 0 11-2 0 1 1 0 012 0z"})],-1)])),t.value?(r(),n("div",{key:0,onClick:o[3]||(o[3]=f=>t.value=!1),class:"origin-bottom-left absolute bottom-full left-0 mb-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",ft,[h(g,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:o[1]||(o[1]=f=>t.value=!1)},{default:$(()=>o[5]||(o[5]=[C(" Il tuo profilo ")])),_:1,__:[5]}),h(g,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:o[2]||(o[2]=f=>t.value=!1)},{default:$(()=>o[6]||(o[6]=[C(" Impostazioni ")])),_:1,__:[6]}),o[7]||(o[7]=e("hr",{class:"my-1"},null,-1)),e("button",{onClick:m,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Esci ")])])):b("",!0)],2)],2),p.value&&!i.isCollapsed?(r(),n("div",xt," v"+u(p.value),1)):b("",!0)])}}},yt={class:"flex"},_t={class:"hidden lg:flex lg:flex-shrink-0 lg:fixed lg:inset-y-0 z-10"},bt={class:"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white border-r border-gray-200 shadow-sm"},kt={class:"flex items-center flex-shrink-0 px-4"},wt={class:"w-10 h-10 bg-primary-600 rounded flex items-center justify-center mr-3"},$t={class:"text-white font-bold text-lg"},Ct={class:"text-xl font-semibold text-gray-900"},Mt={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},jt={class:"text-white font-bold text-sm"},zt={class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},St=["d"],It={class:"flex flex-col h-full pt-5 pb-4 overflow-y-auto bg-white border-r border-gray-200 shadow-sm"},At={class:"flex items-center justify-between px-4 mb-4"},Bt={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center mr-3"},Vt={class:"text-white font-bold text-sm"},Pt={class:"text-xl font-semibold text-gray-900"},Dt={__name:"AppSidebar",props:{isMobileOpen:{type:Boolean,default:!1}},emits:["close"],setup(i){const l=q(),d=w(!1),t=_(()=>l.config||{}),s=_(()=>{var m;return((m=t.value.company)==null?void 0:m.name)||"DatPortal"}),c=_(()=>s.value.split(" ").map(a=>a[0]).join("").toUpperCase().slice(0,2));function v(){d.value=!d.value}function p(){d.value&&(d.value=!1)}return(m,a)=>{const o=B("router-link");return r(),n("div",yt,[e("div",_t,[e("div",{class:M(["flex flex-col transition-all duration-300",[d.value?"w-20":"w-64"]])},[e("div",bt,[e("div",kt,[e("div",{class:M(["flex items-center",{"justify-center":d.value}])},[h(o,{to:"/app/dashboard",class:M(["flex items-center",{hidden:d.value}])},{default:$(()=>[e("div",wt,[e("span",$t,u(c.value),1)]),e("h3",Ct,u(s.value),1)]),_:1},8,["class"]),h(o,{to:"/app/dashboard",class:M(["flex items-center justify-center",{hidden:!d.value}])},{default:$(()=>[e("div",Mt,[e("span",jt,u(c.value),1)])]),_:1},8,["class"])],2),e("button",{onClick:v,class:"ml-auto text-gray-600 focus:outline-none hover:bg-gray-100 p-1 rounded"},[(r(),n("svg",zt,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:d.value?"M13 5l7 7-7 7M5 5l7 7-7 7":"M11 19l-7-7 7-7m8 14l-7-7 7-7"},null,8,St)]))])]),h(ve,{"is-collapsed":d.value,onItemClick:p},null,8,["is-collapsed"]),h(ge,{"is-collapsed":d.value},null,8,["is-collapsed"])])],2)]),e("div",{class:M(["fixed inset-y-0 left-0 z-30 w-64 bg-primary-700 transform transition-transform duration-300 ease-in-out lg:hidden",i.isMobileOpen?"translate-x-0":"-translate-x-full"])},[e("div",It,[e("div",At,[h(o,{to:"/app/dashboard",class:"flex items-center"},{default:$(()=>[e("div",Bt,[e("span",Vt,u(c.value),1)]),e("h3",Pt,u(s.value),1)]),_:1}),e("button",{onClick:a[0]||(a[0]=g=>m.$emit("close")),class:"p-2 rounded-md text-gray-600 hover:bg-gray-100"},a[2]||(a[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),h(ve,{"is-collapsed":!1,onItemClick:a[1]||(a[1]=g=>m.$emit("close"))}),h(ge,{"is-collapsed":!1})])],2)])}}},Ht={class:"flex","aria-label":"Breadcrumb"},Et={class:"flex items-center space-x-2 text-sm text-gray-500"},Tt={key:0,class:"mr-2"},Lt={class:"flex items-center"},Nt={key:0,class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Rt=["d"],Ut={key:2,class:"font-medium text-gray-900"},Ft={__name:"HeaderBreadcrumbs",props:{breadcrumbs:{type:Array,required:!0}},setup(i){return(l,d)=>{const t=B("router-link");return r(),n("nav",Ht,[e("ol",Et,[(r(!0),n(V,null,P(i.breadcrumbs,(s,c)=>(r(),n("li",{key:c,class:"flex items-center"},[c>0?(r(),n("div",Tt,d[0]||(d[0]=[e("svg",{class:"h-3 w-3 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]))):b("",!0),s.to&&c<i.breadcrumbs.length-1?(r(),U(t,{key:1,to:s.to,class:"hover:text-gray-700 transition-colors duration-150"},{default:$(()=>[e("span",Lt,[s.icon?(r(),n("svg",Nt,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:s.icon},null,8,Rt)])):b("",!0),C(" "+u(s.label),1)])]),_:2},1032,["to"])):(r(),n("span",Ut,u(s.label),1))]))),128))])])}}},qt={class:"flex items-center space-x-2"},Ot={key:0,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Kt={key:1,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Gt={__name:"HeaderQuickActions",emits:["quick-create-project","quick-add-task"],setup(i){const l=te(),{isDarkMode:d,toggleDarkMode:t}=be(),s=_(()=>{var v;return((v=l.name)==null?void 0:v.includes("projects"))||l.path.includes("/projects")}),c=_(()=>{var v,p;return((v=l.name)==null?void 0:v.includes("tasks"))||((p=l.name)==null?void 0:p.includes("projects"))||l.path.includes("/tasks")||l.path.includes("/projects")});return(v,p)=>(r(),n("div",qt,[s.value?(r(),n("button",{key:0,onClick:p[0]||(p[0]=m=>v.$emit("quick-create-project")),class:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},p[3]||(p[3]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),C(" Nuovo Progetto ")]))):b("",!0),c.value?(r(),n("button",{key:1,onClick:p[1]||(p[1]=m=>v.$emit("quick-add-task")),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},p[4]||(p[4]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),C(" Nuovo Task ")]))):b("",!0),e("button",{onClick:p[2]||(p[2]=(...m)=>D(t)&&D(t)(...m)),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",title:"Cambia tema"},[D(d)?(r(),n("svg",Kt,p[6]||(p[6]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"},null,-1)]))):(r(),n("svg",Ot,p[5]||(p[5]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"},null,-1)])))])]))}},Wt={class:"relative"},Jt={class:"relative"},Qt={key:0,class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center"},Yt={class:"py-1"},Xt={key:0,class:"px-4 py-8 text-center text-gray-500 text-sm"},Zt={key:1,class:"max-h-64 overflow-y-auto"},es=["onClick"],ts={class:"flex items-start"},ss={class:"flex-shrink-0"},os={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},rs=["d"],as={class:"ml-3 flex-1"},ns={class:"text-sm font-medium text-gray-900"},is={class:"text-xs text-gray-500 mt-1"},ls={class:"text-xs text-gray-400 mt-1"},ds={key:0,class:"flex-shrink-0"},cs={key:2,class:"px-4 py-2 border-t border-gray-100"},us={__name:"HeaderNotifications",setup(i){const l=w(!1),d=w([{id:1,type:"task",title:"Nuovo task assegnato",message:'Ti è stato assegnato un nuovo task nel progetto "Website Redesign"',created_at:new Date().toISOString(),read:!1},{id:2,type:"project",title:"Progetto completato",message:'Il progetto "Mobile App" è stato completato con successo',created_at:new Date(Date.now()-36e5).toISOString(),read:!0}]),t=_(()=>d.value.filter(a=>!a.read).length);function s(a){const o={task:"h-6 w-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center",project:"h-6 w-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center",user:"h-6 w-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center",system:"h-6 w-6 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center"};return o[a]||o.system}function c(a){const o={task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2",project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",user:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",system:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return o[a]||o.system}function v(a){const o=new Date(a),f=new Date-o;return f<6e4?"Adesso":f<36e5?`${Math.floor(f/6e4)}m fa`:f<864e5?`${Math.floor(f/36e5)}h fa`:o.toLocaleDateString("it-IT")}function p(a){a.read||(a.read=!0),l.value=!1}function m(){d.value.forEach(a=>a.read=!0)}return(a,o)=>(r(),n("div",Wt,[e("button",{onClick:o[0]||(o[0]=g=>l.value=!l.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[o[3]||(o[3]=e("span",{class:"sr-only"},"Visualizza notifiche",-1)),e("div",Jt,[o[2]||(o[2]=e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM10 21a2 2 0 01-2-2V7a7 7 0 1114 0v12a2 2 0 01-2 2H10z"})],-1)),t.value>0?(r(),n("span",Qt,u(t.value>9?"9+":t.value),1)):b("",!0)])]),l.value?(r(),n("div",{key:0,onClick:o[1]||(o[1]=g=>l.value=!1),class:"origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",Yt,[o[5]||(o[5]=e("div",{class:"px-4 py-2 border-b border-gray-100"},[e("h3",{class:"text-sm font-medium text-gray-900"},"Notifiche")],-1)),d.value.length===0?(r(),n("div",Xt," Nessuna notifica ")):(r(),n("div",Zt,[(r(!0),n(V,null,P(d.value,g=>(r(),n("div",{key:g.id,class:"px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-50 last:border-b-0",onClick:f=>p(g)},[e("div",ts,[e("div",ss,[e("div",{class:M(s(g.type))},[(r(),n("svg",os,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:c(g.type)},null,8,rs)]))],2)]),e("div",as,[e("p",ns,u(g.title),1),e("p",is,u(g.message),1),e("p",ls,u(v(g.created_at)),1)]),g.read?b("",!0):(r(),n("div",ds,o[4]||(o[4]=[e("div",{class:"h-2 w-2 bg-primary-500 rounded-full"},null,-1)])))])],8,es))),128))])),d.value.length>0?(r(),n("div",cs,[e("button",{onClick:m,class:"text-xs text-primary-600 hover:text-primary-800"}," Segna tutte come lette ")])):b("",!0)])])):b("",!0)]))}},ms={class:"relative"},ps={class:"flex items-start justify-center min-h-screen pt-16 px-4 pb-20 text-center sm:block sm:p-0"},vs={class:"inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"},gs={class:"flex items-center"},hs={class:"flex-1"},fs={key:0,class:"mt-4 max-h-64 overflow-y-auto"},xs={class:"space-y-1"},ys=["onClick"],_s={class:"flex-shrink-0"},bs={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ks=["d"],ws={class:"ml-3 flex-1 min-w-0"},$s={class:"text-sm font-medium text-gray-900 truncate"},Cs={class:"text-xs text-gray-500 truncate"},Ms={class:"ml-2 text-xs text-gray-400"},js={key:1,class:"mt-4 text-center py-4"},zs={key:2,class:"mt-4 text-center py-4"},Ss={__name:"HeaderSearch",setup(i){const l=W(),d=w(!1),t=w(""),s=w([]),c=w(-1),v=w(!1),p=w(null),m=[{id:1,type:"project",title:"Website Redesign",description:"Progetto di redesign del sito web aziendale",path:"/app/projects/1"},{id:2,type:"person",title:"Mario Rossi",description:"Senior Developer",path:"/app/personnel/directory/1"},{id:3,type:"document",title:"Specifiche Tecniche",description:"Documento delle specifiche del progetto mobile",path:"/app/documents/1"},{id:4,type:"task",title:"Implementazione API",description:"Task per l'implementazione delle API REST",path:"/app/projects/1/tasks/5"}];Y(d,async S=>{var k;S?(await xe(),(k=p.value)==null||k.focus()):(t.value="",s.value=[],c.value=-1)});function a(){if(!t.value.trim()){s.value=[];return}v.value=!0,setTimeout(()=>{s.value=m.filter(S=>S.title.toLowerCase().includes(t.value.toLowerCase())||S.description.toLowerCase().includes(t.value.toLowerCase())),c.value=-1,v.value=!1},200)}function o(S){if(s.value.length===0)return;const k=c.value+S;k>=0&&k<s.value.length&&(c.value=k)}function g(){c.value>=0&&s.value[c.value]&&f(s.value[c.value])}function f(S){d.value=!1,l.push(S.path)}function j(S){const k={project:"h-6 w-6 rounded bg-blue-100 text-blue-600 flex items-center justify-center",person:"h-6 w-6 rounded bg-green-100 text-green-600 flex items-center justify-center",document:"h-6 w-6 rounded bg-yellow-100 text-yellow-600 flex items-center justify-center",task:"h-6 w-6 rounded bg-purple-100 text-purple-600 flex items-center justify-center"};return k[S]||k.document}function I(S){const k={project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",person:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",document:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"};return k[S]||k.document}function H(S){return{project:"Progetto",person:"Persona",document:"Documento",task:"Task"}[S]||"Elemento"}return(S,k)=>(r(),n("div",ms,[e("button",{onClick:k[0]||(k[0]=A=>d.value=!d.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},k[7]||(k[7]=[e("span",{class:"sr-only"},"Cerca",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),d.value?(r(),n("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:k[6]||(k[6]=se(A=>d.value=!1,["self"]))},[e("div",ps,[k[11]||(k[11]=e("div",{class:"fixed inset-0 bg-black bg-opacity-25 transition-opacity"},null,-1)),e("div",vs,[e("div",null,[e("div",gs,[e("div",hs,[R(e("input",{ref_key:"searchInput",ref:p,"onUpdate:modelValue":k[1]||(k[1]=A=>t.value=A),onInput:a,onKeydown:[k[2]||(k[2]=J(A=>d.value=!1,["escape"])),J(g,["enter"]),k[3]||(k[3]=J(A=>o(-1),["up"])),k[4]||(k[4]=J(A=>o(1),["down"]))],type:"text",placeholder:"Cerca progetti, persone, documenti...",class:"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"},null,544),[[K,t.value]])]),e("button",{onClick:k[5]||(k[5]=A=>d.value=!1),class:"ml-3 p-2 text-gray-400 hover:text-gray-600"},k[8]||(k[8]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),s.value.length>0?(r(),n("div",fs,[e("div",xs,[(r(!0),n(V,null,P(s.value,(A,G)=>(r(),n("div",{key:A.id,onClick:re=>f(A),class:M(["flex items-center px-3 py-2 rounded-md cursor-pointer",G===c.value?"bg-primary-50":"hover:bg-gray-50"])},[e("div",_s,[e("div",{class:M(j(A.type))},[(r(),n("svg",bs,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:I(A.type)},null,8,ks)]))],2)]),e("div",ws,[e("p",$s,u(A.title),1),e("p",Cs,u(A.description),1)]),e("div",Ms,u(H(A.type)),1)],10,ys))),128))])])):t.value&&!v.value?(r(),n("div",js,k[9]||(k[9]=[e("p",{class:"text-sm text-gray-500"},"Nessun risultato trovato",-1)]))):t.value?b("",!0):(r(),n("div",zs,k[10]||(k[10]=[e("p",{class:"text-xs text-gray-400"},"Inizia a digitare per cercare...",-1)])))])])])])):b("",!0)]))}},Is={class:"relative"},As={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},Bs={class:"text-sm font-medium text-primary-700"},Vs={class:"py-1"},Ps={class:"px-4 py-2 border-b border-gray-100"},Ds={class:"text-sm font-medium text-gray-900"},Hs={class:"text-xs text-gray-500"},Es={__name:"HeaderUserMenu",setup(i){const l=W(),d=F(),t=w(!1),s=_(()=>d.user&&(d.user.name||d.user.username)||"Utente"),c=_(()=>{var m;return((m=d.user)==null?void 0:m.email)||""}),v=_(()=>d.user?s.value.charAt(0).toUpperCase():"U");async function p(){t.value=!1,await d.logout(),l.push("/auth/login")}return(m,a)=>{const o=B("router-link");return r(),n("div",Is,[e("button",{onClick:a[0]||(a[0]=g=>t.value=!t.value),class:"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[a[4]||(a[4]=e("span",{class:"sr-only"},"Apri menu utente",-1)),e("div",As,[e("span",Bs,u(v.value),1)])]),t.value?(r(),n("div",{key:0,onClick:a[3]||(a[3]=g=>t.value=!1),class:"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",Vs,[e("div",Ps,[e("p",Ds,u(s.value),1),e("p",Hs,u(c.value),1)]),h(o,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:a[1]||(a[1]=g=>t.value=!1)},{default:$(()=>a[5]||(a[5]=[C(" Il tuo profilo ")])),_:1,__:[5]}),h(o,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:a[2]||(a[2]=g=>t.value=!1)},{default:$(()=>a[6]||(a[6]=[C(" Impostazioni ")])),_:1,__:[6]}),a[7]||(a[7]=e("div",{class:"border-t border-gray-100 my-1"},null,-1)),e("button",{onClick:p,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Esci ")])])):b("",!0)])}}},Ts={class:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"},Ls={class:"flex justify-between items-center px-4 py-4 sm:px-6 lg:px-8"},Ns={class:"flex items-center space-x-4"},Rs={class:"flex flex-col"},Us={class:"text-lg font-semibold text-gray-900 dark:text-white"},Fs={class:"flex items-center space-x-4"},qs={class:"hidden md:flex items-center space-x-2"},Os={__name:"AppHeader",props:{pageTitle:{type:String,required:!0},breadcrumbs:{type:Array,default:()=>[]}},emits:["toggle-mobile-sidebar"],setup(i){return(l,d)=>(r(),n("header",Ts,[e("div",Ls,[e("div",Ns,[e("button",{onClick:d[0]||(d[0]=t=>l.$emit("toggle-mobile-sidebar")),class:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700"},d[1]||(d[1]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)])),e("div",Rs,[e("h2",Us,u(i.pageTitle),1),i.breadcrumbs.length>0?(r(),U(Ft,{key:0,breadcrumbs:i.breadcrumbs},null,8,["breadcrumbs"])):b("",!0)])]),e("div",Fs,[e("div",qs,[h(Gt)]),h(us),h(Ss),h(Es)])])]))}},Ks={__name:"LoadingSpinner",props:{size:{type:String,default:"md",validator:i=>["sm","md","lg","xl"].includes(i)},message:{type:String,default:""},centered:{type:Boolean,default:!0}},setup(i){const l=i,d=_(()=>{const v={sm:"20px",md:"32px",lg:"48px",xl:"64px"};return`width: ${v[l.size]}; height: ${v[l.size]};`}),t=_(()=>["flex",l.centered?"items-center justify-center":"","space-y-2"]),s=_(()=>["flex items-center justify-center"]),c=_(()=>["text-sm text-gray-600 text-center"]);return(v,p)=>(r(),n("div",{class:M(t.value)},[e("div",{class:M(s.value)},[e("div",{class:"animate-spin rounded-full border-2 border-gray-300 border-t-primary-600",style:ye(d.value)},null,4)],2),i.message?(r(),n("p",{key:0,class:M(c.value)},u(i.message),3)):b("",!0)],2))}},X=(i,l)=>{const d=i.__vccOpts||i;for(const[t,s]of l)d[t]=s;return d},Gs={class:"fixed bottom-0 right-0 z-50 p-6 space-y-4"},Ws={class:"p-4"},Js={class:"flex items-start"},Qs={class:"flex-shrink-0"},Ys={class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Xs=["d"],Zs={class:"ml-3 w-0 flex-1 pt-0.5"},eo={class:"text-sm font-medium text-gray-900"},to={class:"mt-1 text-sm text-gray-500"},so={class:"ml-4 flex-shrink-0 flex"},oo=["onClick"],ro={__name:"NotificationManager",setup(i){const l=w([]);function d(p){const m=Date.now(),a={id:m,type:p.type||"info",title:p.title,message:p.message,duration:p.duration||5e3};l.value.push(a),a.duration>0&&setTimeout(()=>{t(m)},a.duration)}function t(p){const m=l.value.findIndex(a=>a.id===p);m>-1&&l.value.splice(m,1)}function s(p){const m={success:"border-l-4 border-green-400",error:"border-l-4 border-red-400",warning:"border-l-4 border-yellow-400",info:"border-l-4 border-blue-400"};return m[p]||m.info}function c(p){const m={success:"h-8 w-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center",error:"h-8 w-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center",warning:"h-8 w-8 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center",info:"h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center"};return m[p]||m.info}function v(p){const m={success:"M5 13l4 4L19 7",error:"M6 18L18 6M6 6l12 12",warning:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-2.694-.833-3.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z",info:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return m[p]||m.info}return window.showNotification=d,L(()=>{}),(p,m)=>(r(),n("div",Gs,[h(Ie,{name:"notification",tag:"div",class:"space-y-4"},{default:$(()=>[(r(!0),n(V,null,P(l.value,a=>(r(),n("div",{key:a.id,class:M([s(a.type),"max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"])},[e("div",Ws,[e("div",Js,[e("div",Qs,[e("div",{class:M(c(a.type))},[(r(),n("svg",Ys,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:v(a.type)},null,8,Xs)]))],2)]),e("div",Zs,[e("p",eo,u(a.title),1),e("p",to,u(a.message),1)]),e("div",so,[e("button",{onClick:o=>t(a.id),class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},m[0]||(m[0]=[e("span",{class:"sr-only"},"Chiudi",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,oo)])])])],2))),128))]),_:1})]))}},ao=X(ro,[["__scopeId","data-v-220f0827"]]),no={class:"h-screen flex bg-gray-50"},io={class:"flex flex-col flex-1 overflow-hidden lg:ml-64"},lo={class:"flex-1 overflow-y-auto"},co={class:"py-6"},uo={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},mo={key:0,class:"mb-6"},po={key:1,class:"flex items-center justify-center h-64"},vo={__name:"AppLayout",setup(i){const l=te(),d=q(),t=w(!1),s=w(!1);_(()=>d.config||{});const c=_(()=>d.config!==null),v=_(()=>{var f;return(f=l.meta)!=null&&f.title?l.meta.title:{dashboard:"Dashboard",projects:"Progetti","projects-list":"Elenco Progetti","projects-view":"Dettaglio Progetto","projects-create":"Nuovo Progetto",personnel:"Personale","personnel-directory":"Rubrica Aziendale","personnel-orgchart":"Organigramma","personnel-skills":"Competenze"}[l.name]||"DatPortal"}),p=_(()=>{var g;return(g=l.meta)!=null&&g.breadcrumbs?l.meta.breadcrumbs.map(f=>({label:f.label,to:f.to,icon:f.icon})):[]}),m=_(()=>{var g;return((g=l.meta)==null?void 0:g.hasActions)||!1});function a(){t.value=!t.value}function o(){t.value=!1}return Y(l,()=>{s.value=!0,setTimeout(()=>{s.value=!1},300)}),Y(l,()=>{o()}),L(()=>{c.value||d.loadConfig()}),(g,f)=>{const j=B("router-view");return r(),n("div",no,[t.value?(r(),n("div",{key:0,onClick:o,class:"fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"})):b("",!0),h(Dt,{"is-mobile-open":t.value,onClose:o},null,8,["is-mobile-open"]),e("div",io,[h(Os,{"page-title":v.value,breadcrumbs:p.value,onToggleMobileSidebar:a},null,8,["page-title","breadcrumbs"]),e("main",lo,[e("div",co,[e("div",uo,[m.value?(r(),n("div",mo,[Ae(g.$slots,"page-actions")])):b("",!0),s.value?(r(),n("div",po,[h(Ks)])):(r(),U(j,{key:2}))])])])]),h(ao)])}}},go={class:"min-h-screen bg-gray-50"},ho={class:"bg-white shadow-sm border-b"},fo={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},xo={class:"flex justify-between h-16"},yo={class:"flex items-center"},_o={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},bo={class:"text-white font-bold text-sm"},ko={class:"text-xl font-semibold text-gray-900"},wo={class:"hidden md:flex items-center space-x-8"},$o={class:"flex items-center space-x-4 ml-8 pl-8 border-l border-gray-200"},Co={class:"md:hidden flex items-center"},Mo={key:0,class:"md:hidden"},jo={class:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t"},zo={class:"bg-gray-800 text-white"},So={class:"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8"},Io={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},Ao={class:"col-span-1 md:col-span-2"},Bo={class:"flex items-center space-x-3 mb-4"},Vo={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Po={class:"text-white font-bold text-sm"},Do={class:"text-xl font-semibold"},Ho={class:"text-gray-300 max-w-md"},Eo={class:"space-y-2"},To={class:"space-y-2 text-gray-300"},Lo={key:0},No={key:1},Ro={key:2},Uo={class:"mt-8 pt-8 border-t border-gray-700 text-center text-gray-400"},he={__name:"PublicLayout",setup(i){const l=q(),d=w(!1),t=_(()=>l.config||{}),s=_(()=>{var m;return((m=t.value.company)==null?void 0:m.name)||"DatVinci"}),c=_(()=>s.value.split(" ").map(a=>a[0]).join("").toUpperCase().slice(0,2)),v=_(()=>l.config!==null),p=new Date().getFullYear();return L(()=>{v.value||l.loadConfig()}),(m,a)=>{var f,j,I,H,S,k;const o=B("router-link"),g=B("router-view");return r(),n("div",go,[e("nav",ho,[e("div",fo,[e("div",xo,[e("div",yo,[h(o,{to:"/",class:"flex items-center space-x-3"},{default:$(()=>[e("div",_o,[e("span",bo,u(c.value),1)]),e("span",ko,u(s.value),1)]),_:1})]),e("div",wo,[h(o,{to:"/",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:$(()=>a[1]||(a[1]=[C(" Home ")])),_:1,__:[1]}),h(o,{to:"/about",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:$(()=>a[2]||(a[2]=[C(" Chi Siamo ")])),_:1,__:[2]}),h(o,{to:"/services",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:$(()=>a[3]||(a[3]=[C(" Servizi ")])),_:1,__:[3]}),h(o,{to:"/contact",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:$(()=>a[4]||(a[4]=[C(" Contatti ")])),_:1,__:[4]}),e("div",$o,[h(o,{to:"/auth/login",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:$(()=>a[5]||(a[5]=[C(" Accedi ")])),_:1,__:[5]}),h(o,{to:"/auth/register",class:"bg-primary-600 text-white hover:bg-primary-700 px-4 py-2 rounded-md text-sm font-medium"},{default:$(()=>a[6]||(a[6]=[C(" Registrati ")])),_:1,__:[6]})])]),e("div",Co,[e("button",{onClick:a[0]||(a[0]=A=>d.value=!d.value),class:"text-gray-400 hover:text-gray-500"},a[7]||(a[7]=[e("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)]))])])]),d.value?(r(),n("div",Mo,[e("div",jo,[h(o,{to:"/",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:$(()=>a[8]||(a[8]=[C(" Home ")])),_:1,__:[8]}),h(o,{to:"/about",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:$(()=>a[9]||(a[9]=[C(" Chi Siamo ")])),_:1,__:[9]}),h(o,{to:"/services",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:$(()=>a[10]||(a[10]=[C(" Servizi ")])),_:1,__:[10]}),h(o,{to:"/contact",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:$(()=>a[11]||(a[11]=[C(" Contatti ")])),_:1,__:[11]}),a[14]||(a[14]=e("hr",{class:"my-2"},null,-1)),h(o,{to:"/auth/login",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:$(()=>a[12]||(a[12]=[C(" Accedi ")])),_:1,__:[12]}),h(o,{to:"/auth/register",class:"block px-3 py-2 text-base font-medium bg-primary-600 text-white rounded-md"},{default:$(()=>a[13]||(a[13]=[C(" Registrati ")])),_:1,__:[13]})])])):b("",!0)]),e("main",null,[h(g)]),e("footer",zo,[e("div",So,[e("div",Io,[e("div",Ao,[e("div",Bo,[e("div",Vo,[e("span",Po,u(c.value),1)]),e("span",Do,u(s.value),1)]),e("p",Ho,u(D(l).interpolateText((f=t.value.footer)==null?void 0:f.description)||((j=t.value.company)==null?void 0:j.description)||"Innovazione e tecnologia per il futuro digitale della tua azienda."),1)]),e("div",null,[a[19]||(a[19]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Link Rapidi",-1)),e("ul",Eo,[e("li",null,[h(o,{to:"/",class:"text-gray-300 hover:text-white"},{default:$(()=>a[15]||(a[15]=[C("Home")])),_:1,__:[15]})]),e("li",null,[h(o,{to:"/about",class:"text-gray-300 hover:text-white"},{default:$(()=>a[16]||(a[16]=[C("Chi Siamo")])),_:1,__:[16]})]),e("li",null,[h(o,{to:"/services",class:"text-gray-300 hover:text-white"},{default:$(()=>a[17]||(a[17]=[C("Servizi")])),_:1,__:[17]})]),e("li",null,[h(o,{to:"/contact",class:"text-gray-300 hover:text-white"},{default:$(()=>a[18]||(a[18]=[C("Contatti")])),_:1,__:[18]})])])]),e("div",null,[a[20]||(a[20]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Contatti",-1)),e("ul",To,[(I=t.value.contact)!=null&&I.email?(r(),n("li",Lo,u(t.value.contact.email),1)):b("",!0),(H=t.value.contact)!=null&&H.phone?(r(),n("li",No,u(t.value.contact.phone),1)):b("",!0),(S=t.value.contact)!=null&&S.address?(r(),n("li",Ro,u(t.value.contact.address),1)):b("",!0)])])]),e("div",Uo,[e("p",null,u(D(l).interpolateText((k=t.value.footer)==null?void 0:k.copyright)||`© ${D(p)} ${s.value}. Tutti i diritti riservati.`),1)])])])])}}},Fo={class:"bg-white"},qo={class:"relative overflow-hidden"},Oo={class:"max-w-7xl mx-auto"},Ko={class:"relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32"},Go={class:"mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28"},Wo={class:"sm:text-center lg:text-left"},Jo={class:"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl"},Qo={class:"block xl:inline"},Yo={class:"mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0"},Xo={class:"mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start"},Zo={class:"rounded-md shadow"},er={class:"mt-3 sm:mt-0 sm:ml-3"},tr={class:"py-12 bg-white"},sr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},or={class:"lg:text-center"},rr={class:"text-base text-primary-600 font-semibold tracking-wide uppercase"},ar={class:"mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl"},nr={key:0,class:"mt-10"},ir={class:"space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10"},lr={class:"absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white"},dr={class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},cr={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},ur={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},mr={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},pr={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"},vr={class:"ml-16 text-lg leading-6 font-medium text-gray-900"},gr={class:"mt-2 ml-16 text-base text-gray-500"},hr={__name:"Home",setup(i){const l=q(),d=_(()=>l.config||{}),t=_(()=>{var c;return((c=d.value.pages)==null?void 0:c.home)||{}}),s=_(()=>d.value.company||{});return L(()=>{l.config||l.loadConfig()}),(c,v)=>{var m,a,o,g;const p=B("router-link");return r(),n("div",Fo,[e("div",qo,[e("div",Oo,[e("div",Ko,[e("main",Go,[e("div",Wo,[e("h1",Jo,[e("span",Qo,u(((m=t.value.hero)==null?void 0:m.title)||"Innovazione per il futuro"),1)]),e("p",Yo,u(((a=t.value.hero)==null?void 0:a.subtitle)||D(l).interpolateText(s.value.description)||"Supportiamo le aziende nel loro percorso di crescita attraverso soluzioni tecnologiche all'avanguardia"),1),e("div",Xo,[e("div",Zo,[h(p,{to:"/services",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 md:py-4 md:text-lg md:px-10"},{default:$(()=>{var f;return[C(u(((f=t.value.hero)==null?void 0:f.cta_primary)||"Scopri i nostri servizi"),1)]}),_:1})]),e("div",er,[h(p,{to:"/contact",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 md:py-4 md:text-lg md:px-10"},{default:$(()=>{var f;return[C(u(((f=t.value.hero)==null?void 0:f.cta_secondary)||"Contattaci"),1)]}),_:1})])])])])])]),v[0]||(v[0]=e("div",{class:"lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2"},[e("div",{class:"h-56 w-full bg-gradient-to-r from-primary-400 to-primary-600 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center"},[e("svg",{class:"h-24 w-24 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])])],-1))]),e("div",tr,[e("div",sr,[e("div",or,[e("h2",rr,u(((o=t.value.services_section)==null?void 0:o.title)||"I nostri servizi"),1),e("p",ar,u(((g=t.value.services_section)==null?void 0:g.subtitle)||"Soluzioni innovative per ogni esigenza aziendale"),1)]),s.value.platform_features?(r(),n("div",nr,[e("div",ir,[(r(!0),n(V,null,P(s.value.platform_features,f=>(r(),n("div",{key:f.title,class:"relative"},[e("div",lr,[(r(),n("svg",dr,[f.icon==="briefcase"?(r(),n("path",cr)):f.icon==="users"?(r(),n("path",ur)):f.icon==="chart"?(r(),n("path",mr)):(r(),n("path",pr))]))]),e("p",vr,u(f.title),1),e("p",gr,u(f.description),1)]))),128))])])):b("",!0)])])])}}},fr={class:"py-16 bg-white"},xr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},yr={class:"text-center"},_r={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},br={class:"mt-4 text-xl text-gray-600"},kr={key:0,class:"mt-16"},wr={class:"max-w-3xl mx-auto"},$r={class:"text-3xl font-bold text-gray-900 text-center mb-8"},Cr={class:"text-lg text-gray-700 leading-relaxed"},Mr={class:"mt-16 grid grid-cols-1 md:grid-cols-2 gap-12"},jr={key:0,class:"bg-gray-50 p-8 rounded-lg"},zr={class:"text-2xl font-bold text-gray-900 mb-4"},Sr={class:"text-gray-700"},Ir={key:1,class:"bg-gray-50 p-8 rounded-lg"},Ar={class:"text-2xl font-bold text-gray-900 mb-4"},Br={class:"text-gray-700"},Vr={key:1,class:"mt-16"},Pr={class:"text-center mb-12"},Dr={class:"text-3xl font-bold text-gray-900"},Hr={class:"mt-4 text-xl text-gray-600"},Er={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Tr={class:"text-lg font-semibold text-gray-900"},Lr={key:2,class:"mt-16"},Nr={class:"text-center"},Rr={class:"text-3xl font-bold text-gray-900"},Ur={class:"mt-4 text-xl text-gray-600"},Fr={class:"mt-8 inline-flex items-center px-6 py-3 bg-primary-50 rounded-lg"},qr={class:"text-primary-900 font-medium"},Or={__name:"About",setup(i){const l=q(),d=_(()=>l.config||{}),t=_(()=>{var c;return((c=d.value.pages)==null?void 0:c.about)||{}}),s=_(()=>d.value.company||{});return L(()=>{l.config||l.loadConfig()}),(c,v)=>{var p,m;return r(),n("div",fr,[e("div",xr,[e("div",yr,[e("h1",_r,u(((p=t.value.hero)==null?void 0:p.title)||"Chi Siamo"),1),e("p",br,u(((m=t.value.hero)==null?void 0:m.subtitle)||"La nostra storia e i nostri valori"),1)]),t.value.story_section?(r(),n("div",kr,[e("div",wr,[e("h2",$r,u(t.value.story_section.title),1),e("p",Cr,u(D(l).interpolateText(t.value.story_section.content)),1)])])):b("",!0),e("div",Mr,[t.value.mission_section?(r(),n("div",jr,[e("h3",zr,u(t.value.mission_section.title),1),e("p",Sr,u(D(l).interpolateText(t.value.mission_section.content)),1)])):b("",!0),t.value.vision_section?(r(),n("div",Ir,[e("h3",Ar,u(t.value.vision_section.title),1),e("p",Br,u(D(l).interpolateText(t.value.vision_section.content)),1)])):b("",!0)]),t.value.expertise_section&&s.value.expertise?(r(),n("div",Vr,[e("div",Pr,[e("h2",Dr,u(t.value.expertise_section.title),1),e("p",Hr,u(t.value.expertise_section.subtitle),1)]),e("div",Er,[(r(!0),n(V,null,P(s.value.expertise,a=>(r(),n("div",{key:a,class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200 text-center"},[v[0]||(v[0]=e("div",{class:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-6 h-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Tr,u(a),1)]))),128))])])):b("",!0),t.value.team_section?(r(),n("div",Lr,[e("div",Nr,[e("h2",Rr,u(t.value.team_section.title),1),e("p",Ur,u(t.value.team_section.subtitle),1),e("div",Fr,[v[1]||(v[1]=e("svg",{class:"w-5 h-5 text-primary-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),e("span",qr,u(s.value.team_size),1)])])])):b("",!0)])])}}},Kr={class:"py-16 bg-white"},Gr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Wr={class:"text-center"},Jr={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Qr={class:"mt-4 text-xl text-gray-600"},Yr={key:0,class:"mt-8 text-center"},Xr={class:"text-lg text-gray-700 max-w-3xl mx-auto"},Zr={class:"mt-16 grid grid-cols-1 lg:grid-cols-2 gap-16"},ea={key:0},ta={class:"text-2xl font-bold text-gray-900 mb-8"},sa={class:"block text-sm font-medium text-gray-700 mb-2"},oa={class:"block text-sm font-medium text-gray-700 mb-2"},ra={class:"block text-sm font-medium text-gray-700 mb-2"},aa=["disabled"],na={key:1},ia={class:"text-2xl font-bold text-gray-900 mb-8"},la={class:"space-y-6"},da={key:0,class:"flex items-start"},ca={class:"font-medium text-gray-900"},ua={class:"text-gray-600"},ma={key:1,class:"flex items-start"},pa={class:"font-medium text-gray-900"},va={class:"text-gray-600"},ga={key:2,class:"flex items-start"},ha={class:"font-medium text-gray-900"},fa={class:"text-gray-600"},xa={key:3,class:"flex items-start"},ya={class:"font-medium text-gray-900"},_a={class:"text-gray-600"},ba={__name:"Contact",setup(i){const l=q(),d=_(()=>l.config||{}),t=_(()=>{var a;return((a=d.value.pages)==null?void 0:a.contact)||{}}),s=_(()=>d.value.contact||{}),c=w({name:"",email:"",message:""}),v=w(!1),p=w({text:"",type:""}),m=async()=>{var a,o;if(!c.value.name||!c.value.email||!c.value.message){p.value={text:((a=t.value.form)==null?void 0:a.error_message)||"Tutti i campi sono obbligatori",type:"error"};return}v.value=!0,p.value={text:"",type:""};try{await new Promise(g=>setTimeout(g,1e3)),p.value={text:((o=t.value.form)==null?void 0:o.success_message)||"Messaggio inviato con successo!",type:"success"},c.value={name:"",email:"",message:""}}catch{p.value={text:"Errore durante l'invio. Riprova più tardi.",type:"error"}}finally{v.value=!1}};return L(()=>{l.config||l.loadConfig()}),(a,o)=>{var g,f;return r(),n("div",Kr,[e("div",Gr,[e("div",Wr,[e("h1",Jr,u(((g=t.value.hero)==null?void 0:g.title)||"Contattaci"),1),e("p",Qr,u(((f=t.value.hero)==null?void 0:f.subtitle)||"Siamo qui per aiutarti"),1)]),t.value.intro?(r(),n("div",Yr,[e("p",Xr,u(t.value.intro.content),1)])):b("",!0),e("div",Zr,[t.value.form?(r(),n("div",ea,[e("h2",ta,u(t.value.form.title),1),e("form",{onSubmit:se(m,["prevent"]),class:"space-y-6"},[e("div",null,[e("label",sa,u(t.value.form.name_label),1),R(e("input",{"onUpdate:modelValue":o[0]||(o[0]=j=>c.value.name=j),type:"text",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[K,c.value.name]])]),e("div",null,[e("label",oa,u(t.value.form.email_label),1),R(e("input",{"onUpdate:modelValue":o[1]||(o[1]=j=>c.value.email=j),type:"email",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[K,c.value.email]])]),e("div",null,[e("label",ra,u(t.value.form.message_label),1),R(e("textarea",{"onUpdate:modelValue":o[2]||(o[2]=j=>c.value.message=j),rows:"6",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[K,c.value.message]])]),e("button",{type:"submit",disabled:v.value,class:"w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50"},u(v.value?"Invio in corso...":t.value.form.submit_button),9,aa),p.value.text?(r(),n("div",{key:0,class:M([p.value.type==="success"?"text-green-600":"text-red-600","text-sm mt-2"])},u(p.value.text),3)):b("",!0)],32)])):b("",!0),t.value.info?(r(),n("div",na,[e("h2",ia,u(t.value.info.title),1),e("div",la,[s.value.address?(r(),n("div",da,[o[3]||(o[3]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),e("div",null,[e("h3",ca,u(t.value.info.address_label),1),e("p",ua,u(s.value.address),1)])])):b("",!0),s.value.phone?(r(),n("div",ma,[o[4]||(o[4]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})],-1)),e("div",null,[e("h3",pa,u(t.value.info.phone_label),1),e("p",va,u(s.value.phone),1)])])):b("",!0),s.value.email?(r(),n("div",ga,[o[5]||(o[5]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1)),e("div",null,[e("h3",ha,u(t.value.info.email_label),1),e("p",fa,u(s.value.email),1)])])):b("",!0),s.value.hours?(r(),n("div",xa,[o[6]||(o[6]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",null,[e("h3",ya,u(t.value.info.hours_label),1),e("p",_a,u(s.value.hours),1)])])):b("",!0)])])):b("",!0)])])])}}},ka={class:"py-16 bg-white"},wa={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},$a={class:"text-center"},Ca={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Ma={class:"mt-4 text-xl text-gray-600"},ja={key:0,class:"mt-8 text-center"},za={class:"text-lg text-gray-700 max-w-3xl mx-auto"},Sa={key:1,class:"mt-16"},Ia={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Aa={class:"text-xl font-bold text-gray-900 text-center mb-4"},Ba={class:"text-gray-600 text-center"},Va={key:2,class:"mt-20"},Pa={class:"bg-primary-50 rounded-2xl p-12 text-center"},Da={class:"text-3xl font-bold text-gray-900 mb-4"},Ha={class:"text-xl text-gray-600 mb-8"},Ea={__name:"Services",setup(i){const l=q(),d=_(()=>l.config||{}),t=_(()=>{var v;return((v=d.value.pages)==null?void 0:v.services)||{}}),s=_(()=>d.value.company||{}),c=v=>({"Sviluppo Software":"Soluzioni software personalizzate per ottimizzare i processi aziendali e migliorare l'efficienza operativa.","Intelligenza Artificiale":"Implementazione di sistemi AI avanzati per automatizzare processi e analizzare dati complessi.","Consulenza IT":"Consulenza strategica per la trasformazione digitale e l'ottimizzazione dell'infrastruttura tecnologica.","Gestione Progetti Innovativi":"Coordinamento e gestione di progetti tecnologici complessi con metodologie agili.","Supporto su Bandi e Finanziamenti":"Assistenza nella ricerca e gestione di bandi pubblici e finanziamenti per l'innovazione."})[v]||"Servizio professionale di alta qualità per supportare la crescita della tua azienda.";return L(()=>{l.config||l.loadConfig()}),(v,p)=>{var a,o;const m=B("router-link");return r(),n("div",ka,[e("div",wa,[e("div",$a,[e("h1",Ca,u(((a=t.value.hero)==null?void 0:a.title)||"I nostri servizi"),1),e("p",Ma,u(((o=t.value.hero)==null?void 0:o.subtitle)||"Soluzioni complete per la tua azienda"),1)]),t.value.intro?(r(),n("div",ja,[e("p",za,u(t.value.intro.content),1)])):b("",!0),s.value.expertise?(r(),n("div",Sa,[e("div",Ia,[(r(!0),n(V,null,P(s.value.expertise,g=>(r(),n("div",{key:g,class:"bg-white p-8 rounded-lg shadow-lg border border-gray-200 hover:shadow-xl transition-shadow"},[p[0]||(p[0]=e("div",{class:"w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-6"},[e("svg",{class:"w-8 h-8 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Aa,u(g),1),e("p",Ba,u(c(g)),1)]))),128))])])):b("",!0),t.value.cta?(r(),n("div",Va,[e("div",Pa,[e("h2",Da,u(t.value.cta.title),1),e("p",Ha,u(t.value.cta.subtitle),1),h(m,{to:"/contact",class:"inline-flex items-center px-8 py-4 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"},{default:$(()=>[C(u(t.value.cta.button)+" ",1),p[1]||(p[1]=e("svg",{class:"ml-2 w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 8l4 4m0 0l-4 4m4-4H3"})],-1))]),_:1,__:[1]})])])):b("",!0)])])}}},Ta={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},La={class:"max-w-md w-full space-y-8"},Na={class:"mt-2 text-center text-sm text-gray-600"},Ra={key:0,class:"rounded-md bg-red-50 p-4"},Ua={class:"text-sm text-red-700"},Fa={class:"rounded-md shadow-sm -space-y-px"},qa={class:"flex items-center justify-between"},Oa={class:"flex items-center"},Ka=["disabled"],Ga={key:0,class:"absolute left-0 inset-y-0 flex items-center pl-3"},Wa={__name:"Login",setup(i){const l=W(),d=F(),t=w({username:"",password:"",remember:!1}),s=_(()=>d.loading),c=_(()=>d.error);async function v(){(await d.login({username:t.value.username,password:t.value.password,remember:t.value.remember})).success&&l.push("/app/dashboard")}return(p,m)=>{const a=B("router-link");return r(),n("div",Ta,[e("div",La,[e("div",null,[m[5]||(m[5]=e("div",{class:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100"},[e("svg",{class:"h-6 w-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])],-1)),m[6]||(m[6]=e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Accedi al tuo account ",-1)),e("p",Na,[m[4]||(m[4]=C(" Oppure ")),h(a,{to:"/auth/register",class:"font-medium text-primary-600 hover:text-primary-500"},{default:$(()=>m[3]||(m[3]=[C(" registrati per un nuovo account ")])),_:1,__:[3]})])]),e("form",{onSubmit:se(v,["prevent"]),class:"mt-8 space-y-6"},[c.value?(r(),n("div",Ra,[e("div",Ua,u(c.value),1)])):b("",!0),e("div",Fa,[e("div",null,[m[7]||(m[7]=e("label",{for:"username",class:"sr-only"},"Username",-1)),R(e("input",{id:"username","onUpdate:modelValue":m[0]||(m[0]=o=>t.value.username=o),name:"username",type:"text",autocomplete:"username",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Username"},null,512),[[K,t.value.username]])]),e("div",null,[m[8]||(m[8]=e("label",{for:"password",class:"sr-only"},"Password",-1)),R(e("input",{id:"password","onUpdate:modelValue":m[1]||(m[1]=o=>t.value.password=o),name:"password",type:"password",autocomplete:"current-password",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Password"},null,512),[[K,t.value.password]])])]),e("div",qa,[e("div",Oa,[R(e("input",{id:"remember-me","onUpdate:modelValue":m[2]||(m[2]=o=>t.value.remember=o),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[Be,t.value.remember]]),m[9]||(m[9]=e("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Ricordami ",-1))]),m[10]||(m[10]=e("div",{class:"text-sm"},[e("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500"}," Password dimenticata? ")],-1))]),e("div",null,[e("button",{type:"submit",disabled:s.value,class:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"},[s.value?(r(),n("span",Ga,m[11]||(m[11]=[e("svg",{class:"h-5 w-5 text-primary-500 animate-spin",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)]))):b("",!0),C(" "+u(s.value?"Accesso in corso...":"Accedi"),1)],8,Ka)])],32)])])}}},Ja={},Qa={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"};function Ya(i,l){return r(),n("div",Qa,l[0]||(l[0]=[e("div",{class:"max-w-md w-full space-y-8"},[e("div",null,[e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Registra un nuovo account ")]),e("div",{class:"text-center text-gray-600"}," Registrazione in arrivo... ")],-1)]))}const Xa=X(Ja,[["render",Ya]]),Za={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},en={class:"p-5"},tn={class:"flex items-center"},sn={class:"ml-5 w-0 flex-1"},on={class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},rn={class:"text-lg font-medium text-gray-900 dark:text-white"},an={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},nn={key:0,class:"bg-gray-50 dark:bg-gray-700 px-5 py-3"},ln={class:"text-sm"},Q={__name:"StatsCard",props:{title:{type:String,required:!0},value:{type:[Number,String],required:!0},subtitle:{type:String,default:null},icon:{type:String,required:!0},color:{type:String,default:"primary"},link:{type:String,default:null}},setup(i){const l=t=>t==="project"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`}:t==="users"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>`}:t==="clock"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}:t==="team"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
      </svg>`}:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>`},d=t=>{const s={primary:"bg-primary-500",secondary:"bg-secondary-500",red:"bg-red-500",yellow:"bg-yellow-500",blue:"bg-blue-500",green:"bg-green-500"};return s[t]||s.primary};return(t,s)=>{const c=B("router-link");return r(),n("div",Za,[e("div",en,[e("div",tn,[e("div",{class:M(["flex-shrink-0 rounded-md p-3",d(i.color)])},[(r(),U(_e(l(i.icon)),{class:"h-6 w-6 text-white"}))],2),e("div",sn,[e("dl",null,[e("dt",on,u(i.title),1),e("dd",null,[e("div",rn,u(i.value),1),i.subtitle?(r(),n("div",an,u(i.subtitle),1)):b("",!0)])])])])]),i.link?(r(),n("div",nn,[e("div",ln,[h(c,{to:i.link,class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300"},{default:$(()=>s[0]||(s[0]=[C(" Vedi tutti ")])),_:1,__:[0]},8,["to"])])])):b("",!0)])}}},dn={class:"py-6"},cn={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},un={class:"mt-4 md:mt-0 flex space-x-3"},mn={class:"relative"},pn=["disabled"],vn={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},gn={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},hn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},fn={class:"relative h-64"},xn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},yn={class:"relative h-64"},_n={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},bn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},kn={class:"p-6"},wn={key:0,class:"text-center py-8 text-gray-500"},$n={key:1,class:"space-y-4"},Cn={class:"flex justify-between items-start"},Mn={class:"flex-1"},jn={class:"text-sm font-medium text-gray-900 dark:text-white"},zn={class:"text-xs text-gray-500 dark:text-gray-400"},Sn={class:"mt-2 flex justify-between items-center"},In={class:"text-xs text-gray-500 dark:text-gray-400"},An={class:"bg-gray-50 dark:bg-gray-700 px-6 py-3"},Bn={class:"text-sm"},Vn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Pn={class:"p-6"},Dn={key:0,class:"text-center py-8 text-gray-500"},Hn={key:1,class:"space-y-4"},En={class:"flex-shrink-0"},Tn={class:"flex-1 min-w-0"},Ln={class:"text-sm font-medium text-gray-900 dark:text-white"},Nn={class:"text-xs text-gray-500 dark:text-gray-400"},Rn={class:"text-xs text-gray-400 dark:text-gray-500"},Un={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Fn={class:"p-6"},qn={key:0,class:"text-center py-8 text-gray-500"},On={key:1,class:"space-y-4"},Kn={class:"flex justify-between items-start"},Gn={class:"flex-1"},Wn={class:"text-sm font-medium text-gray-900 dark:text-white"},Jn={class:"text-xs text-gray-500 dark:text-gray-400"},Qn={class:"text-right"},Yn={class:"text-sm font-bold text-gray-900 dark:text-white"},Xn={class:"text-xs text-gray-500"},Zn={class:"mt-2"},ei={class:"w-full bg-gray-200 rounded-full h-2"},ti={class:"text-xs text-gray-500 mt-1"},si={__name:"Dashboard",setup(i){Z.register(...Ve),W();const l=w(!1),d=w("7"),t=w({}),s=w([]),c=w([]),v=w([]),p=w(null),m=w(null);let a=null,o=null;const g=async()=>{try{const y=await fetch("/api/dashboard/stats");if(!y.ok)throw new Error("Failed to fetch stats");const x=await y.json();t.value=x.data}catch(y){console.error("Error fetching dashboard stats:",y),t.value={}}},f=async()=>{try{const y=await fetch(`/api/dashboard/upcoming-tasks?days=${d.value}&limit=5`);if(!y.ok)throw new Error("Failed to fetch upcoming tasks");const x=await y.json();s.value=x.data.tasks}catch(y){console.error("Error fetching upcoming tasks:",y),s.value=[]}},j=async()=>{try{const y=await fetch("/api/dashboard/recent-activities?limit=5");if(!y.ok)throw new Error("Failed to fetch recent activities");const x=await y.json();c.value=x.data.activities}catch(y){console.error("Error fetching recent activities:",y),c.value=[]}},I=async()=>{try{const y=await fetch("/api/dashboard/kpis?limit=3");if(!y.ok)throw new Error("Failed to fetch KPIs");const x=await y.json();v.value=x.data.kpis}catch(y){console.error("Error fetching KPIs:",y),v.value=[]}},H=async()=>{try{const y=await fetch("/api/dashboard/charts/project-status");if(!y.ok)throw new Error("Failed to fetch project chart data");const x=await y.json();k(x.data.chart)}catch(y){console.error("Error fetching project chart:",y)}},S=async()=>{try{const y=await fetch("/api/dashboard/charts/task-status");if(!y.ok)throw new Error("Failed to fetch task chart data");const x=await y.json();A(x.data.chart)}catch(y){console.error("Error fetching task chart:",y)}},k=y=>{if(!p.value)return;const x=p.value.getContext("2d");a&&a.destroy(),a=new Z(x,{type:"doughnut",data:{labels:y.labels,datasets:[{data:y.data,backgroundColor:["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6"],borderWidth:2,borderColor:"#ffffff"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}}}}})},A=y=>{if(!m.value)return;const x=m.value.getContext("2d");o&&o.destroy(),o=new Z(x,{type:"bar",data:{labels:y.labels,datasets:[{label:"Tasks",data:y.data,backgroundColor:["#60A5FA","#34D399","#FBBF24","#F87171"],borderColor:["#3B82F6","#10B981","#F59E0B","#EF4444"],borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,ticks:{stepSize:1}}}}})},G=async()=>{l.value=!0;try{await Promise.all([g(),f(),j(),I(),H(),S()])}finally{l.value=!1}},re=y=>new Date(y).toLocaleDateString("it-IT"),we=y=>{const x=new Date(y),N=Math.floor((new Date-x)/(1e3*60));return N<60?`${N} minuti fa`:N<1440?`${Math.floor(N/60)} ore fa`:`${Math.floor(N/1440)} giorni fa`},$e=y=>{const x={high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return x[y]||x.medium},Ce=y=>{const x={todo:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300","in-progress":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",review:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",done:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return x[y]||x.todo},Me=y=>{const x={task:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`},timesheet:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`},event:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`}};return x[y]||x.task},je=y=>{const x={task:"bg-blue-100 text-blue-600",timesheet:"bg-green-100 text-green-600",event:"bg-purple-100 text-purple-600"};return x[y]||x.task},ze=y=>y>=90?"bg-green-500":y>=70?"bg-yellow-500":"bg-red-500";return L(async()=>{await G(),await xe(),p.value&&m.value&&(await H(),await S())}),(y,x)=>{var N,ne,ie,le,de,ce,ue,me;const ae=B("router-link");return r(),n("div",dn,[e("div",cn,[x[4]||(x[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Dashboard"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Benvenuto! Ecco una panoramica delle attività della tua azienda. ")],-1)),e("div",un,[e("div",mn,[R(e("select",{"onUpdate:modelValue":x[0]||(x[0]=z=>d.value=z),onChange:G,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500"},x[1]||(x[1]=[e("option",{value:"7"},"Ultimi 7 giorni",-1),e("option",{value:"30"},"Ultimo mese",-1),e("option",{value:"90"},"Ultimi 3 mesi",-1)]),544),[[Pe,d.value]])]),e("button",{onClick:G,disabled:l.value,type:"button",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(r(),n("svg",{xmlns:"http://www.w3.org/2000/svg",class:M(["h-4 w-4 mr-2",{"animate-spin":l.value}]),viewBox:"0 0 20 20",fill:"currentColor"},x[2]||(x[2]=[e("path",{"fill-rule":"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z","clip-rule":"evenodd"},null,-1)]),2)),x[3]||(x[3]=C(" Aggiorna "))],8,pn)])]),e("div",vn,[h(Q,{title:"Progetti Attivi",value:((N=t.value.projects)==null?void 0:N.active)||0,subtitle:`di ${((ne=t.value.projects)==null?void 0:ne.total)||0} totali`,icon:"project",color:"primary",link:"/projects?status=active"},null,8,["value","subtitle"]),h(Q,{title:"Clienti",value:((ie=t.value.team)==null?void 0:ie.clients)||0,icon:"users",color:"secondary",link:"/crm/clients"},null,8,["value"]),h(Q,{title:"Task Pendenti",value:((le=t.value.tasks)==null?void 0:le.pending)||0,subtitle:`${((de=t.value.tasks)==null?void 0:de.overdue)||0} in ritardo`,icon:"clock",color:((ce=t.value.tasks)==null?void 0:ce.overdue)>0?"red":"yellow",link:"/tasks?status=pending"},null,8,["value","subtitle","color"]),h(Q,{title:"Team Members",value:((ue=t.value.team)==null?void 0:ue.users)||0,subtitle:`${((me=t.value.team)==null?void 0:me.departments)||0} dipartimenti`,icon:"team",color:"blue",link:"/personnel"},null,8,["value","subtitle"])]),e("div",gn,[e("div",hn,[x[5]||(x[5]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Progetti")],-1)),e("div",fn,[e("canvas",{ref_key:"projectChart",ref:p},null,512)])]),e("div",xn,[x[6]||(x[6]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Attività")],-1)),e("div",yn,[e("canvas",{ref_key:"taskChart",ref:m},null,512)])])]),e("div",_n,[e("div",bn,[e("div",kn,[x[7]||(x[7]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività in Scadenza",-1)),s.value.length===0?(r(),n("div",wn," Nessuna attività in scadenza ")):(r(),n("div",$n,[(r(!0),n(V,null,P(s.value,z=>(r(),n("div",{key:z.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",Cn,[e("div",Mn,[e("h3",jn,u(z.name),1),e("p",zn,u(z.project_name),1)]),e("span",{class:M(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",$e(z.priority)])},u(z.priority),3)]),e("div",Sn,[e("span",In," Scadenza: "+u(re(z.due_date)),1),e("span",{class:M(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",Ce(z.status)])},u(z.status),3)])]))),128))]))]),e("div",An,[e("div",Bn,[h(ae,{to:"/tasks",class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500"},{default:$(()=>x[8]||(x[8]=[C(" Vedi tutte le attività ")])),_:1,__:[8]})])])]),e("div",Vn,[e("div",Pn,[x[9]||(x[9]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività Recenti",-1)),c.value.length===0?(r(),n("div",Dn," Nessuna attività recente ")):(r(),n("div",Hn,[(r(!0),n(V,null,P(c.value,z=>(r(),n("div",{key:`${z.type}-${z.id}`,class:"flex items-start space-x-3"},[e("div",En,[e("div",{class:M(["w-8 h-8 rounded-full flex items-center justify-center",je(z.type)])},[(r(),U(_e(Me(z.type)),{class:"w-4 h-4"}))],2)]),e("div",Tn,[e("p",Ln,u(z.title),1),e("p",Nn,u(z.description),1),e("p",Rn,u(we(z.timestamp)),1)])]))),128))]))])]),e("div",Un,[e("div",Fn,[x[10]||(x[10]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"KPIs Principali",-1)),v.value.length===0?(r(),n("div",qn," Nessun KPI configurato ")):(r(),n("div",On,[(r(!0),n(V,null,P(v.value,z=>(r(),n("div",{key:z.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",Kn,[e("div",Gn,[e("h3",Wn,u(z.name),1),e("p",Jn,u(z.description),1)]),e("div",Qn,[e("p",Yn,u(z.current_value)+u(z.unit),1),e("p",Xn," Target: "+u(z.target_value)+u(z.unit),1)])]),e("div",Zn,[e("div",ei,[e("div",{class:M(["h-2 rounded-full",ze(z.performance_percentage)]),style:ye({width:Math.min(z.performance_percentage,100)+"%"})},null,6)]),e("p",ti,u(Math.round(z.performance_percentage))+"% del target",1)])]))),128))]))])])])])}}},oi={};function ri(i,l){return r(),n("div",null,l[0]||(l[0]=[e("h1",{class:"text-2xl font-bold text-gray-900 mb-6"},"Progetti",-1),e("div",{class:"card"},[e("p",{class:"text-gray-600"},"Gestione progetti in fase di migrazione...")],-1)]))}const ai=X(oi,[["render",ri]]),ni={};function ii(i,l){return r(),n("div",null,l[0]||(l[0]=[e("h1",{class:"text-2xl font-bold text-gray-900 mb-6"},"Personale",-1),e("div",{class:"card"},[e("p",{class:"text-gray-600"},"Gestione personale in fase di migrazione...")],-1)]))}const li=X(ni,[["render",ii]]),di=[{path:"/",component:he,children:[{path:"",name:"home",component:hr},{path:"about",name:"about",component:Or},{path:"contact",name:"contact",component:ba},{path:"services",name:"services",component:Ea}]},{path:"/auth",component:he,children:[{path:"login",name:"login",component:Wa},{path:"register",name:"register",component:Xa}]},{path:"/app",component:vo,meta:{requiresAuth:!0},children:[{path:"",redirect:"/app/dashboard"},{path:"dashboard",name:"dashboard",component:si},{path:"projects",name:"projects",component:ai},{path:"projects/:id",name:"project-view",component:()=>Fe(()=>import("./ProjectView.js"),__vite__mapDeps([0,1,2]))},{path:"personnel",name:"personnel",component:li}]}],ke=De({history:He(),routes:di});ke.beforeEach(async(i,l,d)=>{const t=F();if(i.meta.requiresAuth&&(t.sessionChecked||await t.initializeAuth(),!t.isAuthenticated)){d("/auth/login");return}d()});const oe=Ee(Ne),ci=Te();oe.use(ci);oe.use(ke);const ui=F();ui.initializeAuth().then(()=>{oe.mount("#app")});export{X as _,O as a,F as u};
