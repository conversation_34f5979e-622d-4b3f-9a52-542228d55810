const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ProjectView.js","assets/vendor.js","assets/ProjectView.css"])))=>i.map(i=>d[i]);
import{r as j,w as oe,c as n,a as h,b as L,o,d as Ae,e as de,f as b,g as _,n as B,h as G,i as I,t as u,u as ce,j as e,F as N,k as R,l as A,m as X,p as T,q as ze,s as ue,v as U,x as J,y as te,z as me,A as K,T as Be,B as Ee,C as Ve,D as Se,E as ne,G as De,H as ie,I as Le,J as He,K as Te,L as Ne,M as Re}from"./vendor.js";(function(){const i=document.createElement("link").relList;if(i&&i.supports&&i.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))t(s);new MutationObserver(s=>{for(const d of s)if(d.type==="childList")for(const v of d.addedNodes)v.tagName==="LINK"&&v.rel==="modulepreload"&&t(v)}).observe(document,{childList:!0,subtree:!0});function l(s){const d={};return s.integrity&&(d.integrity=s.integrity),s.referrerPolicy&&(d.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?d.credentials="include":s.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function t(s){if(s.ep)return;s.ep=!0;const d=l(s);fetch(s.href,d)}})();const q=j(!1);let we=!1;const Pe=a=>{a?(document.documentElement.classList.add("dark"),localStorage.setItem("darkMode","true")):(document.documentElement.classList.remove("dark"),localStorage.setItem("darkMode","false"))},Fe=()=>{we||(oe(q,a=>{Pe(a)}),we=!0)};function pe(){return Fe(),{isDarkMode:q,toggleDarkMode:()=>{q.value=!q.value},setDarkMode:t=>{q.value=t},initializeDarkMode:()=>{const t=localStorage.getItem("darkMode"),s=document.documentElement.classList.contains("dark");if(t==="true")q.value=!0;else if(t==="false")q.value=!1;else{const p=window.matchMedia("(prefers-color-scheme: dark)").matches;q.value=s||p}Pe(q.value);const d=window.matchMedia("(prefers-color-scheme: dark)"),v=p=>{const m=localStorage.getItem("darkMode");(!m||m==="null")&&(q.value=p.matches)};d.addEventListener("change",v)}}}const Ue={id:"app"},qe={__name:"App",setup(a){const{initializeDarkMode:i}=pe();return i(),(l,t)=>{const s=L("router-view");return o(),n("div",Ue,[h(s)])}}},Oe="modulepreload",Ke=function(a){return"/"+a},$e={},We=function(i,l,t){let s=Promise.resolve();if(l&&l.length>0){document.getElementsByTagName("link");const v=document.querySelector("meta[property=csp-nonce]"),p=(v==null?void 0:v.nonce)||(v==null?void 0:v.getAttribute("nonce"));s=Promise.allSettled(l.map(m=>{if(m=Ke(m),m in $e)return;$e[m]=!0;const c=m.endsWith(".css"),r=c?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${m}"]${r}`))return;const g=document.createElement("link");if(g.rel=c?"stylesheet":Oe,c||(g.as="script"),g.crossOrigin="",g.href=m,p&&g.setAttribute("nonce",p),document.head.appendChild(g),c)return new Promise((x,M)=>{g.addEventListener("load",x),g.addEventListener("error",()=>M(new Error(`Unable to preload CSS for ${m}`)))})}))}function d(v){const p=new Event("vite:preloadError",{cancelable:!0});if(p.payload=v,window.dispatchEvent(p),!p.defaultPrevented)throw v}return s.then(v=>{for(const p of v||[])p.status==="rejected"&&d(p.reason);return i().catch(d)})},F=Ae.create({baseURL:"",timeout:1e4,withCredentials:!0,headers:{"Content-Type":"application/json"}});F.interceptors.request.use(a=>{var l,t;const i=(l=document.querySelector('meta[name="csrf-token"]'))==null?void 0:l.getAttribute("content");return i&&["post","put","patch","delete"].includes((t=a.method)==null?void 0:t.toLowerCase())&&(a.headers["X-CSRFToken"]=i),a},a=>Promise.reject(a));F.interceptors.response.use(a=>a,a=>{var i;return((i=a.response)==null?void 0:i.status)===401&&(localStorage.removeItem("user"),console.warn("Sessione scaduta, autenticazione richiesta")),Promise.reject(a)});const Q=de("auth",()=>{const a=localStorage.getItem("user"),i=j(a?JSON.parse(a):null),l=j(!1),t=j(null),s=j(!1),d=b(()=>!!i.value&&s.value),v={admin:["admin","manage_users","assign_roles","view_all_projects","create_project","edit_project","delete_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","view_crm","manage_clients","manage_proposals","view_reports","view_dashboard","submit_timesheet","view_own_timesheets","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"],manager:["view_dashboard","view_all_projects","edit_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","view_crm","view_reports","submit_timesheet","view_own_timesheets","manage_clients","manage_proposals","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"],employee:["view_dashboard","view_own_timesheets","submit_timesheet"],sales:["view_dashboard","view_crm","manage_clients","manage_proposals","submit_timesheet","view_own_timesheets","view_reports","view_funding","view_products","manage_products"],human_resources:["view_dashboard","manage_users","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","submit_timesheet","view_own_timesheets","view_reports","view_funding","manage_funding","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"]},p=M=>!i.value||!i.value.role?!1:i.value.role==="admin"?!0:(v[i.value.role]||[]).includes(M);async function m(M){var V,D;l.value=!0,t.value=null;try{const S=await F.post("/api/auth/login",M);return S.data.success?(i.value=S.data.data.user,localStorage.setItem("user",JSON.stringify(i.value)),s.value=!0,{success:!0}):(t.value=S.data.message||"Errore durante il login",{success:!1,error:t.value})}catch(S){return t.value=((D=(V=S.response)==null?void 0:V.data)==null?void 0:D.message)||"Errore di connessione",{success:!1,error:t.value}}finally{l.value=!1}}async function c(M){var V,D;l.value=!0,t.value=null;try{const S=await F.post("/api/auth/register",M);return S.data.success?{success:!0,message:S.data.message}:(t.value=S.data.message||"Errore durante la registrazione",{success:!1,error:t.value})}catch(S){return t.value=((D=(V=S.response)==null?void 0:V.data)==null?void 0:D.message)||"Errore di connessione",{success:!1,error:t.value}}finally{l.value=!1}}async function r(){try{await F.post("/api/auth/logout")}catch(M){console.warn("Errore durante il logout:",M)}finally{i.value=null,s.value=!1,localStorage.removeItem("user")}}async function g(){if(s.value)return d.value;try{const M=await F.get("/api/auth/me");return M.data.success?(i.value=M.data.data.user,localStorage.setItem("user",JSON.stringify(i.value)),s.value=!0,!0):(await r(),!1)}catch{return await r(),!1}}async function x(){return i.value?await g():(s.value=!0,!1)}return{user:i,loading:l,error:t,sessionChecked:s,isAuthenticated:d,hasPermission:p,login:m,register:c,logout:r,checkAuth:g,initializeAuth:x}}),Y=de("tenant",()=>{const a=j(null),i=j(!1),l=j(null),t=b(()=>{var r;return((r=a.value)==null?void 0:r.company)||{}}),s=b(()=>{var r;return((r=a.value)==null?void 0:r.contact)||{}}),d=b(()=>{var r;return((r=a.value)==null?void 0:r.pages)||{}}),v=b(()=>{var r;return((r=a.value)==null?void 0:r.navigation)||{}}),p=b(()=>{var r;return((r=a.value)==null?void 0:r.footer)||{}});async function m(){try{if(i.value=!0,window.TENANT_CONFIG){a.value=window.TENANT_CONFIG;return}const r=await fetch("/api/config/tenant");a.value=await r.json()}catch(r){l.value="Errore nel caricamento della configurazione",console.error("Errore caricamento tenant config:",r)}finally{i.value=!1}}function c(r,g={}){if(!r||typeof r!="string")return r;let x=r;const M={"company.name":t.value.name||"DatVinci","company.tagline":t.value.tagline||"","company.description":t.value.description||"","company.mission":t.value.mission||"","company.vision":t.value.vision||"","company.founded":t.value.founded||"","company.team_size":t.value.team_size||"","contact.email":s.value.email||"","contact.phone":s.value.phone||"","contact.address":s.value.address||"",current_year:new Date().getFullYear().toString(),...g};for(const[V,D]of Object.entries(M)){const S=new RegExp(`\\{${V}\\}`,"g");x=x.replace(S,D||"")}return x}return{config:a,loading:i,error:l,company:t,contact:s,pages:d,navigation:v,footer:p,loadConfig:m,interpolateText:c}}),Ge={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"},Je={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},Qe={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},Ye={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},Xe={key:4,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"},Ze={key:5,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},et={key:6,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"},tt={key:7,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},st={key:8,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},ot={key:9,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"},rt={key:10,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},at={key:11,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},nt={key:12,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"},le={__name:"SidebarIcon",props:{icon:{type:String,required:!0},className:{type:String,default:"h-5 w-5"}},setup(a){return(i,l)=>(o(),n("svg",{class:B(a.className),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a.icon==="dashboard"?(o(),n("path",Ge)):a.icon==="projects"?(o(),n("path",Je)):a.icon==="users"?(o(),n("path",Qe)):a.icon==="clients"?(o(),n("path",Ye)):a.icon==="products"?(o(),n("path",Xe)):a.icon==="reports"?(o(),n("path",Ze)):a.icon==="settings"?(o(),n("path",et)):_("",!0),a.icon==="settings"?(o(),n("path",tt)):a.icon==="user-management"?(o(),n("path",st)):a.icon==="communications"?(o(),n("path",ot)):a.icon==="funding"?(o(),n("path",rt)):a.icon==="reporting"?(o(),n("path",at)):(o(),n("path",nt))],2))}},it={key:0,class:"truncate"},lt={key:0,class:"truncate"},O={__name:"SidebarNavItem",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(a){const i=b(()=>["text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400"]);return(l,t)=>{const s=L("router-link");return o(),n("div",null,[a.item.path!=="#"?(o(),G(s,{key:0,to:a.item.path,class:B(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[i.value,{"justify-center":a.isCollapsed}]]),"active-class":"text-primary-600 bg-primary-50 border-r-2 border-primary-600",onClick:t[0]||(t[0]=d=>l.$emit("click"))},{default:I(()=>[h(le,{icon:a.item.icon,class:B(["flex-shrink-0 h-6 w-6",{"mr-0":a.isCollapsed,"mr-3":!a.isCollapsed}])},null,8,["icon","class"]),a.isCollapsed?_("",!0):(o(),n("span",it,u(a.item.name),1))]),_:1},8,["to","class"])):(o(),n("div",{key:1,class:B(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 cursor-not-allowed opacity-75",["text-gray-400 hover:text-gray-500",{"justify-center":a.isCollapsed}]])},[h(le,{icon:a.item.icon,class:B(["flex-shrink-0 h-6 w-6",{"mr-0":a.isCollapsed,"mr-3":!a.isCollapsed}])},null,8,["icon","class"]),a.isCollapsed?_("",!0):(o(),n("span",lt,u(a.item.name),1))],2))])}}},dt={key:0,class:"flex-1 text-left truncate"},ct={key:0,class:"ml-6 space-y-1 mt-1"},ut={__name:"SidebarNavItemCollapsible",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(a){const i=a,l=ce(),t=Q(),s=j(!1),d=b(()=>["text-gray-700 hover:bg-gray-50 hover:text-primary-600",{"text-primary-600 bg-primary-50":v.value}]),v=b(()=>i.item.children?i.item.children.some(r=>r.path!=="#"&&l.path.startsWith(r.path)):!1),p=b(()=>i.item.children?i.item.children.filter(r=>{var g;return r.admin?((g=t.user)==null?void 0:g.role)==="admin":!0}):[]);v.value&&(s.value=!0);function m(){i.isCollapsed||(s.value=!s.value)}function c(r){if(r.path==="#")return!1}return(r,g)=>{const x=L("router-link");return o(),n("div",null,[e("button",{onClick:m,class:B(["group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[d.value,{"justify-center":a.isCollapsed}]])},[h(le,{icon:a.item.icon,class:B(["flex-shrink-0 h-6 w-6",{"mr-0":a.isCollapsed,"mr-3":!a.isCollapsed}])},null,8,["icon","class"]),a.isCollapsed?_("",!0):(o(),n("span",dt,u(a.item.name),1)),a.isCollapsed?_("",!0):(o(),n("svg",{key:1,class:B([{"rotate-90":s.value},"ml-2 h-4 w-4 transition-transform duration-150"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},g[0]||(g[0]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"},null,-1)]),2))],2),s.value&&!a.isCollapsed?(o(),n("div",ct,[(o(!0),n(N,null,R(p.value,M=>(o(),G(x,{key:M.name,to:M.path,class:B(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",M.path==="#"?"text-gray-400 hover:text-gray-500 cursor-not-allowed opacity-75":"text-gray-600 hover:bg-gray-50 hover:text-primary-600"]),"active-class":"text-primary-600 bg-primary-50",onClick:V=>c(M)},{default:I(()=>[A(u(M.name),1)]),_:2},1032,["to","class","onClick"]))),128))])):_("",!0)])}}},mt={class:"mt-5 flex-grow flex flex-col overflow-hidden"},pt={class:"flex-1 px-2 space-y-1"},Ce={__name:"SidebarNavigation",props:{isCollapsed:{type:Boolean,default:!1}},emits:["item-click"],setup(a){const i=Q(),l=b(()=>{var t;return((t=i.user)==null?void 0:t.role)==="admin"});return(t,s)=>(o(),n("div",mt,[e("nav",pt,[h(O,{item:{name:"Dashboard",path:"/app/dashboard",icon:"dashboard"},"is-collapsed":a.isCollapsed,onClick:s[0]||(s[0]=d=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(ut,{item:{name:"Personale",icon:"users",children:[{name:"👥 Team",path:"/app/personnel"},{name:"📖 Directory",path:"/app/personnel/directory"},{name:"🏢 Organigramma",path:"/app/personnel/orgchart"},{name:"🎯 Competenze",path:"/app/personnel/skills"},{name:"🏢 Dipartimenti",path:"#",admin:!0},{name:"⚙️ Amministrazione",path:"#",admin:!0}]},"is-collapsed":a.isCollapsed,onClick:s[1]||(s[1]=d=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(O,{item:{name:"Progetti",path:"/app/projects",icon:"projects"},"is-collapsed":a.isCollapsed,onClick:s[2]||(s[2]=d=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(O,{item:{name:"CRM",path:"#",icon:"clients"},"is-collapsed":a.isCollapsed,onClick:s[3]||(s[3]=d=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(O,{item:{name:"Prodotti",path:"#",icon:"products"},"is-collapsed":a.isCollapsed,onClick:s[4]||(s[4]=d=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(O,{item:{name:"Performance",path:"#",icon:"reports"},"is-collapsed":a.isCollapsed,onClick:s[5]||(s[5]=d=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(O,{item:{name:"Comunicazione",path:"#",icon:"communications"},"is-collapsed":a.isCollapsed,onClick:s[6]||(s[6]=d=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(O,{item:{name:"Finanziamenti",path:"#",icon:"funding"},"is-collapsed":a.isCollapsed,onClick:s[7]||(s[7]=d=>t.$emit("item-click"))},null,8,["is-collapsed"]),h(O,{item:{name:"Rendicontazione",path:"#",icon:"reporting"},"is-collapsed":a.isCollapsed,onClick:s[8]||(s[8]=d=>t.$emit("item-click"))},null,8,["is-collapsed"]),l.value?(o(),G(O,{key:0,item:{name:"Amministrazione",path:"#",icon:"settings"},"is-collapsed":a.isCollapsed,onClick:s[9]||(s[9]=d=>t.$emit("item-click"))},null,8,["is-collapsed"])):_("",!0)])]))}},gt={class:"flex-shrink-0 border-t border-gray-200 p-4"},vt={class:"flex-shrink-0"},ft={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},ht={class:"text-sm font-medium text-primary-700"},xt={key:0,class:"ml-3 flex-1 min-w-0"},yt={class:"text-sm font-medium text-gray-900 truncate"},_t={class:"text-xs text-gray-500 truncate"},bt={class:"py-1"},kt={key:0,class:"mt-3 text-xs text-gray-400 text-center"},je={__name:"SidebarFooter",props:{isCollapsed:{type:Boolean,default:!1}},setup(a){const i=X(),l=Q(),t=j(!1),s=b(()=>l.user&&(l.user.name||l.user.username)||"Utente"),d=b(()=>l.user?s.value.charAt(0).toUpperCase():"U"),v=b(()=>l.user?{admin:"Amministratore",manager:"Manager",employee:"Dipendente",client:"Cliente"}[l.user.role]||l.user.role:""),p=b(()=>"1.0.0");async function m(){t.value=!1,await l.logout(),i.push("/auth/login")}return(c,r)=>{const g=L("router-link");return o(),n("div",gt,[e("div",{class:B(["flex items-center",{"justify-center":a.isCollapsed}])},[e("div",vt,[e("div",ft,[e("span",ht,u(d.value),1)])]),a.isCollapsed?_("",!0):(o(),n("div",xt,[e("p",yt,u(s.value),1),e("p",_t,u(v.value),1)])),e("div",{class:B(["relative",{"ml-3":!a.isCollapsed}])},[e("button",{onClick:r[0]||(r[0]=x=>t.value=!t.value),class:"p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"},r[4]||(r[4]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zM13 12a1 1 0 11-2 0 1 1 0 012 0zM20 12a1 1 0 11-2 0 1 1 0 012 0z"})],-1)])),t.value?(o(),n("div",{key:0,onClick:r[3]||(r[3]=x=>t.value=!1),class:"origin-bottom-left absolute bottom-full left-0 mb-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",bt,[h(g,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:r[1]||(r[1]=x=>t.value=!1)},{default:I(()=>r[5]||(r[5]=[A(" Il tuo profilo ")])),_:1,__:[5]}),h(g,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:r[2]||(r[2]=x=>t.value=!1)},{default:I(()=>r[6]||(r[6]=[A(" Impostazioni ")])),_:1,__:[6]}),r[7]||(r[7]=e("hr",{class:"my-1"},null,-1)),e("button",{onClick:m,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Esci ")])])):_("",!0)],2)],2),p.value&&!a.isCollapsed?(o(),n("div",kt," v"+u(p.value),1)):_("",!0)])}}},wt={class:"flex"},$t={class:"hidden lg:flex lg:flex-shrink-0 lg:fixed lg:inset-y-0 z-10"},Ct={class:"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},jt={class:"flex items-center flex-shrink-0 px-4"},Mt={class:"w-10 h-10 bg-primary-600 rounded flex items-center justify-center mr-3"},zt={class:"text-white font-bold text-lg"},St={class:"text-xl font-semibold text-gray-900 dark:text-white"},Pt={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},It={class:"text-white font-bold text-sm"},At={class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Bt=["d"],Et={class:"flex flex-col h-full pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},Vt={class:"flex items-center justify-between px-4 mb-4"},Dt={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center mr-3"},Lt={class:"text-white font-bold text-sm"},Ht={class:"text-xl font-semibold text-gray-900 dark:text-white"},Tt={__name:"AppSidebar",props:{isMobileOpen:{type:Boolean,default:!1}},emits:["close"],setup(a){const i=Y(),l=j(!1),t=b(()=>i.config||{}),s=b(()=>{var m;return((m=t.value.company)==null?void 0:m.name)||"DatPortal"}),d=b(()=>s.value.split(" ").map(c=>c[0]).join("").toUpperCase().slice(0,2));function v(){l.value=!l.value}function p(){l.value&&(l.value=!1)}return(m,c)=>{const r=L("router-link");return o(),n("div",wt,[e("div",$t,[e("div",{class:B(["flex flex-col transition-all duration-300",[l.value?"w-20":"w-64"]])},[e("div",Ct,[e("div",jt,[e("div",{class:B(["flex items-center",{"justify-center":l.value}])},[h(r,{to:"/app/dashboard",class:B(["flex items-center",{hidden:l.value}])},{default:I(()=>[e("div",Mt,[e("span",zt,u(d.value),1)]),e("h3",St,u(s.value),1)]),_:1},8,["class"]),h(r,{to:"/app/dashboard",class:B(["flex items-center justify-center",{hidden:!l.value}])},{default:I(()=>[e("div",Pt,[e("span",It,u(d.value),1)])]),_:1},8,["class"])],2),e("button",{onClick:v,class:"ml-auto text-gray-600 dark:text-gray-400 focus:outline-none hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded"},[(o(),n("svg",At,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:l.value?"M13 5l7 7-7 7M5 5l7 7-7 7":"M11 19l-7-7 7-7m8 14l-7-7 7-7"},null,8,Bt)]))])]),h(Ce,{"is-collapsed":l.value,onItemClick:p},null,8,["is-collapsed"]),h(je,{"is-collapsed":l.value},null,8,["is-collapsed"])])],2)]),e("div",{class:B(["fixed inset-y-0 left-0 z-30 w-64 bg-primary-700 transform transition-transform duration-300 ease-in-out lg:hidden",a.isMobileOpen?"translate-x-0":"-translate-x-full"])},[e("div",Et,[e("div",Vt,[h(r,{to:"/app/dashboard",class:"flex items-center"},{default:I(()=>[e("div",Dt,[e("span",Lt,u(d.value),1)]),e("h3",Ht,u(s.value),1)]),_:1}),e("button",{onClick:c[0]||(c[0]=g=>m.$emit("close")),class:"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"},c[2]||(c[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),h(Ce,{"is-collapsed":!1,onItemClick:c[1]||(c[1]=g=>m.$emit("close"))}),h(je,{"is-collapsed":!1})])],2)])}}},Nt={class:"flex","aria-label":"Breadcrumb"},Rt={class:"flex items-center space-x-2 text-sm text-gray-500"},Ft={key:0,class:"mr-2"},Ut={class:"flex items-center"},qt={key:0,class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ot=["d"],Kt={key:2,class:"font-medium text-gray-900"},Wt={__name:"HeaderBreadcrumbs",props:{breadcrumbs:{type:Array,required:!0}},setup(a){return(i,l)=>{const t=L("router-link");return o(),n("nav",Nt,[e("ol",Rt,[(o(!0),n(N,null,R(a.breadcrumbs,(s,d)=>(o(),n("li",{key:d,class:"flex items-center"},[d>0?(o(),n("div",Ft,l[0]||(l[0]=[e("svg",{class:"h-3 w-3 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]))):_("",!0),s.to&&d<a.breadcrumbs.length-1?(o(),G(t,{key:1,to:s.to,class:"hover:text-gray-700 transition-colors duration-150"},{default:I(()=>[e("span",Ut,[s.icon?(o(),n("svg",qt,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:s.icon},null,8,Ot)])):_("",!0),A(" "+u(s.label),1)])]),_:2},1032,["to"])):(o(),n("span",Kt,u(s.label),1))]))),128))])])}}},Gt={class:"flex items-center space-x-2"},Jt={key:0,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Qt={key:1,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Yt={__name:"HeaderQuickActions",emits:["quick-create-project","quick-add-task"],setup(a){const i=ce(),{isDarkMode:l,toggleDarkMode:t}=pe(),s=b(()=>{var v;return((v=i.name)==null?void 0:v.includes("projects"))||i.path.includes("/projects")}),d=b(()=>{var v,p;return((v=i.name)==null?void 0:v.includes("tasks"))||((p=i.name)==null?void 0:p.includes("projects"))||i.path.includes("/tasks")||i.path.includes("/projects")});return(v,p)=>(o(),n("div",Gt,[s.value?(o(),n("button",{key:0,onClick:p[0]||(p[0]=m=>v.$emit("quick-create-project")),class:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},p[3]||(p[3]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),A(" Nuovo Progetto ")]))):_("",!0),d.value?(o(),n("button",{key:1,onClick:p[1]||(p[1]=m=>v.$emit("quick-add-task")),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},p[4]||(p[4]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),A(" Nuovo Task ")]))):_("",!0),e("button",{onClick:p[2]||(p[2]=(...m)=>T(t)&&T(t)(...m)),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",title:"Cambia tema"},[T(l)?(o(),n("svg",Qt,p[6]||(p[6]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"},null,-1)]))):(o(),n("svg",Jt,p[5]||(p[5]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"},null,-1)])))])]))}},Xt={class:"relative"},Zt={class:"relative"},es={key:0,class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center"},ts={class:"py-1"},ss={key:0,class:"px-4 py-8 text-center text-gray-500 text-sm"},os={key:1,class:"max-h-64 overflow-y-auto"},rs=["onClick"],as={class:"flex items-start"},ns={class:"flex-shrink-0"},is={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ls=["d"],ds={class:"ml-3 flex-1"},cs={class:"text-sm font-medium text-gray-900"},us={class:"text-xs text-gray-500 mt-1"},ms={class:"text-xs text-gray-400 mt-1"},ps={key:0,class:"flex-shrink-0"},gs={key:2,class:"px-4 py-2 border-t border-gray-100"},vs={__name:"HeaderNotifications",setup(a){const i=j(!1),l=j([{id:1,type:"task",title:"Nuovo task assegnato",message:'Ti è stato assegnato un nuovo task nel progetto "Website Redesign"',created_at:new Date().toISOString(),read:!1},{id:2,type:"project",title:"Progetto completato",message:'Il progetto "Mobile App" è stato completato con successo',created_at:new Date(Date.now()-36e5).toISOString(),read:!0}]),t=b(()=>l.value.filter(c=>!c.read).length);function s(c){const r={task:"h-6 w-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center",project:"h-6 w-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center",user:"h-6 w-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center",system:"h-6 w-6 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center"};return r[c]||r.system}function d(c){const r={task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2",project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",user:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",system:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return r[c]||r.system}function v(c){const r=new Date(c),x=new Date-r;return x<6e4?"Adesso":x<36e5?`${Math.floor(x/6e4)}m fa`:x<864e5?`${Math.floor(x/36e5)}h fa`:r.toLocaleDateString("it-IT")}function p(c){c.read||(c.read=!0),i.value=!1}function m(){l.value.forEach(c=>c.read=!0)}return(c,r)=>(o(),n("div",Xt,[e("button",{onClick:r[0]||(r[0]=g=>i.value=!i.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[r[3]||(r[3]=e("span",{class:"sr-only"},"Visualizza notifiche",-1)),e("div",Zt,[r[2]||(r[2]=e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM10 21a2 2 0 01-2-2V7a7 7 0 1114 0v12a2 2 0 01-2 2H10z"})],-1)),t.value>0?(o(),n("span",es,u(t.value>9?"9+":t.value),1)):_("",!0)])]),i.value?(o(),n("div",{key:0,onClick:r[1]||(r[1]=g=>i.value=!1),class:"origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",ts,[r[5]||(r[5]=e("div",{class:"px-4 py-2 border-b border-gray-100"},[e("h3",{class:"text-sm font-medium text-gray-900"},"Notifiche")],-1)),l.value.length===0?(o(),n("div",ss," Nessuna notifica ")):(o(),n("div",os,[(o(!0),n(N,null,R(l.value,g=>(o(),n("div",{key:g.id,class:"px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-50 last:border-b-0",onClick:x=>p(g)},[e("div",as,[e("div",ns,[e("div",{class:B(s(g.type))},[(o(),n("svg",is,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:d(g.type)},null,8,ls)]))],2)]),e("div",ds,[e("p",cs,u(g.title),1),e("p",us,u(g.message),1),e("p",ms,u(v(g.created_at)),1)]),g.read?_("",!0):(o(),n("div",ps,r[4]||(r[4]=[e("div",{class:"h-2 w-2 bg-primary-500 rounded-full"},null,-1)])))])],8,rs))),128))])),l.value.length>0?(o(),n("div",gs,[e("button",{onClick:m,class:"text-xs text-primary-600 hover:text-primary-800"}," Segna tutte come lette ")])):_("",!0)])])):_("",!0)]))}},fs={class:"relative"},hs={class:"flex items-start justify-center min-h-screen pt-16 px-4 pb-20 text-center sm:block sm:p-0"},xs={class:"inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"},ys={class:"flex items-center"},_s={class:"flex-1"},bs={key:0,class:"mt-4 max-h-64 overflow-y-auto"},ks={class:"space-y-1"},ws=["onClick"],$s={class:"flex-shrink-0"},Cs={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},js=["d"],Ms={class:"ml-3 flex-1 min-w-0"},zs={class:"text-sm font-medium text-gray-900 truncate"},Ss={class:"text-xs text-gray-500 truncate"},Ps={class:"ml-2 text-xs text-gray-400"},Is={key:1,class:"mt-4 text-center py-4"},As={key:2,class:"mt-4 text-center py-4"},Bs={__name:"HeaderSearch",setup(a){const i=X(),l=j(!1),t=j(""),s=j([]),d=j(-1),v=j(!1),p=j(null),m=[{id:1,type:"project",title:"Website Redesign",description:"Progetto di redesign del sito web aziendale",path:"/app/projects/1"},{id:2,type:"person",title:"Mario Rossi",description:"Senior Developer",path:"/app/personnel/directory/1"},{id:3,type:"document",title:"Specifiche Tecniche",description:"Documento delle specifiche del progetto mobile",path:"/app/documents/1"},{id:4,type:"task",title:"Implementazione API",description:"Task per l'implementazione delle API REST",path:"/app/projects/1/tasks/5"}];oe(l,async S=>{var C;S?(await ze(),(C=p.value)==null||C.focus()):(t.value="",s.value=[],d.value=-1)});function c(){if(!t.value.trim()){s.value=[];return}v.value=!0,setTimeout(()=>{s.value=m.filter(S=>S.title.toLowerCase().includes(t.value.toLowerCase())||S.description.toLowerCase().includes(t.value.toLowerCase())),d.value=-1,v.value=!1},200)}function r(S){if(s.value.length===0)return;const C=d.value+S;C>=0&&C<s.value.length&&(d.value=C)}function g(){d.value>=0&&s.value[d.value]&&x(s.value[d.value])}function x(S){l.value=!1,i.push(S.path)}function M(S){const C={project:"h-6 w-6 rounded bg-blue-100 text-blue-600 flex items-center justify-center",person:"h-6 w-6 rounded bg-green-100 text-green-600 flex items-center justify-center",document:"h-6 w-6 rounded bg-yellow-100 text-yellow-600 flex items-center justify-center",task:"h-6 w-6 rounded bg-purple-100 text-purple-600 flex items-center justify-center"};return C[S]||C.document}function V(S){const C={project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",person:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",document:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"};return C[S]||C.document}function D(S){return{project:"Progetto",person:"Persona",document:"Documento",task:"Task"}[S]||"Elemento"}return(S,C)=>(o(),n("div",fs,[e("button",{onClick:C[0]||(C[0]=y=>l.value=!l.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},C[7]||(C[7]=[e("span",{class:"sr-only"},"Cerca",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),l.value?(o(),n("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:C[6]||(C[6]=ue(y=>l.value=!1,["self"]))},[e("div",hs,[C[11]||(C[11]=e("div",{class:"fixed inset-0 bg-black bg-opacity-25 transition-opacity"},null,-1)),e("div",xs,[e("div",null,[e("div",ys,[e("div",_s,[U(e("input",{ref_key:"searchInput",ref:p,"onUpdate:modelValue":C[1]||(C[1]=y=>t.value=y),onInput:c,onKeydown:[C[2]||(C[2]=te(y=>l.value=!1,["escape"])),te(g,["enter"]),C[3]||(C[3]=te(y=>r(-1),["up"])),C[4]||(C[4]=te(y=>r(1),["down"]))],type:"text",placeholder:"Cerca progetti, persone, documenti...",class:"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"},null,544),[[J,t.value]])]),e("button",{onClick:C[5]||(C[5]=y=>l.value=!1),class:"ml-3 p-2 text-gray-400 hover:text-gray-600"},C[8]||(C[8]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),s.value.length>0?(o(),n("div",bs,[e("div",ks,[(o(!0),n(N,null,R(s.value,(y,P)=>(o(),n("div",{key:y.id,onClick:z=>x(y),class:B(["flex items-center px-3 py-2 rounded-md cursor-pointer",P===d.value?"bg-primary-50":"hover:bg-gray-50"])},[e("div",$s,[e("div",{class:B(M(y.type))},[(o(),n("svg",Cs,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:V(y.type)},null,8,js)]))],2)]),e("div",Ms,[e("p",zs,u(y.title),1),e("p",Ss,u(y.description),1)]),e("div",Ps,u(D(y.type)),1)],10,ws))),128))])])):t.value&&!v.value?(o(),n("div",Is,C[9]||(C[9]=[e("p",{class:"text-sm text-gray-500"},"Nessun risultato trovato",-1)]))):t.value?_("",!0):(o(),n("div",As,C[10]||(C[10]=[e("p",{class:"text-xs text-gray-400"},"Inizia a digitare per cercare...",-1)])))])])])])):_("",!0)]))}},Es={class:"relative"},Vs={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},Ds={class:"text-sm font-medium text-primary-700"},Ls={class:"py-1"},Hs={class:"px-4 py-2 border-b border-gray-100 dark:border-gray-700"},Ts={class:"text-sm font-medium text-gray-900 dark:text-white"},Ns={class:"text-xs text-gray-500 dark:text-gray-400"},Rs={__name:"HeaderUserMenu",setup(a){const i=X(),l=Q(),t=j(!1),{isDarkMode:s,toggleDarkMode:d}=pe(),v=b(()=>l.user&&(l.user.name||l.user.username)||"Utente"),p=b(()=>{var r;return((r=l.user)==null?void 0:r.email)||""}),m=b(()=>l.user?v.value.charAt(0).toUpperCase():"U");async function c(){t.value=!1,await l.logout(),i.push("/auth/login")}return(r,g)=>{const x=L("router-link");return o(),n("div",Es,[e("button",{onClick:g[0]||(g[0]=M=>t.value=!t.value),class:"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[g[5]||(g[5]=e("span",{class:"sr-only"},"Apri menu utente",-1)),e("div",Vs,[e("span",Ds,u(m.value),1)])]),t.value?(o(),n("div",{key:0,onClick:g[4]||(g[4]=M=>t.value=!1),class:"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50"},[e("div",Ls,[e("div",Hs,[e("p",Ts,u(v.value),1),e("p",Ns,u(p.value),1)]),h(x,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:g[1]||(g[1]=M=>t.value=!1)},{default:I(()=>g[6]||(g[6]=[A(" Il tuo profilo ")])),_:1,__:[6]}),h(x,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:g[2]||(g[2]=M=>t.value=!1)},{default:I(()=>g[7]||(g[7]=[A(" Impostazioni ")])),_:1,__:[7]}),g[8]||(g[8]=e("div",{class:"border-t border-gray-100 dark:border-gray-700 my-1"},null,-1)),e("button",{onClick:g[3]||(g[3]=(...M)=>T(d)&&T(d)(...M)),class:"flex items-center justify-between w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"},[e("span",null,u(T(s)?"Modalità chiara":"Modalità scura"),1),e("i",{class:B([T(s)?"fas fa-sun":"fas fa-moon","text-xs"])},null,2)]),e("button",{onClick:c,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}," Esci ")])])):_("",!0)])}}},Fs={class:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"},Us={class:"flex justify-between items-center px-4 py-4 sm:px-6 lg:px-8"},qs={class:"flex items-center space-x-4"},Os={class:"flex flex-col"},Ks={class:"text-lg font-semibold text-gray-900 dark:text-white"},Ws={class:"flex items-center space-x-4"},Gs={class:"hidden md:flex items-center space-x-2"},Js={__name:"AppHeader",props:{pageTitle:{type:String,required:!0},breadcrumbs:{type:Array,default:()=>[]}},emits:["toggle-mobile-sidebar"],setup(a){return(i,l)=>(o(),n("header",Fs,[e("div",Us,[e("div",qs,[e("button",{onClick:l[0]||(l[0]=t=>i.$emit("toggle-mobile-sidebar")),class:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700"},l[1]||(l[1]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)])),e("div",Os,[e("h2",Ks,u(a.pageTitle),1),a.breadcrumbs.length>0?(o(),G(Wt,{key:0,breadcrumbs:a.breadcrumbs},null,8,["breadcrumbs"])):_("",!0)])]),e("div",Ws,[e("div",Gs,[h(Yt)]),h(vs),h(Bs),h(Rs)])])]))}},Qs={__name:"LoadingSpinner",props:{size:{type:String,default:"md",validator:a=>["sm","md","lg","xl"].includes(a)},message:{type:String,default:""},centered:{type:Boolean,default:!0}},setup(a){const i=a,l=b(()=>{const v={sm:"20px",md:"32px",lg:"48px",xl:"64px"};return`width: ${v[i.size]}; height: ${v[i.size]};`}),t=b(()=>["flex",i.centered?"items-center justify-center":"","space-y-2"]),s=b(()=>["flex items-center justify-center"]),d=b(()=>["text-sm text-gray-600 text-center"]);return(v,p)=>(o(),n("div",{class:B(t.value)},[e("div",{class:B(s.value)},[e("div",{class:"animate-spin rounded-full border-2 border-gray-300 border-t-primary-600",style:me(l.value)},null,4)],2),a.message?(o(),n("p",{key:0,class:B(d.value)},u(a.message),3)):_("",!0)],2))}},ge=(a,i)=>{const l=a.__vccOpts||a;for(const[t,s]of i)l[t]=s;return l},Ys={class:"fixed bottom-0 right-0 z-50 p-6 space-y-4"},Xs={class:"p-4"},Zs={class:"flex items-start"},eo={class:"flex-shrink-0"},to={class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},so=["d"],oo={class:"ml-3 w-0 flex-1 pt-0.5"},ro={class:"text-sm font-medium text-gray-900"},ao={class:"mt-1 text-sm text-gray-500"},no={class:"ml-4 flex-shrink-0 flex"},io=["onClick"],lo={__name:"NotificationManager",setup(a){const i=j([]);function l(p){const m=Date.now(),c={id:m,type:p.type||"info",title:p.title,message:p.message,duration:p.duration||5e3};i.value.push(c),c.duration>0&&setTimeout(()=>{t(m)},c.duration)}function t(p){const m=i.value.findIndex(c=>c.id===p);m>-1&&i.value.splice(m,1)}function s(p){const m={success:"border-l-4 border-green-400",error:"border-l-4 border-red-400",warning:"border-l-4 border-yellow-400",info:"border-l-4 border-blue-400"};return m[p]||m.info}function d(p){const m={success:"h-8 w-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center",error:"h-8 w-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center",warning:"h-8 w-8 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center",info:"h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center"};return m[p]||m.info}function v(p){const m={success:"M5 13l4 4L19 7",error:"M6 18L18 6M6 6l12 12",warning:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-2.694-.833-3.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z",info:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return m[p]||m.info}return window.showNotification=l,K(()=>{}),(p,m)=>(o(),n("div",Ys,[h(Be,{name:"notification",tag:"div",class:"space-y-4"},{default:I(()=>[(o(!0),n(N,null,R(i.value,c=>(o(),n("div",{key:c.id,class:B([s(c.type),"max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"])},[e("div",Xs,[e("div",Zs,[e("div",eo,[e("div",{class:B(d(c.type))},[(o(),n("svg",to,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:v(c.type)},null,8,so)]))],2)]),e("div",oo,[e("p",ro,u(c.title),1),e("p",ao,u(c.message),1)]),e("div",no,[e("button",{onClick:r=>t(c.id),class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},m[0]||(m[0]=[e("span",{class:"sr-only"},"Chiudi",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,io)])])])],2))),128))]),_:1})]))}},co=ge(lo,[["__scopeId","data-v-220f0827"]]),uo={class:"h-screen flex bg-gray-50 dark:bg-gray-900"},mo={class:"flex flex-col flex-1 overflow-hidden lg:ml-64"},po={class:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900"},go={class:"py-6"},vo={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},fo={key:0,class:"mb-6"},ho={key:1,class:"flex items-center justify-center h-64"},xo={__name:"AppLayout",setup(a){const i=ce(),l=Y(),t=j(!1),s=j(!1);b(()=>l.config||{});const d=b(()=>l.config!==null),v=b(()=>{var x;return(x=i.meta)!=null&&x.title?i.meta.title:{dashboard:"Dashboard",projects:"Progetti","projects-list":"Elenco Progetti","projects-view":"Dettaglio Progetto","projects-create":"Nuovo Progetto",personnel:"Personale","personnel-directory":"Rubrica Aziendale","personnel-orgchart":"Organigramma","personnel-skills":"Competenze"}[i.name]||"DatPortal"}),p=b(()=>{var g;return(g=i.meta)!=null&&g.breadcrumbs?i.meta.breadcrumbs.map(x=>({label:x.label,to:x.to,icon:x.icon})):[]}),m=b(()=>{var g;return((g=i.meta)==null?void 0:g.hasActions)||!1});function c(){t.value=!t.value}function r(){t.value=!1}return oe(i,()=>{s.value=!0,setTimeout(()=>{s.value=!1},300)}),oe(i,()=>{r()}),K(()=>{d.value||l.loadConfig()}),(g,x)=>{const M=L("router-view");return o(),n("div",uo,[t.value?(o(),n("div",{key:0,onClick:r,class:"fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"})):_("",!0),h(Tt,{"is-mobile-open":t.value,onClose:r},null,8,["is-mobile-open"]),e("div",mo,[h(Js,{"page-title":v.value,breadcrumbs:p.value,onToggleMobileSidebar:c},null,8,["page-title","breadcrumbs"]),e("main",po,[e("div",go,[e("div",vo,[m.value?(o(),n("div",fo,[Ee(g.$slots,"page-actions")])):_("",!0),s.value?(o(),n("div",ho,[h(Qs)])):(o(),G(M,{key:2}))])])])]),h(co)])}}},yo={class:"min-h-screen bg-gray-50"},_o={class:"bg-white shadow-sm border-b"},bo={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ko={class:"flex justify-between h-16"},wo={class:"flex items-center"},$o={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Co={class:"text-white font-bold text-sm"},jo={class:"text-xl font-semibold text-gray-900"},Mo={class:"hidden md:flex items-center space-x-8"},zo={class:"flex items-center space-x-4 ml-8 pl-8 border-l border-gray-200"},So={class:"md:hidden flex items-center"},Po={key:0,class:"md:hidden"},Io={class:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t"},Ao={class:"bg-gray-800 text-white"},Bo={class:"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8"},Eo={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},Vo={class:"col-span-1 md:col-span-2"},Do={class:"flex items-center space-x-3 mb-4"},Lo={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Ho={class:"text-white font-bold text-sm"},To={class:"text-xl font-semibold"},No={class:"text-gray-300 max-w-md"},Ro={class:"space-y-2"},Fo={class:"space-y-2 text-gray-300"},Uo={key:0},qo={key:1},Oo={key:2},Ko={class:"mt-8 pt-8 border-t border-gray-700 text-center text-gray-400"},Me={__name:"PublicLayout",setup(a){const i=Y(),l=j(!1),t=b(()=>i.config||{}),s=b(()=>{var m;return((m=t.value.company)==null?void 0:m.name)||"DatVinci"}),d=b(()=>s.value.split(" ").map(c=>c[0]).join("").toUpperCase().slice(0,2)),v=b(()=>i.config!==null),p=new Date().getFullYear();return K(()=>{v.value||i.loadConfig()}),(m,c)=>{var x,M,V,D,S,C;const r=L("router-link"),g=L("router-view");return o(),n("div",yo,[e("nav",_o,[e("div",bo,[e("div",ko,[e("div",wo,[h(r,{to:"/",class:"flex items-center space-x-3"},{default:I(()=>[e("div",$o,[e("span",Co,u(d.value),1)]),e("span",jo,u(s.value),1)]),_:1})]),e("div",Mo,[h(r,{to:"/",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:I(()=>c[1]||(c[1]=[A(" Home ")])),_:1,__:[1]}),h(r,{to:"/about",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:I(()=>c[2]||(c[2]=[A(" Chi Siamo ")])),_:1,__:[2]}),h(r,{to:"/services",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:I(()=>c[3]||(c[3]=[A(" Servizi ")])),_:1,__:[3]}),h(r,{to:"/contact",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:I(()=>c[4]||(c[4]=[A(" Contatti ")])),_:1,__:[4]}),e("div",zo,[h(r,{to:"/auth/login",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:I(()=>c[5]||(c[5]=[A(" Accedi ")])),_:1,__:[5]}),h(r,{to:"/auth/register",class:"bg-primary-600 text-white hover:bg-primary-700 px-4 py-2 rounded-md text-sm font-medium"},{default:I(()=>c[6]||(c[6]=[A(" Registrati ")])),_:1,__:[6]})])]),e("div",So,[e("button",{onClick:c[0]||(c[0]=y=>l.value=!l.value),class:"text-gray-400 hover:text-gray-500"},c[7]||(c[7]=[e("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)]))])])]),l.value?(o(),n("div",Po,[e("div",Io,[h(r,{to:"/",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:I(()=>c[8]||(c[8]=[A(" Home ")])),_:1,__:[8]}),h(r,{to:"/about",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:I(()=>c[9]||(c[9]=[A(" Chi Siamo ")])),_:1,__:[9]}),h(r,{to:"/services",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:I(()=>c[10]||(c[10]=[A(" Servizi ")])),_:1,__:[10]}),h(r,{to:"/contact",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:I(()=>c[11]||(c[11]=[A(" Contatti ")])),_:1,__:[11]}),c[14]||(c[14]=e("hr",{class:"my-2"},null,-1)),h(r,{to:"/auth/login",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:I(()=>c[12]||(c[12]=[A(" Accedi ")])),_:1,__:[12]}),h(r,{to:"/auth/register",class:"block px-3 py-2 text-base font-medium bg-primary-600 text-white rounded-md"},{default:I(()=>c[13]||(c[13]=[A(" Registrati ")])),_:1,__:[13]})])])):_("",!0)]),e("main",null,[h(g)]),e("footer",Ao,[e("div",Bo,[e("div",Eo,[e("div",Vo,[e("div",Do,[e("div",Lo,[e("span",Ho,u(d.value),1)]),e("span",To,u(s.value),1)]),e("p",No,u(T(i).interpolateText((x=t.value.footer)==null?void 0:x.description)||((M=t.value.company)==null?void 0:M.description)||"Innovazione e tecnologia per il futuro digitale della tua azienda."),1)]),e("div",null,[c[19]||(c[19]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Link Rapidi",-1)),e("ul",Ro,[e("li",null,[h(r,{to:"/",class:"text-gray-300 hover:text-white"},{default:I(()=>c[15]||(c[15]=[A("Home")])),_:1,__:[15]})]),e("li",null,[h(r,{to:"/about",class:"text-gray-300 hover:text-white"},{default:I(()=>c[16]||(c[16]=[A("Chi Siamo")])),_:1,__:[16]})]),e("li",null,[h(r,{to:"/services",class:"text-gray-300 hover:text-white"},{default:I(()=>c[17]||(c[17]=[A("Servizi")])),_:1,__:[17]})]),e("li",null,[h(r,{to:"/contact",class:"text-gray-300 hover:text-white"},{default:I(()=>c[18]||(c[18]=[A("Contatti")])),_:1,__:[18]})])])]),e("div",null,[c[20]||(c[20]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Contatti",-1)),e("ul",Fo,[(V=t.value.contact)!=null&&V.email?(o(),n("li",Uo,u(t.value.contact.email),1)):_("",!0),(D=t.value.contact)!=null&&D.phone?(o(),n("li",qo,u(t.value.contact.phone),1)):_("",!0),(S=t.value.contact)!=null&&S.address?(o(),n("li",Oo,u(t.value.contact.address),1)):_("",!0)])])]),e("div",Ko,[e("p",null,u(T(i).interpolateText((C=t.value.footer)==null?void 0:C.copyright)||`© ${T(p)} ${s.value}. Tutti i diritti riservati.`),1)])])])])}}},Wo={class:"bg-white"},Go={class:"relative overflow-hidden"},Jo={class:"max-w-7xl mx-auto"},Qo={class:"relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32"},Yo={class:"mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28"},Xo={class:"sm:text-center lg:text-left"},Zo={class:"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl"},er={class:"block xl:inline"},tr={class:"mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0"},sr={class:"mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start"},or={class:"rounded-md shadow"},rr={class:"mt-3 sm:mt-0 sm:ml-3"},ar={class:"py-12 bg-white"},nr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ir={class:"lg:text-center"},lr={class:"text-base text-primary-600 font-semibold tracking-wide uppercase"},dr={class:"mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl"},cr={key:0,class:"mt-10"},ur={class:"space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10"},mr={class:"absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white"},pr={class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},gr={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},vr={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},fr={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},hr={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"},xr={class:"ml-16 text-lg leading-6 font-medium text-gray-900"},yr={class:"mt-2 ml-16 text-base text-gray-500"},_r={__name:"Home",setup(a){const i=Y(),l=b(()=>i.config||{}),t=b(()=>{var d;return((d=l.value.pages)==null?void 0:d.home)||{}}),s=b(()=>l.value.company||{});return K(()=>{i.config||i.loadConfig()}),(d,v)=>{var m,c,r,g;const p=L("router-link");return o(),n("div",Wo,[e("div",Go,[e("div",Jo,[e("div",Qo,[e("main",Yo,[e("div",Xo,[e("h1",Zo,[e("span",er,u(((m=t.value.hero)==null?void 0:m.title)||"Innovazione per il futuro"),1)]),e("p",tr,u(((c=t.value.hero)==null?void 0:c.subtitle)||T(i).interpolateText(s.value.description)||"Supportiamo le aziende nel loro percorso di crescita attraverso soluzioni tecnologiche all'avanguardia"),1),e("div",sr,[e("div",or,[h(p,{to:"/services",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 md:py-4 md:text-lg md:px-10"},{default:I(()=>{var x;return[A(u(((x=t.value.hero)==null?void 0:x.cta_primary)||"Scopri i nostri servizi"),1)]}),_:1})]),e("div",rr,[h(p,{to:"/contact",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 md:py-4 md:text-lg md:px-10"},{default:I(()=>{var x;return[A(u(((x=t.value.hero)==null?void 0:x.cta_secondary)||"Contattaci"),1)]}),_:1})])])])])])]),v[0]||(v[0]=e("div",{class:"lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2"},[e("div",{class:"h-56 w-full bg-gradient-to-r from-primary-400 to-primary-600 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center"},[e("svg",{class:"h-24 w-24 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])])],-1))]),e("div",ar,[e("div",nr,[e("div",ir,[e("h2",lr,u(((r=t.value.services_section)==null?void 0:r.title)||"I nostri servizi"),1),e("p",dr,u(((g=t.value.services_section)==null?void 0:g.subtitle)||"Soluzioni innovative per ogni esigenza aziendale"),1)]),s.value.platform_features?(o(),n("div",cr,[e("div",ur,[(o(!0),n(N,null,R(s.value.platform_features,x=>(o(),n("div",{key:x.title,class:"relative"},[e("div",mr,[(o(),n("svg",pr,[x.icon==="briefcase"?(o(),n("path",gr)):x.icon==="users"?(o(),n("path",vr)):x.icon==="chart"?(o(),n("path",fr)):(o(),n("path",hr))]))]),e("p",xr,u(x.title),1),e("p",yr,u(x.description),1)]))),128))])])):_("",!0)])])])}}},br={class:"py-16 bg-white"},kr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},wr={class:"text-center"},$r={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Cr={class:"mt-4 text-xl text-gray-600"},jr={key:0,class:"mt-16"},Mr={class:"max-w-3xl mx-auto"},zr={class:"text-3xl font-bold text-gray-900 text-center mb-8"},Sr={class:"text-lg text-gray-700 leading-relaxed"},Pr={class:"mt-16 grid grid-cols-1 md:grid-cols-2 gap-12"},Ir={key:0,class:"bg-gray-50 p-8 rounded-lg"},Ar={class:"text-2xl font-bold text-gray-900 mb-4"},Br={class:"text-gray-700"},Er={key:1,class:"bg-gray-50 p-8 rounded-lg"},Vr={class:"text-2xl font-bold text-gray-900 mb-4"},Dr={class:"text-gray-700"},Lr={key:1,class:"mt-16"},Hr={class:"text-center mb-12"},Tr={class:"text-3xl font-bold text-gray-900"},Nr={class:"mt-4 text-xl text-gray-600"},Rr={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Fr={class:"text-lg font-semibold text-gray-900"},Ur={key:2,class:"mt-16"},qr={class:"text-center"},Or={class:"text-3xl font-bold text-gray-900"},Kr={class:"mt-4 text-xl text-gray-600"},Wr={class:"mt-8 inline-flex items-center px-6 py-3 bg-primary-50 rounded-lg"},Gr={class:"text-primary-900 font-medium"},Jr={__name:"About",setup(a){const i=Y(),l=b(()=>i.config||{}),t=b(()=>{var d;return((d=l.value.pages)==null?void 0:d.about)||{}}),s=b(()=>l.value.company||{});return K(()=>{i.config||i.loadConfig()}),(d,v)=>{var p,m;return o(),n("div",br,[e("div",kr,[e("div",wr,[e("h1",$r,u(((p=t.value.hero)==null?void 0:p.title)||"Chi Siamo"),1),e("p",Cr,u(((m=t.value.hero)==null?void 0:m.subtitle)||"La nostra storia e i nostri valori"),1)]),t.value.story_section?(o(),n("div",jr,[e("div",Mr,[e("h2",zr,u(t.value.story_section.title),1),e("p",Sr,u(T(i).interpolateText(t.value.story_section.content)),1)])])):_("",!0),e("div",Pr,[t.value.mission_section?(o(),n("div",Ir,[e("h3",Ar,u(t.value.mission_section.title),1),e("p",Br,u(T(i).interpolateText(t.value.mission_section.content)),1)])):_("",!0),t.value.vision_section?(o(),n("div",Er,[e("h3",Vr,u(t.value.vision_section.title),1),e("p",Dr,u(T(i).interpolateText(t.value.vision_section.content)),1)])):_("",!0)]),t.value.expertise_section&&s.value.expertise?(o(),n("div",Lr,[e("div",Hr,[e("h2",Tr,u(t.value.expertise_section.title),1),e("p",Nr,u(t.value.expertise_section.subtitle),1)]),e("div",Rr,[(o(!0),n(N,null,R(s.value.expertise,c=>(o(),n("div",{key:c,class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200 text-center"},[v[0]||(v[0]=e("div",{class:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-6 h-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Fr,u(c),1)]))),128))])])):_("",!0),t.value.team_section?(o(),n("div",Ur,[e("div",qr,[e("h2",Or,u(t.value.team_section.title),1),e("p",Kr,u(t.value.team_section.subtitle),1),e("div",Wr,[v[1]||(v[1]=e("svg",{class:"w-5 h-5 text-primary-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),e("span",Gr,u(s.value.team_size),1)])])])):_("",!0)])])}}},Qr={class:"py-16 bg-white"},Yr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Xr={class:"text-center"},Zr={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},ea={class:"mt-4 text-xl text-gray-600"},ta={key:0,class:"mt-8 text-center"},sa={class:"text-lg text-gray-700 max-w-3xl mx-auto"},oa={class:"mt-16 grid grid-cols-1 lg:grid-cols-2 gap-16"},ra={key:0},aa={class:"text-2xl font-bold text-gray-900 mb-8"},na={class:"block text-sm font-medium text-gray-700 mb-2"},ia={class:"block text-sm font-medium text-gray-700 mb-2"},la={class:"block text-sm font-medium text-gray-700 mb-2"},da=["disabled"],ca={key:1},ua={class:"text-2xl font-bold text-gray-900 mb-8"},ma={class:"space-y-6"},pa={key:0,class:"flex items-start"},ga={class:"font-medium text-gray-900"},va={class:"text-gray-600"},fa={key:1,class:"flex items-start"},ha={class:"font-medium text-gray-900"},xa={class:"text-gray-600"},ya={key:2,class:"flex items-start"},_a={class:"font-medium text-gray-900"},ba={class:"text-gray-600"},ka={key:3,class:"flex items-start"},wa={class:"font-medium text-gray-900"},$a={class:"text-gray-600"},Ca={__name:"Contact",setup(a){const i=Y(),l=b(()=>i.config||{}),t=b(()=>{var c;return((c=l.value.pages)==null?void 0:c.contact)||{}}),s=b(()=>l.value.contact||{}),d=j({name:"",email:"",message:""}),v=j(!1),p=j({text:"",type:""}),m=async()=>{var c,r;if(!d.value.name||!d.value.email||!d.value.message){p.value={text:((c=t.value.form)==null?void 0:c.error_message)||"Tutti i campi sono obbligatori",type:"error"};return}v.value=!0,p.value={text:"",type:""};try{await new Promise(g=>setTimeout(g,1e3)),p.value={text:((r=t.value.form)==null?void 0:r.success_message)||"Messaggio inviato con successo!",type:"success"},d.value={name:"",email:"",message:""}}catch{p.value={text:"Errore durante l'invio. Riprova più tardi.",type:"error"}}finally{v.value=!1}};return K(()=>{i.config||i.loadConfig()}),(c,r)=>{var g,x;return o(),n("div",Qr,[e("div",Yr,[e("div",Xr,[e("h1",Zr,u(((g=t.value.hero)==null?void 0:g.title)||"Contattaci"),1),e("p",ea,u(((x=t.value.hero)==null?void 0:x.subtitle)||"Siamo qui per aiutarti"),1)]),t.value.intro?(o(),n("div",ta,[e("p",sa,u(t.value.intro.content),1)])):_("",!0),e("div",oa,[t.value.form?(o(),n("div",ra,[e("h2",aa,u(t.value.form.title),1),e("form",{onSubmit:ue(m,["prevent"]),class:"space-y-6"},[e("div",null,[e("label",na,u(t.value.form.name_label),1),U(e("input",{"onUpdate:modelValue":r[0]||(r[0]=M=>d.value.name=M),type:"text",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[J,d.value.name]])]),e("div",null,[e("label",ia,u(t.value.form.email_label),1),U(e("input",{"onUpdate:modelValue":r[1]||(r[1]=M=>d.value.email=M),type:"email",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[J,d.value.email]])]),e("div",null,[e("label",la,u(t.value.form.message_label),1),U(e("textarea",{"onUpdate:modelValue":r[2]||(r[2]=M=>d.value.message=M),rows:"6",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[J,d.value.message]])]),e("button",{type:"submit",disabled:v.value,class:"w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50"},u(v.value?"Invio in corso...":t.value.form.submit_button),9,da),p.value.text?(o(),n("div",{key:0,class:B([p.value.type==="success"?"text-green-600":"text-red-600","text-sm mt-2"])},u(p.value.text),3)):_("",!0)],32)])):_("",!0),t.value.info?(o(),n("div",ca,[e("h2",ua,u(t.value.info.title),1),e("div",ma,[s.value.address?(o(),n("div",pa,[r[3]||(r[3]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),e("div",null,[e("h3",ga,u(t.value.info.address_label),1),e("p",va,u(s.value.address),1)])])):_("",!0),s.value.phone?(o(),n("div",fa,[r[4]||(r[4]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})],-1)),e("div",null,[e("h3",ha,u(t.value.info.phone_label),1),e("p",xa,u(s.value.phone),1)])])):_("",!0),s.value.email?(o(),n("div",ya,[r[5]||(r[5]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1)),e("div",null,[e("h3",_a,u(t.value.info.email_label),1),e("p",ba,u(s.value.email),1)])])):_("",!0),s.value.hours?(o(),n("div",ka,[r[6]||(r[6]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",null,[e("h3",wa,u(t.value.info.hours_label),1),e("p",$a,u(s.value.hours),1)])])):_("",!0)])])):_("",!0)])])])}}},ja={class:"py-16 bg-white"},Ma={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},za={class:"text-center"},Sa={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Pa={class:"mt-4 text-xl text-gray-600"},Ia={key:0,class:"mt-8 text-center"},Aa={class:"text-lg text-gray-700 max-w-3xl mx-auto"},Ba={key:1,class:"mt-16"},Ea={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Va={class:"text-xl font-bold text-gray-900 text-center mb-4"},Da={class:"text-gray-600 text-center"},La={key:2,class:"mt-20"},Ha={class:"bg-primary-50 rounded-2xl p-12 text-center"},Ta={class:"text-3xl font-bold text-gray-900 mb-4"},Na={class:"text-xl text-gray-600 mb-8"},Ra={__name:"Services",setup(a){const i=Y(),l=b(()=>i.config||{}),t=b(()=>{var v;return((v=l.value.pages)==null?void 0:v.services)||{}}),s=b(()=>l.value.company||{}),d=v=>({"Sviluppo Software":"Soluzioni software personalizzate per ottimizzare i processi aziendali e migliorare l'efficienza operativa.","Intelligenza Artificiale":"Implementazione di sistemi AI avanzati per automatizzare processi e analizzare dati complessi.","Consulenza IT":"Consulenza strategica per la trasformazione digitale e l'ottimizzazione dell'infrastruttura tecnologica.","Gestione Progetti Innovativi":"Coordinamento e gestione di progetti tecnologici complessi con metodologie agili.","Supporto su Bandi e Finanziamenti":"Assistenza nella ricerca e gestione di bandi pubblici e finanziamenti per l'innovazione."})[v]||"Servizio professionale di alta qualità per supportare la crescita della tua azienda.";return K(()=>{i.config||i.loadConfig()}),(v,p)=>{var c,r;const m=L("router-link");return o(),n("div",ja,[e("div",Ma,[e("div",za,[e("h1",Sa,u(((c=t.value.hero)==null?void 0:c.title)||"I nostri servizi"),1),e("p",Pa,u(((r=t.value.hero)==null?void 0:r.subtitle)||"Soluzioni complete per la tua azienda"),1)]),t.value.intro?(o(),n("div",Ia,[e("p",Aa,u(t.value.intro.content),1)])):_("",!0),s.value.expertise?(o(),n("div",Ba,[e("div",Ea,[(o(!0),n(N,null,R(s.value.expertise,g=>(o(),n("div",{key:g,class:"bg-white p-8 rounded-lg shadow-lg border border-gray-200 hover:shadow-xl transition-shadow"},[p[0]||(p[0]=e("div",{class:"w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-6"},[e("svg",{class:"w-8 h-8 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Va,u(g),1),e("p",Da,u(d(g)),1)]))),128))])])):_("",!0),t.value.cta?(o(),n("div",La,[e("div",Ha,[e("h2",Ta,u(t.value.cta.title),1),e("p",Na,u(t.value.cta.subtitle),1),h(m,{to:"/contact",class:"inline-flex items-center px-8 py-4 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"},{default:I(()=>[A(u(t.value.cta.button)+" ",1),p[1]||(p[1]=e("svg",{class:"ml-2 w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 8l4 4m0 0l-4 4m4-4H3"})],-1))]),_:1,__:[1]})])])):_("",!0)])])}}},Fa={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},Ua={class:"max-w-md w-full space-y-8"},qa={class:"mt-2 text-center text-sm text-gray-600"},Oa={key:0,class:"rounded-md bg-red-50 p-4"},Ka={class:"text-sm text-red-700"},Wa={class:"rounded-md shadow-sm -space-y-px"},Ga={class:"flex items-center justify-between"},Ja={class:"flex items-center"},Qa=["disabled"],Ya={key:0,class:"absolute left-0 inset-y-0 flex items-center pl-3"},Xa={__name:"Login",setup(a){const i=X(),l=Q(),t=j({username:"",password:"",remember:!1}),s=b(()=>l.loading),d=b(()=>l.error);async function v(){(await l.login({username:t.value.username,password:t.value.password,remember:t.value.remember})).success&&i.push("/app/dashboard")}return(p,m)=>{const c=L("router-link");return o(),n("div",Fa,[e("div",Ua,[e("div",null,[m[5]||(m[5]=e("div",{class:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100"},[e("svg",{class:"h-6 w-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])],-1)),m[6]||(m[6]=e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Accedi al tuo account ",-1)),e("p",qa,[m[4]||(m[4]=A(" Oppure ")),h(c,{to:"/auth/register",class:"font-medium text-primary-600 hover:text-primary-500"},{default:I(()=>m[3]||(m[3]=[A(" registrati per un nuovo account ")])),_:1,__:[3]})])]),e("form",{onSubmit:ue(v,["prevent"]),class:"mt-8 space-y-6"},[d.value?(o(),n("div",Oa,[e("div",Ka,u(d.value),1)])):_("",!0),e("div",Wa,[e("div",null,[m[7]||(m[7]=e("label",{for:"username",class:"sr-only"},"Username",-1)),U(e("input",{id:"username","onUpdate:modelValue":m[0]||(m[0]=r=>t.value.username=r),name:"username",type:"text",autocomplete:"username",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Username"},null,512),[[J,t.value.username]])]),e("div",null,[m[8]||(m[8]=e("label",{for:"password",class:"sr-only"},"Password",-1)),U(e("input",{id:"password","onUpdate:modelValue":m[1]||(m[1]=r=>t.value.password=r),name:"password",type:"password",autocomplete:"current-password",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Password"},null,512),[[J,t.value.password]])])]),e("div",Ga,[e("div",Ja,[U(e("input",{id:"remember-me","onUpdate:modelValue":m[2]||(m[2]=r=>t.value.remember=r),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[Ve,t.value.remember]]),m[9]||(m[9]=e("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Ricordami ",-1))]),m[10]||(m[10]=e("div",{class:"text-sm"},[e("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500"}," Password dimenticata? ")],-1))]),e("div",null,[e("button",{type:"submit",disabled:s.value,class:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"},[s.value?(o(),n("span",Ya,m[11]||(m[11]=[e("svg",{class:"h-5 w-5 text-primary-500 animate-spin",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)]))):_("",!0),A(" "+u(s.value?"Accesso in corso...":"Accedi"),1)],8,Qa)])],32)])])}}},Za={},en={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"};function tn(a,i){return o(),n("div",en,i[0]||(i[0]=[e("div",{class:"max-w-md w-full space-y-8"},[e("div",null,[e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Registra un nuovo account ")]),e("div",{class:"text-center text-gray-600"}," Registrazione in arrivo... ")],-1)]))}const sn=ge(Za,[["render",tn]]),on={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},rn={class:"p-5"},an={class:"flex items-center"},nn={class:"ml-5 w-0 flex-1"},ln={class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},dn={class:"text-lg font-medium text-gray-900 dark:text-white"},cn={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},un={key:0,class:"bg-gray-50 dark:bg-gray-700 px-5 py-3"},mn={class:"text-sm"},se={__name:"StatsCard",props:{title:{type:String,required:!0},value:{type:[Number,String],required:!0},subtitle:{type:String,default:null},icon:{type:String,required:!0},color:{type:String,default:"primary"},link:{type:String,default:null}},setup(a){const i=t=>t==="project"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`}:t==="users"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>`}:t==="clock"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}:t==="team"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
      </svg>`}:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>`},l=t=>{const s={primary:"bg-primary-500",secondary:"bg-secondary-500",red:"bg-red-500",yellow:"bg-yellow-500",blue:"bg-blue-500",green:"bg-green-500"};return s[t]||s.primary};return(t,s)=>{const d=L("router-link");return o(),n("div",on,[e("div",rn,[e("div",an,[e("div",{class:B(["flex-shrink-0 rounded-md p-3",l(a.color)])},[(o(),G(Se(i(a.icon)),{class:"h-6 w-6 text-white"}))],2),e("div",nn,[e("dl",null,[e("dt",ln,u(a.title),1),e("dd",null,[e("div",dn,u(a.value),1),a.subtitle?(o(),n("div",cn,u(a.subtitle),1)):_("",!0)])])])])]),a.link?(o(),n("div",un,[e("div",mn,[h(d,{to:a.link,class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300"},{default:I(()=>s[0]||(s[0]=[A(" Vedi tutti ")])),_:1,__:[0]},8,["to"])])])):_("",!0)])}}},pn={class:"py-6"},gn={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},vn={class:"mt-4 md:mt-0 flex space-x-3"},fn={class:"relative"},hn=["disabled"],xn={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},yn={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},_n={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},bn={class:"relative h-64"},kn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},wn={class:"relative h-64"},$n={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},Cn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},jn={class:"p-6"},Mn={key:0,class:"text-center py-8 text-gray-500"},zn={key:1,class:"space-y-4"},Sn={class:"flex justify-between items-start"},Pn={class:"flex-1"},In={class:"text-sm font-medium text-gray-900 dark:text-white"},An={class:"text-xs text-gray-500 dark:text-gray-400"},Bn={class:"mt-2 flex justify-between items-center"},En={class:"text-xs text-gray-500 dark:text-gray-400"},Vn={class:"bg-gray-50 dark:bg-gray-700 px-6 py-3"},Dn={class:"text-sm"},Ln={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Hn={class:"p-6"},Tn={key:0,class:"text-center py-8 text-gray-500"},Nn={key:1,class:"space-y-4"},Rn={class:"flex-shrink-0"},Fn={class:"flex-1 min-w-0"},Un={class:"text-sm font-medium text-gray-900 dark:text-white"},qn={class:"text-xs text-gray-500 dark:text-gray-400"},On={class:"text-xs text-gray-400 dark:text-gray-500"},Kn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Wn={class:"p-6"},Gn={key:0,class:"text-center py-8 text-gray-500"},Jn={key:1,class:"space-y-4"},Qn={class:"flex justify-between items-start"},Yn={class:"flex-1"},Xn={class:"text-sm font-medium text-gray-900 dark:text-white"},Zn={class:"text-xs text-gray-500 dark:text-gray-400"},ei={class:"text-right"},ti={class:"text-sm font-bold text-gray-900 dark:text-white"},si={class:"text-xs text-gray-500"},oi={class:"mt-2"},ri={class:"w-full bg-gray-200 rounded-full h-2"},ai={class:"text-xs text-gray-500 mt-1"},ni={__name:"Dashboard",setup(a){ne.register(...De),X();const i=j(!1),l=j("7"),t=j({}),s=j([]),d=j([]),v=j([]),p=j(null),m=j(null);let c=null,r=null;const g=async()=>{try{const $=await fetch("/api/dashboard/stats");if(!$.ok)throw new Error("Failed to fetch stats");const w=await $.json();t.value=w.data}catch($){console.error("Error fetching dashboard stats:",$),t.value={}}},x=async()=>{try{const $=await fetch(`/api/dashboard/upcoming-tasks?days=${l.value}&limit=5`);if(!$.ok)throw new Error("Failed to fetch upcoming tasks");const w=await $.json();s.value=w.data.tasks}catch($){console.error("Error fetching upcoming tasks:",$),s.value=[]}},M=async()=>{try{const $=await fetch("/api/dashboard/recent-activities?limit=5");if(!$.ok)throw new Error("Failed to fetch recent activities");const w=await $.json();d.value=w.data.activities}catch($){console.error("Error fetching recent activities:",$),d.value=[]}},V=async()=>{try{const $=await fetch("/api/dashboard/kpis?limit=3");if(!$.ok)throw new Error("Failed to fetch KPIs");const w=await $.json();v.value=w.data.kpis}catch($){console.error("Error fetching KPIs:",$),v.value=[]}},D=async()=>{try{const $=await fetch("/api/dashboard/charts/project-status");if(!$.ok)throw new Error("Failed to fetch project chart data");const w=await $.json();C(w.data.chart)}catch($){console.error("Error fetching project chart:",$)}},S=async()=>{try{const $=await fetch("/api/dashboard/charts/task-status");if(!$.ok)throw new Error("Failed to fetch task chart data");const w=await $.json();y(w.data.chart)}catch($){console.error("Error fetching task chart:",$)}},C=$=>{if(!p.value)return;const w=p.value.getContext("2d");c&&c.destroy(),c=new ne(w,{type:"doughnut",data:{labels:$.labels,datasets:[{data:$.data,backgroundColor:["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6"],borderWidth:2,borderColor:"#ffffff"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}}}}})},y=$=>{if(!m.value)return;const w=m.value.getContext("2d");r&&r.destroy(),r=new ne(w,{type:"bar",data:{labels:$.labels,datasets:[{label:"Tasks",data:$.data,backgroundColor:["#60A5FA","#34D399","#FBBF24","#F87171"],borderColor:["#3B82F6","#10B981","#F59E0B","#EF4444"],borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,ticks:{stepSize:1}}}}})},P=async()=>{i.value=!0;try{await Promise.all([g(),x(),M(),V(),D(),S()])}finally{i.value=!1}},z=$=>new Date($).toLocaleDateString("it-IT"),f=$=>{const w=new Date($),W=Math.floor((new Date-w)/(1e3*60));return W<60?`${W} minuti fa`:W<1440?`${Math.floor(W/60)} ore fa`:`${Math.floor(W/1440)} giorni fa`},k=$=>{const w={high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return w[$]||w.medium},H=$=>{const w={todo:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300","in-progress":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",review:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",done:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return w[$]||w.todo},Z=$=>{const w={task:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`},timesheet:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`},event:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`}};return w[$]||w.task},ee=$=>{const w={task:"bg-blue-100 text-blue-600",timesheet:"bg-green-100 text-green-600",event:"bg-purple-100 text-purple-600"};return w[$]||w.task},ae=$=>$>=90?"bg-green-500":$>=70?"bg-yellow-500":"bg-red-500";return K(async()=>{await P(),await ze(),p.value&&m.value&&(await D(),await S())}),($,w)=>{var W,fe,he,xe,ye,_e,be,ke;const ve=L("router-link");return o(),n("div",pn,[e("div",gn,[w[4]||(w[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Dashboard"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Benvenuto! Ecco una panoramica delle attività della tua azienda. ")],-1)),e("div",vn,[e("div",fn,[U(e("select",{"onUpdate:modelValue":w[0]||(w[0]=E=>l.value=E),onChange:P,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500"},w[1]||(w[1]=[e("option",{value:"7"},"Ultimi 7 giorni",-1),e("option",{value:"30"},"Ultimo mese",-1),e("option",{value:"90"},"Ultimi 3 mesi",-1)]),544),[[ie,l.value]])]),e("button",{onClick:P,disabled:i.value,type:"button",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(o(),n("svg",{xmlns:"http://www.w3.org/2000/svg",class:B(["h-4 w-4 mr-2",{"animate-spin":i.value}]),viewBox:"0 0 20 20",fill:"currentColor"},w[2]||(w[2]=[e("path",{"fill-rule":"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z","clip-rule":"evenodd"},null,-1)]),2)),w[3]||(w[3]=A(" Aggiorna "))],8,hn)])]),e("div",xn,[h(se,{title:"Progetti Attivi",value:((W=t.value.projects)==null?void 0:W.active)||0,subtitle:`di ${((fe=t.value.projects)==null?void 0:fe.total)||0} totali`,icon:"project",color:"primary",link:"/app/projects?status=active"},null,8,["value","subtitle"]),h(se,{title:"Clienti",value:((he=t.value.team)==null?void 0:he.clients)||0,icon:"users",color:"secondary",link:"/app/crm/clients"},null,8,["value"]),h(se,{title:"Task Pendenti",value:((xe=t.value.tasks)==null?void 0:xe.pending)||0,subtitle:`${((ye=t.value.tasks)==null?void 0:ye.overdue)||0} in ritardo`,icon:"clock",color:((_e=t.value.tasks)==null?void 0:_e.overdue)>0?"red":"yellow",link:"/app/tasks?status=pending"},null,8,["value","subtitle","color"]),h(se,{title:"Team Members",value:((be=t.value.team)==null?void 0:be.users)||0,subtitle:`${((ke=t.value.team)==null?void 0:ke.departments)||0} dipartimenti`,icon:"team",color:"blue",link:"/app/personnel"},null,8,["value","subtitle"])]),e("div",yn,[e("div",_n,[w[5]||(w[5]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Progetti")],-1)),e("div",bn,[e("canvas",{ref_key:"projectChart",ref:p},null,512)])]),e("div",kn,[w[6]||(w[6]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Attività")],-1)),e("div",wn,[e("canvas",{ref_key:"taskChart",ref:m},null,512)])])]),e("div",$n,[e("div",Cn,[e("div",jn,[w[7]||(w[7]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività in Scadenza",-1)),s.value.length===0?(o(),n("div",Mn," Nessuna attività in scadenza ")):(o(),n("div",zn,[(o(!0),n(N,null,R(s.value,E=>(o(),n("div",{key:E.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",Sn,[e("div",Pn,[e("h3",In,u(E.name),1),e("p",An,u(E.project_name),1)]),e("span",{class:B(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",k(E.priority)])},u(E.priority),3)]),e("div",Bn,[e("span",En," Scadenza: "+u(z(E.due_date)),1),e("span",{class:B(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",H(E.status)])},u(E.status),3)])]))),128))]))]),e("div",Vn,[e("div",Dn,[h(ve,{to:"/app/tasks",class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500"},{default:I(()=>w[8]||(w[8]=[A(" Vedi tutte le attività ")])),_:1,__:[8]})])])]),e("div",Ln,[e("div",Hn,[w[9]||(w[9]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività Recenti",-1)),d.value.length===0?(o(),n("div",Tn," Nessuna attività recente ")):(o(),n("div",Nn,[(o(!0),n(N,null,R(d.value,E=>(o(),n("div",{key:`${E.type}-${E.id}`,class:"flex items-start space-x-3"},[e("div",Rn,[e("div",{class:B(["w-8 h-8 rounded-full flex items-center justify-center",ee(E.type)])},[(o(),G(Se(Z(E.type)),{class:"w-4 h-4"}))],2)]),e("div",Fn,[e("p",Un,u(E.title),1),e("p",qn,u(E.description),1),e("p",On,u(f(E.timestamp)),1)])]))),128))]))])]),e("div",Kn,[e("div",Wn,[w[10]||(w[10]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"KPIs Principali",-1)),v.value.length===0?(o(),n("div",Gn," Nessun KPI configurato ")):(o(),n("div",Jn,[(o(!0),n(N,null,R(v.value,E=>(o(),n("div",{key:E.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",Qn,[e("div",Yn,[e("h3",Xn,u(E.name),1),e("p",Zn,u(E.description),1)]),e("div",ei,[e("p",ti,u(E.current_value)+u(E.unit),1),e("p",si," Target: "+u(E.target_value)+u(E.unit),1)])]),e("div",oi,[e("div",ri,[e("div",{class:B(["h-2 rounded-full",ae(E.performance_percentage)]),style:me({width:Math.min(E.performance_percentage,100)+"%"})},null,6)]),e("p",ai,u(Math.round(E.performance_percentage))+"% del target",1)])]))),128))]))])])])])}}},ii=de("projects",()=>{const a=j([]),i=j(null),l=j(!1),t=j(null),s=j({page:1,perPage:20,total:0,totalPages:0}),d=j({search:"",status:"",client:"",type:""}),v=b(()=>{let y=a.value;if(d.value.search){const P=d.value.search.toLowerCase();y=y.filter(z=>{var f,k,H;return z.name.toLowerCase().includes(P)||((f=z.description)==null?void 0:f.toLowerCase().includes(P))||((H=(k=z.client)==null?void 0:k.name)==null?void 0:H.toLowerCase().includes(P))})}return d.value.status&&(y=y.filter(P=>P.status===d.value.status)),d.value.client&&(y=y.filter(P=>P.client_id===d.value.client)),d.value.type&&(y=y.filter(P=>P.project_type===d.value.type)),y}),p=b(()=>{const y={};return a.value.forEach(P=>{y[P.status]||(y[P.status]=[]),y[P.status].push(P)}),y});return{projects:a,currentProject:i,loading:l,error:t,pagination:s,filters:d,filteredProjects:v,projectsByStatus:p,fetchProjects:async(y={})=>{var P,z;l.value=!0,t.value=null;try{const f=new URLSearchParams({page:y.page||s.value.page,per_page:y.perPage||s.value.perPage,search:y.search||d.value.search,status:y.status||d.value.status,client:y.client||d.value.client,type:y.type||d.value.type}),k=await F.get(`/api/projects?${f}`);k.data.success&&(a.value=k.data.data.projects,s.value=k.data.data.pagination)}catch(f){t.value=((z=(P=f.response)==null?void 0:P.data)==null?void 0:z.message)||"Errore nel caricamento progetti",console.error("Error fetching projects:",f)}finally{l.value=!1}},fetchProject:async y=>{var P,z;l.value=!0,t.value=null;try{const f=await F.get(`/api/projects/${y}`);f.data.success&&(i.value=f.data.data.project)}catch(f){throw t.value=((z=(P=f.response)==null?void 0:P.data)==null?void 0:z.message)||"Errore nel caricamento progetto",console.error("Error fetching project:",f),f}finally{l.value=!1}},createProject:async y=>{var P,z;l.value=!0,t.value=null;try{const f=await F.post("/api/projects",y);if(f.data.success){const k=f.data.data.project;return a.value.unshift(k),k}}catch(f){throw t.value=((z=(P=f.response)==null?void 0:P.data)==null?void 0:z.message)||"Errore nella creazione progetto",console.error("Error creating project:",f),f}finally{l.value=!1}},updateProject:async(y,P)=>{var z,f,k;l.value=!0,t.value=null;try{const H=await F.put(`/api/projects/${y}`,P);if(H.data.success){const Z=H.data.data.project,ee=a.value.findIndex(ae=>ae.id===y);return ee!==-1&&(a.value[ee]=Z),((z=i.value)==null?void 0:z.id)===y&&(i.value=Z),Z}}catch(H){throw t.value=((k=(f=H.response)==null?void 0:f.data)==null?void 0:k.message)||"Errore nell'aggiornamento progetto",console.error("Error updating project:",H),H}finally{l.value=!1}},deleteProject:async y=>{var P,z,f;l.value=!0,t.value=null;try{(await F.delete(`/api/projects/${y}`)).data.success&&(a.value=a.value.filter(H=>H.id!==y),((P=i.value)==null?void 0:P.id)===y&&(i.value=null))}catch(k){throw t.value=((f=(z=k.response)==null?void 0:z.data)==null?void 0:f.message)||"Errore nell'eliminazione progetto",console.error("Error deleting project:",k),k}finally{l.value=!1}},setFilters:y=>{d.value={...d.value,...y}},clearFilters:()=>{d.value={search:"",status:"",client:"",type:""}},setCurrentProject:y=>{i.value=y},clearCurrentProject:()=>{i.value=null},$reset:()=>{a.value=[],i.value=null,l.value=!1,t.value=null,s.value={page:1,perPage:20,total:0,totalPages:0},d.value={search:"",status:"",client:"",type:""}}}}),li={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},di={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ci=["value"],ui={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},mi={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},pi={class:"text-lg font-medium text-gray-900 dark:text-white"},gi={key:0,class:"p-6 text-center"},vi={key:1,class:"p-6 text-center"},fi={key:2,class:"divide-y divide-gray-200 dark:divide-gray-700"},hi=["onClick"],xi={class:"flex items-center justify-between"},yi={class:"flex-1"},_i={class:"flex items-center"},bi={class:"text-lg font-medium text-gray-900 dark:text-white"},ki={class:"mt-1 text-sm text-gray-600 dark:text-gray-400"},wi={class:"mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400"},$i={key:0},Ci={key:1,class:"mx-2"},ji={key:2},Mi={key:3,class:"mx-2"},zi={key:4},Si={class:"ml-4 flex items-center space-x-2"},Pi={class:"text-right"},Ii={class:"text-sm font-medium text-gray-900 dark:text-white"},Ai={class:"w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1"},Bi={__name:"Projects",setup(a){const i=X(),l=ii(),t=j(!0),s=j(""),d=j({status:"",client:""}),v=b(()=>l.projects),p=j([]),m=b(()=>{let z=v.value;if(d.value.status&&(z=z.filter(f=>f.status===d.value.status)),d.value.client&&(z=z.filter(f=>f.client_id==d.value.client)),s.value){const f=s.value.toLowerCase();z=z.filter(k=>k.name.toLowerCase().includes(f)||k.description&&k.description.toLowerCase().includes(f)||k.client&&k.client.name&&k.client.name.toLowerCase().includes(f))}return z}),c=async()=>{t.value=!0;try{await l.fetchProjects(),p.value=[]}catch(z){console.error("Error loading projects:",z)}finally{t.value=!1}},r=()=>{},g=()=>{},x=()=>{d.value={status:"",client:""},s.value=""},M=()=>{i.push("/app/projects/create")},V=z=>{i.push(`/app/projects/${z}`)},D=z=>({planning:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",active:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",completed:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400","on-hold":"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"})[z]||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",S=z=>({planning:"Pianificazione",active:"Attivo",completed:"Completato","on-hold":"In Pausa"})[z]||z,C=z=>new Date(z).toLocaleDateString("it-IT"),y=z=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(z),P=z=>({planning:10,active:50,completed:100,"on-hold":25})[z.status]||0;return K(()=>{c()}),(z,f)=>(o(),n("div",null,[e("div",{class:"mb-6"},[e("div",{class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},[f[4]||(f[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Progetti"),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci e monitora tutti i progetti aziendali ")],-1)),e("div",{class:"mt-4 sm:mt-0"},[e("button",{onClick:M,class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},f[3]||(f[3]=[e("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1),A(" Nuovo Progetto ")]))])])]),e("div",li,[e("div",di,[e("div",null,[f[6]||(f[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Stato",-1)),U(e("select",{"onUpdate:modelValue":f[0]||(f[0]=k=>d.value.status=k),onChange:g,class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},f[5]||(f[5]=[Le('<option value="">Tutti gli stati</option><option value="planning">Pianificazione</option><option value="active">Attivo</option><option value="completed">Completato</option><option value="on-hold">In Pausa</option>',5)]),544),[[ie,d.value.status]])]),e("div",null,[f[8]||(f[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Cliente",-1)),U(e("select",{"onUpdate:modelValue":f[1]||(f[1]=k=>d.value.client=k),onChange:g,class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},[f[7]||(f[7]=e("option",{value:""},"Tutti i clienti",-1)),(o(!0),n(N,null,R(p.value,k=>(o(),n("option",{key:k.id,value:k.id},u(k.name),9,ci))),128))],544),[[ie,d.value.client]])]),e("div",null,[f[9]||(f[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Ricerca",-1)),U(e("input",{"onUpdate:modelValue":f[2]||(f[2]=k=>s.value=k),onInput:r,type:"text",placeholder:"Cerca progetti...",class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,544),[[J,s.value]])]),e("div",{class:"flex items-end"},[e("button",{onClick:x,class:"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}," Reset Filtri ")])])]),e("div",ui,[e("div",mi,[e("h3",pi," Progetti ("+u(m.value.length)+") ",1)]),t.value?(o(),n("div",gi,f[10]||(f[10]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-600 dark:text-gray-400"},"Caricamento progetti...",-1)]))):m.value.length===0?(o(),n("div",vi,f[11]||(f[11]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun progetto",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Inizia creando il tuo primo progetto.",-1)]))):(o(),n("div",fi,[(o(!0),n(N,null,R(m.value,k=>(o(),n("div",{key:k.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",onClick:H=>V(k.id)},[e("div",xi,[e("div",yi,[e("div",_i,[e("h4",bi,u(k.name),1),e("span",{class:B([D(k.status),"ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},u(S(k.status)),3)]),e("p",ki,u(k.description),1),e("div",wi,[k.client?(o(),n("span",$i,"Cliente: "+u(k.client.name),1)):_("",!0),k.client?(o(),n("span",Ci,"•")):_("",!0),k.end_date?(o(),n("span",ji,"Scadenza: "+u(C(k.end_date)),1)):_("",!0),k.end_date&&k.budget?(o(),n("span",Mi,"•")):_("",!0),k.budget?(o(),n("span",zi,"Budget: "+u(y(k.budget)),1)):_("",!0)])]),e("div",Si,[e("div",Pi,[e("div",Ii,u(P(k))+"% ",1),e("div",Ai,[e("div",{class:"bg-primary-600 h-2 rounded-full",style:me({width:P(k)+"%"})},null,4)])]),f[12]||(f[12]=e("svg",{class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1))])])],8,hi))),128))]))])]))}},Ei={};function Vi(a,i){return o(),n("div",null,i[0]||(i[0]=[e("h1",{class:"text-2xl font-bold text-gray-900 mb-6"},"Personale",-1),e("div",{class:"card"},[e("p",{class:"text-gray-600"},"Gestione personale in fase di migrazione...")],-1)]))}const Di=ge(Ei,[["render",Vi]]),Li=[{path:"/",component:Me,children:[{path:"",name:"home",component:_r},{path:"about",name:"about",component:Jr},{path:"contact",name:"contact",component:Ca},{path:"services",name:"services",component:Ra}]},{path:"/auth",component:Me,children:[{path:"login",name:"login",component:Xa},{path:"register",name:"register",component:sn}]},{path:"/app",component:xo,meta:{requiresAuth:!0},children:[{path:"",redirect:"/app/dashboard"},{path:"dashboard",name:"dashboard",component:ni},{path:"projects",name:"projects",component:Bi},{path:"projects/:id",name:"project-view",component:()=>We(()=>import("./ProjectView.js"),__vite__mapDeps([0,1,2]))},{path:"personnel",name:"personnel",component:Di}]}],Ie=He({history:Te(),routes:Li});Ie.beforeEach(async(a,i,l)=>{const t=Q();if(a.meta.requiresAuth&&(t.sessionChecked||await t.initializeAuth(),!t.isAuthenticated)){l("/auth/login");return}l()});const re=Ne(qe),Hi=Re();re.use(Hi);re.use(Ie);const Ti=Q();Ti.initializeAuth().then(()=>{console.log("Auth initialized successfully"),re.mount("#app")}).catch(a=>{console.error("Auth initialization failed:",a),re.mount("#app")});export{ge as _,F as a,Q as b,ii as u};
