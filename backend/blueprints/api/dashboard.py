"""
API endpoints for dashboard data.
Provides REST API for dashboard statistics, recent activities, and quick actions.
"""

from flask import Blueprint, request, jsonify
from flask_login import current_user
from sqlalchemy import func, desc, and_, or_
from datetime import datetime, timedelta

from utils.db import db
from models import (
    Project, Task, Timesheet, News, Event, KPI, Client, Document,
    FundingOpportunity, User, Notification, Department, UserSkill, Skill
)
from utils.api_utils import (
    api_response, api_login_required, handle_api_error
)
from utils.permissions import (
    PERMISSION_VIEW_DASHBOARD, PERMISSION_VIEW_ALL_PROJECTS,
    PERMISSION_VIEW_REPORTS, PERMISSION_VIEW_CRM, user_has_permission
)
from extensions import csrf

# Create blueprint
api_dashboard = Blueprint('api_dashboard', __name__, url_prefix='/dashboard')

@api_dashboard.route('/stats', methods=['GET'])
@csrf.exempt
@api_login_required
def get_dashboard_stats():
    """
    Get dashboard statistics and KPIs.

    Returns:
        JSON with dashboard statistics including:
        - Active projects count
        - Tasks statistics
        - Recent activities count
        - KPIs data
    """
    try:
        now = datetime.utcnow()
        week_ahead = now + timedelta(days=7)

        # Active projects count - filtered by permissions
        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            active_projects_count = Project.query.filter_by(status='active').count()
            total_projects_count = Project.query.count()
        else:
            # Only projects where user is a team member
            active_projects_count = Project.query.filter_by(
                status='active'
            ).filter(
                Project.team_members.any(id=current_user.id)
            ).count()
            total_projects_count = Project.query.filter(
                Project.team_members.any(id=current_user.id)
            ).count()

        # Tasks statistics - filtered by permissions
        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            total_tasks = Task.query.count()
            pending_tasks = Task.query.filter(Task.status.in_(['todo', 'in-progress'])).count()
            completed_tasks = Task.query.filter_by(status='done').count()
            overdue_tasks = Task.query.filter(
                and_(Task.due_date < now.date(), Task.status != 'done')
            ).count()
        else:
            total_tasks = Task.query.filter_by(assignee_id=current_user.id).count()
            pending_tasks = Task.query.filter(
                and_(
                    Task.assignee_id == current_user.id,
                    Task.status.in_(['todo', 'in-progress'])
                )
            ).count()
            completed_tasks = Task.query.filter(
                and_(
                    Task.assignee_id == current_user.id,
                    Task.status == 'done'
                )
            ).count()
            overdue_tasks = Task.query.filter(
                and_(
                    Task.assignee_id == current_user.id,
                    Task.due_date < now.date(),
                    Task.status != 'done'
                )
            ).count()

        # Client count - visible only to those with CRM permission
        if user_has_permission(current_user.role, PERMISSION_VIEW_CRM):
            client_count = Client.query.count()
        else:
            client_count = 0

        # Team statistics
        total_users = User.query.filter_by(is_active=True).count()
        total_departments = Department.query.filter_by(is_active=True).count()

        # Recent activities count
        recent_timesheets_count = Timesheet.query.filter(
            and_(
                Timesheet.user_id == current_user.id,
                Timesheet.date >= (now - timedelta(days=7)).date()
            )
        ).count()

        # Unread notifications count
        unread_notifications = Notification.query.filter_by(
            user_id=current_user.id,
            is_read=False
        ).count()

        stats_data = {
            'projects': {
                'active': active_projects_count,
                'total': total_projects_count
            },
            'tasks': {
                'total': total_tasks,
                'pending': pending_tasks,
                'completed': completed_tasks,
                'overdue': overdue_tasks
            },
            'team': {
                'users': total_users,
                'departments': total_departments,
                'clients': client_count
            },
            'activities': {
                'recent_timesheets': recent_timesheets_count,
                'unread_notifications': unread_notifications
            }
        }

        return api_response(
            data=stats_data,
            message="Dashboard statistics retrieved successfully"
        )

    except Exception as e:
        return handle_api_error(e)

@api_dashboard.route('/recent-activities', methods=['GET'])
@csrf.exempt
@api_login_required
def get_recent_activities():
    """
    Get recent activities for the dashboard.

    Query Parameters:
        limit (int): Number of activities to return (default: 10, max: 50)

    Returns:
        JSON with recent activities including:
        - Recent tasks
        - Recent timesheet entries
        - Recent projects
        - Recent events
    """
    try:
        # Get limit parameter
        limit = min(int(request.args.get('limit', 10)), 50)

        now = datetime.utcnow()
        week_ago = now - timedelta(days=7)

        activities = []

        # Recent tasks (assigned to user or all if has permission)
        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            recent_tasks = Task.query.filter(
                Task.updated_at >= week_ago
            ).order_by(desc(Task.updated_at)).limit(limit).all()
        else:
            recent_tasks = Task.query.filter(
                and_(
                    Task.assignee_id == current_user.id,
                    Task.updated_at >= week_ago
                )
            ).order_by(desc(Task.updated_at)).limit(limit).all()

        for task in recent_tasks:
            activities.append({
                'type': 'task',
                'id': task.id,
                'title': task.name,
                'description': f"Task {task.status} in project {task.project.name}",
                'timestamp': task.updated_at.isoformat(),
                'link': f"/projects/{task.project_id}#task-{task.id}",
                'status': task.status,
                'priority': task.priority
            })

        # Recent timesheet entries (user's own)
        recent_timesheets = Timesheet.query.filter(
            and_(
                Timesheet.user_id == current_user.id,
                Timesheet.created_at >= week_ago
            )
        ).order_by(desc(Timesheet.created_at)).limit(limit).all()

        for timesheet in recent_timesheets:
            activities.append({
                'type': 'timesheet',
                'id': timesheet.id,
                'title': f"Logged {timesheet.hours}h",
                'description': f"Time logged for {timesheet.project.name}",
                'timestamp': timesheet.created_at.isoformat(),
                'link': f"/projects/{timesheet.project_id}",
                'hours': timesheet.hours,
                'date': timesheet.date.isoformat()
            })

        # Recent events (user's or all if has permission)
        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            recent_events = Event.query.filter(
                Event.created_at >= week_ago
            ).order_by(desc(Event.created_at)).limit(limit).all()
        else:
            recent_events = Event.query.filter(
                and_(
                    Event.created_by == current_user.id,
                    Event.created_at >= week_ago
                )
            ).order_by(desc(Event.created_at)).limit(limit).all()

        for event in recent_events:
            activities.append({
                'type': 'event',
                'id': event.id,
                'title': event.title,
                'description': f"Event scheduled for {event.start_time.strftime('%Y-%m-%d %H:%M')}",
                'timestamp': event.created_at.isoformat(),
                'link': f"/calendar#event-{event.id}",
                'start_time': event.start_time.isoformat(),
                'event_type': event.event_type
            })

        # Sort all activities by timestamp (most recent first)
        activities.sort(key=lambda x: x['timestamp'], reverse=True)

        # Limit to requested number
        activities = activities[:limit]

        return api_response(
            data={'activities': activities},
            message=f"Retrieved {len(activities)} recent activities"
        )

    except Exception as e:
        return handle_api_error(e)

@api_dashboard.route('/upcoming-tasks', methods=['GET'])
@csrf.exempt
@api_login_required
def get_upcoming_tasks():
    """
    Get upcoming tasks for the dashboard.

    Query Parameters:
        days (int): Number of days ahead to look (default: 7)
        limit (int): Number of tasks to return (default: 10)

    Returns:
        JSON with upcoming tasks
    """
    try:
        # Get parameters
        days = int(request.args.get('days', 7))
        limit = min(int(request.args.get('limit', 10)), 50)

        now = datetime.utcnow()
        future_date = now + timedelta(days=days)

        # Get upcoming tasks - filtered by permissions
        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            upcoming_tasks = Task.query.filter(
                and_(
                    Task.due_date.between(now.date(), future_date.date()),
                    Task.status != 'done'
                )
            ).order_by(Task.due_date, Task.priority.desc()).limit(limit).all()
        else:
            upcoming_tasks = Task.query.filter(
                and_(
                    Task.assignee_id == current_user.id,
                    Task.due_date.between(now.date(), future_date.date()),
                    Task.status != 'done'
                )
            ).order_by(Task.due_date, Task.priority.desc()).limit(limit).all()

        tasks_data = []
        for task in upcoming_tasks:
            # Calculate days until due
            days_until_due = (task.due_date - now.date()).days

            tasks_data.append({
                'id': task.id,
                'name': task.name,
                'description': task.description,
                'project_id': task.project_id,
                'project_name': task.project.name,
                'assignee_id': task.assignee_id,
                'assignee_name': task.assignee.full_name if task.assignee else None,
                'status': task.status,
                'priority': task.priority,
                'due_date': task.due_date.isoformat(),
                'days_until_due': days_until_due,
                'estimated_hours': task.estimated_hours,
                'is_overdue': days_until_due < 0,
                'link': f"/projects/{task.project_id}#task-{task.id}"
            })

        return api_response(
            data={'tasks': tasks_data},
            message=f"Retrieved {len(tasks_data)} upcoming tasks"
        )

    except Exception as e:
        return handle_api_error(e)

@api_dashboard.route('/kpis', methods=['GET'])
@csrf.exempt
@api_login_required
def get_dashboard_kpis():
    """
    Get KPIs for the dashboard.

    Query Parameters:
        category (str): Filter by KPI category
        limit (int): Number of KPIs to return (default: 6)

    Returns:
        JSON with KPI data
    """
    try:
        # Get parameters
        category = request.args.get('category')
        limit = min(int(request.args.get('limit', 6)), 20)

        # Build query
        query = KPI.query

        if category:
            query = query.filter_by(category=category)

        # Get KPIs - filtered by permissions
        if user_has_permission(current_user.role, PERMISSION_VIEW_REPORTS):
            kpis = query.order_by(KPI.category, KPI.name).limit(limit).all()
        else:
            # For non-admin users, show all available KPIs
            kpis = query.order_by(KPI.category, KPI.name).limit(limit).all()

        kpis_data = []
        for kpi in kpis:
            kpis_data.append({
                'id': kpi.id,
                'name': kpi.name,
                'category': kpi.category,
                'description': kpi.description,
                'current_value': kpi.current_value,
                'target_value': kpi.target_value,
                'unit': kpi.unit,
                'trend': kpi.trend,
                'last_updated': kpi.last_updated.isoformat() if kpi.last_updated else None,
                'performance_percentage': (
                    (kpi.current_value / kpi.target_value * 100)
                    if kpi.target_value and kpi.target_value > 0
                    else 0
                )
            })

        return api_response(
            data={'kpis': kpis_data},
            message=f"Retrieved {len(kpis_data)} KPIs"
        )

    except Exception as e:
        return handle_api_error(e)

@api_dashboard.route('/charts/project-status', methods=['GET'])
@csrf.exempt
@api_login_required
def get_project_status_chart():
    """
    Get project status distribution for charts.

    Returns:
        JSON with project status data for charts
    """
    try:
        # Get project status distribution - filtered by permissions
        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            project_status = db.session.query(
                Project.status, func.count(Project.id)
            ).group_by(Project.status).all()
        else:
            project_status = db.session.query(
                Project.status, func.count(Project.id)
            ).filter(
                Project.team_members.any(id=current_user.id)
            ).group_by(Project.status).all()

        chart_data = {
            'labels': [status for status, _ in project_status],
            'data': [count for _, count in project_status],
            'total': sum(count for _, count in project_status)
        }

        return api_response(
            data={'chart': chart_data},
            message="Project status chart data retrieved successfully"
        )

    except Exception as e:
        return handle_api_error(e)

@api_dashboard.route('/charts/task-status', methods=['GET'])
@csrf.exempt
@api_login_required
def get_task_status_chart():
    """
    Get task status distribution for charts.

    Returns:
        JSON with task status data for charts
    """
    try:
        # Get task status distribution - filtered by permissions
        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            task_status = db.session.query(
                Task.status, func.count(Task.id)
            ).group_by(Task.status).all()
        else:
            task_status = db.session.query(
                Task.status, func.count(Task.id)
            ).filter(Task.assignee_id == current_user.id).group_by(Task.status).all()

        chart_data = {
            'labels': [status for status, _ in task_status],
            'data': [count for _, count in task_status],
            'total': sum(count for _, count in task_status)
        }

        return api_response(
            data={'chart': chart_data},
            message="Task status chart data retrieved successfully"
        )

    except Exception as e:
        return handle_api_error(e)

@api_dashboard.route('/quick-actions', methods=['GET'])
@api_login_required
def get_quick_actions():
    """
    Get available quick actions for the current user.

    Returns:
        JSON with available quick actions based on user permissions
    """
    try:
        actions = []

        # Basic actions available to all users
        actions.extend([
            {
                'id': 'create_task',
                'title': 'Create Task',
                'description': 'Create a new task',
                'icon': 'plus-circle',
                'url': '/tasks/create',
                'category': 'tasks'
            },
            {
                'id': 'log_time',
                'title': 'Log Time',
                'description': 'Log time for a project',
                'icon': 'clock',
                'url': '/timesheets/create',
                'category': 'time'
            },
            {
                'id': 'view_calendar',
                'title': 'View Calendar',
                'description': 'View upcoming events',
                'icon': 'calendar',
                'url': '/calendar',
                'category': 'schedule'
            }
        ])

        # Actions for users with project permissions
        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            actions.extend([
                {
                    'id': 'create_project',
                    'title': 'Create Project',
                    'description': 'Start a new project',
                    'icon': 'folder-plus',
                    'url': '/projects/create',
                    'category': 'projects'
                },
                {
                    'id': 'view_reports',
                    'title': 'View Reports',
                    'description': 'Access project reports',
                    'icon': 'chart-bar',
                    'url': '/reports',
                    'category': 'reports'
                }
            ])

        # Actions for users with CRM permissions
        if user_has_permission(current_user.role, PERMISSION_VIEW_CRM):
            actions.extend([
                {
                    'id': 'create_client',
                    'title': 'Add Client',
                    'description': 'Add a new client',
                    'icon': 'user-plus',
                    'url': '/crm/clients/create',
                    'category': 'crm'
                },
                {
                    'id': 'view_opportunities',
                    'title': 'View Opportunities',
                    'description': 'Check funding opportunities',
                    'icon': 'trending-up',
                    'url': '/funding',
                    'category': 'funding'
                }
            ])

        return api_response(
            data={'actions': actions},
            message=f"Retrieved {len(actions)} quick actions"
        )

    except Exception as e:
        return handle_api_error(e)

@api_dashboard.route('/news', methods=['GET'])
@api_login_required
def get_dashboard_news():
    """
    Get latest news for the dashboard.

    Query Parameters:
        limit (int): Number of news items to return (default: 5)

    Returns:
        JSON with latest published news
    """
    try:
        limit = min(int(request.args.get('limit', 5)), 20)

        # Get latest published news
        latest_news = News.query.filter_by(
            is_published=True
        ).order_by(desc(News.created_at)).limit(limit).all()

        news_data = []
        for news in latest_news:
            news_data.append({
                'id': news.id,
                'title': news.title,
                'content': news.content[:200] + '...' if len(news.content) > 200 else news.content,
                'author_id': news.author_id,
                'author_name': news.author.full_name if news.author else 'System',
                'created_at': news.created_at.isoformat(),
                'is_published': news.is_published,
                'link': f"/news/{news.id}"
            })

        return api_response(
            data={'news': news_data},
            message=f"Retrieved {len(news_data)} news items"
        )

    except Exception as e:
        return handle_api_error(e)