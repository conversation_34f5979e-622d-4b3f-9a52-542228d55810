"""
API endpoints for authentication and user session management.
Provides REST API for current user data, session validation, and user preferences.
"""

from flask import Blueprint, request, jsonify
from flask_login import current_user, login_user, logout_user
from werkzeug.security import check_password_hash
from datetime import datetime

from utils.db import db
from models import User, UserProfile
from utils.api_utils import (
    api_response, api_login_required, handle_api_error
)
from utils.permissions import ROLE_PERMISSIONS
from extensions import csrf

# Create blueprint
api_auth = Blueprint('api_auth', __name__, url_prefix='/auth')

def get_user_permissions(user_role):
    """
    Get all permissions for a user role.

    Args:
        user_role (str): User role

    Returns:
        list: List of permissions for the role
    """
    if not user_role or user_role not in ROLE_PERMISSIONS:
        return []

    # Admin has all permissions
    if user_role == 'admin':
        from utils.permissions import ALL_PERMISSIONS
        return list(ALL_PERMISSIONS)

    return list(ROLE_PERMISSIONS.get(user_role, set()))

@api_auth.route('/login', methods=['POST'])
@csrf.exempt
def login():
    """
    Login API endpoint.

    Expects JSON data with username/email and password.
    Returns user data and session info on success.
    """
    try:
        data = request.get_json()

        if not data:
            return api_response(
                success=False,
                message="No data provided",
                status_code=400
            )

        # Get credentials - support both username and email
        username_or_email = data.get('username') or data.get('email')
        password = data.get('password')

        if not username_or_email or not password:
            return api_response(
                message="Username/email and password are required",
                status_code=400
            )

        # Find user by username or email
        user = User.query.filter(
            (User.username == username_or_email) |
            (User.email == username_or_email)
        ).first()

        if not user or not check_password_hash(user.password_hash, password):
            return api_response(
                message="Invalid credentials",
                status_code=401
            )

        if not user.is_active:
            return api_response(
                message="Account is deactivated",
                status_code=401
            )

        # Login user
        login_user(user, remember=True)

        # Update last login
        user.last_login = datetime.utcnow()
        db.session.commit()

        # Get user permissions
        permissions = get_user_permissions(user.role)

        return api_response(
            data={
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'full_name': user.full_name,
                    'role': user.role,
                    'permissions': permissions
                }
            },
            message="Login successful"
        )

    except Exception as e:
        return handle_api_error(e)

@api_auth.route('/logout', methods=['POST'])
@api_login_required
def logout():
    """
    Logout API endpoint.

    Logs out the current user and clears the session.
    """
    try:
        logout_user()
        return api_response(
            message="Logout successful"
        )

    except Exception as e:
        return handle_api_error(e)

@api_auth.route('/me', methods=['GET'])
@csrf.exempt
@api_login_required
def get_current_user():
    """
    Get current user information with permissions and preferences.

    Returns:
        JSON with current user data including:
        - Basic user information
        - Role and permissions
        - User preferences
        - Profile completion status
    """
    try:
        # Get user permissions
        permissions = get_user_permissions(current_user.role)

        # Calculate profile completion
        profile_completion = 0
        if current_user.profile:
            total_fields = 6  # Basic fields to check
            completed_fields = 0

            if current_user.first_name:
                completed_fields += 1
            if current_user.last_name:
                completed_fields += 1
            if current_user.email:
                completed_fields += 1
            if current_user.phone:
                completed_fields += 1
            if current_user.profile.address:
                completed_fields += 1
            if current_user.bio:
                completed_fields += 1

            profile_completion = (completed_fields / total_fields) * 100

        user_data = {
            'id': current_user.id,
            'username': current_user.username,
            'email': current_user.email,
            'first_name': current_user.first_name,
            'last_name': current_user.last_name,
            'full_name': current_user.full_name,
            'role': current_user.role,
            'department_id': current_user.department_id,
            'department_name': current_user.department_obj.name if current_user.department_obj else None,
            'position': current_user.position,
            'phone': current_user.phone,
            'bio': current_user.bio,
            'profile_image': current_user.profile_image,
            'is_active': current_user.is_active,
            'dark_mode': current_user.dark_mode,
            'created_at': current_user.created_at.isoformat() if current_user.created_at else None,
            'last_login': current_user.last_login.isoformat() if current_user.last_login else None,
            'hire_date': current_user.hire_date.isoformat() if current_user.hire_date else None,
            'permissions': permissions,
            'profile_completion': round(profile_completion, 1),
            'preferences': {
                'dark_mode': current_user.dark_mode,
                'language': 'it',  # Default language
                'timezone': 'Europe/Rome'  # Default timezone
            }
        }

        return api_response(
            data={'user': user_data},
            message="Current user data retrieved successfully"
        )

    except Exception as e:
        return handle_api_error(e)

@api_auth.route('/debug', methods=['GET'])
@csrf.exempt
def debug_auth():
    """Debug endpoint per verificare lo stato dell'autenticazione"""
    from flask_login import current_user
    from flask import session

    return api_response(
        data={
            'current_user_authenticated': current_user.is_authenticated,
            'current_user_id': getattr(current_user, 'id', None),
            'current_user_username': getattr(current_user, 'username', None),
            'session_keys': list(session.keys()),
            'session_permanent': session.permanent,
            'has_user_id_in_session': '_user_id' in session,
            'user_id_from_session': session.get('_user_id')
        },
        message="Debug info"
    )

@api_auth.route('/check-session', methods=['GET'])
@csrf.exempt
@api_login_required
def check_session():
    """
    Check if current session is valid.

    Returns:
        JSON with session validity status
    """
    try:
        return api_response(
            data={
                'valid': True,
                'user_id': current_user.id,
                'username': current_user.username,
                'role': current_user.role,
                'last_activity': datetime.utcnow().isoformat()
            },
            message="Session is valid"
        )

    except Exception as e:
        return handle_api_error(e)

@api_auth.route('/preferences', methods=['GET', 'PUT'])
@api_login_required
def user_preferences():
    """
    Get or update user preferences.

    GET: Returns current user preferences
    PUT: Updates user preferences

    Returns:
        JSON with user preferences
    """
    try:
        if request.method == 'GET':
            preferences = {
                'dark_mode': current_user.dark_mode,
                'language': 'it',  # Default for now
                'timezone': 'Europe/Rome',  # Default for now
                'notifications': {
                    'email': True,  # Default settings
                    'browser': True,
                    'mobile': True
                }
            }

            return api_response(
                data={'preferences': preferences},
                message="User preferences retrieved successfully"
            )

        elif request.method == 'PUT':
            data = request.get_json()

            if not data:
                return api_response(
                    success=False,
                    message="No data provided",
                    status_code=400
                )

            # Update dark mode preference
            if 'dark_mode' in data:
                current_user.dark_mode = bool(data['dark_mode'])

            # Save changes
            db.session.commit()

            return api_response(
                data={'preferences': {
                    'dark_mode': current_user.dark_mode,
                    'language': 'it',
                    'timezone': 'Europe/Rome'
                }},
                message="User preferences updated successfully"
            )

    except Exception as e:
        return handle_api_error(e)

@api_auth.route('/profile', methods=['PUT'])
@api_login_required
def update_profile():
    """
    Update current user profile information.

    Returns:
        JSON with updated user data
    """
    try:
        data = request.get_json()

        if not data:
            return api_response(
                success=False,
                message="No data provided",
                status_code=400
            )

        # Update basic user fields
        if 'first_name' in data:
            current_user.first_name = data['first_name']
        if 'last_name' in data:
            current_user.last_name = data['last_name']
        if 'phone' in data:
            current_user.phone = data['phone']
        if 'bio' in data:
            current_user.bio = data['bio']

        # Update profile fields if profile exists
        if current_user.profile:
            if 'address' in data:
                current_user.profile.address = data['address']
            if 'emergency_contact_name' in data:
                current_user.profile.emergency_contact_name = data['emergency_contact_name']
            if 'emergency_contact_phone' in data:
                current_user.profile.emergency_contact_phone = data['emergency_contact_phone']
        else:
            # Create profile if it doesn't exist
            profile = UserProfile(
                user_id=current_user.id,
                address=data.get('address'),
                emergency_contact_name=data.get('emergency_contact_name'),
                emergency_contact_phone=data.get('emergency_contact_phone')
            )
            db.session.add(profile)

        db.session.commit()

        return api_response(
            data={'user': {
                'id': current_user.id,
                'first_name': current_user.first_name,
                'last_name': current_user.last_name,
                'full_name': current_user.full_name,
                'phone': current_user.phone,
                'bio': current_user.bio
            }},
            message="Profile updated successfully"
        )

    except Exception as e:
        return handle_api_error(e)
