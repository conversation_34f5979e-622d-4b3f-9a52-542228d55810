import os
from datetime import timedelta

class Config:
    # Flask config
    DEBUG = os.environ.get('FLASK_DEBUG', 'True') == 'True'
    TESTING = False

    # Database config
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        "pool_recycle": 300,
        "pool_pre_ping": True,
    }

    # API keys
    OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')
    PERPLEXITY_API_KEY = os.environ.get('PERPLEXITY_API_KEY')

    # Application config
    COMPANY_NAME = "DatVinci"
    APPLICATION_NAME = "DatPortal"
    UPLOAD_FOLDER = os.path.join('static', 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max upload size
    PASSWORD_RESET_TOKEN_EXPIRATION_SECONDS = 3600 * 24  # 24 ore

    # Email config
    MAIL_SERVER = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')
    MAIL_PORT = int(os.environ.get('MAIL_PORT', 587))
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'True') == 'True'
    MAIL_USE_SSL = os.environ.get('MAIL_USE_SSL', 'False') == 'True'
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER', f'noreply@{COMPANY_NAME.lower()}.com')

    # Session management
    SESSION_PERMANENT = True
    PERMANENT_SESSION_LIFETIME = timedelta(minutes=30)  # Idle timeout
    ABSOLUTE_SESSION_LIFETIME = 3600  # Absolute timeout in seconds
    REMEMBER_COOKIE_DURATION = timedelta(days=7)

    # Cookie configuration for CORS/SPA
    SESSION_COOKIE_SECURE = False  # Set to True in production with HTTPS
    SESSION_COOKIE_HTTPONLY = True  # Prevent XSS attacks
    SESSION_COOKIE_SAMESITE = 'Lax'  # Allow cross-origin requests
    SESSION_COOKIE_NAME = 'session'
